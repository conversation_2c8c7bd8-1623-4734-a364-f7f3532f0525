/**
 * Authentication Rate Limiting Debugger
 * 
 * This utility helps debug and monitor authentication rate limiting issues
 * by tracking API calls, refresh attempts, and providing detailed logging.
 */

import { addAuthEventListener, getAuthState } from './authManager';

interface ApiCallLog {
  timestamp: number;
  endpoint: string;
  component: string;
  status: 'pending' | 'success' | 'error' | 'rate_limited';
  error?: string;
  duration?: number;
}

interface RefreshAttemptLog {
  timestamp: number;
  reason: string;
  status: 'started' | 'success' | 'failed' | 'rate_limited';
  error?: string;
  duration?: number;
}

// Logging storage
const apiCallLogs: ApiCallLog[] = [];
const refreshAttemptLogs: RefreshAttemptLog[] = [];
const MAX_LOGS = 100; // Keep last 100 entries

/**
 * Log an API call attempt
 */
export const logApiCall = (endpoint: string, component: string): number => {
  const logId = Date.now();
  const log: ApiCallLog = {
    timestamp: logId,
    endpoint,
    component,
    status: 'pending'
  };
  
  apiCallLogs.push(log);
  if (apiCallLogs.length > MAX_LOGS) {
    apiCallLogs.shift();
  }
  
  console.log(`📡 API Call: ${component} -> ${endpoint}`);
  return logId;
};

/**
 * Update an API call log with result
 */
export const updateApiCallLog = (
  logId: number, 
  status: 'success' | 'error' | 'rate_limited', 
  error?: string
) => {
  const log = apiCallLogs.find(l => l.timestamp === logId);
  if (log) {
    log.status = status;
    log.error = error;
    log.duration = Date.now() - log.timestamp;
    
    const statusEmoji = status === 'success' ? '✅' : status === 'rate_limited' ? '⏱️' : '❌';
    console.log(`${statusEmoji} API Call Result: ${log.component} -> ${log.endpoint} (${log.duration}ms)`, 
                error ? { error } : '');
  }
};

/**
 * Log a refresh attempt
 */
export const logRefreshAttempt = (reason: string): number => {
  const logId = Date.now();
  const log: RefreshAttemptLog = {
    timestamp: logId,
    reason,
    status: 'started'
  };
  
  refreshAttemptLogs.push(log);
  if (refreshAttemptLogs.length > MAX_LOGS) {
    refreshAttemptLogs.shift();
  }
  
  console.log(`🔄 Refresh Attempt: ${reason}`);
  return logId;
};

/**
 * Update a refresh attempt log with result
 */
export const updateRefreshAttemptLog = (
  logId: number, 
  status: 'success' | 'failed' | 'rate_limited', 
  error?: string
) => {
  const log = refreshAttemptLogs.find(l => l.timestamp === logId);
  if (log) {
    log.status = status;
    log.error = error;
    log.duration = Date.now() - log.timestamp;
    
    const statusEmoji = status === 'success' ? '✅' : status === 'rate_limited' ? '⏱️' : '❌';
    console.log(`${statusEmoji} Refresh Result: ${log.reason} (${log.duration}ms)`, 
                error ? { error } : '');
  }
};

/**
 * Analyze recent API call patterns
 */
export const analyzeApiCallPatterns = () => {
  const recentCalls = apiCallLogs.slice(-20); // Last 20 calls
  const now = Date.now();
  const last5Minutes = recentCalls.filter(log => now - log.timestamp < 5 * 60 * 1000);
  
  const analysis = {
    totalCalls: recentCalls.length,
    callsLast5Min: last5Minutes.length,
    errorRate: recentCalls.filter(log => log.status === 'error').length / recentCalls.length,
    rateLimitedCalls: recentCalls.filter(log => log.status === 'rate_limited').length,
    componentBreakdown: {} as Record<string, number>,
    endpointBreakdown: {} as Record<string, number>,
    simultaneousCalls: [] as { timestamp: number; count: number; components: string[] }[]
  };
  
  // Component and endpoint breakdown
  recentCalls.forEach(log => {
    analysis.componentBreakdown[log.component] = (analysis.componentBreakdown[log.component] || 0) + 1;
    analysis.endpointBreakdown[log.endpoint] = (analysis.endpointBreakdown[log.endpoint] || 0) + 1;
  });
  
  // Find simultaneous calls (within 100ms of each other)
  const timeGroups = new Map<number, ApiCallLog[]>();
  recentCalls.forEach(log => {
    const timeSlot = Math.floor(log.timestamp / 100) * 100; // Group by 100ms slots
    if (!timeGroups.has(timeSlot)) {
      timeGroups.set(timeSlot, []);
    }
    timeGroups.get(timeSlot)!.push(log);
  });
  
  timeGroups.forEach((logs, timestamp) => {
    if (logs.length > 1) {
      // Use ES5-compatible approach for unique components
      const componentSet = new Set(logs.map(log => log.component));
      const uniqueComponents: string[] = [];
      componentSet.forEach(component => uniqueComponents.push(component));

      analysis.simultaneousCalls.push({
        timestamp,
        count: logs.length,
        components: uniqueComponents
      });
    }
  });
  
  return analysis;
};

/**
 * Generate a comprehensive debug report
 */
export const generateDebugReport = () => {
  const authState = getAuthState();
  const apiAnalysis = analyzeApiCallPatterns();
  const recentRefreshes = refreshAttemptLogs.slice(-10);
  
  const report = {
    timestamp: new Date().toISOString(),
    authState,
    apiCallAnalysis: apiAnalysis,
    recentRefreshAttempts: recentRefreshes,
    recommendations: [] as string[]
  };
  
  // Generate recommendations
  if (apiAnalysis.simultaneousCalls.length > 0) {
    report.recommendations.push(
      `Found ${apiAnalysis.simultaneousCalls.length} instances of simultaneous API calls. Consider implementing sequential loading.`
    );
  }
  
  if (apiAnalysis.rateLimitedCalls > 0) {
    report.recommendations.push(
      `${apiAnalysis.rateLimitedCalls} API calls were rate limited. Check refresh timing and component initialization.`
    );
  }
  
  if (authState.consecutiveFailures > 0) {
    report.recommendations.push(
      `${authState.consecutiveFailures} consecutive refresh failures detected. Check token validity and network connectivity.`
    );
  }
  
  if (apiAnalysis.errorRate > 0.2) {
    report.recommendations.push(
      `High error rate (${Math.round(apiAnalysis.errorRate * 100)}%). Check authentication state and API connectivity.`
    );
  }
  
  return report;
};

/**
 * Start monitoring auth events
 */
export const startAuthMonitoring = (): (() => void) => {
  console.log('🔍 Starting auth rate limiting monitoring...');
  
  const cleanup = addAuthEventListener((event, data) => {
    switch (event) {
      case 'refresh_start':
        logRefreshAttempt('Auth event triggered');
        break;
      case 'refresh_success':
        console.log('✅ Auth refresh successful');
        break;
      case 'refresh_failure':
        console.log('❌ Auth refresh failed:', data);
        break;
    }
  });
  
  return cleanup;
};

/**
 * Display debug information in console
 */
export const displayDebugInfo = () => {
  const report = generateDebugReport();
  
  console.group('🔐 Authentication Rate Limiting Debug Report');
  console.log('Generated at:', report.timestamp);
  
  console.group('📊 Auth State');
  console.log('Currently refreshing:', report.authState.isRefreshing);
  console.log('Last refresh attempt:', new Date(report.authState.lastRefreshAttempt).toLocaleTimeString());
  console.log('Consecutive failures:', report.authState.consecutiveFailures);
  console.log('Can refresh:', report.authState.canRefresh);
  console.groupEnd();
  
  console.group('📡 API Call Analysis');
  console.log('Total recent calls:', report.apiCallAnalysis.totalCalls);
  console.log('Calls last 5 min:', report.apiCallAnalysis.callsLast5Min);
  console.log('Error rate:', Math.round(report.apiCallAnalysis.errorRate * 100) + '%');
  console.log('Rate limited calls:', report.apiCallAnalysis.rateLimitedCalls);
  console.log('Component breakdown:', report.apiCallAnalysis.componentBreakdown);
  console.log('Simultaneous calls:', report.apiCallAnalysis.simultaneousCalls);
  console.groupEnd();
  
  if (report.recommendations.length > 0) {
    console.group('💡 Recommendations');
    report.recommendations.forEach(rec => console.log('•', rec));
    console.groupEnd();
  }
  
  console.groupEnd();
  
  return report;
};

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).authRateLimitDebugger = {
    logApiCall,
    updateApiCallLog,
    logRefreshAttempt,
    updateRefreshAttemptLog,
    analyzeApiCallPatterns,
    generateDebugReport,
    displayDebugInfo,
    startAuthMonitoring
  };
  
  console.log('🔧 Auth rate limit debugger available at window.authRateLimitDebugger');
}
