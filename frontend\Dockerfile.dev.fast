# Fast startup frontend Dockerfile for development
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install minimal system dependencies
RUN apk add --no-cache curl

# Copy package files
COPY package*.json ./

# Install dependencies with speed optimizations
RUN npm ci --silent --no-audit --no-fund && \
    npm cache clean --force

# Copy configuration files
COPY tsconfig.json ./

# Copy environment template (will be overridden by volume mounts)
COPY .env* ./

# Create directories and copy setupProxy.js to correct location
RUN mkdir -p src public
COPY src/setupProxy.js ./src/

# Expose port
EXPOSE 3000

# Simple health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=45s --retries=2 \
    CMD curl -f http://localhost:3000 || exit 1

# Start development server
CMD ["npm", "start"]