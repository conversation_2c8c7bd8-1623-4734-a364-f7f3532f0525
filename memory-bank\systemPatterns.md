## Writer v2 System Patterns & Conventions

**Backend (Flask):**

*   **Blueprints:** Application is organized into modules using Flask Blueprints (e.g., `auth_bp`, `content_bp`, `ai_assistant_bp`).

    ### Example: Defining and Registering a Blueprint
    ```python
    # app/auth/routes.py
    from flask import Blueprint, render_template, request, g
    from .utils import login_required # Example utility

    auth_bp = Blueprint('auth', __name__, url_prefix='/auth', template_folder='templates')

    @auth_bp.route('/login')
    def login():
        # Access request-specific data via g
        user_agent = request.headers.get('User-Agent')
        g.request_info = f"Login attempt from {user_agent}"
        return render_template('auth/login.html')

    @auth_bp.route('/profile')
    @login_required
    def profile():
        # Access data stored in g from another part of the request lifecycle (e.g., middleware)
        user = getattr(g, 'user', None)
        return f"Welcome {user.username if user else 'Guest'}!"
    ```

    ```python
    # app/__init__.py (Application Factory)
    from flask import Flask
    from .auth.routes import auth_bp
    from .content.routes import content_bp
    # ... other imports

    def create_app(config_name='default'):
        app = Flask(__name__)
        # ... load config ...

        # Register blueprints
        app.register_blueprint(auth_bp)
        app.register_blueprint(content_bp, url_prefix='/content') # Example with prefix override

        # ... other app setup (extensions, error handlers) ...

        return app
    ```
*   **Repository Pattern:** Each data model, rather than having direct database calls mixed into route handlers or business logic, uses a Repository class:
    * All repositories descend from a base `SupabaseRepository` that handles common operations.
    * Repositories are constructed with a `supabase_client`.
    * Route handlers get repositories from Flask's `g` object, populated by request hooks.
    * The pattern allows:
      * Consistent error handling
      * Centralized logging
      * Query reuse
      * Unit testability (repositories can be mocked)
*   **Request Context (`g`):** Supabase client and repository instances are attached to the Flask request context (`g`) using a `before_request` hook (`app/utils/supabase_hooks.py`) for easy access within routes.

    ### Example: Using `g` for Request-Scoped Data
    ```python
    from flask import Flask, g, request, current_app
    import time

    app = Flask(__name__)

    @app.before_request
    def before_request_func():
        # Store start time for calculating request duration
        g.start_time = time.time()
        # Example: Load user based on API key/session (replace with actual logic)
        # This data will be available throughout the request.
        user_id = request.headers.get('X-User-ID')
        if user_id:
            # In a real app, fetch user from DB based on user_id
            g.user = {'id': user_id, 'name': f'User_{user_id}'} # Example user object
        else:
            g.user = None
        current_app.logger.info(f"Request started for user: {getattr(g.user, 'id', 'Guest')}")

    @app.after_request
    def after_request_func(response):
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            current_app.logger.info(f"Request duration: {duration:.4f}s")
        # You can modify the response here if needed
        response.headers['X-Request-Duration-ms'] = str(duration * 1000)
        return response

    @app.teardown_request
    def teardown_request_func(error=None):
        # This runs even if an exception occurred during the request
        # Useful for cleanup tasks, like closing DB connections attached to g
        db_conn = g.pop('db_conn', None)
        if db_conn:
            db_conn.close()
            current_app.logger.info("Database connection closed.")
        if error:
            current_app.logger.error(f"Request ended with error: {error}")

    @app.route('/')
    def index():
        # Access g.user set in before_request
        user = getattr(g, 'user', None)
        greeting = f"Hello, {user['name']}!" if user else "Hello, Guest!"
        # Access g.start_time (though less common in the view itself)
        start = getattr(g, 'start_time', 0)
        return f"{greeting} Request started at {start}"

    # Example of attaching a resource to g (like a DB connection)
    def get_db():
        if 'db_conn' not in g:
            # Simulate connecting to a database
            g.db_conn = {'status': 'connected', 'close': lambda: print("Simulating DB close")}
            current_app.logger.info("Simulated DB connection opened.")
        return g.db_conn

    @app.route('/data')
    def get_data():
        db = get_db() # Get or create DB connection via g
        # Use the db connection...
        return f"Using DB connection with status: {db['status']}"

    if __name__ == '__main__':
        app.run(debug=True)
    ```
*   **Authentication:** Backend uses Supabase JWTs. Authentication is enforced via the `@supabase_auth_required` decorator (`app/utils/supabase_auth.py`).

    ### Example: Supabase Auth Decorator Usage
    ```python
    # app/auth/routes.py (Example Endpoint)
    from app.utils.supabase_auth import supabase_auth_required, get_current_user

    @auth_bp.route("/profile", methods=["GET"])
    @supabase_auth_required  # Apply the decorator
    def get_profile():
        user = get_current_user() # Access user info from g.user
        if not user:
            return jsonify({"error": "User not found after auth check"}), 404 # Should not happen if decorator works
        # Access user details like user.id or user.get('email')
        return jsonify(user_id=user.id, email=user.get('email'))

    ```

    **Decorator Logic (`@supabase_auth_required` in `app/utils/supabase_auth.py`):**
    1.  Extracts the `Bearer` token from the `Authorization` header.
    2.  Verifies the JWT signature using `PyJWT` (`jwt.decode`) against secrets defined in Flask config (`JWT_SECRET_KEY` or `SUPABASE_JWT_SECRET`). It logs warnings if secrets are missing or if verification is bypassed in development.
    3.  If the token is valid, extracts the `sub` (user ID).
    4.  **RLS Context:** Temporarily sets the Supabase PostgREST client's auth context to the *user's token* (`supabase.postgrest.auth(token)`).
    5.  Attempts to fetch the full user profile from the database using `g.user_repository.get_user_by_id()`. This fetch respects RLS policies because of the temporary context switch.
    6.  **RLS Reset:** Resets the PostgREST auth context back to the *service role key* (`supabase.postgrest.auth(service_key)`) to prevent subsequent operations from unintentionally using the user's permissions.
    7.  If the DB profile fetch succeeds, attaches the full user profile object to `g.user`.
    8.  If the DB profile fetch fails (but the token was valid), it creates a `FallbackUser` object containing basic claims from the JWT (ID, email) and attaches it to `g.user`. This allows processing requests even if the profile is inaccessible, relying only on the validated token identity.
    9.  If token extraction or verification fails at any point, it aborts the request with a 401 Unauthorized JSON error.
    10. Helper functions like `get_current_user()` provide access to `g.user` within protected routes.
*   **Repository Initialization Pattern:** To ensure repositories are available when needed:
    * A before_request hook registers `init_supabase_repositories()` to initialize repositories for each request
    * The `@supabase_auth_required` decorator checks for missing repositories and reinitializes them if needed
    * Route handlers include fallback initialization logic as a last resort
    * Error paths clearly report specific failures
    * Repositories follow this initialization hierarchy:
      1. Global initialization via before_request hook (primary method)
      2. Auth decorator check and initialization (backup)
      3. In-route handler recovery (last resort)
*   **Authentication & Authorization Pattern:** Authentication is handled via JWT tokens issued by Supabase Auth
    * JWT token verification has multiple fallback mechanisms:
      * Try verification with `JWT_SECRET_KEY`
      * If that fails, try with `SUPABASE_JWT_SECRET`
      * In development mode, provide extra diagnostics with signature verification bypass
    * Auth failures result in 401 Unauthorized responses
    * The `@supabase_auth_required` decorator on routes:
      * Verifies token validity
      * Places user info in Flask's `g` context
      * Initializes repositories if needed
      * Allows the route handler to execute only if auth succeeds
*   **FallbackUser Pattern:** When database user lookup fails but a valid JWT is present:
    * Create a `FallbackUser` object from JWT claims
    * Implement full dictionary-like interface (compatible with DB-sourced users)
    * Support both attribute access (`user.id`) and dictionary access (`user["id"]`, `user.get("id")`)
    * Provide all expected dict-like methods: `get()`, `__getitem__`, `__contains__`, `__iter__`, `keys()`, `values()`, `items()`
    * This allows handlers to work with either real DB users or fallback JWT-derived users
*   **Flask Context Usage Pattern:** Flask's `g` object stores request-scoped data:
    * Repositories (`g.content_item_repository`, etc.)
    * Current user (`g.user`)
    * Authentication token (`g.token`)
    * User ID (`g.user_id`)
*   **Error Handling Pattern:** API errors return JSON with an `{"error": "message"}` structure
    * HTTP status codes match the error type (401 auth, 404 not found, 500 server)
    * Detailed error info is logged but not exposed to clients
    * Repository methods catch and transform low-level exceptions
*   **Centralized Configuration:** Environment variables (`.env` loaded by `dotenv`) are mapped into `app.config` during app creation (`app/__init__.py`). AI model config is further centralized in `app/ai_assistant/config.py`.
*   **Service Layer:** Some logic is encapsulated in services (e.g., `app/youtube/youtube_service/__init__.py`) although route handlers still contain significant logic.
*   **Centralized AI Prompts:** AI system prompts are managed in `app/ai_assistant/prompts.py`.

    ### Example: OpenAI Python Client Usage (Backend)
    ```python
    # app/services/ai_service.py (Conceptual Example)
    import os
    from openai import OpenAI, AsyncOpenAI
    from flask import current_app

    # Initialize clients (consider lazy loading or app context factory)
    # Assumes OPENAI_API_KEY and potentially OPENAI_BASE_URL are set in env
    # or configured elsewhere (e.g., app/ai_assistant/config.py)
    try:
        sync_client = OpenAI()
        async_client = AsyncOpenAI()
        current_app.logger.info("OpenAI clients initialized.")
    except Exception as e:
        current_app.logger.error(f"Failed to initialize OpenAI clients: {e}")
        sync_client = None
        async_client = None

    def generate_chat_completion(messages: list, model="gpt-4o") -> str | None:
        if not sync_client:
            current_app.logger.error("OpenAI sync client not available.")
            return None
        try:
            completion = sync_client.chat.completions.create(
                model=model,
                messages=messages,
                # Add other parameters like temperature, max_tokens etc. as needed
            )
            return completion.choices[0].message.content
        except Exception as e:
            current_app.logger.error(f"Error calling OpenAI ChatCompletion: {e}")
            return None

    async def generate_chat_completion_async(messages: list, model="gpt-4o") -> str | None:
        if not async_client:
             current_app.logger.error("OpenAI async client not available.")
             return None
        try:
            completion = await async_client.chat.completions.create(
                model=model,
                messages=messages,
            )
            return completion.choices[0].message.content
        except Exception as e:
            current_app.logger.error(f"Error calling OpenAI ChatCompletion (async): {e}")
            return None

    # Example usage within a Flask route
    # from flask import request, jsonify
    # from .ai_service import generate_chat_completion
    # @app.route('/ask', methods=['POST'])
    # def ask_ai():
    #     user_prompt = request.json.get('prompt')
    #     if not user_prompt:
    #         return jsonify({"error": "Prompt is required"}), 400
    #     messages = [
    #         {"role": "system", "content": "You are a helpful assistant."},
    #         {"role": "user", "content": user_prompt}
    #     ]
    #     response = generate_chat_completion(messages)
    #     if response:
    #         return jsonify({"response": response})
    #     else:
    #         return jsonify({"error": "Failed to get response from AI"}), 500
    ```
*   **Error Handling:** Basic Flask error handlers (`@app.errorhandler`) are registered in `app/__init__.py`. Repositories have a `handle_error` method.

    ### Example: Flask Error Handlers
    ```python
    # app/errors.py
    from flask import render_template, jsonify, request
    from werkzeug.exceptions import HTTPException
    from http import HTTPStatus

    def register_error_handlers(app):

        @app.errorhandler(HTTPStatus.NOT_FOUND) # Handle 404
        def not_found_error(error):
            # Differentiate between API and HTML requests
            if request.accept_mimetypes.accept_json and \
               not request.accept_mimetypes.accept_html:
                return jsonify(error=f"Resource not found: {error.description}"), HTTPStatus.NOT_FOUND
            return render_template('errors/404.html'), HTTPStatus.NOT_FOUND

        @app.errorhandler(HTTPStatus.INTERNAL_SERVER_ERROR) # Handle 500
        def internal_error(error):
            # Log the error details
            app.logger.error(f"Server Error: {error}", exc_info=True)
            # Could also notify admins here (e.g., via email or Sentry)
            if request.accept_mimetypes.accept_json and \
               not request.accept_mimetypes.accept_html:
                 return jsonify(error="Internal server error occurred."), HTTPStatus.INTERNAL_SERVER_ERROR
            return render_template('errors/500.html'), HTTPStatus.INTERNAL_SERVER_ERROR

        @app.errorhandler(HTTPException) # Generic handler for other HTTP errors
        def handle_http_exception(e):
             if request.accept_mimetypes.accept_json and \
                not request.accept_mimetypes.accept_html:
                 response = e.get_response()
                 response.data = json.dumps({
                     "code": e.code,
                     "name": e.name,
                     "error": e.description,
                 })
                 response.content_type = "application/json"
                 return response
             # For HTML, rely on Werkzeug's default HTML error pages or add templates
             return e

        @app.errorhandler(Exception) # Catch-all for non-HTTP exceptions
        def handle_generic_exception(e):
            # Important: Don't accidentally catch HTTPExceptions again if already handled
            if isinstance(e, HTTPException):
                return e # Let the HTTPException handler deal with it

            # Handle unexpected errors
            app.logger.error(f"Unhandled Exception: {e}", exc_info=True)
            if request.accept_mimetypes.accept_json and \
               not request.accept_mimetypes.accept_html:
                return jsonify(error="An unexpected error occurred."), HTTPStatus.INTERNAL_SERVER_ERROR
            return render_template('errors/500.html'), HTTPStatus.INTERNAL_SERVER_ERROR

    # In app/__init__.py
    # from . import errors
    # def create_app(...):
    #    app = Flask(...)
    #    ...
    #    errors.register_error_handlers(app)
    #    ...
    #    return app
    ```
*   **Logging:** Centralized logging configuration (`monitoring.py`, configured in `app/__init__.py`) using Python's `logging` module, including file rotation.

**Frontend (React):**

*   **Component-Based Architecture:** Standard React component structure (`src/components`, `src/pages`).
*   **Redux Toolkit:** Used for non-authentication global state management (e.g., UI state like dark mode/sidebar, activity). `createSlice`, `createAsyncThunk` are used. `useAppDispatch`, `useAppSelector` hooks provide type safety.
*   **React Context API:** Used for specific state sharing needs, notably Authentication via `AuthContext`. Also used for Content Selection (`ContentSelectionContext`) and Business Context Selection (`BusinessContextSelectionContext`).
*   **API Service Layer:** Centralized API communication using an `axios` instance (`src/services/api.ts`) with interceptors to automatically attach auth tokens (Supabase JWT) and handle common errors (like 401).
*   **Environment Configuration:** Uses `src/services/envConfig.ts` which reads from `window.ENV` (injected by `public/env-config.js`) or `process.env` for accessing environment variables.
*   **Routing:** Uses `react-router-dom` v6 for client-side routing (`App.tsx`). Includes protected route logic.
*   **UI Library:** Material UI (MUI) v5 is used extensively for components and styling. Custom theming is applied via `src/styles/theme.ts` and `ThemeProvider`.
*   **Styling:** Mix of MUI `sx` prop, global CSS (`src/styles/index.css`), and component-specific styles.
*   **Lazy Loading:** React Suspense and `lazy` are used for code-splitting page components (`App.tsx`).
*   **Supabase Client:** Uses `@supabase/supabase-js` client (`src/lib/supabase.ts`).

    ### Example: Supabase JS Client Usage
    ```typescript
    // src/lib/supabase.ts
    import { createClient, SupabaseClient } from '@supabase/supabase-js';

    const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
    const supabaseAnonKey = process.env.REACT_APP_SUPABASE_KEY; // Public Anon Key

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Supabase URL and Key must be defined in environment variables');
    }

    // Note: Use the public anon key for client-side initialization
    export const supabase: SupabaseClient = createClient(supabaseUrl, supabaseAnonKey);

    // Example function to fetch data (using async/await)
    export async function fetchUserProfile(userId: string) {
      const { data, error } = await supabase
        .from('profiles')
        .select('username, avatar_url')
        .eq('id', userId)
        .single(); // Use .single() if expecting one row

      if (error) {
        console.error('Error fetching profile:', error);
        return null;
      }
      return data;
    }
    ```
*   **Supabase Auth Integration:** Frontend auth flow is primarily managed through `AuthContext` (`src/context/AuthContext.tsx`), which handles Supabase session state and provides user/session info.

    ### Example: Supabase JS Auth Usage (within AuthContext)
    ```typescript
    // src/context/AuthContext.tsx (simplified example)
    import React, { createContext, useContext, useState, useEffect } from 'react';
    import { supabase } from '../lib/supabase';
    import type { Session, User } from '@supabase/supabase-js';

    interface AuthContextType {
      session: Session | null;
      user: User | null;
      signOut: () => Promise<void>;
      // Add signIn, signUp methods as needed
    }

    const AuthContext = createContext<AuthContextType | undefined>(undefined);

    export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
      const [session, setSession] = useState<Session | null>(null);
      const [user, setUser] = useState<User | null>(null);

      useEffect(() => {
        // Fetch initial session
        supabase.auth.getSession().then(({ data: { session } }) => {
          setSession(session);
          setUser(session?.user ?? null);
        });

        // Listen for auth changes
        const { data: authListener } = supabase.auth.onAuthStateChange(
          (_event, session) => {
            setSession(session);
            setUser(session?.user ?? null);
          }
        );

        // Cleanup listener on unmount
        return () => {
          authListener?.subscription.unsubscribe();
        };
      }, []);

      const signOut = async () => {
        const { error } = await supabase.auth.signOut();
        if (error) console.error('Error signing out:', error);
        // State will update via onAuthStateChange listener
      };

      const value = { session, user, signOut };

      return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
    };

    export const useAuth = () => {
      const context = useContext(AuthContext);
      if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
      }
      return context;
    };
    ```
*   **Error Handling:** Includes utility for suppressing common `ResizeObserver` warnings (`src/utils/errorHandlers.ts`). Snackbar/Alert components used for user notifications.

**General:**

*   **Docker:** `docker-compose.yml` defines development/deployment setup with separate backend, frontend, and database services.
*   **Supabase Focus:** The project has undergone a migration to use Supabase as the primary backend-as-a-service, handling both auth and data.

## AI Workflow Orchestration (Future Direction)

For building complex, multi-step AI-driven features (e.g., advanced content generation, analysis pipelines), the planned approach is a hybrid model leveraging dedicated workflow orchestration frameworks and component libraries:

*   **Primary Orchestration Framework: PocketFlow**
    *   **Purpose:** Define the overall structure and control flow of complex AI tasks.
    *   **Core Constructs:** Utilize PocketFlow `Flows` to define the sequence of operations and `Nodes` (with `prep`, `exec`, `post` methods) to encapsulate individual processing steps.
    *   **State Management:** Leverage the PocketFlow `Shared Store` for explicit and traceable state management between `Nodes` within a `Flow`.
    *   **Benefits:** Provides structure, debuggability, and maintainability for complex sequences compared to implementing orchestration manually or solely within a general-purpose library.

*   **Component Library: Langchain**
    *   **Purpose:** Provide specific, pre-built components and utilities to be used *within* the `exec` method of PocketFlow `Nodes`.
    *   **Usage:** Utilize Langchain for:
        *   Standardized LLM interface wrappers (`LLMs`).
        *   Prompt templating and management (`PromptTemplate`).
        *   Document loading, splitting, and vector store interactions (`document_loaders`, `text_splitters`, `vectorstores`).
        *   Specific, self-contained tools or simple chains that perform a distinct sub-task within a Node's responsibility.
    *   **Avoid:** Implementing complex Langchain `Chains` or `Agents` with their own control flow or memory *inside* a single PocketFlow Node. Break such logic down into multiple PocketFlow `Nodes` to maintain workflow clarity.

*   **Graph-Based Alternative/Extension: LangGraph**
    *   **Purpose:** Explore LangGraph as an alternative or complementary approach, particularly for workflows that are highly dynamic, cyclical, or require sophisticated agentic behavior where the exact sequence isn't strictly linear.
    *   **Consideration:** LangGraph builds on Langchain primitives but offers a stateful graph abstraction. Evaluate its suitability for specific workflows where its model might be more natural than PocketFlow's linear/branching `Flows`. May be integrated *within* a PocketFlow Node for highly complex sub-tasks or used as the primary orchestrator if the workflow is predominantly graph-like.

*   **Integration Principle:** The goal is to combine the structured orchestration and state management of PocketFlow (or potentially LangGraph for specific cases) with the rich component ecosystem of Langchain, using each for its strengths. PocketFlow defines the "assembly line," while Langchain provides many of the specialized "tools" used at each station (Node).

*   **Iterative Test Debugging:** Address test failures systematically:
    1.  Run the specific failing test (`pytest -k test_name`).
    2.  Analyze the traceback and any logging output (`-s -v` flags are helpful).
    3.  Formulate a hypothesis about the cause (e.g., incorrect mock, assertion error, logic error).
    4.  Examine relevant application code and test setup code.
    5.  Implement a fix (adjust mock, change assertion, modify test data, fix application code).
    6.  Re-run the test. Repeat until it passes.
*   **Precise Mocking:** When mocking dependencies, especially classes or factory functions:
    *   Ensure the patch target string is correct (points to where the object is *looked up*, not necessarily where it's defined).
    *   Configure the `return_value` of mocks appropriately. For factory functions (e.g., `get_ai_generator`), the factory patch's `return_value` should be the mock *instance* you want the application code to receive.
    *   Verify calls on the correct object (often the mock *instance*, not the mock class or factory function itself). Use `mock_instance.method.assert_called_once_with(...)`.
*   **Context Management in Tests:** Be mindful of how application context (like Flask's `g`) is managed and populated in tests. Ensure necessary setup (fixtures, explicit context blocks) is performed before the code under test accesses context-bound objects. Patching the classes used by context-populating hooks is often reliable.
*   **Assertion Accuracy:** Write assertions that precisely reflect the expected state or behavior.
    *   Use `assert_called_once_with(...)` for specific calls.
    *   Use `assert_not_called()` when a call should *not* happen.
    *   Check call counts (`call_count`) if a method is expected to be called multiple times.
    *   Verify the structure and content of data passed to mocks (e.g., `call_args`, `call_kwargs`).

## Frontend Development

### API Interaction Pattern

- **Centralized API Service:** Use a dedicated service file (e.g., `src/services/api.ts` for base Axios config, `src/services/youtubeService.ts` for feature-specific calls) to encapsulate all backend communication.
  - **Benefits:** Improves code organization, simplifies testing (mocking), centralizes error handling and request/response interception (e.g., auth tokens).
  - **Implementation:** Components should import functions from service files rather than calling `axios` or `fetch` directly.
  - **Example:** `frontend/src/services/youtubeService.ts` encapsulates the call to `/youtube/save-to-library`.

### Structured Content Rendering Pattern

- **Problem:** AI responses may contain structured data (e.g., JSON with predefined keys like `hook`, `outline`, `cta`) that should be displayed more clearly than raw Markdown.
- **Solution:** Use a dedicated React component (`frontend/src/components/StructuredJsonRenderer.tsx`) within the message display component (`ChatMessageItem`).
  - This component attempts to parse the message content (after stripping potential Markdown code fences using a utility like `stripFences.ts`).
  - If parsing is successful and known keys (`hook`, `outline`, `cta`) are found, it renders each piece of data with appropriate MUI components (e.g., `Typography` for text, `List` for arrays/outlines) and labels.
  - If parsing fails or the structure is not recognized, it falls back to rendering the original content using the standard `<MarkdownRenderer />`.
- **Benefits:** Provides a user-friendly display for structured AI outputs, improving readability over raw JSON or plain text.

### In-Message Feedback Pattern

- **Problem:** Need a consistent way to gather user feedback (e.g., rating, comments) on specific items displayed in the UI, such as AI-generated messages.
- **Solution:** Implement a reusable feedback widget component (`frontend/src/components/ScriptFeedbackWidget.tsx`) placed directly below the content it relates to (e.g., within `ChatMessageItem`).
  - The widget presents simple rating controls (e.g., 👍/👎 icons).
  - Upon interaction, it can reveal an optional text input for comments.
  - On submission, it calls a dedicated backend endpoint (`/api/feedback`) via a helper function in the API service layer (`frontend/src/services/api.ts - submitFeedback`).
  - The payload includes identifiers for the context (e.g., `session_id`, `message_id`), a specific `feedback_type` (e.g., `'script_usability'`), the `rating` ('up'/'down'), and the optional `comment`.
  - The backend endpoint (`app/feedback/routes.py`) validates the payload, associates it with the authenticated user, and persists it to a dedicated `feedback` table in the database (Supabase).
  - Use UI notifications (e.g., Snackbar) passed down via props (`setNotification`) to inform the user of submission success or failure.
- **Database Schema:** The `feedback` table should include columns for `id`, `created_at`, `user_id` (FK to `auth.users`), context identifiers (`session_id`, `message_id`), `feedback_type` (TEXT, with CHECK constraint), `rating` (TEXT, with CHECK constraint for 'up'/'down'), and `comment` (TEXT, nullable). RLS policies should ensure users can only insert their own feedback.
- **Data Migration:** When changing rating systems (e.g., integer to text 'up'/'down'), existing data must be migrated (e.g., using an `UPDATE` script mapping old values) before applying new `CHECK` constraints.
- **Benefits:** Provides a direct, low-friction way for users to provide feedback on specific pieces of content, linked to the relevant context and user.

### Multi-Step Wizard API Pattern

- **Problem:** Need a structured way to implement multi-step, AI-driven workflows where each step depends on the output of the previous one, while allowing user interaction/selection at each stage.
- **Solution:** Implement a dedicated set of backend API endpoints, one for each logical step of the wizard.
  - **State Management:** Use a dedicated database table (e.g., `wizard_sessions`) to store the state of each user's wizard session, including inputs and outputs for each step.
  - **Endpoints:** Define specific endpoints (e.g., `/ai/wizard/brainstorm`, `/ai/wizard/hook`, `/ai/wizard/intro`, etc.) within a dedicated Blueprint (`wizard_bp`).
  - **Standardized Parameters:** Use consistent parameter names across all endpoints:
    - `session_id`: UUID of the wizard session
    - `selected_index`: Integer index of the selected item from previous step
    - `business_context_id`: UUID of the business context (for brainstorm stage)
    - `content_idea`: String content idea (for brainstorm stage)
    - `style_preferences`: String style preferences (for edit stage)
  - **Standardized Response Format:**
    - Success: `{"data": {...}, "message": "Success message", "session_id": "uuid-string"}`
    - Error: `{"error": "Error message", "details": {...}}`
  - **Error Handling:** Use helper functions (`error_response`, `success_response`) to ensure consistent response formats.
  - **Request Flow:**
    1. The first endpoint (e.g., `/brainstorm`) creates a new session row in the database and performs the first AI call, saving the result.
    2. Subsequent requests from the client include the `session_id` and any user selections (e.g., `chosen_title_index`).
    3. Each endpoint handler:
       - Retrieves the session data using the `session_id` and validates user ownership.
       - Reads necessary data from previous steps stored in the session row.
       - Validates inputs (e.g., chosen index is within bounds).
       - Builds the appropriate prompt using data from the session and request.
       - Calls the AI generator.
       - Parses the AI response.
       - Updates the session row in the database with the results of the current step and user choices.
       - Returns the generated content for the current step to the client.
  - **Client Interaction:** The frontend UI guides the user through the steps, displaying results from one endpoint and collecting choices needed for the next.
  - **Save/Integration:** The final step might involve saving the completed artifact (e.g., the polished script) to another system (like the Content Library) by calling its existing API (e.g., POST `/content/`).
- **Benefits:**
  - Clear separation of concerns for each step.
  - Enables intermediate user interaction and refinement.
  - Persistent state allows users to potentially pause and resume.
  - Easier to test individual steps.
- **Considerations:** Potential latency due to multiple sequential API calls; consider background processing for long AI steps.

## API Design & Error Handling

*   **RESTful Principles:** Adhere to standard REST principles where applicable (verbs, status codes).
*   **JSON Payloads:** Use JSON for request and response bodies.
*   **Authentication:** Use Supabase JWT via Authorization Bearer token (`supabase_auth_required` decorator). User ID is accessed via `g.user_id`.
*   **Error Responses:**
    *   Use appropriate HTTP status codes (400 for bad input, 401 for unauthenticated, 403 for forbidden, 404 for not found, 500 for server errors, 503 for service unavailable).
    *   **Standard JSON Error Body:** For client errors (4xx) and expected server errors (e.g., failed DB update, AI error), return a JSON body with a consistent structure: `{"error": "Descriptive error message"}`. Avoid generic HTML error pages or vague messages like "Internal server error" for predictable issues. Use `jsonify({"error": message}), HTTPStatus.CODE`.
    *   **Logging:** Log detailed error information including stack traces on the server side for unexpected 500 errors (use `logger.exception`). Log warnings for expected client errors or recoverable issues.
*   **Repository Access:** Access database repositories via the Flask `g` object, initialized by the `init_supabase_repositories` hook (`@supabase_before_request`).
*   **Input Validation:** Perform explicit validation of request payloads (required fields, types, formats) at the beginning of route handlers. Return specific 400 errors for validation failures.

### Example: MUI Custom Theme Provider
```typescript
// src/styles/theme.ts
import { createTheme } from '@mui/material/styles';
import { red } from '@mui/material/colors';

const theme = createTheme({
  palette: {
    primary: {
      main: '#556cd6',
    },
    secondary: {
      main: '#19857b',
    },
    error: {
      main: red.A400,
    },
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
  },
  // Add custom properties
  status: {
    warning: '#ff9800', // Example custom property
  },
});

// Augment the Theme interface to include custom properties
declare module '@mui/material/styles' {
  interface Theme {
    status: {
      warning: React.CSSProperties['color'];
    };
  }
  // Allow configuration using `createTheme`
  interface ThemeOptions {
    status?: {
      warning?: React.CSSProperties['color'];
    };
  }
}

export default theme;
```

```tsx
// src/App.tsx (or similar entry point)
import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import theme from './styles/theme';
import YourAppComponent from './YourAppComponent';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <YourAppComponent />
    </ThemeProvider>
  );
}

export default App;
```

*   **Styling:** Mix of MUI `sx` prop, global CSS (`src/styles/index.css`), and component-specific styles.

    ### Example: MUI `sx` Prop Usage
    ```tsx
    import Box from '@mui/material/Box';
    import Button from '@mui/material/Button';

    function StyledComponent() {
      return (
        <Box
          sx={{
            // Direct CSS values
            width: 300,
            height: 'auto',
            // Theme palette colors
            bgcolor: 'primary.light',
            color: 'text.primary',
            // Theme spacing units (1 = theme.spacing(1) = 8px by default)
            p: 2, // padding: 16px
            mt: 1, // margin-top: 8px
            // Responsive values (array or object syntax)
            borderWidth: { xs: 1, md: 2 },
            borderColor: 'secondary.main',
            borderStyle: 'solid',
            borderRadius: 1, // border-radius: 8px (using theme.shape.borderRadius * 1)
            // Theme typography variants
            typography: 'body1',
            // Theme shadows
            boxShadow: 3,
            // Pseudo-selectors
            '&:hover': {
              bgcolor: 'primary.main',
              color: 'white',
            },
            // Nested selectors
            '& .child-class': {
              fontWeight: 'bold',
            },
            // Accessing custom theme properties
            borderLeft: (theme) => `5px solid ${theme.status.warning}`,
          }}
        >
          This is a styled Box.
          <div className="child-class">This is a child div.</div>
          <Button sx={{ ml: 1 }}>A Button</Button>
        </Box>
      );
    }
    ```

    ### Example: Supabase Python Client Usage (Backend Repository)
    ```python
    # app/repositories/base_repository.py (Conceptual Example)
    import os
    from supabase import create_client, Client
    from flask import current_app, g

    class SupabaseRepository:
        def __init__(self, supabase_client: Client = None):
            if supabase_client:
                self._client = supabase_client
            else:
                # Fallback or specific initialization if needed,
                # but typically rely on client injected via g
                url = os.environ.get("SUPABASE_URL")
                key = os.environ.get("SUPABASE_KEY") # Use Service Role Key on backend
                if not url or not key:
                    raise ValueError("Supabase URL and Key must be set for backend")
                self._client = create_client(url, key)

        @property
        def client(self) -> Client:
            # Prefer client from Flask's g if available (set by hooks)
            if hasattr(g, 'supabase_client') and g.supabase_client:
                return g.supabase_client
            # Otherwise, use the one potentially initialized in __init__
            if not hasattr(self, '_client'):
                 # Attempt initialization if not done and not in request context
                 url = os.environ.get("SUPABASE_URL")
                 key = os.environ.get("SUPABASE_KEY")
                 if not url or not key:
                     raise RuntimeError("Supabase client not initialized and env vars missing.")
                 self._client = create_client(url, key)
                 current_app.logger.warning("Supabase client initialized outside request context in repository.")

            return self._client

    # app/repositories/content_repository.py
    from .base_repository import SupabaseRepository

    class ContentItemRepository(SupabaseRepository):
        def get_item_by_id(self, item_id: str):
            try:
                response = self.client.table('content_items') \
                                     .select('*') \
                                     .eq('id', item_id) \
                                     .single() \
                                     .execute()
                return response.data
            except Exception as e:
                current_app.logger.error(f"Error fetching item {item_id}: {e}")
                return None

        def save_item(self, item_data: dict):
            try:
                response = self.client.table('content_items') \
                                     .upsert(item_data) \
                                     .execute()
                # Check response.data for success/failure details if needed
                return response.data[0] if response.data else None
            except Exception as e:
                current_app.logger.error(f"Error saving item: {e}")
                return None

    # app/utils/supabase_hooks.py (Conceptual - where g.supabase_client is set)
    from flask import g, current_app
    from supabase import create_client
    import os

    def init_supabase_client():
        if 'supabase_client' not in g:
            url = os.environ.get("SUPABASE_URL")
            key = os.environ.get("SUPABASE_KEY") # Service Role Key
            if url and key:
                g.supabase_client = create_client(url, key)
            else:
                current_app.logger.error("Supabase env vars not set for client initialization.")
                g.supabase_client = None

    def register_hooks(app):
        app.before_request(init_supabase_client)
        # Add teardown if needed, though client itself doesn't need explicit closing
    ```