"""
Unit tests for the Supabase YouTubeVideoRepository implementation
Tests the functionality of the YouTubeVideoRepository class
"""

import os
import pytest
import uuid
from datetime import datetime

# Import the repository class
from app.repositories.supabase_video_data_repository import VideoDataRepository

# Check if Supabase is configured
supabase_url = os.environ.get("SUPABASE_URL")
supabase_key = os.environ.get("SUPABASE_KEY")
skip_supabase_tests = not (supabase_url and supabase_key)


@pytest.mark.skipif(skip_supabase_tests, reason="Supabase not configured")
class TestYouTubeVideoRepository:
    """Test the YouTubeVideoRepository class"""

    def test_initialization(self, supabase_client):
        """Test that the repository initializes correctly"""
        repo = VideoDataRepository(supabase_client)

        assert repo is not None
        assert repo.supabase is not None
        assert repo.table_name == "video_data"  # Correct table name

    def test_create_video_data(self, supabase_client, test_user_id):  # Renamed test
        """Test saving a video"""
        repo = VideoDataRepository(supabase_client)

        # Create test video data
        video_data = {
            # Use youtube_id as per repository method
            "youtube_id": f"test-video-{uuid.uuid4()}",
            "title": "Test Video",
            # Pass other fields via metadata if needed
            "metadata": {
                "channel_id": "test-channel",
                "channel_title": "Test Channel",
                "description": "This is a test video",
                "published_at": datetime.utcnow().isoformat(),
                "thumbnail_url": "https://example.com/thumbnail.jpg",
                "duration": "PT5M30S",
                "view_count": 1000,
                "like_count": 100,
                "comment_count": 50,
                # "tags": ["test", "video", "pytest"] # Removed - Column does not exist
            },
        }

        # Call create_video_data with keyword arguments matching its signature
        video = repo.create_video_data(
            user_id=test_user_id,
            youtube_id=video_data["youtube_id"],
            title=video_data["title"],
            metadata=video_data["metadata"],
        )

        # Verify video was saved
        assert video is not None
        assert video.get("user_id") == test_user_id
        assert video.get("youtube_id") == video_data["youtube_id"]  # Check youtube_id
        assert video.get("title") == video_data["title"]
        # Assert metadata fields if needed
        assert (
            video.get("channel_id") == video_data["metadata"]["channel_id"]
        )  # Check top-level field

        # Clean up
        try:
            supabase_client.table("video_data").delete().eq(
                "id", video.get("id")
            ).execute()  # Use correct table name
        except Exception as e:
            print(f"Error cleaning up test video: {e}")

    def test_get_by_id(self, supabase_client, test_user_id):  # Renamed test
        """Test getting video by ID"""
        repo = VideoDataRepository(supabase_client)

        # Create a video to get
        video_id = f"get-test-{uuid.uuid4()}"
        # Use create_video_data
        video = repo.create_video_data(
            user_id=test_user_id,
            youtube_id=video_id,  # Use youtube_id
            title="Get Test Video",
            metadata={"channel_id": "test-channel", "channel_title": "Test Channel"},
        )

        # Get the video
        # Use get_by_id
        retrieved_video = repo.get_by_id(
            video.get("id"), test_user_id
        )  # Use internal ID and user_id

        # Verify video was retrieved
        assert retrieved_video is not None
        assert retrieved_video.get("youtube_id") == video_id  # Check youtube_id
        assert retrieved_video.get("title") == "Get Test Video"

        # Clean up
        try:
            supabase_client.table("video_data").delete().eq(
                "id", video.get("id")
            ).execute()  # Use correct table name
        except Exception as e:
            print(f"Error cleaning up test video: {e}")

    def test_update_video_data(self, supabase_client, test_user_id):  # Renamed test
        """Test updating a video"""
        repo = VideoDataRepository(supabase_client)

        # Create a video to update
        video_id = f"update-test-{uuid.uuid4()}"
        # Use create_video_data
        video = repo.create_video_data(
            user_id=test_user_id,
            youtube_id=video_id,  # Use youtube_id
            title="Update Test Video",
            metadata={"channel_id": "test-channel", "channel_title": "Test Channel"},
        )

        # Get video data to update
        video = repo.get_by_id(video.get("id"), test_user_id)
        assert video is not None

        # Define update data
        update_data = {
            "title": "Updated Video Title",
            # "tags": ["updated", "tags"], # Remove tags as it likely doesn't exist in test DB schema
            # "summary": "Updated video summary.", # Removed previously
            "processing_status": "completed",  # Mark as processed
            # Update timestamp is handled by the repo method
            # "updated_at": datetime.now(timezone.utc).isoformat()
        }

        # Update video data
        # Use update_video_data
        internal_id = video.get("id")  # Need the internal DB ID for update
        updated_video = repo.update_video_data(
            video_id=internal_id,
            user_id=test_user_id,
            data=update_data,  # Pass as data dictionary
        )

        # Verify video was updated
        assert updated_video is not None
        assert updated_video.get("youtube_id") == video_id  # Check youtube_id
        assert updated_video.get("title") == update_data["title"]
        assert (
            updated_video.get("processing_status") == update_data["processing_status"]
        )
        # Verify the updated_at timestamp has changed
        assert updated_video.get("updated_at") > video.get("updated_at")

        # Get the video to confirm update
        # Use get_by_id
        retrieved_video = repo.get_by_id(internal_id, test_user_id)
        assert retrieved_video.get("title") == update_data["title"]

        # Clean up
        try:
            supabase_client.table("video_data").delete().eq(
                "id", video.get("id")
            ).execute()  # Use correct table name
        except Exception as e:
            print(f"Error cleaning up test video: {e}")

    def test_get_by_user_id(self, supabase_client, test_user_id):  # Renamed test
        """Test getting all videos for a user"""
        repo = VideoDataRepository(supabase_client)

        # Create multiple videos
        videos = []
        for i in range(3):
            # Use create_video_data
            video = repo.create_video_data(
                user_id=test_user_id,
                youtube_id=f"list-test-{i}-{uuid.uuid4()}",  # Use youtube_id
                title=f"Test Video {i}",
                metadata={
                    "channel_id": "test-channel",
                    "channel_title": "Test Channel",
                },
            )
            videos.append(video)

        # Get user videos
        # Use get_by_user_id
        user_videos = repo.get_by_user_id(test_user_id)

        # Verify videos were retrieved
        assert user_videos is not None
        assert len(user_videos) >= 3

        # Clean up
        for video in videos:
            try:
                supabase_client.table("video_data").delete().eq(
                    "id", video.get("id")
                ).execute()  # Use correct table name
            except Exception as e:
                print(f"Error cleaning up test video: {e}")

    # Removing test_get_videos_by_channel as the method doesn't exist
    # def test_get_videos_by_channel(self, supabase_client, test_user_id):
    #     """Test getting videos by channel"""
    #     pass # Test removed

    # Removing test_search_videos as the method doesn't exist
    # def test_search_videos(self, supabase_client, test_user_id):
    #     """Test searching videos"""
    #     pass # Test removed

    def test_update_transcript(self, supabase_client, test_user_id):  # Renamed test
        """Test saving a transcript"""
        repo = VideoDataRepository(supabase_client)

        # Create a video
        # Ensure youtube_id is within varchar(20) limit
        video_id = f"tr_test_{uuid.uuid4()}"[:20]
        # Use create_video_data
        video = repo.create_video_data(
            user_id=test_user_id,
            youtube_id=video_id,  # Use youtube_id
            title="Transcript Test Video",
            metadata={"channel_id": "test-channel", "channel_title": "Test Channel"},
        )

        # Transcript data
        transcript_data = [
            {"text": "Hello, welcome to the video.", "start": 0.0, "duration": 2.5},
            {"text": "Today we'll talk about testing.", "start": 2.5, "duration": 3.0},
            {"text": "Thanks for watching!", "start": 5.5, "duration": 2.0},
        ]

        # Save transcript
        # Use update_transcript
        internal_id = video.get("id")
        updated_video = repo.update_transcript(
            video_id=internal_id,
            user_id=test_user_id,
            transcript=" ".join(t["text"] for t in transcript_data),  # Pass full text
            transcript_segments=transcript_data,  # Pass segments
        )

        # Verify transcript was saved
        # Verify update was successful
        assert updated_video is not None
        assert updated_video.get("transcript") is not None

        # Get transcript
        # Get the video data and check transcript fields
        retrieved_video = repo.get_by_id(internal_id, test_user_id)
        retrieved_transcript_text = retrieved_video.get("transcript")
        retrieved_segments = retrieved_video.get("transcript_segments")

        # Verify transcript retrieval
        assert retrieved_transcript_text is not None
        assert retrieved_segments is not None
        assert len(retrieved_segments) == len(transcript_data)

        # Clean up
        try:
            supabase_client.table("video_data").delete().eq(
                "id", video.get("id")
            ).execute()  # Use correct table name
            # Transcripts are usually stored in the same table now
            # supabase_client.table("youtube_transcripts").delete().eq("video_id", video_id).execute()
        except Exception as e:
            print(f"Error cleaning up test video and transcript: {e}")

    # Removing test_get_transcript as it's covered by test_update_transcript and get_by_id
    # def test_get_transcript(self, supabase_client, test_user_id):
    #     pass # Test removed

    # Removing test_has_transcript as the method doesn't exist
    # def test_has_transcript(self, supabase_client, test_user_id):
    #     """Test checking if a video has a transcript"""
    #     pass # Test removed

    def test_error_handling(self, supabase_client, test_user_id):
        """Test error handling in the repository"""
        repo = VideoDataRepository(supabase_client)

        # Test with invalid video ID
        invalid_id = str(uuid.uuid4())  # Use a non-existent UUID string ID
        # Use get_by_id with UUID string ID (assuming user_id is UUID)
        # Note: get_by_id expects the *internal integer* ID, not UUID.
        # This test might be fundamentally flawed if trying to get by UUID via get_by_id.
        # Let's test get_by_youtube_id instead for error handling.
        video = repo.get_by_youtube_id(
            invalid_id, test_user_id
        )  # Test getting by non-existent YouTube ID

        # Should return None for non-existent video
        assert video is None

        # Test transcript retrieval for non-existent video
        # get_transcript doesn't exist, test get_by_id already covers non-existent video
        # transcript = repo.get_transcript(invalid_id, test_user_id)

        # Should return None for non-existent transcript
        # Assertion removed as the method call was removed
