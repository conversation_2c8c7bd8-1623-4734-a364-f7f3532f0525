import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  OutlinedInput,
  CircularProgress, // Added for loading indicator
} from "@mui/material";
import { SelectChangeEvent } from "@mui/material/Select";

interface SaveToLibraryDialogProps {
  open: boolean;
  onClose: () => void;
  // Updated onSave signature: uses snake_case 'content_type' to match API payload
  onSave: (details: {
    title: string;
    content: string;
    content_type: string;
    tags: string[];
  }) => Promise<void>;
  contentPreview: string; // This is the actual content to save
  initialTitle?: string;
}

const contentTypes = [
  "script",
  "blog_post",
  "email",
  "social_media_post",
  "general_ai_response",
  "draft",
];

const SaveToLibraryDialog: React.FC<SaveToLibraryDialogProps> = ({
  open,
  onClose,
  onSave,
  contentPreview,
  initialTitle = "",
}) => {
  const [title, setTitle] = useState(initialTitle);
  const [contentType, setContentType] = useState<string>("draft");
  const [tags, setTags] = useState<string[]>(["ai-generated", "draft"]);
  const [tagInput, setTagInput] = useState("");
  const [isLoading, setIsLoading] = useState(false); // Added loading state

  // Reset state when dialog opens or initial props change
  useEffect(() => {
    if (open) {
      setTitle(initialTitle || "Untitled AI Response");
      setContentType("draft");
      setTags(["ai-generated", "draft"]);
      setTagInput("");
    }
  }, [open, initialTitle]);

  const handleContentTypeChange = (event: SelectChangeEvent<string>) => {
    setContentType(event.target.value as string);
  };

  const handleTagInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTagInput(event.target.value);
  };

  const handleAddTag = () => {
    const newTag = tagInput.trim();
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag]);
    }
    setTagInput(""); // Clear input after adding
  };

  const handleTagDelete = (tagToDelete: string) => {
    setTags((prevTags) => prevTags.filter((tag) => tag !== tagToDelete));
  };

  const handleSaveClick = async () => {
    // Make async
    const trimmedTitle = title.trim();
    if (!trimmedTitle) {
      // Basic validation: Consider using a more integrated notification system later
      alert("Title is required.");
      return;
    }

    // Ensure default tags are present without duplicates
    const finalTags = Array.from(new Set([...tags, "ai-generated", "draft"]));

    setIsLoading(true); // Start loading
    try {
      // Call the updated onSave prop, passing the full content with snake_case 'content_type'
      await onSave({
        title: trimmedTitle,
        content: contentPreview, // Pass the actual content
        content_type: contentType, // Pass state value as 'content_type'
        tags: finalTags,
      });
      // Success handling (closing dialog, showing notification) is delegated
      // to the parent component via the promise resolution in onSave.
      // We only reset loading state here in the 'finally' block.
      // onClose(); // Let the parent handle closing on success
    } catch (error) {
      console.error("Failed to save content:", error);
      // Error notification/handling is delegated to the parent component
      // via the promise rejection in onSave.
    } finally {
      setIsLoading(false); // Stop loading regardless of success or error
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Save AI Response to Content Library</DialogTitle>
      <DialogContent dividers>
        <Box mb={2}>
          <Typography variant="subtitle1" gutterBottom>
            Content Preview:
          </Typography>
          <Box
            sx={{
              maxHeight: "200px",
              overflowY: "auto",
              border: "1px solid",
              borderColor: "divider",
              borderRadius: 1,
              p: 1.5,
              bgcolor: "action.hover",
              whiteSpace: "pre-wrap", // Preserve whitespace and line breaks
              wordBreak: "break-word",
              fontSize: "0.875rem",
            }}
          >
            {contentPreview}
          </Box>
        </Box>

        <TextField
          label="Title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          fullWidth
          required
          margin="normal"
          variant="outlined"
        />

        <FormControl fullWidth margin="normal" required>
          <InputLabel id="content-type-label">Content Type</InputLabel>
          <Select
            labelId="content-type-label"
            id="content-type-select"
            value={contentType}
            label="Content Type"
            onChange={handleContentTypeChange}
          >
            {contentTypes.map((type) => (
              <MenuItem key={type} value={type}>
                {type
                  .replace(/_/g, " ")
                  .replace(/\b\w/g, (l) => l.toUpperCase())}{" "}
                {/* Format for display */}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <FormControl fullWidth margin="normal">
          <InputLabel htmlFor="tags-input">
            Tags (comma-separated or press Enter)
          </InputLabel>
          <OutlinedInput
            id="tags-input"
            label="Tags (comma-separated or press Enter)"
            value={tagInput}
            onChange={handleTagInputChange}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === ",") {
                e.preventDefault(); // Prevent form submission or comma typing
                handleAddTag();
              }
            }}
            endAdornment={
              <Button onClick={handleAddTag} size="small">
                Add
              </Button>
            }
          />
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mt: 1 }}>
            {tags.map((tag) => (
              <Chip
                key={tag}
                label={tag}
                onDelete={() => handleTagDelete(tag)}
                size="small"
              />
            ))}
          </Box>
        </FormControl>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSaveClick} variant="contained" color="primary">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SaveToLibraryDialog;
