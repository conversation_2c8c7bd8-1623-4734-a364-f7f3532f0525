#!/usr/bin/env python3
"""
Basic test to check if we can import the FastAPI app and run basic tests.
This will help identify the root cause of test failures.
"""

def test_basic_imports():
    """Test that we can import the main FastAPI app."""
    try:
        from main import app
        print("✅ Successfully imported FastAPI app")
        assert app is not None
    except Exception as e:
        print(f"❌ Failed to import FastAPI app: {e}")
        raise

def test_fastapi_testclient():
    """Test that we can create a FastAPI TestClient."""
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        print("✅ Successfully created FastAPI TestClient")
        assert client is not None
    except Exception as e:
        print(f"❌ Failed to create TestClient: {e}")
        raise

def test_basic_health_endpoint():
    """Test that we can call a basic health endpoint."""
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        response = client.get("/health")
        print(f"✅ Health endpoint response: {response.status_code}")
        print(f"   Response body: {response.text}")
        
        # Should get either 200 or 404, but not a server error
        assert response.status_code in [200, 404]
    except Exception as e:
        print(f"❌ Failed to call health endpoint: {e}")
        raise

def test_api_health_endpoint():
    """Test that we can call the /api/health endpoint."""
    try:
        from fastapi.testclient import TestClient
        from main import app
        
        client = TestClient(app)
        response = client.get("/api/health")
        print(f"✅ API Health endpoint response: {response.status_code}")
        print(f"   Response body: {response.text}")
        
        # Should get 200 for this endpoint
        assert response.status_code == 200
    except Exception as e:
        print(f"❌ Failed to call /api/health endpoint: {e}")
        raise

if __name__ == "__main__":
    print("🔍 Running basic import and endpoint tests...")
    
    try:
        test_basic_imports()
        test_fastapi_testclient()
        test_basic_health_endpoint()
        test_api_health_endpoint()
        print("\n🎉 All basic tests passed!")
    except Exception as e:
        print(f"\n❌ Basic tests failed: {e}")
        import traceback
        traceback.print_exc()
