{"name": "writer-v2-test", "version": "1.0.0", "type": "module", "description": "Test utilities for Writer v2", "dependencies": {"@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@types/quill": "^2.0.14", "framer-motion": "^12.7.4", "jsonwebtoken": "^9.0.2", "node-fetch": "^2.7.0", "quill": "^2.0.3", "react-error-boundary": "^5.0.0", "react-markdown": "^10.1.0", "react-quill": "^2.0.0"}, "devDependencies": {"@playwright/test": "^1.51.1", "@types/redux-mock-store": "^1.5.0", "axios": "^1.8.4", "lighthouse": "^12.5.0", "playwright": "^1.51.1", "playwright-core": "^1.51.1", "puppeteer": "^24.4.0", "redux-mock-store": "^1.5.5"}}