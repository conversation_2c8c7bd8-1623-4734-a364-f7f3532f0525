import json
import logging
import os
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class MockAIGenerator:
    """
    A mock AI generator that returns predefined responses for testing.
    This is used when no real AI service is configured.
    """
    def __init__(self):
        self.mock_responses = {
            "brainstorm": json.dumps([
                "10 Essential Tips for Creating Engaging YouTube Scripts",
                "How to Write a Viral Video Script in Under 30 Minutes",
                "The Ultimate Guide to Scriptwriting for Social Media Videos",
                "5 Scriptwriting Secrets Used by Top Content Creators",
                "From Idea to Script: A Step-by-Step Guide for Video Creators"
            ]),
            # Also provide a plain array version for direct use
            "brainstorm_array": [
                "10 Essential Tips for Creating Engaging YouTube Scripts",
                "How to Write a Viral Video Script in Under 30 Minutes",
                "The Ultimate Guide to Scriptwriting for Social Media Videos",
                "5 Scriptwriting Secrets Used by Top Content Creators",
                "From Idea to Script: A Step-by-Step Guide for Video Creators"
            ],
            "hook": json.dumps([
                "Did you know that 90% of viral videos share one common scriptwriting technique? I'll reveal it in this video.",
                "I wasted 3 years creating scripts the wrong way. Here's what I learned that changed everything.",
                "This simple 3-part script formula has helped me gain over 1 million subscribers. Let me show you how it works.",
                "Most creators overcomplicate their scripts. Here's the stripped-down approach that actually works.",
                "I analyzed 100 top-performing videos and discovered these 5 scriptwriting patterns that drive engagement."
            ]),
            "intro": json.dumps([
                "Hey there! Welcome back to the channel. Today I'm going to share my proven system for creating video scripts that keep viewers watching until the very end. If you're struggling with viewer retention, this video is going to be a game-changer for you. Make sure to stick around until the end where I'll share a bonus tip that most creators don't know about.",
                "What's up, content creators! In this video, I'm breaking down my exact process for writing scripts that convert viewers into subscribers. After years of trial and error, I've developed a framework that consistently helps videos perform better. By the end of this tutorial, you'll have a clear roadmap for creating your own high-converting scripts.",
                "Hey everyone! Today we're diving deep into scriptwriting for videos. Whether you're just starting out or you've been creating content for years, having a solid script is the foundation of any successful video. I'll walk you through my step-by-step process that has helped me grow my channel to where it is today. Let's get started!",
                "Welcome to another video! If you've ever stared at a blank page wondering how to start your script, this video is for you. I'm going to show you the exact template I use for all my videos that has helped me grow to 100,000 subscribers. Make sure to download the free template in the description below.",
                "Hey there! In today's video, I'm sharing the scriptwriting secrets that took me from 0 to 500,000 views per video. These are practical techniques you can implement right away to see results. If you're serious about growing your channel, make sure to hit that subscribe button and notification bell so you don't miss any of my content creation tips."
            ]),
            "outline": json.dumps([
                {
                    "title": "Introduction",
                    "points": ["Hook: Mention the common struggle with scriptwriting", "Introduce yourself and your experience", "Overview of what viewers will learn"]
                },
                {
                    "title": "Why Scripts Matter",
                    "points": ["Impact on viewer retention", "Efficiency in filming", "Consistency across content"]
                },
                {
                    "title": "The 3-Part Script Formula",
                    "points": ["Part 1: The Hook (first 15 seconds)", "Part 2: The Content Structure", "Part 3: The Call to Action"]
                },
                {
                    "title": "Common Mistakes to Avoid",
                    "points": ["Being too verbose", "Lack of clear structure", "Forgetting the viewer's perspective"]
                },
                {
                    "title": "Practical Examples",
                    "points": ["Example 1: Tutorial script", "Example 2: Story-based script", "Example 3: Review script"]
                }
            ]),
            "draft": "# How to Write Effective Video Scripts: A Complete Guide\n\n## Introduction\nHey there, welcome back to the channel! Today we're diving into something that can make or break your videos - scriptwriting. I've spent years refining my process, and I'm excited to share these techniques with you.\n\n## Why Scripts Matter\nBefore we jump in, let's talk about why having a solid script is so important:\n- Scripts keep your videos focused and on-track\n- They help you deliver information clearly and concisely\n- Good scripts dramatically improve viewer retention\n- They make the filming and editing process much more efficient\n\n## The 3-Part Script Formula\n\n### Part 1: The Hook (First 15 Seconds)\nYour opening needs to grab attention immediately. Here are some proven hook strategies:\n- Ask a thought-provoking question\n- Share a surprising statistic\n- Make a bold claim (that you'll back up)\n- Tease the value viewers will get\n\nExample: \"Did you know that videos with well-written scripts get 60% more engagement? Today I'll show you exactly how to create scripts that keep viewers watching.\"\n\n### Part 2: The Content Structure\nThis is the meat of your video. Break your content into clear sections:\n- Use a logical progression of ideas\n- Include timestamps for longer videos\n- Alternate between teaching and examples\n- Address potential questions or objections\n\n### Part 3: The Call to Action\nDon't leave viewers hanging! Tell them what to do next:\n- Be specific about what action you want them to take\n- Explain the benefit of taking that action\n- Keep it simple and direct\n\nExample: \"If you found this helpful, hit that subscribe button and notification bell so you don't miss my weekly content creation tips. And drop a comment letting me know which script technique you're going to try first!\"\n\n## Common Mistakes to Avoid\n\n1. **Being too verbose**\nViewers have short attention spans. Keep your language concise and get to the point quickly.\n\n2. **Lack of clear structure**\nWithout a logical flow, viewers get confused and click away. Always outline before writing.\n\n3. **Forgetting the viewer's perspective**\nYour script should address what the viewer wants to know, not just what you want to say.\n\n## Practical Examples\n\nLet me walk you through three different script templates:\n\n### Example 1: Tutorial Script\n- Start with the end result to build excitement\n- Break the process into clear, numbered steps\n- Address common problems at each stage\n- End with a showcase of the finished product\n\n### Example 2: Story-based Script\n- Begin with a relatable situation or problem\n- Share your personal journey or experience\n- Include specific turning points and lessons learned\n- Conclude with the transformation and takeaways\n\n### Example 3: Review Script\n- Start with your overall verdict for those short on time\n- Cover specific features/aspects in a logical order\n- Balance pros and cons for credibility\n- End with recommendations for different types of users\n\n## Conclusion\nRemember, the best scripts feel natural while still being structured. With practice, you'll find the perfect balance between preparation and authenticity.\n\nThank you for watching! If you have any questions about scriptwriting, drop them in the comments below. And don't forget to download my free script template using the link in the description.\n\nSee you in the next video!",
            "edited": "# How to Write Effective Video Scripts: A Complete Guide\n\n## Introduction\nHey there, welcome back to the channel! Today we're diving into something that can make or break your videos - scriptwriting. I've spent years refining my process, and I'm excited to share these techniques with you.\n\n## Why Scripts Matter\nBefore we jump in, let's talk about why having a solid script is so important:\n- Scripts keep your videos focused and on-track\n- They help you deliver information clearly and concisely\n- Good scripts dramatically improve viewer retention\n- They make the filming and editing process much more efficient\n\n## The 3-Part Script Formula\n\n### Part 1: The Hook (First 15 Seconds)\nYour opening needs to grab attention immediately. Here are some proven hook strategies:\n- Ask a thought-provoking question\n- Share a surprising statistic\n- Make a bold claim (that you'll back up)\n- Tease the value viewers will get\n\nExample: \"Did you know that videos with well-written scripts get 60% more engagement? Today I'll show you exactly how to create scripts that keep viewers watching.\"\n\n### Part 2: The Content Structure\nThis is the meat of your video. Break your content into clear sections:\n- Use a logical progression of ideas\n- Include timestamps for longer videos\n- Alternate between teaching and examples\n- Address potential questions or objections\n\n### Part 3: The Call to Action\nDon't leave viewers hanging! Tell them what to do next:\n- Be specific about what action you want them to take\n- Explain the benefit of taking that action\n- Keep it simple and direct\n\nExample: \"If you found this helpful, hit that subscribe button and notification bell so you don't miss my weekly content creation tips. And drop a comment letting me know which script technique you're going to try first!\"\n\n## Common Mistakes to Avoid\n\n1. **Being too verbose**\nViewers have short attention spans. Keep your language concise and get to the point quickly.\n\n2. **Lack of clear structure**\nWithout a logical flow, viewers get confused and click away. Always outline before writing.\n\n3. **Forgetting the viewer's perspective**\nYour script should address what the viewer wants to know, not just what you want to say.\n\n## Practical Examples\n\nLet me walk you through three different script templates:\n\n### Example 1: Tutorial Script\n- Start with the end result to build excitement\n- Break the process into clear, numbered steps\n- Address common problems at each stage\n- End with a showcase of the finished product\n\n### Example 2: Story-based Script\n- Begin with a relatable situation or problem\n- Share your personal journey or experience\n- Include specific turning points and lessons learned\n- Conclude with the transformation and takeaways\n\n### Example 3: Review Script\n- Start with your overall verdict for those short on time\n- Cover specific features/aspects in a logical order\n- Balance pros and cons for credibility\n- End with recommendations for different types of users\n\n## Conclusion\nRemember, the best scripts feel natural while still being structured. With practice, you'll find the perfect balance between preparation and authenticity.\n\nThank you for watching! If you have any questions about scriptwriting, drop them in the comments below. And don't forget to download my free script template using the link in the description.\n\nSee you in the next video!"
        }

    def generate_content(self, prompt: str, business_context: Optional[Dict] = None, content_examples: Optional[Dict] = None, max_tokens: Optional[int] = None, system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Mock implementation of generate_content that returns predefined responses
        based on keywords in the prompt.
        """
        logger.info("Using mock AI generator to generate content")

        # Check if this is a business optimization request
        if system_prompt and "business profile" in system_prompt.lower() and "optimize" in prompt.lower():
            # Return a mock optimized business profile
            return {
                "generated_text": json.dumps({
                    "profile_overview": {
                        "value": "We create engaging digital content that helps businesses connect with their audience through authentic storytelling and strategic messaging.",
                        "why": "More concise and clearly communicates the value proposition"
                    },
                    "brand_voice": {
                        "value": "Professional yet conversational, authoritative but approachable, with a focus on clarity and actionable insights.",
                        "why": "Provides more specific guidance on tone and style"
                    },
                    "target_audience": {
                        "value": "Small to medium-sized businesses and entrepreneurs looking to enhance their digital presence and content marketing strategy.",
                        "why": "More specific target definition helps focus messaging"
                    }
                }),
                "tokens_used": 350
            }

        # Determine which type of content to return based on the prompt
        if "brainstorm" in prompt.lower() or "title" in prompt.lower():
            # For wizard routes, return the array directly to avoid JSON parsing issues
            if "wizard" in prompt.lower() or "script" in prompt.lower():
                # Log that we're using the array version for the wizard
                logger.info("Using brainstorm_array for wizard prompt")
                return {"generated_text": json.dumps(self.mock_responses["brainstorm_array"]), "tokens_used": 150}
            else:
                return {"generated_text": self.mock_responses["brainstorm"], "tokens_used": 150}
        elif "hook" in prompt.lower():
            return {"generated_text": self.mock_responses["hook"], "tokens_used": 200}
        elif "intro" in prompt.lower() or "cta" in prompt.lower():
            return {"generated_text": self.mock_responses["intro"], "tokens_used": 300}
        elif "outline" in prompt.lower():
            return {"generated_text": self.mock_responses["outline"], "tokens_used": 400}
        elif "draft" in prompt.lower():
            return {"generated_text": self.mock_responses["draft"], "tokens_used": 800}
        elif "edit" in prompt.lower():
            return {"generated_text": self.mock_responses["edited"], "tokens_used": 850}
        else:
            # Default response if no specific type is detected
            return {"generated_text": json.dumps(["Mock response for: " + prompt[:50] + "..."]), "tokens_used": 100}


def get_ai_generator():
    """
    Returns an AI generator client based on configuration.
    If no real AI service is configured, returns a mock generator for testing.
    """
    # Check for environment variables that would indicate a real AI service
    openai_api_key = os.environ.get("OPENAI_API_KEY")
    anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY")

    if openai_api_key or anthropic_api_key:
        # TODO: Implement real AI client integration when keys are available
        logger.warning("Real AI keys detected but integration not implemented yet. Using mock generator.")
        return MockAIGenerator()
    else:
        # Use mock generator for testing
        logger.info("No AI service keys found. Using mock generator for testing.")
        return MockAIGenerator()