#!/bin/bash

echo "🔍 Checking container status..."

echo ""
echo "=== CONTAINER STATUS ==="
docker-compose -f docker-compose.yml -f docker-compose.dev.yml ps

echo ""
echo "=== PORT BINDINGS ==="
docker port writerv2-frontend-1 2>/dev/null || echo "Frontend container not found"
docker port writerv2-backend-1 2>/dev/null || echo "Backend container not found"

echo ""
echo "=== FRONTEND LOGS (last 20 lines) ==="
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs --tail=20 frontend

echo ""
echo "=== BACKEND LOGS (last 10 lines) ==="
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs --tail=10 backend

echo ""
echo "=== NETWORK TEST ==="
echo "Testing frontend at http://localhost:3000..."
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:3000 || echo "Frontend unreachable"

echo "Testing backend at http://localhost:5000/api/health..."
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:5000/api/health || echo "Backend unreachable"

echo ""
echo "=== QUICK FIXES ==="
echo "If frontend shows 'loading forever':"
echo "1. Check if HOST=0.0.0.0 is set in container logs above"
echo "2. Try: docker-compose -f docker-compose.yml -f docker-compose.dev.yml restart frontend"
echo "3. Check browser console for errors"