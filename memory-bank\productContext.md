## Writer v2 Product Context

**Core Purpose:** Writer v2 is an AI-powered content generation platform designed to help users create high-quality content quickly and consistently.

**Key Features:**

*   **User Authentication:** Secure registration and login using Supabase Auth.
*   **Content Library:** Manage, store, and retrieve user-generated content examples and templates. Items can be flagged as supplementary business context.
*   **Business Context:** Define and store dedicated brand voice, target audience, offer descriptions, etc.
*   **AI Assistant:**
    *   Chat interface for conversational AI interaction.
    *   Generate various content types based on prompts, combining dedicated business context with flagged content library examples.
    *   Format raw text (e.g., transcripts) into structured content like blog posts.
    *   Integrates with OpenRouter for AI model access (via standardized configuration).
    *   **Enhanced Interaction:** Includes per-message copy-to-clipboard, structured JSON display (for hooks, outlines, CTAs), and a feedback mechanism (👍/👎 + comments).
*   **YouTube Integration:**
    *   Search YouTube videos by keyword or URL.
    *   Fetch video details (metadata, statistics).
    *   Retrieve video transcripts.
    *   Analyze video performance (outlier score calculation).
    *   Save YouTube video data (including transcript) to the Content Library.
*   **Feedback System:** 
    *   Allows users to provide feedback on generated content or the application itself.
    *   Includes specific in-message feedback for AI Assistant responses (via `/api/feedback` endpoint).
*   **User Activity Tracking:** Log user actions within the application.

**Technology Stack:**

*   **Backend:** Flask (Python)
*   **Frontend:** React (TypeScript), Material UI (MUI), Axios, Framer Motion. State management primarily uses React Context API (`AuthContext` for authentication) supplemented by Redux Toolkit for other global state (UI, activity).
*   **Authentication & Database:** Supabase (PostgreSQL backend)
*   **AI Provider:** OpenRouter (Configurable via centralized `config.py`)
*   **Deployment:** Docker (using `docker-compose.yml` for local dev/deployment)
*   **API Communication:** RESTful API endpoints.

**Target Users:** Content creators, marketers, businesses needing consistent and tailored content generation.

**Design Philosophy (Frontend):** Approachable, techy but user-friendly, minimalist with personality, intuitive UX, supports light/dark modes, responsive design.

[2025-04-08 17:19:01] - Feature Added: Content Library items can now be marked as 'Business Context' via a toggle in the UI. Items marked this way are included alongside dedicated Business Context items when providing context to the AI Assistant.