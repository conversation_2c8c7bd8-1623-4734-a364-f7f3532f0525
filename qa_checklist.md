# Manual QA Checklist for AI Script Writing Wizard

## Setup
- [ ] Ensure you have at least one Business Context created
- [ ] Make sure you're logged in with a valid user account

## Basic Flow Testing
- [ ] Navigate to the Wizard page
- [ ] Verify all 7 steps are displayed in the stepper
- [ ] Verify the Business Context dropdown loads correctly
- [ ] Enter a Content Idea and select a Business Context
- [ ] Click Next and verify brainstorm title ideas are generated
- [ ] Select a title idea and click Next
- [ ] Verify the selected title is displayed with information about hooks
- [ ] Click Next to generate hook options
- [ ] Verify hook options are generated
- [ ] Select a hook and click Next
- [ ] Verify intro options are generated
- [ ] Select an intro and click Next
- [ ] Verify outline is generated
- [ ] Click Next to generate the draft
- [ ] Verify draft is generated
- [ ] Enter style preferences and click Apply Edit
- [ ] Verify edited script is displayed
- [ ] Click Finish to complete the wizard
- [ ] Verify final script is displayed with Save to Library button

## Error Handling
- [ ] Try to proceed without selecting a Business Context
- [ ] Try to proceed without entering a Content Idea
- [ ] Try to proceed without selecting a title in step 1
- [ ] Try to proceed without selecting a hook in step 3
- [ ] Try to proceed without selecting an intro in step 4
- [ ] Disconnect internet and try to make an API call
- [ ] Reconnect and verify recovery
- [ ] Test the improved error messages for different error types

## Navigation and State Management
- [ ] Use the Back button to navigate between steps
- [ ] Verify state is maintained when going back and forth
- [ ] Try to refresh the page and check if warning appears
- [ ] Try to navigate away and check if warning appears
- [ ] Click Reset and verify confirmation dialog appears
- [ ] Cancel the reset and verify wizard state is maintained
- [ ] Confirm reset and verify wizard is reset to initial state

## Save to Library
- [ ] Complete the wizard and click Save to Library
- [ ] Verify the save dialog opens with correct preview
- [ ] Enter a title, select content type, and add tags
- [ ] Click Save and verify success notification
- [ ] Verify confirmation dialog for navigation appears
- [ ] Choose to stay on the page and verify wizard state is maintained
- [ ] Complete the wizard again and save to library
- [ ] Choose to navigate to Content Library
- [ ] Verify you're redirected to the Content Library
- [ ] Verify the saved script appears in the Content Library
- [ ] Open the saved script and verify content is correct
- [ ] Verify the script is editable in the Content Library

## Responsive Testing
- [ ] Test the wizard on a desktop browser (1920x1080)
- [ ] Test the wizard on a tablet viewport (768x1024)
- [ ] Test the wizard on a mobile viewport (375x667)
- [ ] Verify all UI elements are properly visible and usable on all devices
- [ ] Verify text areas and buttons are appropriately sized on all devices

## Performance
- [ ] Monitor network requests during wizard usage
- [ ] Check for any excessive API calls or performance issues
- [ ] Verify loading indicators appear during API calls
- [ ] Measure time for each step to complete

## Accessibility
- [ ] Verify all form controls have proper labels
- [ ] Verify focus states are visible
- [ ] Verify color contrast meets accessibility standards
- [ ] Test keyboard navigation through the wizard

## Edge Cases
- [ ] Test with very long content ideas
- [ ] Test with special characters in inputs
- [ ] Test with minimal text in style preferences
- [ ] Test with very long style preferences
