# app/ai_assistant/prompts/generic.py
"""Consolidated system prompt generation for the <PERSON> assistant."""

from typing import List, Optional, Dict, Any, Literal, Tuple
import logging

# Import formatters using relative path within the package
from .formatters import (
    _format_business_context,
    _format_content_examples,
    _format_content_items,
    _format_video_data,
)

logger = logging.getLogger(__name__)

# --- Consolidated Prompt Configuration ---

PROMPT_CONFIG: Dict[str, Dict[str, str]] = {
    "general": {
        "intro": (
            "You are a senior AI writing partner—friendly, direct, and relentlessly practical."
        ),
        "hint": (
            "Always:\n"
            "  1. Use clear, simple words (grade‑8 reading level)\n"
            "  2. Format with ⚡ bold H2s + bullets\n"
            "  3. Default to ≤3 crisp paragraphs unless extra depth is requested\n"
            "  4. Ask 1 clarifying question if the task is underspecified"
        ),
},

    "script": {
    "intro": (
        "You are a conversion‑first scriptwriter for social videos, reels, and shorts."
    ),
    "hint": (
        "Structure every script as:\n"
        "  • Hook (≤7 words)\n"
        "  • Setup (why this matters in 1 sentence)\n"
        "  • Delivery (3‑5 rapid‑fire beats)\n"
        "  • Call‑to‑Action (1 clear ask)\n"
        "Embed creator personality, vivid verbs, and pattern‑interrupts (e.g., analogy, stat, question)."
    ),
},

    "business_optimization": {
    "intro": (
        "Your job: audit the business profile fields below for clarity, differentiation, and persuasion. Focus on improving empty fields and enhancing existing content."
    ),
    "hint": (
        "Evaluate each field in the profile and suggest improvements where needed.\n"
        "Return fields that need improvement in a single JSON object.\n"
        "For each field, use this structure: { 'field_name': { 'value': 'new value', 'why': 'reason for change' } }\n"
        "Skip any field that needs no edit.\n"
        "Example format:\n"
        "{ \n"
        "  \"offer_description\": { \"value\": \"New offer description\", \"why\": \"More concise and compelling\" },\n"
        "  \"brand_voice\": { \"value\": \"Professional yet approachable\", \"why\": \"Better aligns with target audience\" }\n"
        "}\n"
        "IMPORTANT RULES:\n"
        "1. Your response MUST be a valid JSON object. Do not include any text outside the JSON object.\n"
        "2. Do not include markdown code fences (```) in your response - just return the raw JSON object.\n"
        "3. Make sure your JSON is properly formatted with double quotes around all keys and string values.\n"
        "4. Keep your response concise and focused on the most important improvements.\n"
        "5. For array fields (content_focus, recommended_platforms, recommended_content_formats), return a comma-separated string.\n"
        "6. If a field is empty, prioritize providing content for it.\n"
        "7. If specific fields to optimize are provided, ONLY suggest improvements for those fields.\n"
    ),
},

    "youtube_content": {
    "intro": (
        "You are a YouTube growth copywriter—hook‑driven titles, CTR‑boosting thumbnails, SEO‑clean descriptions."
    ),
    "hint": (
        "• Title: begin with a 3‑5‑word curiosity hook + keyword; ≤60 chars\n"
        "• Thumbnail overlay: ≤4 words, high‑contrast, avoid title repetition\n"
        "• Description: 2‑line teaser, bullet highlights, links at bottom\n"
        "• Offer 3 variant sets (title, overlay, description) unless told otherwise"
    ),
},

    # Add other formats here if needed
}

# --- Reusable Text Blocks ---

YOUTUBE_CAPS_BLOCK = (
    "Extra powers:\n"
    "  1. Pull full transcripts or key moments\n"
    "  2. Auto‑chunk long videos into highlight reels\n"
)


OPTIMIZATION_JSON_CLAUSE = (
    "Respond with ONLY a JSON object of revised fields using the exact format specified above.\n"
    "Each field must have a 'value' and 'why' property. Do not include any explanatory text outside the JSON object.\n"
    "If a field is marked 'Not provided (NEEDS CONTENT)', suggest appropriate content for it.\n"
    "IMPORTANT: Your response MUST be a valid JSON object. Do not include any text outside the JSON object.\n"
    "Do not include markdown code fences (```) in your response - just return the raw JSON object.\n"
    "Make sure your JSON is properly formatted with double quotes around all keys and string values."
)

# --- Optimization Context Fields ---
# Defines the fields to display when showing the context for optimization.
# Tuple format: (key_in_dict, display_label, is_list_field)
OPTIMIZATION_FIELDS_TO_DISPLAY: List[Tuple[str, str, bool]] = [
    ('name', 'Name', False),
    ('profile_overview', 'Profile Overview', False),
    ('offer_description', 'Offer Description', False),
    ('target_audience', 'Target Audience', False),
    ('brand_voice', 'Brand Voice', False),
    ('key_benefits', 'Key Benefits', False),
    ('unique_value_proposition', 'Unique Value Proposition', False),
    ('primary_objectives', 'Primary Objectives', False),
    ('audience_motivation', 'Audience Motivation', False),
    ('audience_decision_style', 'Audience Decision Style', False),
    ('audience_preference_structure', 'Audience Preference Structure', False),
    ('audience_decision_speed', 'Audience Decision Speed', False),
    ('audience_reaction_to_change', 'Audience Reaction to Change', False),
    ('content_focus', 'Content Focus', True),
    ('recommended_platforms', 'Recommended Platforms', True),
    ('recommended_content_formats', 'Recommended Content Formats', True),
]


# Valid content format types (dynamically generated from config keys)
# ContentType = Literal[tuple(PROMPT_CONFIG.keys())]
# Statically define ContentType for type checker compatibility
ContentType = Literal[
    "general",
    "script",
    "business_optimization",
    "youtube_content"
]


# --- Private Helper Functions for Prompt Building ---

def _make_intro(content_format: ContentType) -> str:
    """Generates the introductory sentence. Returns empty string if not found."""
    return PROMPT_CONFIG.get(content_format, {}).get("intro", "")

def _make_format_hint(content_format: ContentType) -> str:
    """Generates the format-specific hint. Returns empty string if not found."""
    return PROMPT_CONFIG.get(content_format, {}).get("hint", "")

def _make_youtube_capabilities(include: bool) -> Optional[str]:
    """Generates the YouTube capabilities block if requested."""
    return YOUTUBE_CAPS_BLOCK if include else None

def _make_optimization_profile_context(context: Optional[Dict[str, Any]], fields_to_optimize: Optional[List[str]] = None) -> Optional[str]:
    """Formats the business profile context for optimization analysis.

    Args:
        context: The business profile data to format
        fields_to_optimize: Optional list of specific field keys to optimize. If provided,
                           these fields will be marked in the output.

    Returns:
        Formatted string with the business profile context, or None if context is None
    """
    if not context:
        return None
    profile_lines = ["Current Business Profile to Analyze:"]
    empty_fields = []
    fields_to_optimize_set = set(fields_to_optimize or [])
    has_specific_fields = bool(fields_to_optimize_set)

    for key, label, is_list in OPTIMIZATION_FIELDS_TO_DISPLAY:
        value = context.get(key)
        # Mark fields that should be optimized
        field_marker = " [TO OPTIMIZE]" if has_specific_fields and key in fields_to_optimize_set else ""

        if is_list:
            # Ensure value is a list and format it, handle None or empty list
            items = value if isinstance(value, list) else []
            if items:
                display_value = ', '.join(items)
            else:
                display_value = 'Not provided (NEEDS CONTENT)'
                empty_fields.append(key)
        else:
            # Handle non-list values
            if value:
                display_value = value
            else:
                display_value = 'Not provided (NEEDS CONTENT)'
                empty_fields.append(key)
        profile_lines.append(f"- {label}{field_marker}: {display_value}")

    # Add a summary of empty fields that need attention
    if empty_fields:
        profile_lines.append("\nFields that need content: " + ", ".join(empty_fields))

    # Add a note about which fields to optimize if specified
    if has_specific_fields:
        profile_lines.append(f"\nIMPORTANT: Only optimize the fields marked with [TO OPTIMIZE]. Read all fields for context but only suggest changes for the marked fields.")

    return "\n".join(profile_lines)

def _make_user_request_context(message: Optional[str]) -> Optional[str]:
    """Adds the original user message for context if available."""
    return f"\nUser Request Context: {message}" if message else None

def _make_context_section(
    formatter: callable, context_data: Optional[Any]
) -> Optional[str]:
    """Calls a specific formatter and returns its output if non-empty."""
    if context_data is None:
        return None
    formatted_string = formatter(context_data)
    return formatted_string if formatted_string else None


# --- Consolidated Prompt Builder ---

# TODO: Consider caching the output of this function based on input args
#       for performance, as it might be called frequently with identical params.
def build_system_prompt(
    *,
    content_format: ContentType = "general",
    business_context: Optional[Dict[str, Any]] = None,
    content_examples: Optional[List[Dict[str, Any]]] = None,
    content_items: Optional[List[Dict[str, Any]]] = None,
    video_data: Optional[Dict[str, Any]] = None,
    include_youtube_capabilities: bool = False,
    optimization_mode: bool = False,
    optimization_context: Optional[Dict[str, Any]] = None,
    original_user_message_for_optimization: Optional[str] = None,
    fields_to_optimize: Optional[List[str]] = None

) -> str:
    """
    Builds the system prompt for the AI assistant based on various inputs.

    Args:
        content_format: The type of content being generated/task requested.
                        Validated against PROMPT_CONFIG keys. Defaults to "general".
        business_context: General business context.
        content_examples: Examples of desired content.
        content_items: Items from the content library.
        video_data: Data related to a YouTube video.
        include_youtube_capabilities: Whether to include the YouTube capabilities text.
        optimization_mode: If True, uses optimization intro/hint and appends JSON requirement.
        optimization_context: The specific business profile data being optimized.
        original_user_message_for_optimization: The user's original message triggering optimization.
        fields_to_optimize: Optional list of specific field keys to optimize. If provided, only these fields
                           will be optimized, though all fields will be read for context.

    Returns:
        The fully constructed system prompt string.
    """
    # If optimization_mode is True, the effective format is always business_optimization
    if optimization_mode:
        effective_format = "business_optimization"
    else:
        # Validate content_format ONLY if not in optimization mode
        if content_format not in PROMPT_CONFIG:
            logger.warning(
                f"Invalid content_format '{content_format}'. "
                f"Valid formats are: {list(PROMPT_CONFIG.keys())}. Defaulting to 'general'."
            )
            content_format = "general"
        effective_format = content_format

    segments = []

    # Determine the effective format for intro/hint
    # effective_format = "business_optimization" if optimization_mode else content_format # Logic moved up

    # 1. Introduction
    segments.append(_make_intro(effective_format))

    # 2. Optimization Context (only in optimization mode)
    if optimization_mode:
        segments.append(_make_optimization_profile_context(optimization_context, fields_to_optimize))
        segments.append(_make_user_request_context(original_user_message_for_optimization))

    # 3. Format-Specific Hint
    segments.append(_make_format_hint(effective_format))

    # 4. YouTube Capabilities
    segments.append(_make_youtube_capabilities(include_youtube_capabilities))

    # 5. Formatted Context Sections (using helpers)
    formatted_bc = _make_context_section(_format_business_context, business_context)
    if formatted_bc:
        segments.append("IMPORTANT: Strictly adhere to the following Business Context details:")
        segments.append(formatted_bc)

    segments.append(_make_context_section(_format_content_examples, content_examples))
    segments.append(_make_context_section(_format_content_items, content_items))
    segments.append(_make_context_section(_format_video_data, video_data))

    # 6. Optimization JSON Requirement
    if optimization_mode:
        segments.append(OPTIMIZATION_JSON_CLAUSE)

    # Clean up None/empty segments and join
    # Filter only needed for Optional return types like _make_youtube_capabilities
    final_segments = [s for s in segments if s] # Keep filter for None, empty strings handled by helpers
    return "\n\n".join(final_segments).strip()


# --- Removed Old/Unused Code ---
# - get_default_system_prompt() removed
# - DEFAULT_YOUTUBE_ANALYSIS_USER_PROMPT removed
# - DEFAULT_TRANSCRIPT_SUMMARY_USER_PROMPT removed
# - Comments referencing deleted functions removed

# --- Default User Prompts (Constants can remain if used elsewhere) ---
DEFAULT_YOUTUBE_ANALYSIS_USER_PROMPT: str = (
    "Analyze this YouTube video content: {title}"
)
DEFAULT_TRANSCRIPT_SUMMARY_USER_PROMPT: str = (
    "Summarize the main points of this transcript"
)

# --- Remove Old Functions ---
# get_chat_system_prompt is removed
# get_general_content_prompt is removed
# get_general_system_prompt is removed
# get_business_profile_optimization_prompt is removed
# (The actual deletion happens by not including them here)