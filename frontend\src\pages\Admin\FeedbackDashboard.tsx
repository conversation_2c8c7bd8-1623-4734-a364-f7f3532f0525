import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  CircularProgress,
  Rating,
  Tabs,
  Tab,
  Alert,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { api } from "../../services/api";

// Chart components
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  ChartData,
  ChartOptions,
} from "chart.js";
import { Pie, Bar } from "react-chartjs-2";

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  Title
);

interface FeedbackSummary {
  total: number;
  by_type: Record<string, number>;
  average_rating: number | null;
  recent: Feedback[];
}

interface Feedback {
  id: number;
  user_id: number | null;
  feedback_type: string;
  rating: number | null;
  comment: string | null;
  feature_request: string | null;
  bug_report: string | null;
  metadata: Record<string, any> | null;
  created_at: string;
}

const FeedbackDashboard: React.FC = () => {
  const theme = useTheme();

  // State
  const [summary, setSummary] = useState<FeedbackSummary | null>(null);
  const [feedbackList, setFeedbackList] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<number>(0);

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch summary
        const summaryResponse = await api.get("/api/feedback/summary");
        setSummary(summaryResponse.data);

        // Fetch feedback list
        const listResponse = await api.get("/api/feedback", {
          params: {
            offset: page * rowsPerPage,
            limit: rowsPerPage,
            type: activeTab === 0 ? undefined : getTypeFromTabIndex(activeTab),
          },
        });

        setFeedbackList(listResponse.data.results);
        setTotalCount(listResponse.data.total);
        setError(null);
      } catch (err) {
        console.error("Error fetching feedback data:", err);
        setError("Failed to load feedback data. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [page, rowsPerPage, activeTab]);

  // Handle pagination
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setPage(0);
  };

  // Helper function to get feedback type from tab index
  const getTypeFromTabIndex = (index: number): string | undefined => {
    switch (index) {
      case 1:
        return "general";
      case 2:
        return "feature";
      case 3:
        return "bug";
      default:
        return undefined;
    }
  };

  // Prepare chart data
  const pieChartData = {
    labels: summary
      ? Object.keys(summary.by_type).map(
          (type) => type.charAt(0).toUpperCase() + type.slice(1)
        )
      : [],
    datasets: [
      {
        data: summary ? Object.values(summary.by_type) : [],
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.secondary.main,
          theme.palette.error.main,
        ],
        borderWidth: 1,
      },
    ],
  };

  // Render loading state
  if (loading && !summary) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Render error state
  if (error && !summary) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Feedback Dashboard
      </Typography>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Feedback
              </Typography>
              <Typography variant="h3">{summary?.total || 0}</Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Average Rating
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Typography variant="h3" sx={{ mr: 1 }}>
                  {summary?.average_rating?.toFixed(1) || "N/A"}
                </Typography>
                {summary?.average_rating && (
                  <Rating
                    value={summary.average_rating}
                    precision={0.1}
                    readOnly
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Feature Requests
              </Typography>
              <Typography variant="h3">
                {summary?.by_type?.feature || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Bug Reports
              </Typography>
              <Typography variant="h3">{summary?.by_type?.bug || 0}</Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Charts */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Feedback by Type
            </Typography>
            <Box sx={{ height: 300 }}>
              <Pie
                data={pieChartData}
                options={{ maintainAspectRatio: false }}
              />
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Feedback
            </Typography>
            {summary?.recent && summary.recent.length > 0 ? (
              <Box>
                {summary.recent.map((feedback) => (
                  <Box key={feedback.id} sx={{ mb: 2 }}>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        mb: 1,
                      }}
                    >
                      <Chip
                        label={feedback.feedback_type}
                        color={
                          feedback.feedback_type === "bug"
                            ? "error"
                            : feedback.feedback_type === "feature"
                            ? "secondary"
                            : "primary"
                        }
                        size="small"
                      />
                      <Typography variant="caption">
                        {new Date(feedback.created_at).toLocaleString()}
                      </Typography>
                    </Box>
                    <Typography variant="body2">
                      {feedback.comment ||
                        feedback.feature_request ||
                        feedback.bug_report ||
                        "No comment provided"}
                    </Typography>
                    {feedback.rating && (
                      <Rating value={feedback.rating} size="small" readOnly />
                    )}
                    <Divider sx={{ mt: 1 }} />
                  </Box>
                ))}
              </Box>
            ) : (
              <Typography>No recent feedback available</Typography>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Feedback List */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          All Feedback
        </Typography>

        <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
          <Tab label="All" />
          <Tab label="General" />
          <Tab label="Feature Requests" />
          <Tab label="Bug Reports" />
        </Tabs>

        {loading && feedbackList.length === 0 ? (
          <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Type</TableCell>
                    <TableCell>Rating</TableCell>
                    <TableCell>Feedback</TableCell>
                    <TableCell>Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {feedbackList.map((feedback) => (
                    <TableRow key={feedback.id}>
                      <TableCell>
                        <Chip
                          label={feedback.feedback_type}
                          color={
                            feedback.feedback_type === "bug"
                              ? "error"
                              : feedback.feedback_type === "feature"
                              ? "secondary"
                              : "primary"
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {feedback.rating ? (
                          <Rating
                            value={feedback.rating}
                            size="small"
                            readOnly
                          />
                        ) : (
                          "N/A"
                        )}
                      </TableCell>
                      <TableCell>
                        {feedback.comment ||
                          feedback.feature_request ||
                          feedback.bug_report ||
                          "No comment provided"}
                      </TableCell>
                      <TableCell>
                        {new Date(feedback.created_at).toLocaleString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>

            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={totalCount}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </>
        )}
      </Paper>
    </Box>
  );
};

export default FeedbackDashboard;
