import axios from "axios";
import { api } from "./api";
// import { OPENROUTER_API_KEY } from './envConfig'; // Removed as key is not used directly here

// Model configuration
// TODO: Model should ideally come from backend config
const MODEL = "deepseek/deepseek-r1-distill-llama-70b:free";

// Log the OpenRouter configuration
console.log("OpenRouter Service initialized with model:", MODEL);
// console.log('OpenRouter API Key configured:', Boolean(OPENROUTER_API_KEY)); // Removed log

/**
 * Check if the OpenRouter API key is properly configured
 * @returns An object with status and message
 */
export const checkOpenRouterApiKey = async (): Promise<{
  isValid: boolean;
  message: string;
}> => {
  try {
    // Use the backend to verify the API key
    const response = await api.get("/ai_assistant/config/verify-api-key");
    return {
      isValid: response.data.valid,
      message: response.data.message,
    };
  } catch (error: any) {
    console.error("Error verifying API key:", error);
    return {
      isValid: false,
      message: error.message || "Error verifying API key",
    };
  }
};

// Backward compatibility function for existing code
export const checkOpenRouterApiKeySync = (): {
  isConfigured: boolean;
  message: string;
} => {
  // This is a synchronous version that returns a compatible interface
  // It will be used by existing code until it's updated
  console.warn(
    "checkOpenRouterApiKeySync is deprecated, use the async checkOpenRouterApiKey instead"
  );
  return {
    isConfigured: true, // Assume configured to prevent blocking UI
    message: "API key validation will be performed when needed",
  };
};

/**
 * Verify the OpenRouter API key by making a test request
 * @returns An object with status and message
 */
export const verifyOpenRouterApiKey = async (): Promise<{
  isValid: boolean;
  message: string;
}> => {
  try {
    const response = await api.post("/ai_assistant/config/test-connection", {
      model: MODEL,
    });

    return {
      isValid: response.data.success,
      message: response.data.message,
    };
  } catch (error: any) {
    console.error("Error testing API connection:", error);
    return {
      isValid: false,
      message: error.message || "Error testing API connection",
    };
  }
};

/**
 * Format a transcript using the OpenRouter API
 * @param transcript The transcript to format
 * @param options Formatting options
 * @returns The formatted transcript
 */
export const formatTranscript = async (
  transcript: string,
  options: {
    format: "blog" | "article" | "social" | "summary";
    tone?: string;
    length?: "short" | "medium" | "long";
    keywords?: string[];
  }
): Promise<{ success: boolean; content?: string; error?: string }> => {
  // Create an AbortController with a longer timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 120000); // 60 second timeout

  try {
    const response = await api.post(
      "/ai_assistant/format-transcript",
      {
        transcript,
        options,
      },
      {
        signal: controller.signal,
        timeout: 120000, // Override the default timeout specifically for this request
      }
    );

    clearTimeout(timeoutId);

    return {
      success: true,
      content: response.data.content,
    };
  } catch (error: any) {
    clearTimeout(timeoutId);

    console.error("Error formatting transcript:", error);

    // Provide more specific error messages based on the error type
    let errorMessage = "Error formatting transcript";

    if (error.name === "AbortError" || error.message.includes("timeout")) {
      errorMessage =
        "Request timed out. The formatting process took too long to complete.";
    } else if (error.response) {
      // Server responded with an error status
      errorMessage = `Server error (${error.response.status}): ${
        error.response.data?.error || "Unknown server error"
      }`;
    } else if (error.request) {
      // Request was made but no response received
      errorMessage =
        "No response received from server. Please check your network connection.";
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
};

/**
 * Format a transcript using the OpenRouter API (legacy version)
 * @param transcript The raw transcript text to format
 * @returns A promise that resolves to the formatted transcript
 */
export const formatTranscriptWithAI = async (
  transcript: string
): Promise<string> => {
  if (!transcript.trim()) {
    return "";
  }

  try {
    // Call the new function with default options
    const result = await formatTranscript(transcript, {
      format: "blog",
      tone: "professional",
      length: "long",
    });

    if (!result.success || !result.content) {
      const errorMsg = result.error || "Failed to format transcript";
      console.error("Formatting failed:", errorMsg);
      throw new Error(errorMsg);
    }

    return result.content;
  } catch (error: any) {
    console.error("Error in formatTranscriptWithAI:", error);

    // Enhance the error message for better debugging
    let enhancedError;

    if (error.message.includes("timeout")) {
      enhancedError = new Error(
        "Transcript formatting timed out. The process took too long to complete. Please try again with a shorter transcript or try later when the server is less busy."
      );
    } else if (error.message.includes("network")) {
      enhancedError = new Error(
        "Network error while formatting transcript. Please check your internet connection and try again."
      );
    } else {
      enhancedError = error;
    }

    throw enhancedError;
  }
};

// renderFormattedContent function removed entirely
