{"name": "chrome-extension", "version": "0.5.1", "description": "chrome extension - core settings", "type": "module", "private": true, "sideEffects": false, "scripts": {"clean:node_modules": "pnpm dlx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:turbo && pnpm clean:node_modules", "ready": "tsc -b pre-build.tsconfig.json", "build": "vite build", "dev": "vite build --mode development", "test": "vitest run", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../.prettierignore", "type-check": "tsc --noEmit"}, "dependencies": {"@extension/env": "workspace:*", "@extension/shared": "workspace:*", "@extension/storage": "workspace:*", "@modelcontextprotocol/sdk": "^1.13.1", "vite-plugin-node-polyfills": "^0.23.0", "webextension-polyfill": "^0.12.0", "zustand": "^5.0.5"}, "devDependencies": {"@extension/dev-utils": "workspace:*", "@extension/hmr": "workspace:*", "@extension/tsconfig": "workspace:*", "@extension/vite-config": "workspace:*", "@laynezh/vite-plugin-lib-assets": "^0.6.1", "@types/chrome": "0.0.304", "@types/node": "^22.5.5", "magic-string": "^0.30.10", "ts-loader": "^9.5.1"}}