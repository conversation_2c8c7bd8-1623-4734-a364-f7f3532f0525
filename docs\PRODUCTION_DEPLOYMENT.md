# Writer v2 Production Deployment Guide

This guide covers the production-ready deployment process for Writer v2, implementing all requirements from the Launch Readiness Implementation Guide.

## 🚀 Quick Start

### Prerequisites

1. **Required Software:**
   - Docker & Docker Compose
   - Python 3.9+
   - Git

2. **Required Accounts & Keys:**
   - Supabase account with project
   - OpenRouter API key OR OpenAI API key
   - (Optional) YouTube API key

### 1. Environment Setup

1. **Create Environment File:**
   ```bash
   # Copy the template and fill in your actual values
   cp docs/env-template.txt .env.local
   ```

2. **Fill in Required Values in `.env.local`:**
   ```env
   # Supabase (REQUIRED - get from your Supabase dashboard)
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   SUPABASE_JWT_SECRET=your-jwt-secret

   # AI Provider (REQUIRED - choose one)
   OPENROUTER_API_KEY=your-openrouter-key
   # OR
   # OPENAI_API_KEY=your-openai-key

   # Security
   JWT_SECRET_KEY=your-secure-random-string
   ```

### 2. Deploy to Production

**Option A: Windows**
```cmd
# Run the automated deployment script
deploy-production.bat
```

**Option B: Manual Commands**
```bash
# Validate environment
python scripts/launch_validation.py

# Deploy with production profile
docker-compose --profile prod up -d
```

### 3. Verify Deployment

The deployment script automatically validates the deployment, but you can run it manually:

```bash
python scripts/launch_validation.py
```

## 📊 Deployment Modes

### Production Mode (Default)
- **Command:** `docker-compose up` or `docker-compose --profile prod up`
- **Features:**
  - No hot reload (stable for production)
  - Optimized logging
  - Health checks enabled
  - Relative API URLs (`/api`)
  - Enhanced security

### Development Mode
- **Command:** `docker-compose --profile dev up` or `deploy-development.bat`
- **Features:**
  - Hot reload enabled
  - Debug logging
  - Source code mounted
  - Immediate code changes

## 🔍 Health Checks & Monitoring

### Health Endpoints

- **Primary:** `http://localhost:5000/api/health`
- **Alias:** `http://localhost:5000/health` (for compatibility)
- **Detailed:** `http://localhost:5000/api/health/detailed`

### Validation Script

The `scripts/launch_validation.py` script checks:

- ✅ Required environment variables
- ✅ Supabase connectivity
- ✅ AI provider configuration
- ✅ Health endpoints
- ✅ Secret exposure prevention
- ✅ Docker container status

### Monitoring Commands

```bash
# View real-time logs
docker-compose logs -f

# Check container status
docker-compose ps

# View health status
curl http://localhost:5000/health
```

## 🛡️ Security Features

### Environment Security
- **No hardcoded secrets** in source code
- **Environment file** (`.env.local`) excluded from Git
- **Validation** ensures required secrets are present

### Logging Security
- **Filtered proxy logs** - no Authorization headers logged
- **Minimal error exposure** in production
- **Secret detection** in deployment validation

### Container Security
- **Health checks** fail if credentials missing
- **Read-only mounts** where appropriate
- **Non-root user** execution (in Docker containers)

## 🔧 Troubleshooting

### Common Issues

**1. "Missing environment variables"**
```bash
# Check your .env.local file
cat .env.local

# Ensure all required variables are set
python scripts/launch_validation.py
```

**2. "Supabase connection failed"**
```bash
# Test Supabase connectivity
python supabase_health_check.py

# Check your SUPABASE_URL and SUPABASE_KEY
```

**3. "AI Assistant connection failed"**
```bash
# Verify your AI provider key
curl -H "Authorization: Bearer YOUR_KEY" \
  "https://openrouter.ai/api/v1/models"
```

**4. "Health check failing"**
```bash
# Check container logs
docker-compose logs backend

# Verify the health endpoint directly
curl http://localhost:5000/health
```

### Log Analysis

```bash
# Backend logs
docker-compose logs backend

# Frontend logs
docker-compose logs frontend

# Real-time monitoring
docker-compose logs -f --tail=100
```

## 🚦 Production Checklist

Before deploying to production, ensure:

- [ ] `.env.local` created with all required secrets
- [ ] Launch validation passes: `python scripts/launch_validation.py`
- [ ] Health endpoints respond: `/health` and `/api/health`
- [ ] AI assistant connection works: `/api/ai_assistant/test-connection`
- [ ] Supabase connection verified
- [ ] No secrets in logs
- [ ] Frontend can proxy to backend via `/api`

## 📁 File Structure

```
Writer v2/
├── docs/
│   ├── env-template.txt          # Environment template
│   └── PRODUCTION_DEPLOYMENT.md  # This guide
├── scripts/
│   └── launch_validation.py      # Deployment validation
├── .env.local                    # Your secrets (create this)
├── docker-compose.yml           # Container configuration
├── deploy-production.bat        # Windows deployment script
└── deploy-development.bat       # Windows development script
```

## 🔄 Updates & Maintenance

### Updating the Application

```bash
# Pull latest code
git pull

# Rebuild and redeploy
docker-compose --profile prod down
docker-compose --profile prod build --no-cache
docker-compose --profile prod up -d

# Validate deployment
python scripts/launch_validation.py
```

### Environment Updates

When updating environment variables:

1. Update `.env.local`
2. Restart containers: `docker-compose restart`
3. Validate: `python scripts/launch_validation.py`

## 🆘 Support

If you encounter issues:

1. **Check logs:** `docker-compose logs`
2. **Run validation:** `python scripts/launch_validation.py`
3. **Review this guide**
4. **Check the launch readiness guide:** `docs/launch_readiness_implementation_guide.md`

---

**🎉 Congratulations!** You now have a production-ready Writer v2 deployment with enterprise-grade security and monitoring. 