import sys
import os
import pytest
from dotenv import load_dotenv
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone
from supabase import create_client
from fastapi.testclient import TestClient

# Import the FastAPI app instance from your main application file
from main import app as fastapi_app
from app.models.supabase_client import set_test_client
from gotrue.errors import AuthApiError

# Add the project root directory to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Load environment variables from .env.test
load_dotenv(dotenv_path=".env.test", override=True)


# --- Application Fixture ---

@pytest.fixture(scope="session")
def app():
    """Session-wide test FastAPI application."""
    # The app is already created, we just return it.
    # Configuration is handled by Pydantic settings in main.py
    yield fastapi_app


@pytest.fixture(scope="session")
def client(app):
    """A test client for the FastAPI app."""
    # Use FastAPI's TestClient
    with TestClient(app) as c:
        yield c


# --- Authentication Fixture ---


@pytest.fixture(scope="session")
def test_user_credentials():
    """Provides standard test user email and password."""
    # Using simplest possible email format
    return ("<EMAIL>", "password123")


import os  # Import os


@pytest.fixture(scope="session")
def test_login_response(client, test_user_credentials):
    """
    Ensures test user exists & is confirmed via admin client, then simulates a login response
    by directly signing in with the admin client and formatting the result.
    Avoids calling the potentially mocked /api/auth/login endpoint during setup.
    """
    test_email, test_password = test_user_credentials

    # --- Force reload env vars and create temporary admin client ---
    load_dotenv(dotenv_path=".env.test", override=True)  # Force override
    temp_supabase_url = os.environ.get("SUPABASE_URL")
    temp_service_key = os.environ.get("SUPABASE_KEY")  # Use SERVICE ROLE KEY

    if not temp_supabase_url or not temp_service_key:
        pytest.fail(
            "SUPABASE_URL or SUPABASE_KEY (Service Role) not found after reloading .env.test"
        )

    # Use the freshly loaded service key for a temporary admin client
    try:
        temp_admin_client = create_client(temp_supabase_url, temp_service_key)
    except Exception as e:
        pytest.fail(f"Failed to create temporary Supabase admin client: {e}")
    # --- End temporary admin client setup ---

    # --- Use Temporary Admin Client to Ensure User Exists and is Confirmed ---

    user_id = None
    user_obj = None  # Store the user object
    try:
        # Attempt to get user first
        list_users_response = temp_admin_client.auth.admin.list_users()
        found_user = next(
            (u for u in list_users_response if u.email == test_email), None
        )

        if found_user:
            print(f"Info: Test user {test_email} already exists.")
            user_id = found_user.id
            user_obj = found_user
            # Ensure user is confirmed
            if not found_user.email_confirmed_at:
                print(f"Info: Confirming email for existing user {test_email}...")
                temp_admin_client.auth.admin.update_user_by_id(
                    user_id, {"email_confirm": True}
                )
                # Re-fetch user to get updated confirmation status if needed, though not strictly necessary for sign-in
        else:
            print(f"Info: Creating test user {test_email}...")
            # Create user, confirm immediately
            create_user_response = temp_admin_client.auth.admin.create_user(
                {
                    "email": test_email,
                    "password": test_password,
                    "email_confirm": True,  # Confirm directly
                    "user_metadata": {"name": "Auth Test User"},
                }
            )
            user_id = create_user_response.user.id
            user_obj = create_user_response.user
            print(f"Info: User {test_email} created with ID {user_id}.")

    except AuthApiError as e:
        # Handle specific errors if necessary, otherwise fail
        pytest.fail(
            f"An unexpected AuthApiError occurred during user creation/check for {test_email}: {e}"
        )
    except Exception as e:
        pytest.fail(
            f"An unexpected error occurred during user creation/check for {test_email}: {e}"
        )

    if not user_id or not user_obj:
        pytest.fail(
            f"Failed to obtain user_id or user object for {test_email} after creation/check."
        )
    # --- End User Existence/Confirmation Check ---

    # --- Directly Sign In User via Admin Client to get Session ---
    try:
        print(f"Attempting direct sign-in for {test_email}...")
        # Use the standard sign_in_with_password (available on admin client too)
        sign_in_response = temp_admin_client.auth.sign_in_with_password(
            {"email": test_email, "password": test_password}
        )
        print(f"Direct sign-in successful for {test_email}.")

        # Ensure we got session data
        if not sign_in_response.session or not sign_in_response.user:
            pytest.fail(
                f"Direct sign-in for {test_email} did not return expected session/user data. Response: {sign_in_response}"
            )

        # Construct the dictionary matching the structure previously expected from the API
        # We need to convert the response objects to dictionaries
        # user_dict = user_obj.dict() # Convert the user model object to dict
        user_dict = user_obj.model_dump()  # Use model_dump()
        # session_dict = sign_in_response.session.dict() # Convert the session model object to dict
        session_dict = sign_in_response.session.model_dump()  # Use model_dump()

        # Ensure essential keys are present after conversion (adjust based on actual model dict() output)
        if "id" not in user_dict or "access_token" not in session_dict:
            pytest.fail(
                f"Converted user/session dicts missing essential keys. User: {user_dict}, Session: {session_dict}"
            )

        login_data = {"user": user_dict, "session": session_dict}

        print(f"Constructed login_data: {login_data}")  # Debugging output

    except Exception as e:
        pytest.fail(f"Direct sign-in using admin client failed for {test_email}: {e}")
    # --- End Direct Sign In ---

    # The check that was failing before
    if (
        not login_data
        or "user" not in login_data
        or "id" not in login_data.get("user", {})
        or "session" not in login_data
        or not isinstance(login_data.get("session"), dict)
        or "access_token" not in login_data["session"]
        or not login_data["session"]["access_token"]
    ):
        pytest.fail(
            f"CRITICAL: Constructed login_data is invalid BEFORE returning. Data: {login_data}"
        )  # Fail fast if constructed data is wrong

    return login_data  # Return the constructed dictionary


@pytest.fixture(scope="function")
def auth_headers(test_login_response):
    """Provides authorization headers from the test user login."""
    # Correctly extract the token from the session dictionary
    session_data = test_login_response.get("session", {})
    token = session_data.get("access_token")
    if not token:
        pytest.fail("Failed to extract access_token from test_login_response session data.")
    return {"Authorization": f"Bearer {token}"}


# You might need a fixture for a test user ID if tests require it
@pytest.fixture(scope="function")
def test_user_id(test_login_response):
    """Gets the user ID from the test user login response."""
    user_info = test_login_response.get("user")
    user_id = user_info.get("id")

    if not user_id:
        pytest.fail("Failed to get user ID from login response.")

    return user_id


# --- Mock Supabase Client Fixture ---


# Reinstate and improve the global mock client fixture
# Change scope to 'function' to ensure it runs for every test
@pytest.fixture(scope="function")
def setup_mock_supabase_client(test_user_id):
    """Sets a mock Supabase client instance for testing, handling common chains."""
    # Create a mock response object that can be reused
    mock_response = MagicMock(name="MockSupabaseResponse")
    mock_response.data = []  # Default empty list

    # Create the main client mock
    mock_client_instance = MagicMock(name="GlobalMockSupabaseClient")

    # --- Configure Mock to Return Mock Response after Execute ---
    # The goal is that any chain ending in .execute() returns mock_response.
    # We configure intermediate steps to return a mock that *has* .execute()
    mock_executable = MagicMock(name="MockExecutable")
    mock_executable.execute.return_value = mock_response

    # Configure common starting chains to lead to the mock_executable
    mock_client_instance.table.return_value.select.return_value = mock_executable
    mock_client_instance.table.return_value.insert.return_value = mock_executable
    mock_client_instance.table.return_value.update.return_value = mock_executable
    mock_client_instance.table.return_value.delete.return_value = mock_executable
    # Add common filters returning the same mock_executable, allowing further chaining if needed by MagicMock
    mock_executable.eq.return_value = mock_executable
    mock_executable.limit.return_value = mock_executable
    mock_executable.order.return_value = mock_executable
    mock_executable.single.return_value = (
        mock_executable  # single() is often called before execute()
    )
    mock_executable.maybe_single.return_value = mock_executable  # maybe_single() too

    # --- Special override for insert (to return data) ---
    mock_insert_response = MagicMock(name="MockInsertResponse")
    # Use timezone-aware UTC time
    mock_insert_response.data = [
        {
            "id": 1,
            "user_id": test_user_id,
            "created_at": datetime.now(
                timezone.utc
            ).isoformat(),  # Use correct datetime usage with timezone.utc
            "title": "Test Prompt Title",
            "context": "Test Context",
            "call_to_action": "Test CTA",
            "persona": "Test Persona",
            "format": "Test Format",
            "tone": "Test Tone",
            "complexity": "high",
            "target_audience": "Test Audience",
            "user_query": "Test User Query",
            "seo_keywords": ["test", "seo"],
            "additional_notes": "Test Notes",
        }
    ]
    mock_insert_response.count = 1
    mock_client_instance.table().insert().execute.return_value = mock_insert_response
    # --- End Supabase Mock Configuration ---

    set_test_client(mock_client_instance)
    yield mock_client_instance
    # Teardown: Reset the test client
    set_test_client(None)


# --- Mock Repository Fixtures ---

# Path confirmed by search
USER_REPO_PATH = "app.repositories.supabase_user_repository.UserRepository"
# Define other paths
ACTIVITY_REPO_PATH = (
    "app.repositories.activity_log_repository.ActivityLogRepository"  # Corrected Path
)
CONTENT_REPO_PATH = "app.repositories.supabase_content_item_repository.ContentItemRepository"  # Corrected typo
CONTEXT_REPO_PATH = (
    "app.repositories.supabase_business_context_repository.BusinessContextRepository"
)
CHAT_REPO_PATH = (
    "app.repositories.supabase_chat_session_repository.ChatSessionRepository"
)
FEEDBACK_REPO_PATH = "app.repositories.supabase_feedback_repository.FeedbackRepository"
VIDEO_REPO_PATH = "app.repositories.supabase_video_data_repository.VideoDataRepository"  # Ensure this is correct


@pytest.fixture(scope="function")  # Use function scope for isolation
def mock_user_repo_class():
    """Mocks the UserRepository class for dependency injection."""
    # Add a name to the MagicMock for easier debugging if needed
    with patch(
        USER_REPO_PATH, MagicMock(name="MockUserRepositoryClass")
    ) as MockUserRepo:
        # Configure the return value for the login method (called by the route)
        mock_instance = MockUserRepo.return_value

        # Define the fully serializable dictionary to be returned by the mocked login method
        mock_login_response = {
            "user": {
                "id": "mock_user_id_dict",  # String
                "email": "<EMAIL>",  # String
                "aud": "authenticated",  # String
                "role": "authenticated",  # String
                "app_metadata": {"provider": "email"},  # Dict[str, str]
                "user_metadata": {"name": "Auth Test User Dict"},  # Dict[str, str]
                "created_at": "2024-07-04T10:00:00Z",  # ISO String
                "updated_at": "2024-07-04T11:00:00Z",  # ISO String
            },
            "session": {
                "access_token": "mock_access_token_dict",  # String
                "refresh_token": "mock_refresh_token_dict",  # String
                "expires_at": **********,  # Example Unix timestamp (int) - JSON serializable
                "id": "mock_session_id_dict",  # String
                "user_id": "mock_user_id_dict",  # String (match user ID)
                "created_at": "2024-07-04T10:00:00Z",  # ISO String
                "updated_at": "2024-07-04T11:00:00Z",  # ISO String
            },
        }

        # Set the return value of the mocked login method to the plain dictionary
        mock_instance.login.return_value = mock_login_response

        # Configure other methods if needed by specific tests
        # mock_instance.get_by_id.return_value = MagicMock(id='test-user-id', email='<EMAIL>')
        # mock_instance.create.return_value = MagicMock(id='new-user-id', email='<EMAIL>')
        yield MockUserRepo


@pytest.fixture(scope="function")  # Changed scope to function to resolve ScopeMismatch
def mock_activity_repo_class():
    """Provides a module-scoped mock for the ActivityRepository class."""
    # Use autospec=True for better mocking based on the actual class signature
    # Use the corrected ACTIVITY_REPO_PATH constant
    with patch(ACTIVITY_REPO_PATH, autospec=True) as MockActivityRepo:
        yield MockActivityRepo  # Yield the mocked class


@pytest.fixture(scope="function")  # Changed scope to function to resolve ScopeMismatch
def mock_content_repo_class():
    with patch(CONTENT_REPO_PATH, autospec=True) as MockContentRepo:
        yield MockContentRepo


@pytest.fixture(scope="function")  # Changed scope to function to resolve ScopeMismatch
def mock_context_repo_class():
    with patch(CONTEXT_REPO_PATH, autospec=True) as MockContextRepo:
        yield MockContextRepo


@pytest.fixture(scope="function")  # Changed scope to function to resolve ScopeMismatch
def mock_chat_repo_class():
    with patch(CHAT_REPO_PATH, autospec=True) as MockChatRepo:
        yield MockChatRepo


@pytest.fixture(scope="function")  # Changed scope to function to resolve ScopeMismatch
def mock_feedback_repo_class():
    with patch(FEEDBACK_REPO_PATH, autospec=True) as MockFeedbackRepo:
        yield MockFeedbackRepo


@pytest.fixture(scope="function")  # Changed scope to function to resolve ScopeMismatch
def mock_video_repo_class():
    with patch(VIDEO_REPO_PATH, autospec=True) as MockVideoRepo:
        yield MockVideoRepo


# --- Mock Authentication Fixture ---


@pytest.fixture(scope="function")
def mock_auth_verification(test_user_id):
    """Mocks the verify_supabase_token function to bypass real auth."""
    mock_user = {
        "id": test_user_id,
        "aud": "authenticated",
        "role": "authenticated",
        "email": "<EMAIL>",
        "user_metadata": {"name": "Test User"},
    }

    def mock_verify(token):
        print(
            f"Mock verify_supabase_token called with token: {token[:10] if token else 'None'}... Returning mock user: {mock_user['id']}"
        )
        # Basic check: if a token is passed (even a dummy one), return the mock user.
        if token:
            return mock_user
        return None  # Return None if no token was passed

    with patch(
        "app.utils.supabase_auth.verify_supabase_token", side_effect=mock_verify
    ) as _mock_verify_func:
        yield None  # Yield None - the value isn't used, just need the patch to be active
