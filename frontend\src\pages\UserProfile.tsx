import React, { useState, useEffect } from "react";
import {
  Container,
  Paper,
  Box,
  Typography,
  TextField,
  Button,
  Avatar,
  Grid,
  Divider,
  Alert,
  CircularProgress,
  Snackbar,
} from "@mui/material";
import { LoadingButton } from "@mui/lab";
import { Save as SaveIcon } from "@mui/icons-material";
import { useAuth } from "../contexts/AuthContext";
import { useSelector } from "react-redux";
import { selectCurrentUser } from "../store/auth/authSlice";
import { motion } from "framer-motion";


// Define the expected structure of the Redux user
interface ReduxUserProfile {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
}

const UserProfile: React.FC = () => {
  const { user: supabaseUser, updateProfile, resendConfirmationEmail } = useAuth();
  const reduxUser = useSelector(selectCurrentUser) as ReduxUserProfile | null;

  // Utility function to safely extract string from error objects
  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.details) return error.details;
    if (error?.error) return getErrorMessage(error.error);
    if (typeof error === 'object') return JSON.stringify(error);
    return 'An unknown error occurred';
  };
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [emailVerified, setEmailVerified] = useState(false);
  const [verificationSent, setVerificationSent] = useState(false);

  // Load user data
  useEffect(() => {
    // Try to get user data from Supabase first, fall back to Redux
    if (supabaseUser) {
      setEmail(supabaseUser.email || "");

      // For Supabase user, get data from user metadata
      const metadata = supabaseUser.user_metadata || {};
      setFirstName(metadata.firstName || metadata.first_name || "");
      setLastName(metadata.lastName || metadata.last_name || "");
      setUsername(metadata.username || supabaseUser.email?.split("@")[0] || "");
      setEmailVerified(supabaseUser.email_confirmed_at !== null);
    }
    // For Redux user, use the fields directly
    else if (reduxUser) {
      setEmail(reduxUser.email || "");
      setFirstName(reduxUser.firstName || "");
      setLastName(reduxUser.lastName || "");
      setUsername(reduxUser.username || "");
      setEmailVerified(reduxUser.isActive || false);
    }
  }, [supabaseUser, reduxUser]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Update profile in Supabase
      const result = await updateProfile({
        firstName,
        lastName,
        username,
      });

      if (result.success) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        setError(getErrorMessage(result.error || "Failed to update profile"));
      }
    } catch (err: any) {
      setError(getErrorMessage(err.message || "An unexpected error occurred"));
    } finally {
      setLoading(false);
    }
  };

  const handleSendVerificationEmail = async () => {
    setLoading(true);
    setVerificationSent(false);
    setError(null);

    try {
      // Send verification email using centralized AuthContext function
      const result = await resendConfirmationEmail();

      if (!result.success) {
        throw new Error(result.error || "Failed to send verification email");
      }

      setVerificationSent(true);
      setTimeout(() => setVerificationSent(false), 5000);
    } catch (err: any) {
      setError(getErrorMessage(err.message || "Failed to send verification email"));
    } finally {
      setLoading(false);
    }
  };

  // Generate initials for avatar
  const initials =
    firstName && lastName
      ? `${firstName[0] || ""}${lastName[0] || ""}`.toUpperCase()
      : username
      ? username[0].toUpperCase()
      : "U";

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper elevation={2} sx={{ p: 4, borderRadius: 2 }}>
          <Box sx={{ display: "flex", alignItems: "center", mb: 4 }}>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: "primary.main",
                fontSize: "2rem",
                mr: 3,
              }}
            >
              {initials}
            </Avatar>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom>
                Profile
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage your account information
              </Typography>
            </Box>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Snackbar
            open={success}
            autoHideDuration={3000}
            onClose={() => setSuccess(false)}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
          >
            <Alert severity="success" sx={{ width: "100%" }}>
              Profile updated successfully!
            </Alert>
          </Snackbar>

          <Snackbar
            open={verificationSent}
            autoHideDuration={5000}
            onClose={() => setVerificationSent(false)}
            anchorOrigin={{ vertical: "top", horizontal: "center" }}
          >
            <Alert severity="success" sx={{ width: "100%" }}>
              Verification email sent. Please check your inbox!
            </Alert>
          </Snackbar>

          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Account Information
                </Typography>
                <Divider sx={{ mb: 2 }} />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  label="First Name"
                  fullWidth
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  label="Last Name"
                  fullWidth
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  label="Username"
                  fullWidth
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>

              <Grid item xs={12}>
                <Box display="flex" alignItems="center">
                  <TextField
                    label="Email"
                    fullWidth
                    value={email}
                    disabled
                    InputLabelProps={{ shrink: true }}
                    sx={{ mr: 2 }}
                  />
                  {!emailVerified && (
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={handleSendVerificationEmail}
                      disabled={loading}
                    >
                      Verify Email
                    </Button>
                  )}
                </Box>
                {!emailVerified && (
                  <Typography
                    variant="caption"
                    color="warning.main"
                    sx={{ mt: 1, display: "block" }}
                  >
                    Your email is not verified. Please verify your email to
                    ensure account security.
                  </Typography>
                )}
                {emailVerified && (
                  <Typography
                    variant="caption"
                    color="success.main"
                    sx={{ mt: 1, display: "block" }}
                  >
                    Your email is verified.
                  </Typography>
                )}
              </Grid>

              <Grid item xs={12}>
                <Box
                  sx={{ mt: 2, display: "flex", justifyContent: "flex-end" }}
                >
                  <LoadingButton
                    type="submit"
                    variant="contained"
                    color="primary"
                    loading={loading}
                    loadingIndicator={
                      <CircularProgress color="inherit" size={16} />
                    }
                    startIcon={<SaveIcon />}
                  >
                    Save Changes
                  </LoadingButton>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </motion.div>
    </Container>
  );
};

export default UserProfile;
