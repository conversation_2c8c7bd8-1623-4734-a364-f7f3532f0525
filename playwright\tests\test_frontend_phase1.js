// Phase 1 Frontend Testing with Playwright
// Tests core MVP functionality: Auth, Business Context, Chat

const { test, expect } = require('@playwright/test');

test.describe('Phase 1 FastAPI Migration - Frontend Tests', () => {
  
  test('should complete authentication flow', async ({ page }) => {
    console.log('🧪 Testing authentication flow...');
    
    // Navigate to login page
    await page.goto('http://localhost:3000/login');
    await page.waitForLoadState('networkidle');
    
    // Fill login form
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', 'MK*9v9sseuu3#Z3qgypf');
    
    // Submit login
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("Sign In")');
    
    // Wait for redirect or success
    await page.waitForTimeout(3000);
    
    // Check if we're redirected to dashboard/main page
    const currentUrl = page.url();
    console.log(`Current URL after login: ${currentUrl}`);
    
    // Verify we're not still on login page
    expect(currentUrl).not.toContain('/login');
    
    console.log('✅ Authentication flow completed');
  });
  
  test('should access AI chat interface', async ({ page }) => {
    console.log('🧪 Testing AI chat interface...');
    
    // Login first
    await page.goto('http://localhost:3000/login');
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', 'MK*9v9sseuu3#Z3qgypf');
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("Sign In")');
    await page.waitForTimeout(3000);
    
    // Navigate to chat/AI assistant page
    await page.goto('http://localhost:3000/ai-assistant');
    await page.waitForLoadState('networkidle');
    
    // Look for chat interface elements
    const chatElements = await page.locator('textarea, input[placeholder*="message"], input[placeholder*="chat"], [data-testid*="chat"]').count();
    console.log(`Found ${chatElements} potential chat input elements`);
    
    // Check for any obvious chat interface
    const hasTextarea = await page.locator('textarea').count() > 0;
    const hasChatInput = await page.locator('input[placeholder*="message"], input[placeholder*="chat"]').count() > 0;
    
    if (hasTextarea || hasChatInput) {
      console.log('✅ Chat interface elements found');
    } else {
      console.log('⚠️ Chat interface elements not clearly identified, but page loaded');
    }
  });
  
  test('should access business context management', async ({ page }) => {
    console.log('🧪 Testing business context interface...');
    
    // Login first
    await page.goto('http://localhost:3000/login');
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', 'MK*9v9sseuu3#Z3qgypf');
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("Sign In")');
    await page.waitForTimeout(3000);
    
    // Try to navigate to business context page
    await page.goto('http://localhost:3000/business');
    await page.waitForLoadState('networkidle');
    
    // Check if the page loaded (not 404)
    const pageTitle = await page.title();
    const currentUrl = page.url();
    
    console.log(`Business page title: ${pageTitle}`);
    console.log(`Business page URL: ${currentUrl}`);
    
    // Look for business context related elements
    const businessElements = await page.locator('*:has-text("business"), *:has-text("context"), *:has-text("profile")').count();
    console.log(`Found ${businessElements} potential business context elements`);
    
    if (businessElements > 0 || !currentUrl.includes('404')) {
      console.log('✅ Business context interface accessible');
    } else {
      console.log('⚠️ Business context interface may need investigation');
    }
  });
  
  test('should check overall application health', async ({ page }) => {
    console.log('🧪 Testing overall application health...');
    
    // Test main app loads
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('networkidle');
    
    const pageTitle = await page.title();
    console.log(`Main page title: ${pageTitle}`);
    
    // Check for critical errors
    const errors = [];
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    page.on('requestfailed', request => {
      if (request.url().includes('/api/')) {
        errors.push(`API request failed: ${request.url()}`);
      }
    });
    
    await page.waitForTimeout(2000);
    
    if (errors.length === 0) {
      console.log('✅ No critical errors detected');
    } else {
      console.log(`⚠️ Detected ${errors.length} errors:`);
      errors.forEach(error => console.log(`  - ${error}`));
    }
    
    console.log('✅ Application health check completed');
  });
  
}); 