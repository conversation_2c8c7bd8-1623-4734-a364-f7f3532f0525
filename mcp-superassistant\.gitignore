# dependencies
**/node_modules

# testing
**/coverage

# build
**/dist
**/build
**/dist-zip
chrome-extension/manifest.js
chrome-extension/pre-build.tsconfig.tsbuildinfo
eslint.config.js
tsconfig.tsbuildinfo

# env
**/.env.*
**/.env

# etc
.DS_Store
.idea
**/.turbo
**/.gitt
# compiled
**/tailwind-output.css

# trash
.trash
.trash/*
mcp-superassistant-proxy/bun.lock
# pnpm-lock.yaml

# Added by Task Master AI
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 



.windsurfrules
ADAPTER_BUTTON_STYLING.md
bun.lock
commit_diff.patch
DYNAMIC_THEMING.md
file_structure.md
FIREBASE_REMOTE_CONFIG_ADAPTER_INTEGRATION.md
FIREBASE_REMOTE_CONFIG_INTEGRATION copy.md
FIREBASE_REMOTE_CONFIG_INTEGRATION.md
FIREBASE_REMOTE_CONFIG_SUMMARY.md
FIREBASE_REMOTE_CONFIG_TESTING.md
FIREBASE_SERVICE_WORKER_SOLUTION.md
FIREBASE_SETUP_GUIDE.md
firebase-remote-config-gemini-production.json
firebase-remote-config-gemini.json
firebase-remote-config-unified.json
MCP_CLIENT_REWRITE_COMPLETE.md
MCP_COMMUNICATION_FIXES.md
SESSION_10_COMPLETE.md
TEST_MCP_TOGGLE.md
websocket doc.md
chrome-extension/public/icon-16.png
chrome-extension/public/icon-34 copy.png
chrome-extension/public/icon-34.png
chrome-extension/src/mcpclient/architecture-option-3-plugin-based.md
pages/content/src/plugins/adapters/gemini-migration-guide.md
pages/content/src/plugins/adapters/scira.adapter.ts
pages/content/src/plugins/adapters/defaultConfigs/README.md
pages/content/src/shared/firebase-config.ts
pages/content/src/utils/migration-bridge.ts
pages/content/src/utils/siteAdapter.ts
pages/content/src/utils/themeDetector.ts
