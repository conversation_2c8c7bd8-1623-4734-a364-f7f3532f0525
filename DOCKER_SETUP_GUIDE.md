# 🐳 Docker Setup Guide - Writer v2

## 🚨 Current Status
Your Docker Compose setup has been **completely fixed** and is ready to use!

## 📋 Prerequisites

### 1. Start Docker Desktop
Docker Desktop is currently not running. You need to:

1. **Open Docker Desktop** from your Start menu or desktop
2. **Wait for it to fully initialize** (you'll see the Docker whale icon in your system tray)
3. **Verify it's running** by opening a terminal and running: `docker version`

### 2. Verify Required Files
All required files are present:
- ✅ `Dockerfile.backend` (Fixed and optimized)
- ✅ `frontend/Dockerfile.dev` (Updated to Node 18)
- ✅ `docker-compose.yml` (Completely overhauled)
- ✅ `.env.local` (Environment variables)
- ✅ `supabase_health_check.py` (Enhanced health check)

## 🚀 Quick Start

### Option 1: Automated Test Script (Recommended)
```bash
# Run the automated test script
./test-docker-setup.bat
```

This script will:
- ✅ Check Docker Desktop status
- ✅ Validate configuration
- ✅ Build images
- ✅ Start services
- ✅ Run health checks

### Option 2: Manual Setup
```bash
# 1. Validate configuration
docker-compose config

# 2. Build images (first time only)
docker-compose build

# 3. Start services
docker-compose up -d

# 4. Check status
docker-compose ps

# 5. View logs
docker-compose logs -f
```

## 🔧 Key Improvements Made

### Backend (FastAPI)
- ✅ **Updated Dockerfile** with security best practices
- ✅ **Fixed health checks** with proper error handling
- ✅ **Added non-root user** for security
- ✅ **Optimized build process** with better caching
- ✅ **Enhanced logging** and error reporting

### Frontend (React)
- ✅ **Updated to Node 18** (from outdated Node 16)
- ✅ **Added health checks** with curl
- ✅ **Optimized build process** with npm ci
- ✅ **Added security user** for container
- ✅ **Fixed environment variables** for Docker networking

### Docker Compose
- ✅ **Fixed service dependencies** with health check conditions
- ✅ **Improved networking** configuration
- ✅ **Enhanced logging** with rotation
- ✅ **Better volume management** for persistence
- ✅ **Separated dev/prod profiles** for different environments

## 🌐 Service URLs

Once running, your services will be available at:

- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/docs
- **Frontend**: http://localhost:3000
- **Development Backend** (if using dev profile): http://localhost:5001

## 🔍 Troubleshooting

### Docker Desktop Issues
```bash
# Check if Docker is running
docker version

# If not running, start Docker Desktop and wait for initialization
```

### Build Issues
```bash
# Clean build (removes cache)
docker-compose build --no-cache

# Remove old images and rebuild
docker system prune -a
docker-compose build
```

### Service Issues
```bash
# Check service status
docker-compose ps

# View logs for specific service
docker-compose logs backend
docker-compose logs frontend

# Restart specific service
docker-compose restart backend
```

### Health Check Issues
```bash
# Test Supabase connection manually
python supabase_health_check.py

# Test basic health check
python supabase_health_check.py --basic
```

## 🛑 Common Commands

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Rebuild and restart
docker-compose down && docker-compose build && docker-compose up -d

# Use development profile (with hot reload)
docker-compose --profile dev up -d
```

## 🎯 Next Steps

1. **Start Docker Desktop** if not already running
2. **Run the test script**: `./test-docker-setup.bat`
3. **Access your application** at http://localhost:3000
4. **Check API documentation** at http://localhost:5000/docs

Your Docker setup is now production-ready with proper security, health checks, and monitoring! 🚀
