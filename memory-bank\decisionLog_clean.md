# Decision Log

- **Decision:** Opted for an inline Create Profile Wizard (`CreateProfileWizard.tsx`) instead of a modal dialog for creating new business contexts to provide a more integrated user experience within the `BusinessContextPage`.
- **Decision:** Refactored the conditional rendering in `BusinessContextPage.tsx` using a ternary operator (`isCreateWizardOpen ? <Wizard /> : <AccordionView />`) to ensure mutual exclusivity between the wizard and the main accordion view, resolving overlapping UI issues.
- **Decision:** Merged 'Decision Style' fields directly into the 'Target Audience' accordion section instead of creating separate collapsible sub-panels, simplifying the UI structure.
- **Decision:** Removed deprecated `frontend/src/pages/BusinessContext.tsx` component and its associated test file (`BusinessContext.test.tsx`) after investigation revealed significant duplication and outdated functionality compared to `BusinessContextPage.tsx`. Routing was verified to use the latter component.
- **Decision:** Removed the planned sticky section navigation feature for the Business Context page as it was deemed unnecessary for the current scope.
- **Decision:** Addressed 415 error on POST `/youtube/save-to-library` by ensuring frontend (`axios`) sends `Content-Type: application/json` (via sending `{}`) and making the backend Flask route (`request.get_json(silent=True) or {}`) robust to empty bodies.
- **Decision (AI Assistant UI):** Implemented Copy-to-Clipboard button to be visible on *all* non-loading assistant messages, not just specifically identified scripts, for broader utility.
- **Decision (AI Assistant UI):** Implemented Feedback widget (👍/👎) to be visible on *all* non-loading messages (both user and assistant) to gather feedback more broadly.
- **Decision (AI Assistant UI):** Relocated Copy and Save-to-Library buttons from the top-right corner to a bottom action bar within the message card for better grouping with the feedback widget.
- **Decision (DB Migration):** Migrated existing integer-based ratings (1-5) in the `feedback` table to text ('up'/'down') by mapping 4/5 to 'up' and 1/2/3 to 'down' before applying the new text-based CHECK constraint, preserving historical data context.
- **Decision (Prompts Refactor):** Consolidated multiple prompt generation functions (`get_chat_system_prompt`, `get_general_content_prompt`, etc.) into a single `build_system_prompt` function within `app/ai_assistant/prompts/generic.py`. This function uses flags (`optimization_mode`, `include_youtube_capabilities`) and a `content_format` parameter to control output, removing redundancy and centralizing prompt logic.
- **Decision (Prompts Typing):** Replaced dynamically generated `ContentType = Literal[tuple(PROMPT_CONFIG.keys())]` with a static `Literal["general", "script", ...]` in `generic.py` to satisfy static type checker requirements (Pylance) which cannot evaluate runtime expressions in type definitions.
- **Decision (Prompts Refactor):** Simplified `transcript.py` by removing the multi-format `get_transcript_format_prompt` and replacing it with a single `format_transcript` function using a fixed template for basic markdown structuring (headings, paragraphs, bullets) while preserving exact wording.
- **Decision (Wizard Implementation):** Created a dedicated `wizard_sessions` table in Supabase to store the state of each user's wizard session, including inputs and outputs for each step.
- **Decision (Wizard Data Flow):** Implemented a pattern where each sequential wizard endpoint handler retrieves the session data, validates user ownership, reads necessary data from previous steps, builds prompts using session data, calls the AI generator, and updates the session row with results.
- **Decision (Wizard Save Mechanism):** Decided to implement the final step of the wizard by saving the completed script to the Content Library using the existing POST `/content/` endpoint, rather than creating a new dedicated save endpoint. This leverages the existing content management infrastructure and ensures consistency with other content items. 