import { createSlice, createAsyncThunk, PayloadAction, createSelector } from "@reduxjs/toolkit";
import { Session, User } from "@supabase/supabase-js";
// Removed unused supabase import
// import { supabase } from '../../lib/supabase';
import type { RootState } from "../store";

// Types
// Removed UserProfile interface as it's derived
// export interface UserProfile { ... }

export interface AuthState {
  // Supabase specific - Source of Truth
  supabaseSession: Session | null;
  supabaseUser: User | null;

  // Legacy fields removed:
  // user: UserProfile | null;
  // token: string | null;
  // isAuthenticated: boolean;
  // sessionExpiry: number | null;

  // Standard fields
  isLoading: boolean;
  error: string | null;
  lastActivity: number;
}

// Map Supabase user to our UserProfile format
// Keep this utility function for selectors or components that need the mapped format
const mapSupabaseUserToProfile = (user: User | null): any | null => {
  // Use 'any' for now, define profile type elsewhere if needed
  if (!user) return null;

  const userData = user.user_metadata || {};

  return {
    id: user.id,
    username: userData.username || user.email?.split("@")[0] || "",
    email: user.email || "",
    firstName: userData.firstName || userData.first_name || "",
    lastName: userData.lastName || userData.last_name || "",
    createdAt: user.created_at || new Date().toISOString(),
    updatedAt: user.updated_at || new Date().toISOString(),
    isActive: user.confirmed_at !== null,
  };
};

// Initial state - Remove loading from localStorage
export const initialState: AuthState = {
  // Supabase specific
  supabaseSession: null,
  supabaseUser: null,

  // Legacy fields removed
  // user: JSON.parse(localStorage.getItem('user') || 'null'),
  // token: localStorage.getItem('token'),
  // isAuthenticated: Boolean(localStorage.getItem('token')),
  // sessionExpiry: localStorage.getItem('sessionExpiry') ? Number(localStorage.getItem('sessionExpiry')) : null,

  // Standard fields
  isLoading: true, // Start loading until first session check completes
  error: null,
  lastActivity: Date.now(),
};

// Async thunks REMOVED
// export const syncSupabaseSession = ...
// export const signInWithSupabase = ...
// export const signUpWithSupabase = ...
// export const signOutFromSupabase = ...

// Auth slice
const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    // Legacy reducers REMOVED or simplified
    /* logout: (state) => { ... }, */
    // checkSession REMOVED
    /* checkSession: (state) => { ... }, */

    clearError: (state) => {
      state.error = null;
    },
    updateActivity: (state) => {
      state.lastActivity = Date.now();
    },
    // Only updates Supabase state, removes legacy logic and localStorage calls
    setSupabaseSession: (state, action: PayloadAction<Session | null>) => {
      state.supabaseSession = action.payload;
      state.supabaseUser = action.payload?.user || null;
      state.isLoading = false; // Mark loading as false once session is set/checked
      state.error = null; // Clear any previous error on session update

      // Legacy field updates REMOVED
      // localStorage updates REMOVED
    },
    // Added simple loading/error states if needed by components directly interacting
    // Though ideally these are handled by useAuth() context
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
    },
  },
  // extraReducers REMOVED as thunks are removed
  /*
  extraReducers: (builder) => {
    builder
      // ... cases for removed thunks ...
  },
  */
});

// Export actions
export const {
  clearError,
  updateActivity,
  setSupabaseSession,
  setLoading, // Export new actions if needed
  setError,
} = authSlice.actions;

// Selectors - Updated to derive from Supabase state
export const selectSupabaseSession = (state: RootState) =>
  state.auth.supabaseSession;
export const selectSupabaseUser = (state: RootState) => state.auth.supabaseUser;
export const selectAuthLoading = (state: RootState) => state.auth.isLoading;
export const selectAuthError = (state: RootState) => state.auth.error;

// Derived selectors
export const selectIsAuthenticated = (state: RootState): boolean =>
  Boolean(state.auth.supabaseSession);

// Memoized selector to prevent unnecessary re-renders
export const selectCurrentUser = createSelector(
  [selectSupabaseUser],
  (supabaseUser) => mapSupabaseUserToProfile(supabaseUser)
);

export const selectUserId = (state: RootState): string | undefined =>
  state.auth.supabaseUser?.id;

export default authSlice.reducer;
