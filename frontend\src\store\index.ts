import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./auth/authSlice";
import contentReducer from "./content/contentSlice";
import businessContextReducer from "./businessContext/businessContextSlice";
import aiAssistantReducer from "./aiAssistant/aiAssistantSlice";
import youtubeReducer from "./youtube/youtubeSlice";
import uiReducer from "./ui/uiSlice";
import activityReducer from "./activity/activitySlice";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    content: contentReducer,
    businessContext: businessContextReducer,
    aiAssistant: aiAssistantReducer,
    youtube: youtubeReducer,
    ui: uiReducer,
    activity: activityReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: ["auth/login/fulfilled", "auth/register/fulfilled"],
        // Ignore these field paths in all actions
        ignoredActionPaths: ["meta.arg", "payload.timestamp"],
        // Ignore these paths in the state
        ignoredPaths: ["auth.user"],
      },
    }),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
