import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";

// Define types
interface BusinessContext {
  id: string;
  offerDescription: string;
  targetAudience: string;
  brandVoice: string;
  keyBenefits: string;
  uniqueValueProposition: string;
  createdAt: string;
  updatedAt: string;
}

interface BusinessContextState {
  context: BusinessContext | null;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: BusinessContextState = {
  context: null,
  isLoading: false,
  error: null,
};

// Slice
const businessContextSlice = createSlice({
  name: "businessContext",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Add extra reducers when implementing API calls
  },
});

// Actions
export const { clearError } = businessContextSlice.actions;

// Selectors
export const selectBusinessContext = (state: RootState) =>
  state.businessContext.context;
export const selectBusinessContextLoading = (state: RootState) =>
  state.businessContext.isLoading;
export const selectBusinessContextError = (state: RootState) =>
  state.businessContext.error;

// Reducer
export default businessContextSlice.reducer;
