services:
  # PostgreSQL Database
  db:
    image: postgres:13
    volumes:
      - postgres_data:/var/lib/postgres/data/
    environment:
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend Flask API
  backend:
    image: ${DOCKER_REGISTRY}/writer-v2:${TAG:-latest}
    restart: always
    depends_on:
      db:
        condition: service_healthy
    environment:
      - FLASK_APP=app.py
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      - SECRET_KEY=${SECRET_KEY}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
      # Supabase Configuration
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    command: gunicorn --bind 0.0.0.0:5000 --workers 4 --threads 2 app:app

  # Frontend React Application
  frontend:
    image: ${DOCKER_REGISTRY}/writer-v2-frontend:${TAG:-latest}
    restart: always
    depends_on:
      - backend

  # Nginx for routing and SSL termination
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/prod.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/www:/var/www
    depends_on:
      - frontend
      - backend
    restart: always

volumes:
  postgres_data: 