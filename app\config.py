"""
FastAPI configuration settings for Writer v2.
Separated from main.py to avoid circular imports.
"""

import os
from functools import lru_cache
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Settings(BaseSettings):
    """Application settings using Pydantic BaseSettings for environment variable management."""
    
    # Security
    jwt_secret_key: str = Field(default="dev-key-for-development-only", env="JWT_SECRET_KEY")
    jwt_access_token_expires: int = Field(default=31536000, env="JWT_ACCESS_TOKEN_EXPIRES")
    
    # Database
    database_url: str = Field(default="postgresql://localhost/writer_v2", env="DATABASE_URL")
    
    # Supabase
    supabase_url: str = Field(env="SUPABASE_URL")
    supabase_key: str = Field(env="SUPABASE_KEY")
    supabase_service_role_key: str = Field(env="SUPABASE_SERVICE_ROLE_KEY")
    supabase_jwt_secret: Optional[str] = Field(default=None, env="SUPABASE_JWT_SECRET")
    
    # AI Services
    ai_provider: Optional[str] = Field(default="openrouter", env="AI_PROVIDER")
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    openrouter_api_key: Optional[str] = Field(default=None, env="OPENROUTER_API_KEY")
    ai_default_model: Optional[str] = Field(default=None, env="AI_DEFAULT_MODEL")
    
    # YouTube API
    youtube_api_key: Optional[str] = Field(default=None, env="YOUTUBE_API_KEY")
    
    # CORS
    cors_allowed_origins: str = Field(
        default="http://localhost:3000,http://localhost:5000", 
        env="CORS_ALLOWED_ORIGINS"
    )
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Caching
    cache_default_ttl: int = Field(default=300, env="CACHE_DEFAULT_TTL")
    cache_default_max_size: int = Field(default=100, env="CACHE_DEFAULT_MAX_SIZE")
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "allow"  # Allow extra fields from .env file


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings."""
    return Settings() 