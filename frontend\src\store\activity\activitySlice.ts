import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";
import {
  getUserActivities,
  logUserActivity,
  UserActivity,
  LogActivityRequest,
} from "../../services/activityService";

// Define state interface
interface ActivityState {
  activities: UserActivity[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: ActivityState = {
  activities: [],
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchUserActivities = createAsyncThunk(
  "activity/fetchUserActivities",
  async (limit: number = 10, { rejectWithValue }) => {
    try {
      const response = await getUserActivities(limit);
      return response.activities;
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to fetch activities");
    }
  }
);

export const createActivity = createAsyncThunk(
  "activity/createActivity",
  async (activity: LogActivityRequest, { rejectWithValue }) => {
    try {
      const response = await logUserActivity(activity);
      if (response.success) {
        return response.activity;
      }
      return rejectWithValue("Failed to log activity");
    } catch (error: any) {
      return rejectWithValue(error.message || "Failed to log activity");
    }
  }
);

// Slice
const activitySlice = createSlice({
  name: "activity",
  initialState,
  reducers: {
    clearActivities: (state) => {
      state.activities = [];
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch activities
    builder.addCase(fetchUserActivities.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(
      fetchUserActivities.fulfilled,
      (state, action: PayloadAction<UserActivity[]>) => {
        state.isLoading = false;
        state.activities = action.payload;
      }
    );
    builder.addCase(fetchUserActivities.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });

    // Create activity
    builder.addCase(createActivity.pending, (state) => {
      state.isLoading = true;
      state.error = null;
    });
    builder.addCase(
      createActivity.fulfilled,
      (state, action: PayloadAction<UserActivity>) => {
        state.isLoading = false;
        // Add to the beginning of the array and keep it limited to the most recent 10
        state.activities = [action.payload, ...state.activities.slice(0, 9)];
      }
    );
    builder.addCase(createActivity.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
    });
  },
});

// Actions
export const { clearActivities, clearError } = activitySlice.actions;

// Selectors
export const selectActivities = (state: RootState) => state.activity.activities;
export const selectActivityLoading = (state: RootState) =>
  state.activity.isLoading;
export const selectActivityError = (state: RootState) => state.activity.error;

// Reducer
export default activitySlice.reducer;
