import React from 'react';
import { renderHook, act } from '@testing-library/react';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { Session, User, AuthChangeEvent } from '@supabase/supabase-js';
import { AuthProvider, useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { setSupabaseSession, initialState as authInitialState } from '../store/auth/authSlice';

// Mock the supabase client methods
jest.mock('../lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
      // Mock other auth methods if needed for testing signIn, signOut etc.
    },
  },
}));

// Mock Redux store
const mockStore = configureStore([]);

// Mock data
const mockUser: User = {
  id: 'test-user-id-context',
  app_metadata: { provider: 'email' },
  user_metadata: { name: 'Test Context User' },
  aud: 'authenticated',
  created_at: new Date().toISOString(),
};
const mockSession: Session = {
  access_token: 'mock-access-token-context',
  refresh_token: 'mock-refresh-token-context',
  expires_in: 3600,
  token_type: 'bearer',
  user: mockUser,
};

// Wrapper component with Redux Provider and AuthProvider
const createWrapper = (store: any) => {
  return ({ children }: { children: React.ReactNode }) => (
    <Provider store={store}>
      <AuthProvider>{children}</AuthProvider>
    </Provider>
  );
};

describe('useAuth hook and AuthProvider', () => {
  let store: any;
  let onAuthStateChangeCallback: (event: AuthChangeEvent, session: Session | null) => void;
  let mockUnsubscribe: jest.Mock;

  beforeEach(() => {
    // Reset mocks and store before each test
    jest.clearAllMocks();
    store = mockStore({ auth: authInitialState });
    store.dispatch = jest.fn(); // Mock dispatch

    // Capture the callback and mock unsubscribe for onAuthStateChange
    mockUnsubscribe = jest.fn();
    (supabase.auth.onAuthStateChange as jest.Mock).mockImplementation((callback) => {
      onAuthStateChangeCallback = callback;
      return {
        data: {
          subscription: {
            unsubscribe: mockUnsubscribe,
          },
        },
      };
    });

    // Mock initial getSession call
    (supabase.auth.getSession as jest.Mock).mockResolvedValue({ data: { session: null }, error: null });
  });

  it('should initialize with no session and call getSession', async () => {
    const wrapper = createWrapper(store);
    const { result } = renderHook(() => useAuth(), { wrapper });

    // Wait for initial effects
    await act(async () => {
        await Promise.resolve(); // Allow promises to resolve
    });

    expect(supabase.auth.getSession).toHaveBeenCalledTimes(1);
    expect(supabase.auth.onAuthStateChange).toHaveBeenCalledTimes(1);
    expect(result.current.session).toBeNull();
    expect(result.current.user).toBeNull();
    expect(result.current.loading).toBe(false); // Should be false after init
  });

  it('should handle SIGNED_IN event from onAuthStateChange and dispatch once', async () => {
    const wrapper = createWrapper(store);
    renderHook(() => useAuth(), { wrapper });

    // Simulate initial setup finished
    await act(async () => {
        await Promise.resolve();
    });

    // Reset dispatch mock count after initial setup potentially dispatches
    (store.dispatch as jest.Mock).mockClear();

    // Simulate Supabase firing the event
    act(() => {
      onAuthStateChangeCallback('SIGNED_IN', mockSession);
    });

    const { result: updatedResult } = renderHook(() => useAuth(), { wrapper });

    expect(updatedResult.current.session).toEqual(mockSession);
    expect(updatedResult.current.user).toEqual(mockUser);
    expect(store.dispatch).toHaveBeenCalledTimes(1);
    expect(store.dispatch).toHaveBeenCalledWith(setSupabaseSession(mockSession));
  });

  it('should handle SIGNED_OUT event from onAuthStateChange and dispatch once', async () => {
    // Initial state with a session
    (supabase.auth.getSession as jest.Mock).mockResolvedValueOnce({ data: { session: mockSession }, error: null });
    store = mockStore({ auth: { ...authInitialState, supabaseSession: mockSession, supabaseUser: mockUser, isLoading: false } });
    store.dispatch = jest.fn();

    const wrapper = createWrapper(store);
    renderHook(() => useAuth(), { wrapper });

    // Simulate initial setup finished
    await act(async () => {
        await Promise.resolve();
    });
    (store.dispatch as jest.Mock).mockClear(); // Clear dispatch count from init

    // Simulate Supabase firing the event
    act(() => {
      onAuthStateChangeCallback('SIGNED_OUT', null);
    });

    const { result: updatedResult } = renderHook(() => useAuth(), { wrapper });

    expect(updatedResult.current.session).toBeNull();
    expect(updatedResult.current.user).toBeNull();
    expect(store.dispatch).toHaveBeenCalledTimes(1);
    expect(store.dispatch).toHaveBeenCalledWith(setSupabaseSession(null));
  });

  it('should unsubscribe on unmount', () => {
    const wrapper = createWrapper(store);
    const { unmount } = renderHook(() => useAuth(), { wrapper });

    unmount();

    expect(mockUnsubscribe).toHaveBeenCalledTimes(1);
  });
}); 