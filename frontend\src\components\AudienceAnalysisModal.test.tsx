import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import AudienceAnalysisModal from './AudienceAnalysisModal';
import { api } from '../services/api';

// Mock the API
jest.mock('../services/api', () => ({
  api: {
    post: jest.fn(),
  }
}));

describe('AudienceAnalysisModal', () => {
  const mockBusinessContext = {
    id: 'test-id',
    user_id: 'user-123',
    name: 'Test Profile',
    profile_overview: 'This is a test profile',
    content_focus: ['Marketing', 'Sales'],
    primary_objectives: 'Increase leads',
    offer_description: 'Our product helps businesses grow',
    target_audience: 'Small business owners',
    audience_motivation: 'Looking to scale their business',
    brand_voice: 'Professional but friendly',
    key_benefits: 'Save time, increase revenue',
    unique_value_proposition: 'All-in-one solution',
    audience_decision_style: 'Analytical',
    audience_preference_structure: 'Prefers detailed information',
    audience_decision_speed: 'Takes time to decide',
    audience_reaction_to_change: 'Open to new ideas',
    recommended_platforms: ['LinkedIn', 'Email'],
    recommended_content_formats: ['Blog posts', 'Case studies'],
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  const mockAnalysisResult = {
    summary: 'This audience is primarily motivated by solving problems and prefers structured information.',
    nhb_criteria: {
      motivation_direction: {
        value: 'Away',
        evidence: 'The audience motivation indicates they are primarily motivated by avoiding problems.',
        recommendations: ['Focus on pain points', 'Emphasize solutions to problems']
      },
      preference_structure: {
        value: 'Procedural',
        evidence: 'The audience prefers structured information as indicated by their preference structure.',
        recommendations: ['Use step-by-step guides', 'Provide clear instructions']
      }
    },
    emotional_state: {
      dominant_state: 'Helpless',
      evidence: 'The audience feels overwhelmed by their problems.',
      content_approach: 'Focus on solution-oriented content with clear steps.',
      recommendations: ['Provide actionable solutions', 'Break down complex processes']
    },
    question_strategy: {
      what: 'Use What questions to define problems clearly',
      why: 'Use Why questions sparingly',
      how: 'Emphasize How questions to provide solutions',
      who: 'Use Who questions to establish credibility',
      when: 'Use When questions to create urgency',
      where: 'Use Where questions to provide context'
    },
    content_recommendations: {
      content_types: ['How-to guides', 'Checklists', 'Case studies'],
      messaging_approach: 'Direct, solution-focused messaging',
      content_structure: 'Clear, step-by-step structure with visible progress indicators'
    }
  };

  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (api.post as jest.Mock).mockResolvedValue({
      data: {
        analysis: mockAnalysisResult,
        tokens_used: 450
      },
    });
  });

  it('renders loading state initially', () => {
    render(
      <AudienceAnalysisModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
      />
    );

    expect(screen.getByText('Analyzing your audience...')).toBeInTheDocument();
  });

  it('fetches audience analysis when opened', async () => {
    render(
      <AudienceAnalysisModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
      />
    );

    await waitFor(() => {
      expect(api.post).toHaveBeenCalledWith(
        `/business/${mockBusinessContext.id}/analyze-audience`
      );
    });
  });

  it('displays analysis results after loading', async () => {
    render(
      <AudienceAnalysisModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Audience Analysis Summary')).toBeInTheDocument();
      expect(screen.getByText('Emotional State Analysis')).toBeInTheDocument();
      expect(screen.getByText('Audience Characteristics (NHB+ Framework)')).toBeInTheDocument();
      expect(screen.getByText('Question Strategy')).toBeInTheDocument();
      expect(screen.getByText('Content Strategy Recommendations')).toBeInTheDocument();
      
      // Check for specific content from the mock data
      expect(screen.getByText('Dominant State:')).toBeInTheDocument();
      expect(screen.getByText('Helpless')).toBeInTheDocument();
      expect(screen.getByText('Motivation Direction:')).toBeInTheDocument();
      expect(screen.getByText('Away')).toBeInTheDocument();
    });
  });

  it('closes the modal when Close button is clicked', async () => {
    render(
      <AudienceAnalysisModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Close')).toBeInTheDocument();
    });

    screen.getByText('Close').click();
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles API errors gracefully', async () => {
    (api.post as jest.Mock).mockRejectedValue({
      response: { data: { error: 'API error' } },
    });

    render(
      <AudienceAnalysisModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('API error')).toBeInTheDocument();
    });
  });
});
