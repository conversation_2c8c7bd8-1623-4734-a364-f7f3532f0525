import React, { useState } from "react";
import {
  Box,
  IconButton,
  TextField,
  Button,
  CircularProgress,
  Typography,
  Tooltip,
} from "@mui/material";
import {
  ThumbUpAlt as ThumbUpIcon,
  ThumbDownAlt as ThumbDownIcon,
  ThumbUpOffAlt as ThumbUpOffIcon,
  ThumbDownOffAlt as ThumbDownOffIcon,
} from "@mui/icons-material";
import { submitFeedback, FeedbackPayload } from "../services/api";

interface Props {
  sessionId: string;
  messageId: string;
  setNotification: (notification: {
    open: boolean;
    message: string;
    severity: "success" | "info" | "warning" | "error";
  }) => void;
}

const ScriptFeedbackWidget: React.FC<Props> = ({
  sessionId,
  messageId,
  setNotification,
}) => {
  const [rating, setRating] = useState<"up" | "down" | null>(null);
  const [comment, setComment] = useState("");
  const [showCommentInput, setShowCommentInput] = useState(false);
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleRating = (newRating: "up" | "down") => {
    if (submitted) return; // Don't allow changes after submission
    setRating(newRating);
    setShowCommentInput(true); // Show comment input immediately on rating
  };

  const handleSubmit = async () => {
    if (!rating || loading || submitted) return;

    setLoading(true);
    const payload: FeedbackPayload = {
      session_id: sessionId,
      message_id: messageId,
      feedback_type: "script_usability",
      rating: rating,
      comment: comment || undefined,
    };

    try {
      await submitFeedback(payload);
      setNotification({
        open: true,
        message: "Feedback submitted successfully!",
        severity: "success",
      });
      setSubmitted(true); // Mark as submitted to prevent re-submission
      setShowCommentInput(false); // Optionally hide input after success
    } catch (error: any) {
      console.error("Feedback submission error:", error);
      setNotification({
        open: true,
        message:
          error?.response?.data?.error ||
          error?.message ||
          "Failed to submit feedback. Please try again.",
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        mt: 1,
        p: 1,
        borderTop: (theme) => `1px solid ${theme.palette.divider}`,
        display: "flex",
        flexDirection: "column",
        alignItems: "flex-start",
        gap: 1,
      }}
    >
      {!submitted ? (
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <Typography variant="caption" sx={{ mr: 1 }}>
            Rate this script:
          </Typography>
          <Tooltip title="Good script">
            <IconButton
              size="small"
              onClick={() => handleRating("up")}
              color={rating === "up" ? "success" : "default"}
            >
              {rating === "up" ? (
                <ThumbUpIcon fontSize="small" />
              ) : (
                <ThumbUpOffIcon fontSize="small" />
              )}
            </IconButton>
          </Tooltip>
          <Tooltip title="Bad script">
            <IconButton
              size="small"
              onClick={() => handleRating("down")}
              color={rating === "down" ? "error" : "default"}
            >
              {rating === "down" ? (
                <ThumbDownIcon fontSize="small" />
              ) : (
                <ThumbDownOffIcon fontSize="small" />
              )}
            </IconButton>
          </Tooltip>
        </Box>
      ) : (
        <Typography variant="caption" color="text.secondary">
          Feedback submitted. Thank you!
        </Typography>
      )}

      {showCommentInput && !submitted && (
        <Box sx={{ width: "100%" }}>
          <TextField
            fullWidth
            multiline
            minRows={2}
            maxRows={4}
            size="small"
            placeholder="Tell us more (optional)"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            variant="outlined"
            sx={{ mb: 1 }}
            InputProps={{ sx: { fontSize: "0.875rem" } }}
          />
          <Button
            variant="contained"
            size="small"
            onClick={handleSubmit}
            disabled={loading || !rating} // Disable if loading or no rating selected
            startIcon={
              loading ? <CircularProgress size={16} color="inherit" /> : null
            }
          >
            {loading ? "Submitting..." : "Submit Feedback"}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default ScriptFeedbackWidget;
