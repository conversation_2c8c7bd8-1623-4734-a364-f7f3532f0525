@echo off
echo ===================================================
echo Supabase Repository Tests Runner
echo ===================================================
echo.

REM Create results directory if it doesn't exist
if not exist "test_results" mkdir test_results

REM Get timestamp for results folder
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,8%-%dt:~8,6%"
set "results_dir=test_results\repo_tests_%timestamp%"
mkdir "%results_dir%"

echo Test results will be saved to: %results_dir%
echo.

REM Check if Supabase environment variables are set
if "%SUPABASE_URL%"=="" (
    echo WARNING: SUPABASE_URL environment variable is not set.
    echo Tests will be skipped unless properly configured.
    echo.
)

if "%SUPABASE_KEY%"=="" (
    echo WARNING: SUPABASE_KEY environment variable is not set.
    echo Tests will be skipped unless properly configured.
    echo.
)

echo Running Supabase repository tests...
echo.

REM Run individual repository tests
echo Running ActivityLogRepository tests...
pytest tests/supabase/test_activity_log_repository.py -v > "%results_dir%\activity_log_tests.log" 2>&1
if %errorlevel% neq 0 (
    echo [FAILED] ActivityLogRepository tests
) else (
    echo [PASSED] ActivityLogRepository tests
)

echo Running ContentRepository tests...
pytest tests/supabase/test_content_repository.py -v > "%results_dir%\content_tests.log" 2>&1
if %errorlevel% neq 0 (
    echo [FAILED] ContentRepository tests
) else (
    echo [PASSED] ContentRepository tests
)

echo Running BusinessContextRepository tests...
pytest tests/supabase/test_business_context_repository.py -v > "%results_dir%\business_context_tests.log" 2>&1
if %errorlevel% neq 0 (
    echo [FAILED] BusinessContextRepository tests
) else (
    echo [PASSED] BusinessContextRepository tests
)

echo Running ChatSessionRepository tests...
pytest tests/supabase/test_chat_session_repository.py -v > "%results_dir%\chat_session_tests.log" 2>&1
if %errorlevel% neq 0 (
    echo [FAILED] ChatSessionRepository tests
) else (
    echo [PASSED] ChatSessionRepository tests
)

echo Running YouTubeVideoRepository tests...
pytest tests/supabase/test_youtube_video_repository.py -v > "%results_dir%\youtube_video_tests.log" 2>&1
if %errorlevel% neq 0 (
    echo [FAILED] YouTubeVideoRepository tests
) else (
    echo [PASSED] YouTubeVideoRepository tests
)

echo Running FeedbackRepository tests...
pytest tests/supabase/test_feedback_repository.py -v > "%results_dir%\feedback_tests.log" 2>&1
if %errorlevel% neq 0 (
    echo [FAILED] FeedbackRepository tests
) else (
    echo [PASSED] FeedbackRepository tests
)

echo Running Repository Integration tests...
pytest tests/supabase/test_repository_integration.py -v > "%results_dir%\integration_tests.log" 2>&1
if %errorlevel% neq 0 (
    echo [FAILED] Repository Integration tests
) else (
    echo [PASSED] Repository Integration tests
)

echo.
echo ===================================================
echo Test Summary
echo ===================================================

REM Count the number of test files that passed/failed
set /a total_tests=0
set /a passed_tests=0

REM Check each log file for test results
for %%f in ("%results_dir%\*.log") do (
    set /a total_tests+=1
    findstr /C:"FAILED" "%%f" >nul
    if %errorlevel% neq 0 (
        set /a passed_tests+=1
    )
)

echo Total test files: %total_tests%
echo Passed test files: %passed_tests%
echo Failed test files: %total_tests% - %passed_tests%

REM Generate summary file
echo Test Summary > "%results_dir%\summary.txt"
echo =========== >> "%results_dir%\summary.txt"
echo Date: %date% Time: %time% >> "%results_dir%\summary.txt"
echo. >> "%results_dir%\summary.txt"
echo Total test files: %total_tests% >> "%results_dir%\summary.txt"
echo Passed test files: %passed_tests% >> "%results_dir%\summary.txt"
echo Failed test files: %total_tests% - %passed_tests% >> "%results_dir%\summary.txt"
echo. >> "%results_dir%\summary.txt"

REM Check individual test logs for more details
echo Detailed Test Results: >> "%results_dir%\summary.txt"
echo ===================== >> "%results_dir%\summary.txt"

for %%f in ("%results_dir%\*.log") do (
    echo. >> "%results_dir%\summary.txt"
    echo File: %%~nf >> "%results_dir%\summary.txt"
    findstr /C:"collected" "%%f" >> "%results_dir%\summary.txt"
    findstr /C:"FAILURES" "%%f" >> "%results_dir%\summary.txt"
)

echo.
echo Detailed logs saved to: %results_dir%
echo Summary report saved to: %results_dir%\summary.txt
echo.

if %passed_tests% equ %total_tests% (
    echo All repository tests PASSED!
    exit /b 0
) else (
    echo Some repository tests FAILED. Check logs for details.
    exit /b 1
) 