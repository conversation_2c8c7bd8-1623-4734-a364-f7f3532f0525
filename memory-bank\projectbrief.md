## Writer v2 Project Brief

**1. Project Vision & Goals:**

*   **Vision:** Create an intelligent content assistant (Writer v2) that empowers users (content creators, marketers) to produce high-quality, tailored content efficiently.
*   **Core Goal:** Integrate AI capabilities with user-specific context (business details, content examples) and external data sources (like YouTube) to generate relevant and effective content.
*   **Key Objectives:**
    *   Provide a seamless user experience for managing content, business context, and AI interactions.
    *   Enable powerful AI content generation features (chat, formatting, analysis).
    *   Integrate YouTube search, data retrieval (metadata, transcripts), and analysis (outlier scoring) directly into the workflow.
    *   Build a robust and maintainable application using modern web technologies (Flask, React, Supabase).
    *   Ensure secure authentication and data handling.

**2. Scope & Key Features (Based on Existing Context):**

*   **User Management:** Secure user registration and login (Supabase Auth).
*   **Content Library:** CRUD operations for user-uploaded content items (text, potentially YouTube video data).
*   **Business Context Management:** CRUD operations for user-defined business details (brand voice, audience, offers).
*   **AI Assistant:**
    *   Conversational chat interface.
    *   Content generation leveraging user prompt, business context, and content library items.
    *   Integration with AI providers (currently OpenRouter, potentially OpenAI).
    *   Ability to process and format text (e.g., YouTube transcripts).
    *   Enhanced message interaction: Copy-to-clipboard, structured JSON display, feedback submission (👍/👎).
*   **YouTube Integration:**
    *   Search for videos (keyword/URL).
    *   Fetch video metadata and statistics.
    *   Retrieve video transcripts.
    *   Calculate and display a video "outlier score" based on performance metrics.
    *   Save YouTube video information (metadata, transcript) to the Content Library.
*   **Feedback Mechanism:** 
    *   Allow users to submit general feedback.
    *   Provide specific in-message feedback for AI Assistant responses.
*   **Activity Tracking:** Log user actions within the platform.
*   **Dashboard:** Provide an overview or central hub for users (details TBC).

**3. Target Audience:**

*   Content Creators
*   Marketers
*   Businesses requiring consistent content generation.

**4. High-Level Architecture:**

*   **Frontend:** React (TypeScript) single-page application with Redux state management and MUI for UI components.
*   **Backend:** Flask (Python) API providing RESTful endpoints.
*   **Backend-as-a-Service (BaaS):** Supabase for Authentication and PostgreSQL Database.
*   **Deployment:** Dockerized environment using `docker-compose`.

**5. Current Status (Summary from `progress.md`):**

*   Core application structure (Flask/React/Supabase) is established.
*   Major backend refactoring to use Supabase exclusively (replacing potential SQLAlchemy use) is largely complete.
*   Key features (Auth, Content/Business Context CRUD, AI Chat, YouTube Search/Transcript/Analysis) have foundational backend and frontend implementations.
*   Docker setup for local development exists.
*   Ongoing work focuses on refining AI/YouTube integration, frontend state consistency, UI for YouTube analysis, error handling, and performance.

**6. Non-Goals (Assumed):**

*   Real-time collaboration features.
*   Direct video editing capabilities.
*   Complex project management features beyond content organization. 