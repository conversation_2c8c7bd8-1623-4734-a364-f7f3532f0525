{"mcpServers": {"Sequential Thinking": {"command": "C:\\Users\\<USER>\\AppData\\Local\\fnm_multishells\\12784_1743600085389\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@modelcontextprotocol\\server-sequential-thinking\\dist\\index.js", "type": "stdio"}, "Playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "supabase": {"command": "cmd", "type": "stdio", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-postgres", "************************************************************/postgres"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "type": "stdio"}, "mcp-installer": {"command": "npx", "args": ["@anaisbetts/mcp-installer"]}, "folder_structure_mcp": {"command": "node", "args": ["C:\\\\MCP\\\\folder_structure_mcp\\\\dist\\\\index.js"], "type": "stdio"}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "--", "F:/Valentin Marketing/Writer v2"], "type": "stdio"}}}