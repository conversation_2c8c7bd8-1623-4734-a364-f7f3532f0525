#!/usr/bin/env python
"""
Sample Data Migration Test Script

This script generates sample data in PostgreSQL, runs the migration to Supabase,
and validates that the data has been correctly migrated.
"""

import os
import sys
import time
import json
import random
import string
import argparse
import uuid
import logging
from datetime import datetime, timedelta
import subprocess
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Check for required packages
try:
    import psycopg2
    import psycopg2.extras
    import requests
    from dotenv import load_dotenv
except ImportError:
    print("Error: Required packages not found.")
    print(
        "Please install the required packages with: pip install psycopg2-binary requests python-dotenv"
    )
    sys.exit(1)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)


class SampleDataGenerator:
    """Generates sample data for testing the migration process."""

    def __init__(
        self,
        pg_host: str = "localhost",
        pg_port: int = 5432,
        pg_database: str = None,
        pg_user: str = "postgres",
        pg_password: str = None,
        sample_size: int = 10,
    ):
        """Initialize the data generator with database connection parameters."""
        self.pg_host = pg_host
        self.pg_port = pg_port
        self.pg_database = pg_database or os.getenv("PG_DATABASE")
        self.pg_user = pg_user
        self.pg_password = pg_password or os.getenv("PG_PASSWORD")
        self.sample_size = sample_size
        self.conn = None
        self.generated_data = {}

        if not self.pg_database:
            raise ValueError("PostgreSQL database name must be provided")
        if not self.pg_password:
            raise ValueError("PostgreSQL password must be provided")

    def connect(self) -> None:
        """Connect to the PostgreSQL database."""
        try:
            logger.info(f"Connecting to PostgreSQL database {self.pg_database}...")
            self.conn = psycopg2.connect(
                host=self.pg_host,
                port=self.pg_port,
                dbname=self.pg_database,
                user=self.pg_user,
                password=self.pg_password,
            )
            self.conn.autocommit = True
            logger.info("Connected successfully to PostgreSQL")
        except Exception as e:
            logger.error(f"Failed to connect to PostgreSQL: {e}")
            raise

    def disconnect(self) -> None:
        """Close the database connection."""
        if self.conn:
            self.conn.close()
            logger.info("Disconnected from PostgreSQL")

    def _random_string(self, length: int = 10) -> str:
        """Generate a random string of characters."""
        return "".join(random.choices(string.ascii_letters + string.digits, k=length))

    def _random_date(
        self, start_date: datetime = None, end_date: datetime = None
    ) -> datetime:
        """Generate a random date between start_date and end_date."""
        if not start_date:
            start_date = datetime.now() - timedelta(days=365)
        if not end_date:
            end_date = datetime.now()

        time_between_dates = end_date - start_date
        days_between_dates = time_between_dates.days
        random_days = random.randrange(days_between_dates)
        return start_date + timedelta(days=random_days)

    def generate_users(self) -> List[Dict[str, Any]]:
        """Generate sample user data."""
        logger.info(f"Generating {self.sample_size} sample users...")
        users = []

        with self.conn.cursor() as cursor:
            for i in range(self.sample_size):
                user_id = str(uuid.uuid4())
                email = f"test_user_{i}_{int(time.time())}@example.com"
                created_at = self._random_date()

                # Insert user into database
                cursor.execute(
                    """
                    INSERT INTO users (id, email, created_at, updated_at)
                    VALUES (%s, %s, %s, %s)
                    RETURNING id, email, created_at, updated_at
                    """,
                    (user_id, email, created_at, created_at),
                )

                user = cursor.fetchone()
                users.append(
                    {
                        "id": user[0],
                        "email": user[1],
                        "created_at": user[2],
                        "updated_at": user[3],
                    }
                )

        logger.info(f"Generated {len(users)} users")
        self.generated_data["users"] = users
        return users

    def generate_business_contexts(self) -> List[Dict[str, Any]]:
        """Generate sample business context data."""
        logger.info(f"Generating {self.sample_size} sample business contexts...")
        business_contexts = []

        with self.conn.cursor() as cursor:
            for i in range(self.sample_size):
                context_id = str(uuid.uuid4())
                user_id = (
                    random.choice(self.generated_data["users"])["id"]
                    if self.generated_data.get("users")
                    else str(uuid.uuid4())
                )
                offer_description = f"Sample offer description {i}"
                target_audience = f"Target audience for context {i}"
                brand_voice = f"Professional brand voice {i}"
                key_benefits = f"Key benefits for context {i}"
                unique_value_proposition = f"Unique value proposition {i}"
                created_at = self._random_date()

                # Insert business context into database
                cursor.execute(
                    """
                    INSERT INTO business_contexts (id, user_id, offer_description, target_audience, brand_voice, key_benefits, unique_value_proposition, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id, user_id, offer_description, target_audience, brand_voice, key_benefits, unique_value_proposition, created_at, updated_at
                    """,
                    (
                        context_id,
                        user_id,
                        offer_description,
                        target_audience,
                        brand_voice,
                        key_benefits,
                        unique_value_proposition,
                        created_at,
                        created_at,
                    ),
                )

                context = cursor.fetchone()
                business_contexts.append(
                    {
                        "id": context[0],
                        "user_id": context[1],
                        "offer_description": context[2],
                        "target_audience": context[3],
                        "brand_voice": context[4],
                        "key_benefits": context[5],
                        "unique_value_proposition": context[6],
                        "created_at": context[7],
                        "updated_at": context[8],
                    }
                )

        logger.info(f"Generated {len(business_contexts)} business contexts")
        self.generated_data["business_contexts"] = business_contexts
        return business_contexts

    def generate_content_items(self) -> List[Dict[str, Any]]:
        """Generate sample content items."""
        logger.info(f"Generating {self.sample_size * 2} sample content items...")
        content_items = []

        with self.conn.cursor() as cursor:
            for i in range(self.sample_size * 2):
                content_id = str(uuid.uuid4())
                user_id = (
                    random.choice(self.generated_data["users"])["id"]
                    if self.generated_data.get("users")
                    else str(uuid.uuid4())
                )
                title = f"Content Item {i}"
                content = f"Sample content for item {i}. This is test data for migration validation."
                content_type = random.choice(["article", "blog", "note"])
                created_at = self._random_date()

                # Insert content item into database
                cursor.execute(
                    """
                    INSERT INTO content_items (id, user_id, title, content, content_type, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING id, user_id, title, content, content_type, created_at, updated_at
                    """,
                    (
                        content_id,
                        user_id,
                        title,
                        content,
                        content_type,
                        created_at,
                        created_at,
                    ),
                )

                item = cursor.fetchone()
                content_items.append(
                    {
                        "id": item[0],
                        "user_id": item[1],
                        "title": item[2],
                        "content": item[3],
                        "content_type": item[4],
                        "created_at": item[5],
                        "updated_at": item[6],
                    }
                )

        logger.info(f"Generated {len(content_items)} content items")
        self.generated_data["content_items"] = content_items
        return content_items

    def generate_chat_sessions(self) -> List[Dict[str, Any]]:
        """Generate sample chat sessions."""
        logger.info(f"Generating {self.sample_size} sample chat sessions...")
        chat_sessions = []

        with self.conn.cursor() as cursor:
            for i in range(self.sample_size):
                session_id = str(uuid.uuid4())
                user_id = (
                    random.choice(self.generated_data["users"])["id"]
                    if self.generated_data.get("users")
                    else str(uuid.uuid4())
                )
                title = f"Chat Session {i}"
                created_at = self._random_date()

                # Insert chat session into database
                cursor.execute(
                    """
                    INSERT INTO chat_sessions (id, user_id, title, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING id, user_id, title, created_at, updated_at
                    """,
                    (session_id, user_id, title, created_at, created_at),
                )

                session = cursor.fetchone()
                chat_sessions.append(
                    {
                        "id": session[0],
                        "user_id": session[1],
                        "title": session[2],
                        "created_at": session[3],
                        "updated_at": session[4],
                    }
                )

        logger.info(f"Generated {len(chat_sessions)} chat sessions")
        self.generated_data["chat_sessions"] = chat_sessions
        return chat_sessions

    def generate_messages(self) -> List[Dict[str, Any]]:
        """Generate sample messages for chat sessions."""
        logger.info("Generating messages for sample chat sessions...")
        messages = []

        with self.conn.cursor() as cursor:
            for session in self.generated_data.get("chat_sessions", []):
                # Generate 3-10 messages per session
                message_count = random.randint(3, 10)

                for i in range(message_count):
                    message_id = str(uuid.uuid4())
                    session_id = session["id"]
                    content = f"Sample message {i} for session {session_id[:8]}"
                    role = random.choice(["user", "assistant"])
                    created_at = self._random_date(
                        start_date=session["created_at"],
                        end_date=session["created_at"] + timedelta(days=7),
                    )

                    # Insert message into database
                    cursor.execute(
                        """
                        INSERT INTO messages (id, session_id, content, role, created_at)
                        VALUES (%s, %s, %s, %s, %s)
                        RETURNING id, session_id, content, role, created_at
                        """,
                        (message_id, session_id, content, role, created_at),
                    )

                    message = cursor.fetchone()
                    messages.append(
                        {
                            "id": message[0],
                            "session_id": message[1],
                            "content": message[2],
                            "role": message[3],
                            "created_at": message[4],
                        }
                    )

        logger.info(f"Generated {len(messages)} messages")
        self.generated_data["messages"] = messages
        return messages

    def generate_activity_logs(self) -> List[Dict[str, Any]]:
        """Generate sample activity logs."""
        logger.info(f"Generating {self.sample_size * 3} sample activity logs...")
        activity_logs = []

        with self.conn.cursor() as cursor:
            for i in range(self.sample_size * 3):
                log_id = str(uuid.uuid4())
                user_id = (
                    random.choice(self.generated_data["users"])["id"]
                    if self.generated_data.get("users")
                    else str(uuid.uuid4())
                )
                activity_type = random.choice(
                    [
                        "login",
                        "content_create",
                        "content_update",
                        "chat_create",
                        "feedback_submit",
                    ]
                )  # Renamed from action
                title = f"Sample {activity_type} Activity"  # Added title
                description = json.dumps(
                    {
                        "page": random.choice(
                            ["/home", "/content", "/chat", "/feedback"]
                        ),
                        "result": "success",
                    }
                )  # Use details as description string
                created_at = self._random_date()

                # Insert activity log into database
                cursor.execute(
                    """
                    INSERT INTO user_activities (id, user_id, activity_type, title, description, created_at) -- Use correct table and columns
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING id, user_id, activity_type, title, description, created_at -- Return correct columns
                    """,
                    (
                        log_id,
                        user_id,
                        activity_type,
                        title,
                        description,
                        created_at,
                    ),  # Pass correct values
                )

                log = cursor.fetchone()
                activity_logs.append(
                    {
                        "id": log[0],
                        "user_id": log[1],
                        "activity_type": log[2],  # Use activity_type
                        "title": log[3],  # Add title
                        "description": log[4],  # Use description
                        "created_at": log[4],
                    }
                )

        logger.info(f"Generated {len(activity_logs)} activity logs")
        self.generated_data["user_activities"] = activity_logs  # Use correct key
        return activity_logs

    def generate_feedback(self) -> List[Dict[str, Any]]:
        """Generate sample feedback data."""
        logger.info(f"Generating {self.sample_size} sample feedback entries...")
        feedback_entries = []

        with self.conn.cursor() as cursor:
            for i in range(self.sample_size):
                feedback_id = str(uuid.uuid4())
                user_id = (
                    random.choice(self.generated_data["users"])["id"]
                    if self.generated_data.get("users")
                    else str(uuid.uuid4())
                )
                rating = random.randint(1, 5)
                comments = (
                    f"Sample feedback comments {i}. This is test data for migration."
                )
                created_at = self._random_date()

                # Insert feedback into database
                cursor.execute(
                    """
                    INSERT INTO feedback (id, user_id, rating, comments, created_at)
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING id, user_id, rating, comments, created_at
                    """,
                    (feedback_id, user_id, rating, comments, created_at),
                )

                feedback = cursor.fetchone()
                feedback_entries.append(
                    {
                        "id": feedback[0],
                        "user_id": feedback[1],
                        "rating": feedback[2],
                        "comments": feedback[3],
                        "created_at": feedback[4],
                    }
                )

        logger.info(f"Generated {len(feedback_entries)} feedback entries")
        self.generated_data["feedback"] = feedback_entries
        return feedback_entries

    def generate_youtube_videos(self) -> List[Dict[str, Any]]:
        """Generate sample YouTube video data."""
        logger.info(f"Generating {self.sample_size} sample YouTube videos...")
        youtube_videos = []

        with self.conn.cursor() as cursor:
            for i in range(self.sample_size):
                video_id = str(uuid.uuid4())
                user_id = (
                    random.choice(self.generated_data["users"])["id"]
                    if self.generated_data.get("users")
                    else str(uuid.uuid4())
                )
                youtube_id = f"yt{self._random_string(11)}"
                title = f"Sample YouTube Video {i}"
                description = f"Description for sample YouTube video {i}"
                created_at = self._random_date()

                # Insert YouTube video into database
                cursor.execute(
                    """
                    INSERT INTO youtube_videos (id, user_id, youtube_id, title, description, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    RETURNING id, user_id, youtube_id, title, description, created_at, updated_at
                    """,
                    (
                        video_id,
                        user_id,
                        youtube_id,
                        title,
                        description,
                        created_at,
                        created_at,
                    ),
                )

                video = cursor.fetchone()
                youtube_videos.append(
                    {
                        "id": video[0],
                        "user_id": video[1],
                        "youtube_id": video[2],
                        "title": video[3],
                        "description": video[4],
                        "created_at": video[5],
                        "updated_at": video[6],
                    }
                )

        logger.info(f"Generated {len(youtube_videos)} YouTube videos")
        self.generated_data["youtube_videos"] = youtube_videos
        return youtube_videos

    def generate_all(self) -> Dict[str, List[Dict[str, Any]]]:
        """Generate all sample data types."""
        logger.info("Generating all sample data types...")

        self.connect()

        try:
            self.generate_users()
            self.generate_business_contexts()
            self.generate_content_items()
            self.generate_chat_sessions()
            self.generate_messages()
            # self.generate_activity_logs() # Commented out
            self.generate_feedback()
            self.generate_youtube_videos()

            logger.info("All sample data generated successfully")

            # Print summary of generated data
            logger.info("=== Generated Data Summary ===")
            for data_type, data_list in self.generated_data.items():
                logger.info(f"{data_type}: {len(data_list)} records")

            return self.generated_data
        finally:
            self.disconnect()

    def cleanup_generated_data(self) -> None:
        """Clean up the generated sample data from the database."""
        logger.info("Cleaning up generated sample data...")

        self.connect()

        try:
            with self.conn.cursor() as cursor:
                # Delete in reverse order of dependencies
                if self.generated_data.get("messages"):
                    message_ids = [m["id"] for m in self.generated_data["messages"]]
                    placeholders = ",".join(["%s"] * len(message_ids))
                    cursor.execute(
                        f"DELETE FROM messages WHERE id IN ({placeholders})",
                        message_ids,
                    )
                    logger.info(f"Deleted {cursor.rowcount} messages")

                if self.generated_data.get("youtube_videos"):
                    video_ids = [v["id"] for v in self.generated_data["youtube_videos"]]
                    placeholders = ",".join(["%s"] * len(video_ids))
                    cursor.execute(
                        f"DELETE FROM youtube_videos WHERE id IN ({placeholders})",
                        video_ids,
                    )
                    logger.info(f"Deleted {cursor.rowcount} YouTube videos")

                if self.generated_data.get("feedback"):
                    feedback_ids = [f["id"] for f in self.generated_data["feedback"]]
                    placeholders = ",".join(["%s"] * len(feedback_ids))
                    cursor.execute(
                        f"DELETE FROM feedback WHERE id IN ({placeholders})",
                        feedback_ids,
                    )
                    logger.info(f"Deleted {cursor.rowcount} feedback entries")

                # if self.generated_data.get('user_activities'): # Commented out
                #     log_ids = [l['id'] for l in self.generated_data['user_activities']] # Use correct key
                #     placeholders = ','.join(['%s'] * len(log_ids))
                #     cursor.execute(f"DELETE FROM user_activities WHERE id IN ({placeholders})", log_ids) # Use correct table name
                #     logger.info(f"Deleted {cursor.rowcount} activity logs")

                if self.generated_data.get("chat_sessions"):
                    session_ids = [
                        s["id"] for s in self.generated_data["chat_sessions"]
                    ]
                    placeholders = ",".join(["%s"] * len(session_ids))
                    cursor.execute(
                        f"DELETE FROM chat_sessions WHERE id IN ({placeholders})",
                        session_ids,
                    )
                    logger.info(f"Deleted {cursor.rowcount} chat sessions")

                if self.generated_data.get("content_items"):
                    content_ids = [
                        c["id"] for c in self.generated_data["content_items"]
                    ]
                    placeholders = ",".join(["%s"] * len(content_ids))
                    cursor.execute(
                        f"DELETE FROM content_items WHERE id IN ({placeholders})",
                        content_ids,
                    )
                    logger.info(f"Deleted {cursor.rowcount} content items")

                if self.generated_data.get("business_contexts"):
                    context_ids = [
                        b["id"] for b in self.generated_data["business_contexts"]
                    ]
                    placeholders = ",".join(["%s"] * len(context_ids))
                    cursor.execute(
                        f"DELETE FROM business_contexts WHERE id IN ({placeholders})",
                        context_ids,
                    )
                    logger.info(f"Deleted {cursor.rowcount} business contexts")

                if self.generated_data.get("users"):
                    user_ids = [u["id"] for u in self.generated_data["users"]]
                    placeholders = ",".join(["%s"] * len(user_ids))
                    cursor.execute(
                        f"DELETE FROM users WHERE id IN ({placeholders})", user_ids
                    )
                    logger.info(f"Deleted {cursor.rowcount} users")

            logger.info("Sample data cleanup completed")
        finally:
            self.disconnect()


class MigrationTester:
    """Runs the complete migration test process."""

    def __init__(
        self,
        pg_host: str = "localhost",
        pg_port: int = 5432,
        pg_database: str = None,
        pg_user: str = "postgres",
        pg_password: str = None,
        supabase_url: str = None,
        supabase_key: str = None,
        sample_size: int = 10,
        cleanup: bool = True,
    ):
        """Initialize the migration tester with database connection parameters."""
        self.pg_host = pg_host
        self.pg_port = pg_port
        self.pg_database = pg_database or os.getenv("PG_DATABASE")
        self.pg_user = pg_user
        self.pg_password = pg_password or os.getenv("PG_PASSWORD")
        self.supabase_url = supabase_url or os.getenv("SUPABASE_URL")
        self.supabase_key = supabase_key or os.getenv("SUPABASE_KEY")
        self.sample_size = sample_size
        self.cleanup = cleanup
        self.data_generator = None
        self.generated_data = {}

        if not self.pg_database:
            raise ValueError("PostgreSQL database name must be provided")
        if not self.pg_password:
            raise ValueError("PostgreSQL password must be provided")
        if not self.supabase_url:
            raise ValueError("Supabase URL must be provided")
        if not self.supabase_key:
            raise ValueError("Supabase API key must be provided")

    def run_test(self) -> bool:
        """Run the complete migration test process."""
        try:
            logger.info("Starting sample data migration test")

            # Step 1: Generate sample data
            self._generate_sample_data()

            # Step 2: Run migration
            self._run_migration()

            # Step 3: Validate migrated data
            validation_result = self._validate_migrated_data()

            # Step 4: Clean up if requested
            if self.cleanup:
                self._cleanup_data()

            return validation_result
        except Exception as e:
            logger.error(f"Migration test failed: {e}")
            if self.cleanup:
                self._cleanup_data()
            return False

    def _generate_sample_data(self) -> None:
        """Generate sample data in PostgreSQL."""
        logger.info("Step 1: Generating sample data in PostgreSQL")

        self.data_generator = SampleDataGenerator(
            pg_host=self.pg_host,
            pg_port=self.pg_port,
            pg_database=self.pg_database,
            pg_user=self.pg_user,
            pg_password=self.pg_password,
            sample_size=self.sample_size,
        )

        self.generated_data = self.data_generator.generate_all()

        logger.info("Sample data generation completed")

    def _run_migration(self) -> None:
        """Run the migration from PostgreSQL to Supabase."""
        logger.info("Step 2: Running migration from PostgreSQL to Supabase")

        # Check if migration script exists
        migration_script = "scripts/migrate_to_supabase.py"
        if not os.path.exists(migration_script):
            migration_script = "../scripts/migrate_to_supabase.py"
            if not os.path.exists(migration_script):
                raise FileNotFoundError(
                    "Migration script 'migrate_to_supabase.py' not found"
                )

        # Run migration script
        try:
            logger.info(f"Executing migration script: {migration_script}")
            result = subprocess.run(
                [
                    "python",
                    migration_script,
                    "--pg-host",
                    self.pg_host,
                    "--pg-port",
                    str(self.pg_port),
                    "--pg-database",
                    self.pg_database,
                    "--pg-user",
                    self.pg_user,
                    "--pg-password",
                    self.pg_password,
                    "--supabase-url",
                    self.supabase_url,
                    "--supabase-key",
                    self.supabase_key,
                    "--dry-run",
                    "false",
                ],
                capture_output=True,
                text=True,
                check=True,
            )

            logger.info("Migration script output:")
            for line in result.stdout.splitlines():
                logger.info(line)

            if result.stderr:
                logger.warning("Migration script errors/warnings:")
                for line in result.stderr.splitlines():
                    logger.warning(line)

            logger.info("Migration completed successfully")
        except subprocess.CalledProcessError as e:
            logger.error(f"Migration script failed with exit code {e.returncode}")
            logger.error(f"Output: {e.stdout}")
            logger.error(f"Error: {e.stderr}")
            raise RuntimeError(f"Migration script failed: {e.stderr}")

    def _validate_migrated_data(self) -> bool:
        """Validate that the data has been correctly migrated to Supabase."""
        logger.info("Step 3: Validating migrated data in Supabase")

        validation_results = {}
        all_passed = True

        # Validate each data type
        for data_type, data_list in self.generated_data.items():
            if not data_list:
                continue

            table_name = data_type
            logger.info(
                f"Validating {len(data_list)} records in the {table_name} table..."
            )

            # Check that all records exist in Supabase
            validation_results[table_name] = self._validate_table(table_name, data_list)

            if validation_results[table_name]["missing_count"] > 0:
                all_passed = False
                logger.error(
                    f"Validation failed for {table_name}: {validation_results[table_name]['missing_count']} records missing"
                )
            else:
                logger.info(
                    f"Validation passed for {table_name}: All {len(data_list)} records found in Supabase"
                )

        # Print validation summary
        logger.info("=== Validation Summary ===")
        for table_name, result in validation_results.items():
            total = result["total_count"]
            found = result["found_count"]
            missing = result["missing_count"]
            logger.info(
                f"{table_name}: {found}/{total} records found, {missing} missing"
            )

        if all_passed:
            logger.info("All data has been successfully migrated to Supabase")
        else:
            logger.error("Some data is missing in Supabase after migration")

        return all_passed

    def _validate_table(
        self, table_name: str, records: List[Dict[str, Any]]
    ) -> Dict[str, int]:
        """Validate that all records exist in the specified Supabase table."""
        total_count = len(records)
        found_count = 0
        missing_ids = []

        # Set up Supabase API request headers
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}",
            "Content-Type": "application/json",
        }

        # Check each record
        for record in records:
            record_id = record["id"]
            url = f"{self.supabase_url}/rest/v1/{table_name}?id=eq.{record_id}"

            try:
                response = requests.get(url, headers=headers)
                response.raise_for_status()

                data = response.json()
                if data and len(data) > 0:
                    found_count += 1
                else:
                    missing_ids.append(record_id)
            except Exception as e:
                logger.error(f"Error checking record {record_id} in {table_name}: {e}")
                missing_ids.append(record_id)

        # Log the missing IDs if any
        if missing_ids:
            logger.error(f"Missing records in {table_name}:")
            for missing_id in missing_ids[:10]:  # Log first 10 missing IDs
                logger.error(f"  - {missing_id}")
            if len(missing_ids) > 10:
                logger.error(f"  ... and {len(missing_ids) - 10} more")

        return {
            "total_count": total_count,
            "found_count": found_count,
            "missing_count": len(missing_ids),
            "missing_ids": missing_ids,
        }

    def _cleanup_data(self) -> None:
        """Clean up the generated sample data from both PostgreSQL and Supabase."""
        logger.info("Step 4: Cleaning up generated sample data")

        # Clean up PostgreSQL data
        if self.data_generator:
            self.data_generator.cleanup_generated_data()

        # Clean up Supabase data
        self._cleanup_supabase_data()

        logger.info("Cleanup completed")

    def _cleanup_supabase_data(self) -> None:
        """Clean up the sample data from Supabase."""
        logger.info("Cleaning up sample data from Supabase...")

        # Set up Supabase API request headers
        headers = {
            "apikey": self.supabase_key,
            "Authorization": f"Bearer {self.supabase_key}",
            "Content-Type": "application/json",
        }

        # Delete in reverse order of dependencies
        table_order = [
            "messages",
            "youtube_videos",
            "feedback",
            "activity_logs",
            "chat_sessions",
            "content_items",
            "business_contexts",
            "users",
        ]

        for table_name in table_order:
            if table_name not in self.generated_data:
                continue

            record_ids = [record["id"] for record in self.generated_data[table_name]]

            if not record_ids:
                continue

            # Delete 10 records at a time to avoid request size limitations
            chunk_size = 10
            for i in range(0, len(record_ids), chunk_size):
                chunk = record_ids[i : i + chunk_size]
                id_str = ",".join([f"eq.{id}" for id in chunk])
                url = f"{self.supabase_url}/rest/v1/{table_name}?id={id_str}"

                try:
                    response = requests.delete(url, headers=headers)
                    response.raise_for_status()
                    logger.info(
                        f"Deleted {len(chunk)} records from {table_name} in Supabase"
                    )
                except Exception as e:
                    logger.error(
                        f"Error deleting records from {table_name} in Supabase: {e}"
                    )

        logger.info("Supabase data cleanup completed")


def main():
    """Main function to run the sample data migration test."""
    parser = argparse.ArgumentParser(
        description="Test data migration from PostgreSQL to Supabase with sample data"
    )

    # Database connection parameters
    parser.add_argument("--pg-host", default="localhost", help="PostgreSQL host")
    parser.add_argument("--pg-port", type=int, default=5432, help="PostgreSQL port")
    parser.add_argument("--pg-database", help="PostgreSQL database name")
    parser.add_argument("--pg-user", default="postgres", help="PostgreSQL username")
    parser.add_argument("--pg-password", help="PostgreSQL password")
    parser.add_argument("--supabase-url", help="Supabase URL")
    parser.add_argument("--supabase-key", help="Supabase API key")

    # Test parameters
    parser.add_argument(
        "--sample-size",
        type=int,
        default=10,
        help="Number of sample records to generate",
    )
    parser.add_argument(
        "--no-cleanup",
        action="store_true",
        help="Don't clean up sample data after test",
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Enable verbose logging"
    )

    args = parser.parse_args()

    # Set log level based on verbosity
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    try:
        tester = MigrationTester(
            pg_host=args.pg_host,
            pg_port=args.pg_port,
            pg_database=args.pg_database,
            pg_user=args.pg_user,
            pg_password=args.pg_password,
            supabase_url=args.supabase_url,
            supabase_key=args.supabase_key,
            sample_size=args.sample_size,
            cleanup=not args.no_cleanup,
        )

        success = tester.run_test()

        if success:
            logger.info("Sample data migration test passed")
            return 0
        else:
            logger.error("Sample data migration test failed")
            return 1
    except Exception as e:
        logger.exception(f"Error running migration test: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
