# AI Script Writing Wizard Implementation Plan

## Project Context
This plan outlines the implementation of an AI-driven wizard for generating video scripts within our React + TypeScript + MUI v5 frontend and Flask + Supabase backend application. The wizard will guide users through brainstorming, refinement, drafting, and editing phases, ultimately integrating with our existing Content Library.

### Technical Stack
- Frontend: React 18+, TypeScript, Material UI v5, React Context API
- Backend: Flask, Supabase, OpenAI API
- Testing: Jest, React Testing Library, Playwright
- State Management: React Context + Local State
- Database: Supabase (PostgreSQL)

### Related Components
- Content Library (`frontend/src/pages/ContentLibrary.tsx`)
- Business Context System (`frontend/src/components/BusinessContextSidebar.tsx`)
- AI Assistant Integration (`frontend/src/services/ai.ts`)

## Implementation Phases

### Phase 1: Infrastructure Setup

#### 1.1 Type Definitions
```typescript
// frontend/src/types/wizard.ts
interface WizardState {
  businessContextId: string;
  currentStep: number;
  draftText: string;
  sessionData: WizardSessionData;
}

interface WizardSessionData {
  id: string;
  contentIdea: string;
  brainstormResults: BrainstormResult[];
  hookResults: HookResult[];
  outlineResults: OutlineResult[];
  selectedIndices: {
    hook: number;
    outline: number;
    draft: number;
  };
}

interface WizardAction {
  type: WizardActionType;
  payload: any;
}
```

#### 1.2 Context Setup
```typescript
// frontend/src/contexts/WizardContext.tsx
export const WizardProvider: React.FC = ({ children }) => {
  const [state, dispatch] = useReducer(wizardReducer, initialState);
  const debouncedSave = useDebounce(saveWizardProgress, 1000);

  useEffect(() => {
    if (state.draftText) {
      debouncedSave(state);
    }
  }, [state.draftText]);

  return (
    <WizardContext.Provider value={{ state, dispatch }}>
      {children}
    </WizardContext.Provider>
  );
};
```

#### 1.3 Error Handling
```typescript
// frontend/src/components/WizardErrorBoundary.tsx
export const WizardErrorBoundary: React.FC = ({ children }) => {
  return (
    <ErrorBoundary
      FallbackComponent={WizardErrorFallback}
      onReset={handleReset}
      onError={logError}
    >
      {children}
    </ErrorBoundary>
  );
};
```

### Phase 2: Business Context Integration

#### 2.1 Layout Component
```typescript
// frontend/src/components/WizardLayout.tsx
export const WizardLayout: React.FC<WizardLayoutProps> = ({
  children,
  businessContextId,
  onBusinessContextChange,
}) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  
  return (
    <Box sx={{ display: 'flex' }}>
      <BusinessContextSidebar
        selectedId={businessContextId}
        onSelect={handleContextChange}
      />
      <Box component="main" sx={{ flexGrow: 1 }}>
        {children}
      </Box>
      <ConfirmationDialog
        open={showConfirmDialog}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />
    </Box>
  );
};
```

### Phase 3: Interactive Components

#### 3.1 Hook Card Component
```typescript
// frontend/src/components/HookCard.tsx
export const HookCard: React.FC<HookCardProps> = memo(({
  hook,
  isSelected,
  onSelect,
}) => {
  return (
    <Card
      role="button"
      tabIndex={0}
      onClick={() => onSelect(hook.id)}
      sx={{ 
        cursor: 'pointer',
        transition: 'all 0.2s',
        transform: isSelected ? 'scale(1.02)' : 'none'
      }}
    >
      {/* Card content */}
    </Card>
  );
});
```

#### 3.2 Outline Builder
```typescript
// frontend/src/components/OutlineBuilder.tsx
export const OutlineBuilder: React.FC<OutlineBuilderProps> = ({
  items,
  onChange,
}) => {
  const [localItems, setLocalItems] = useState(items);
  
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;
    
    const reorderedItems = reorder(
      localItems,
      result.source.index,
      result.destination.index
    );
    
    setLocalItems(reorderedItems);
    onChange(reorderedItems);
  };

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId="outline">
        {/* Draggable outline items */}
      </Droppable>
    </DragDropContext>
  );
};
```

### Phase 4: Integration & Polish

#### 4.1 Analytics Integration
```typescript
// frontend/src/services/analytics.ts
export const trackWizardProgress = (step: number, data: any) => {
  analytics.track('wizard_progress', {
    step,
    businessContextId: data.businessContextId,
    timeSpent: data.timeSpent,
    // Additional metrics
  });
};
```

#### 4.2 Performance Optimization
- Implement React.memo for pure components
- Use useMemo for expensive computations
- Implement virtualization for long lists
- Add Suspense boundaries for code splitting

## Testing Strategy

### Unit Tests
```typescript
// frontend/src/tests/WizardPage.test.tsx
describe('WizardPage', () => {
  it('handles business context changes correctly', () => {
    // Test implementation
  });
  
  it('maintains state through wizard steps', () => {
    // Test implementation
  });
});
```

### E2E Tests
```typescript
// playwright/tests/wizard.spec.ts
test('completes full wizard flow', async ({ page }) => {
  // Test implementation
});
```

## Database Schema
```sql
CREATE TABLE wizard_sessions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  business_context_id UUID REFERENCES business_contexts(id),
  content_idea TEXT,
  brainstorm_results JSONB,
  hook_results JSONB,
  outline_results JSONB,
  draft_text TEXT,
  edited_text TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## API Endpoints
- POST /api/wizard/session - Create new session
- PUT /api/wizard/session/:id - Update session
- POST /api/wizard/brainstorm - Generate ideas
- POST /api/wizard/hook - Generate hooks
- POST /api/wizard/outline - Generate outline
- POST /api/wizard/draft - Generate draft
- POST /api/wizard/export - Export content

## Success Criteria
- Complete wizard flow works end-to-end
- Business context changes handled safely
- Auto-save functionality working
- Export to Content Library successful
- All tests passing
- Performance metrics within targets
- Accessibility standards met

## Timeline
- Phase 1: 1 week
- Phase 2: 1 week
- Phase 3: 2 weeks
- Phase 4: 1 week
- Testing & Polish: 1 week

## Risk Mitigation
- Implement proper error boundaries
- Add comprehensive logging
- Create recovery mechanisms
- Regular backups of wizard sessions
- Fallback UI states for all error cases

# Flask to FastAPI Migration Plan

## Executive Summary & Prioritization Rationale

**Overall Strategy:**
The migration from Flask to FastAPI will be executed in two distinct phases. Phase 1 (MVP-Critical) focuses exclusively on delivering a functional, end-to-end user experience with the core features of the application. Phase 2 (Post-MVP) will address the remaining features, performance optimizations, and technical debt.

**Prioritization Rationale:**
The MVP is defined as the minimum set of features that allows a user to sign up, define their brand identity, and engage in a meaningful conversation with the AI assistant.

* **MVP-Critical (Phase 1):**
    * Authentication (/auth): Users must be able to register and log in
    * Business Context (/business): AI relies on this context for personalized responses
    * AI Chat & Streaming (/chat): Primary user-facing feature
    * AI Assistant Health/Config (/ai): Essential backend routes for chat functionality

* **Post-MVP (Phase 2 - Deferred):**
    * Content Library, YouTube Analytics, Feedback, Activity Log, Script Wizard

## Phase 1: MVP-Critical Migration

### Step 1: Project Setup & Dependencies

**Objective:** Establish FastAPI application foundation

**Actions:**
- Create new `app/main.py` as FastAPI entry point
- Update `requirements.txt`:
  - Add: `fastapi`, `uvicorn[standard]`, `asyncpg`, `httpx`
  - Remove: `flask`, `flask-cors`, `requests`
  - Keep: `supabase`, `openai`, `python-dotenv`, `pydantic`, etc.
- Update main entry point to use uvicorn

**Success Criteria:**
- FastAPI app starts successfully
- Basic health check endpoint responds
- No import errors

### Step 2: Core FastAPI App Structure

**Objective:** Replace Flask patterns with FastAPI equivalents

**Actions:**
- Initialize FastAPI app in `app/main.py`
- Add `CORSMiddleware` configuration (replace Flask-CORS)
- Create dependency injection system to replace Flask's `g` object
- Set up async database session management pattern
- Create base dependency providers

**Key Transformations:**
- Flask blueprints → `APIRouter` with prefixes
- `@app.before_request` → Dependency with `yield`
- `flask.g` → `Depends()` injection
- Flask-CORS → `CORSMiddleware`

**Success Criteria:**
- CORS properly configured for frontend
- Dependency injection system working
- Basic app structure established

### Step 3: Authentication Migration

**Objective:** Convert auth system to FastAPI dependencies

**Actions:**
- Convert `@supabase_auth_required` decorator to FastAPI dependency
- Create `get_current_user` dependency function  
- Update JWT token verification to async
- Migrate auth routes (`register`, `login`, `profile`) to FastAPI router
- Maintain existing `FallbackUser` pattern

**Key Files:**
- `app/utils/supabase_auth.py` → async dependencies
- `app/routers/auth.py` → FastAPI router
- `app/models/user_profile.py` → add Pydantic models

**Success Criteria:**
- User registration works
- User login returns valid JWT
- Protected endpoints require authentication
- Fallback user pattern preserved

### Step 4: Repository Layer Migration

**Objective:** Convert repository methods from sync to async

**Actions:**
- Update base `SupabaseRepository` to async
- Convert all repository methods to `async def`
- Update database calls to use async patterns
- Modify repository initialization for dependency injection
- Create async session management with proper cleanup

**Key Files:**
- `app/repositories/supabase_repository.py` → async base
- `app/repositories/supabase_user_repository.py` → async methods
- `app/repositories/supabase_business_context_repository.py` → async methods
- `app/repositories/supabase_chat_session_repository.py` → async methods

**Success Criteria:**
- All repository methods are async
- Database connections properly managed
- No sync database calls in async context

### Step 5: Pydantic Models Creation

**Objective:** Add request/response validation with Pydantic

**Actions:**
- Create user models: `UserProfileCreate`, `UserProfile`, `UserLogin`
- Create business context models: `BusinessContextCreate`, `BusinessContextUpdate`, `BusinessContext`
- Create chat models: `ChatMessage`, `ChatSession`, `ChatStreamRequest`
- Ensure all models have `ConfigDict(from_attributes=True)`
- Add proper field validation and descriptions

**Key Files:**
- `app/models/user_profile.py` → add Pydantic models
- `app/models/business_context.py` → add Pydantic models
- `app/models/chat_session.py` → add Pydantic models

**Success Criteria:**
- Request validation works automatically
- Response serialization handles database objects
- Clear API documentation generated

### Step 6: Business Context Routes Migration

**Objective:** Convert business context management to FastAPI

**Actions:**
- Convert `business.py` blueprint to FastAPI router
- Update all route functions to `async def`
- Use Pydantic models for request/response validation
- Apply authentication dependencies
- Update repository calls to async

**Key Routes:**
- `POST /business/` → create business context
- `GET /business/` → list user's contexts  
- `GET /business/{context_id}` → get specific context
- `PUT /business/{context_id}` → update context
- `DELETE /business/{context_id}` → delete context

**Success Criteria:**
- All CRUD operations work
- Proper validation and error handling
- Authentication enforced

### Step 7: AI Chat & Streaming Migration

**Objective:** Implement async streaming responses for AI chat

**Actions:**
- Convert `chat.py` blueprint to FastAPI router
- Implement `StreamingResponse` for `/chat/stream` endpoint
- Create async generator for Server-Sent Events (SSE)
- Update AI adapter methods to async (httpx instead of requests)
- Ensure proper async handling of database operations
- Maintain chat session persistence

**Key Components:**
- `StreamingResponse` with async generator
- `httpx.AsyncClient` for AI API calls
- Proper SSE formatting
- Error handling in async generators

**Success Criteria:**
- Chat streaming works with real AI responses
- Database operations don't block streaming
- Error handling preserves stream integrity

### Step 8: AI Assistant Routes Migration

**Objective:** Convert AI assistant health and config endpoints

**Actions:**
- Convert `ai_assistant.py` routes to FastAPI router
- Migrate health check endpoints
- Migrate configuration endpoints
- Update OpenRouter adapter to use `httpx.AsyncClient`
- Test AI provider connectivity

**Key Routes:**
- `/ai/config` → get AI configuration
- `/ai/test-connection` → test AI provider connection
- Health check endpoints

**Success Criteria:**
- Health checks respond correctly
- AI provider connection testing works
- Configuration endpoints functional

## Testing Strategy

### Unit Tests
- Test business logic within migrated repositories
- Ensure Pydantic model validation works
- Test async dependency injection

### Integration Tests
- Use FastAPI's `TestClient` for HTTP testing
- **Priority 1:** Authentication flow (register → login → protected endpoint)
- **Priority 2:** Business context CRUD operations
- **Priority 3:** Chat stream endpoint with SSE format

### Manual End-to-End Testing
- Complete user journey: Sign up → Create Business Profile → Start chat → Get AI response
- Frontend integration testing
- CORS functionality verification

## Risk Assessment & Mitigation

### High Risk Areas
1. **AI Streaming Response** - Complex async generator with SSE format
   - *Mitigation:* Test with mock responses first, ensure proper error handling

2. **Database Session Management** - Async session lifecycle
   - *Mitigation:* Use dependency with yield pattern, test connection cleanup

3. **Authentication Flow** - JWT verification and user lookup
   - *Mitigation:* Maintain fallback user pattern, make JWT verification async

### Medium Risk Areas
1. **Repository Method Conversion** - Sync to async conversion
   - *Mitigation:* Follow consistent pattern, add proper error handling

2. **AI Adapter HTTP Calls** - requests to httpx conversion
   - *Mitigation:* Use httpx.AsyncClient with same patterns

## Success Criteria for MVP

- ✅ User can register and login
- ✅ User can create/read/update/delete business contexts
- ✅ User can start chat and receive streaming AI responses
- ✅ AI assistant health checks work
- ✅ Frontend can connect without CORS issues
- ✅ All existing tests pass with FastAPI

## Technical Transformation Summary

| Flask Pattern | FastAPI Equivalent |
|---------------|-------------------|
| Flask blueprints | APIRouter with prefixes |
| @app.before_request | Dependency with yield |
| flask.g | Depends() injection |
| @supabase_auth_required | Depends(get_current_user) |
| Flask-CORS | CORSMiddleware |
| requests | httpx.AsyncClient |
| sync repository methods | async repository methods |
| Flask abort() | HTTPException |
| Flask jsonify() | Automatic JSON response |

## Post-Migration Verification

1. **Functional Testing:**
   - All MVP user workflows complete successfully
   - Performance meets or exceeds current Flask implementation
   - Error handling maintains user experience

2. **Technical Validation:**
   - No sync database calls in async context
   - Proper resource cleanup (connections, sessions)
   - Memory usage within acceptable bounds

3. **Frontend Integration:**
   - No breaking changes to API contracts
   - CORS properly configured
   - WebSocket connections (if any) work correctly


