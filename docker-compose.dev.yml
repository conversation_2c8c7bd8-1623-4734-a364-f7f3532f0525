# Development override for faster startup
services:
  frontend:
    build:
      dockerfile: Dockerfile.dev.fast
    volumes:
      # Mount source for live development
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - frontend_node_modules:/app/node_modules
    environment:
      # Disable source maps for faster builds
      - GENERATE_SOURCEMAP=false
      # Speed up polling
      - CHOKIDAR_USEPOLLING=true
      - CHOKIDAR_INTERVAL=1000
      # Optimize webpack
      - FAST_REFRESH=true
      # Fix Docker networking for React dev server
      - HOST=0.0.0.0
      - PORT=3000
      - WDS_SOCKET_HOST=localhost
      - WDS_SOCKET_PORT=3000
      - REACT_APP_WDS_SOCKET_HOST=localhost
    depends_on:
      # Don't wait for backend health check
      - backend
    healthcheck:
      # Reduce health check frequency for development
      interval: 45s
      timeout: 10s
      start_period: 60s
      retries: 2

volumes:
  frontend_node_modules: