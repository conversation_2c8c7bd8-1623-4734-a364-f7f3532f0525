#!/usr/bin/env python3
"""
Test script to verify API key configuration for Writer v2.
Run this after updating your API keys in .env.local
"""

import os
import sys
import asyncio
import httpx
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

def load_env_file():
    """Load environment variables from .env.local"""
    env_file = Path(".env.local")
    if not env_file.exists():
        print("❌ .env.local file not found!")
        return False
    
    with open(env_file) as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                os.environ[key] = value
    return True

async def test_openrouter_api():
    """Test OpenRouter API connection"""
    api_key = os.environ.get('OPENROUTER_API_KEY')
    if not api_key or api_key == 'your-new-openrouter-api-key-here':
        print("⚠️  OpenRouter API key not configured")
        return False
    
    print(f"🧪 Testing OpenRouter API key: {api_key[:20]}...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                'https://openrouter.ai/api/v1/chat/completions',
                json={
                    'model': 'deepseek/deepseek-chat-v3-0324:free',
                    'messages': [{'role': 'user', 'content': 'Hello'}],
                    'max_tokens': 10
                },
                headers={
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://writer-v2.app',
                    'X-Title': 'Writer v2'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print("✅ OpenRouter API is working!")
                print(f"   Response: {data.get('choices', [{}])[0].get('message', {}).get('content', 'No content')}")
                return True
            else:
                print(f"❌ OpenRouter API failed: HTTP {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                return False
                
    except Exception as e:
        print(f"❌ OpenRouter API error: {e}")
        return False

async def test_openai_api():
    """Test OpenAI API connection"""
    api_key = os.environ.get('OPENAI_API_KEY')
    if not api_key or api_key == 'your-openai-api-key-here':
        print("⚠️  OpenAI API key not configured")
        return False
    
    print(f"🧪 Testing OpenAI API key: {api_key[:20]}...")
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                'https://api.openai.com/v1/chat/completions',
                json={
                    'model': 'gpt-3.5-turbo',
                    'messages': [{'role': 'user', 'content': 'Hello'}],
                    'max_tokens': 10
                },
                headers={
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                print("✅ OpenAI API is working!")
                print(f"   Response: {data.get('choices', [{}])[0].get('message', {}).get('content', 'No content')}")
                return True
            else:
                print(f"❌ OpenAI API failed: HTTP {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                return False
                
    except Exception as e:
        print(f"❌ OpenAI API error: {e}")
        return False

async def test_application_ai():
    """Test the application's AI integration"""
    try:
        from app.ai_assistant.common import get_ai_generator
        
        print("🧪 Testing application AI integration...")
        ai_gen = get_ai_generator()
        print(f"✅ AI Generator initialized: {type(ai_gen).__name__}")
        
        # Test a simple generation
        result = await ai_gen.generate_content('Say hello in one word')
        if result.get('error'):
            print(f"❌ AI Generation failed: {result.get('error_message')}")
            return False
        else:
            print(f"✅ AI Generation successful: {result.get('generated_text', 'No text')}")
            return True
            
    except Exception as e:
        print(f"❌ Application AI integration error: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Writer v2 API Key Configuration Test")
    print("=" * 50)
    
    # Load environment variables
    if not load_env_file():
        return
    
    ai_provider = os.environ.get('AI_PROVIDER', 'openrouter').lower()
    print(f"📋 Current AI Provider: {ai_provider}")
    print()
    
    # Test APIs based on configuration
    if ai_provider == 'openrouter':
        openrouter_ok = await test_openrouter_api()
        if openrouter_ok:
            app_ok = await test_application_ai()
        else:
            print("⚠️  Skipping application test due to API failure")
            app_ok = False
    elif ai_provider == 'openai':
        openai_ok = await test_openai_api()
        if openai_ok:
            app_ok = await test_application_ai()
        else:
            print("⚠️  Skipping application test due to API failure")
            app_ok = False
    else:
        print(f"❌ Unknown AI provider: {ai_provider}")
        return
    
    print()
    print("=" * 50)
    if ai_provider == 'openrouter' and openrouter_ok and app_ok:
        print("🎉 All tests passed! Your OpenRouter configuration is working.")
    elif ai_provider == 'openai' and openai_ok and app_ok:
        print("🎉 All tests passed! Your OpenAI configuration is working.")
    else:
        print("❌ Some tests failed. Please check your API key configuration.")
        print()
        print("💡 Next steps:")
        print("   1. Verify your API key is correct in .env.local")
        print("   2. Check that you have sufficient credits/quota")
        print("   3. Ensure the API key has the right permissions")

if __name__ == "__main__":
    asyncio.run(main())
