"""
Utility functions for executing Supabase migrations.
"""

import os
import logging
import json

import requests

logger = logging.getLogger(__name__)


def execute_migration(sql: str) -> bool:
    """
    Execute a SQL migration in Supabase.

    Args:
        sql: The SQL migration to execute

    Returns:
        bool: True if migration was successful, False otherwise
    """
    try:
        # Get Supabase credentials from environment
        service_role_key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
        project_url = os.environ.get("SUPABASE_URL")

        if not service_role_key or not project_url:
            logger.error(
                "SUPABASE_SERVICE_ROLE_KEY or SUPABASE_URL not set in environment"
            )
            return False

        # Execute the SQL using the REST API via exec_sql RPC function
        response = requests.post(
            f"{project_url}/rest/v1/rpc/exec_sql",
            headers={
                "apikey": service_role_key,
                "Authorization": f"Bearer {service_role_key}",
                "Content-Type": "application/json",
                "Prefer": "return=minimal",
            },
            json={"sql": sql},
        )

        # Log the full response for debugging
        logger.debug("Response status: %s", response.status_code)
        logger.debug("Response headers: %s", response.headers)
        logger.debug("Response body: %s", response.text)

        # Consider both 200 and 204 as success status codes
        if response.status_code not in [200, 204]:
            try:
                error_data = response.json()
                error_message = error_data.get("message", "Unknown error")
                error_details = error_data.get("details", "")
                error_hint = error_data.get("hint", "")
                logger.error("Migration failed: %s", error_message)
                if error_details:
                    logger.error("Details: %s", error_details)
                if error_hint:
                    logger.error("Hint: %s", error_hint)
            except json.JSONDecodeError:
                logger.error(
                    f"Migration failed with status {response.status_code}: {response.text}"
                )
            return False

        logger.info("Migration executed successfully")
        return True

    except Exception as e:
        logger.error("Error executing migration: %s", str(e))
        return False
