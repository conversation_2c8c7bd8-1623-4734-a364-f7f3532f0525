import { test, expect } from '@playwright/test';

// Test configuration
const TEST_USER = {
  email: '<EMAIL>',
  password: 'MK*9v9sseuu3#Z3qgypf'
};

const BASE_URL = 'http://localhost:3000';
const API_BASE_URL = 'http://localhost:5000';

test.describe('AI Assistant Chat Functionality', () => {
  let page;
  let context;

  test.beforeEach(async ({ browser }) => {
    // Create a new context and page for each test
    context = await browser.newContext({
      viewport: { width: 1280, height: 720 },
      // Enable console logging
      recordVideo: { dir: 'test-results/videos/' }
    });
    
    page = await context.newPage();
    
    // Listen for console messages
    page.on('console', msg => {
      console.log(`[BROWSER CONSOLE ${msg.type().toUpperCase()}]:`, msg.text());
    });
    
    // Listen for page errors
    page.on('pageerror', error => {
      console.error('[PAGE ERROR]:', error.message);
    });
    
    // Listen for network requests
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        console.log(`[REQUEST] ${request.method()} ${request.url()}`);
      }
    });
    
    // Listen for network responses
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log(`[RESPONSE] ${response.status()} ${response.url()}`);
      }
    });
  });

  test.afterEach(async () => {
    await context.close();
  });

  test('should authenticate user and access AI Assistant', async () => {
    console.log('Starting authentication test...');
    
    // Navigate to login page
    await page.goto(`${BASE_URL}/login`);
    await page.waitForLoadState('networkidle');
    
    // Fill login form (Material-UI TextField components)
    await page.fill('#email', TEST_USER.email);
    await page.fill('#password', TEST_USER.password);

    // Submit login
    await page.click('button[type="submit"]');
    
    // Wait for redirect and check if we're authenticated
    await page.waitForURL(/\/(?!login)/, { timeout: 10000 });
    
    // Navigate to AI Assistant
    await page.goto(`${BASE_URL}/ai-assistant`);
    await page.waitForLoadState('networkidle');
    
    // Check if AI Assistant page loaded
    const pageTitle = await page.textContent('h1, h2, [data-testid="page-title"]');
    console.log('AI Assistant page title:', pageTitle);
    
    // Verify we're on the AI Assistant page
    expect(page.url()).toContain('/ai-assistant');
  });

  test('should load chat sessions and display chat interface', async () => {
    console.log('Testing chat interface loading...');
    
    // Login first
    await page.goto(`${BASE_URL}/login`);
    await page.fill('#email', TEST_USER.email);
    await page.fill('#password', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(?!login)/, { timeout: 10000 });
    
    // Navigate to AI Assistant
    await page.goto(`${BASE_URL}/ai-assistant`);
    await page.waitForLoadState('networkidle');
    
    // Wait for chat sessions to load
    await page.waitForTimeout(3000);
    
    // Check for chat interface elements
    const messageInput = page.locator('input[placeholder*="message"], textarea[placeholder*="message"], [data-testid="message-input"]');
    const sendButton = page.locator('button:has-text("Send"), button[aria-label*="send"], [data-testid="send-button"]');
    
    // Log what we found
    const messageInputCount = await messageInput.count();
    const sendButtonCount = await sendButton.count();
    
    console.log(`Found ${messageInputCount} message input(s)`);
    console.log(`Found ${sendButtonCount} send button(s)`);
    
    // Take screenshot for debugging
    await page.screenshot({ path: 'test-results/chat-interface.png', fullPage: true });
    
    // Verify chat interface elements exist
    expect(messageInputCount).toBeGreaterThan(0);
  });

  test('should send a chat message and receive response', async () => {
    console.log('Testing chat message sending...');
    
    // Login and navigate to AI Assistant
    await page.goto(`${BASE_URL}/login`);
    await page.fill('#email', TEST_USER.email);
    await page.fill('#password', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(?!login)/, { timeout: 10000 });
    
    await page.goto(`${BASE_URL}/ai-assistant`);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find message input and send button with enhanced selectors
    const messageInput = page.locator('#message-input, input[id="message-input"], textarea[id="message-input"], input[placeholder*="message"], textarea[placeholder*="message"]').first();

    // Look for send button with multiple strategies
    let sendButton = page.locator('button[type="submit"], button:has-text("Send"), button[aria-label*="send"]').first();

    // If no direct send button found, look for buttons with send icon or keyboard shortcut
    if (await sendButton.count() === 0) {
      sendButton = page.locator('button').filter({ hasText: /send|submit/i }).first();
    }

    // If still no button, look for buttons near the input field
    if (await sendButton.count() === 0) {
      sendButton = page.locator('form').locator('button').first();
    }
    
    // Type a test message
    const testMessage = 'Hello, this is a test message. Please respond briefly.';
    await messageInput.fill(testMessage);
    
    // Take screenshot before sending
    await page.screenshot({ path: 'test-results/before-send.png', fullPage: true });
    
    // Send the message (try Enter key if button click fails)
    try {
      await sendButton.click();
    } catch (error) {
      console.log('Button click failed, trying Enter key:', error.message);
      await messageInput.press('Enter');
    }
    
    // Wait for response (look for loading indicators or new messages)
    await page.waitForTimeout(2000);
    
    // Look for chat messages or response indicators
    const chatMessages = page.locator('[data-testid="chat-message"], .message, .chat-message');
    const loadingIndicators = page.locator('[data-testid="loading"], .loading, .spinner');
    
    console.log(`Found ${await chatMessages.count()} chat messages`);
    console.log(`Found ${await loadingIndicators.count()} loading indicators`);
    
    // Take screenshot after sending
    await page.screenshot({ path: 'test-results/after-send.png', fullPage: true });
    
    // Wait longer for AI response
    await page.waitForTimeout(10000);
    
    // Check for response
    const finalMessageCount = await chatMessages.count();
    console.log(`Final message count: ${finalMessageCount}`);
    
    // Take final screenshot
    await page.screenshot({ path: 'test-results/final-state.png', fullPage: true });
  });

  test('should handle API errors gracefully', async () => {
    console.log('Testing error handling...');
    
    // Login and navigate to AI Assistant
    await page.goto(`${BASE_URL}/login`);
    await page.fill('#email', TEST_USER.email);
    await page.fill('#password', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(?!login)/, { timeout: 10000 });
    
    await page.goto(`${BASE_URL}/ai-assistant`);
    await page.waitForLoadState('networkidle');
    
    // Intercept API calls and simulate errors
    await page.route('**/api/ai_assistant/chat/**', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Simulated server error' })
      });
    });
    
    // Try to send a message
    const messageInput = page.locator('input[placeholder*="message"], textarea[placeholder*="message"], [data-testid="message-input"]').first();
    const sendButton = page.locator('button:has-text("Send"), button[aria-label*="send"], [data-testid="send-button"]').first();
    
    if (await messageInput.count() > 0) {
      await messageInput.fill('Test error handling');
      
      if (await sendButton.count() > 0) {
        await sendButton.click();
      }
      
      // Wait for error handling
      await page.waitForTimeout(3000);
      
      // Look for error messages
      const errorMessages = page.locator('[data-testid="error"], .error, .alert-error');
      console.log(`Found ${await errorMessages.count()} error messages`);
      
      // Take screenshot
      await page.screenshot({ path: 'test-results/error-handling.png', fullPage: true });
    }
  });

  test('should test backend API endpoints directly', async () => {
    console.log('Testing backend API endpoints...');
    
    // First get auth token by logging in
    await page.goto(`${BASE_URL}/login`);
    await page.fill('#email', TEST_USER.email);
    await page.fill('#password', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForURL(/\/(?!login)/, { timeout: 10000 });
    
    // Get the auth token from localStorage or session
    const authToken = await page.evaluate(() => {
      // Try different ways to get the token
      const supabaseSession = localStorage.getItem('sb-zhtmqikyqgawninsgiup-auth-token');
      if (supabaseSession) {
        try {
          const parsed = JSON.parse(supabaseSession);
          return parsed.access_token;
        } catch (e) {
          console.log('Failed to parse supabase session:', e);
        }
      }
      return null;
    });
    
    console.log('Auth token found:', !!authToken);
    
    if (authToken) {
      // Test auth endpoint
      const authResponse = await page.request.get(`${API_BASE_URL}/api/ai_assistant/chat/auth-test`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Auth test response status:', authResponse.status());
      const authData = await authResponse.json();
      console.log('Auth test response:', authData);
      
      // Test chat sessions endpoint
      const sessionsResponse = await page.request.get(`${API_BASE_URL}/api/ai_assistant/chat/sessions`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Sessions response status:', sessionsResponse.status());
      const sessionsData = await sessionsResponse.json();
      console.log('Sessions response:', sessionsData);
    }
  });
});
