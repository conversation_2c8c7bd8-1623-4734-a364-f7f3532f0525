#!/usr/bin/env python
"""
Pre-Migration Safety Check Script

This script performs a series of checks to verify that the environment is ready
for the migration from PostgreSQL to Supabase in production.
"""

import argparse
import os
import sys
import json
import subprocess
import requests
from datetime import datetime
from dotenv import load_dotenv
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT


class MigrationSafetyCheck:
    def __init__(self, config_file=None, verbose=False):
        self.verbose = verbose
        self.checks_passed = 0
        self.checks_failed = 0
        self.warnings = 0
        self.results = {}

        # Load configuration
        self.config = self._load_config(config_file)

        # Load environment variables
        load_dotenv()

        # Initialize log file
        self.log_file = (
            f"migration_safety_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        )
        self.log(
            f"Migration Safety Check started at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

    def _load_config(self, config_file):
        """Load configuration from JSON file or use defaults"""
        default_config = {
            "postgresql": {
                "host": "localhost",
                "port": 5432,
                "database": "writer_v2",
                "user": "postgres",
                "password": "postgres",
            },
            "supabase": {
                "url": os.getenv("SUPABASE_URL", ""),
                "key": os.getenv("SUPABASE_KEY", ""),
            },
            "api": {
                "url": "http://localhost:5000",
                "endpoints": [
                    "/api/content/list",
                    "/api/business/contexts",
                    "/api/ai/sessions",
                    "/api/activity/logs",
                ],
            },
            "required_tools": ["python", "pytest", "docker"],
            "required_files": [
                "Set Up/supabase_data_migration.py",
                "Set Up/Supabase_Migration_Plan.md",
                "Set Up/Supabase_Production_Migration_Plan.md",
                "tests/supabase/run_repository_tests.bat",
                "tests/supabase/run_performance_benchmark.bat",
                "run_supabase_tests.bat",
            ],
            "backups": {"required": True, "backup_dir": "./backups"},
        }

        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, "r") as f:
                    user_config = json.load(f)
                    # Merge configs, user config takes precedence
                    for key, value in user_config.items():
                        if key in default_config and isinstance(
                            default_config[key], dict
                        ):
                            default_config[key].update(value)
                        else:
                            default_config[key] = value
            except Exception as e:
                self.log(f"Error loading config file: {str(e)}", level="ERROR")

        return default_config

    def log(self, message, level="INFO"):
        """Log a message to the console and log file"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] [{level}] {message}"

        if self.verbose or level in ["ERROR", "WARNING"]:
            print(log_message)

        with open(self.log_file, "a") as f:
            f.write(log_message + "\n")

    def check_environment_variables(self):
        """Check if all required environment variables are set"""
        self.log("Checking environment variables...")
        required_vars = ["SUPABASE_URL", "SUPABASE_KEY"]

        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            self.log(
                f"Missing environment variables: {', '.join(missing_vars)}",
                level="ERROR",
            )
            self.checks_failed += 1
            self.results["environment_variables"] = {
                "status": "FAILED",
                "missing": missing_vars,
            }
            return False

        self.log("All required environment variables are set.")
        self.checks_passed += 1
        self.results["environment_variables"] = {"status": "PASSED"}
        return True

    def check_postgres_connection(self):
        """Check connection to PostgreSQL database"""
        self.log("Checking PostgreSQL connection...")
        try:
            conn = psycopg2.connect(
                host=self.config["postgresql"]["host"],
                port=self.config["postgresql"]["port"],
                database=self.config["postgresql"]["database"],
                user=self.config["postgresql"]["user"],
                password=self.config["postgresql"]["password"],
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            db_version = cursor.fetchone()
            cursor.close()
            conn.close()

            self.log(f"Successfully connected to PostgreSQL: {db_version[0]}")
            self.checks_passed += 1
            self.results["postgres_connection"] = {
                "status": "PASSED",
                "version": db_version[0],
            }
            return True
        except Exception as e:
            self.log(f"Failed to connect to PostgreSQL: {str(e)}", level="ERROR")
            self.checks_failed += 1
            self.results["postgres_connection"] = {"status": "FAILED", "error": str(e)}
            return False

    def check_postgres_data(self):
        """Check if PostgreSQL database has data"""
        self.log("Checking PostgreSQL data...")
        try:
            conn = psycopg2.connect(
                host=self.config["postgresql"]["host"],
                port=self.config["postgresql"]["port"],
                database=self.config["postgresql"]["database"],
                user=self.config["postgresql"]["user"],
                password=self.config["postgresql"]["password"],
            )
            cursor = conn.cursor()

            tables = [
                "users",
                "content_items",
                "business_contexts",
                "chat_sessions",
                "messages",
                # "user_activities", # Commented out as functionality is postponed
                "feedback",
                "youtube_videos",
            ]

            table_counts = {}
            empty_tables = []

            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table};")
                    count = cursor.fetchone()[0]
                    table_counts[table] = count
                    if count == 0:
                        empty_tables.append(table)
                except Exception as e:
                    self.log(f"Error querying table {table}: {str(e)}", level="WARNING")
                    table_counts[table] = "ERROR"
                    self.warnings += 1

            cursor.close()
            conn.close()

            if empty_tables:
                self.log(
                    f"The following tables are empty: {', '.join(empty_tables)}",
                    level="WARNING",
                )
                self.warnings += 1

            self.log(
                f"PostgreSQL data check complete. Table counts: {json.dumps(table_counts, indent=2)}"
            )
            self.checks_passed += 1
            self.results["postgres_data"] = {
                "status": "PASSED" if not empty_tables else "WARNING",
                "table_counts": table_counts,
                "empty_tables": empty_tables,
            }
            return True
        except Exception as e:
            self.log(f"Failed to check PostgreSQL data: {str(e)}", level="ERROR")
            self.checks_failed += 1
            self.results["postgres_data"] = {"status": "FAILED", "error": str(e)}
            return False

    def check_supabase_connection(self):
        """Check connection to Supabase"""
        self.log("Checking Supabase connection...")

        if not self.config["supabase"]["url"] or not self.config["supabase"]["key"]:
            self.log("Supabase URL or key not provided.", level="ERROR")
            self.checks_failed += 1
            self.results["supabase_connection"] = {
                "status": "FAILED",
                "error": "Supabase URL or key not provided",
            }
            return False

        try:
            headers = {
                "apikey": self.config["supabase"]["key"],
                "Authorization": f"Bearer {self.config['supabase']['key']}",
            }
            response = requests.get(
                f"{self.config['supabase']['url']}/rest/v1/", headers=headers
            )

            if response.status_code == 200:
                self.log("Successfully connected to Supabase.")
                self.checks_passed += 1
                self.results["supabase_connection"] = {"status": "PASSED"}
                return True
            else:
                self.log(
                    f"Failed to connect to Supabase. Status code: {response.status_code}",
                    level="ERROR",
                )
                self.checks_failed += 1
                self.results["supabase_connection"] = {
                    "status": "FAILED",
                    "error": f"Status code: {response.status_code}",
                }
                return False
        except Exception as e:
            self.log(f"Failed to connect to Supabase: {str(e)}", level="ERROR")
            self.checks_failed += 1
            self.results["supabase_connection"] = {"status": "FAILED", "error": str(e)}
            return False

    def check_api_endpoints(self):
        """Check if API endpoints are responding"""
        self.log("Checking API endpoints...")

        endpoints = self.config["api"]["endpoints"]
        base_url = self.config["api"]["url"]
        results = {}
        all_passed = True

        for endpoint in endpoints:
            try:
                self.log(f"Testing endpoint: {endpoint}")
                response = requests.get(f"{base_url}{endpoint}")

                if 200 <= response.status_code < 300 or response.status_code == 401:
                    # 401 is acceptable as it means authentication is required
                    self.log(
                        f"Endpoint {endpoint} is responding correctly (status: {response.status_code})"
                    )
                    results[endpoint] = {
                        "status": "PASSED",
                        "response_code": response.status_code,
                    }
                else:
                    self.log(
                        f"Endpoint {endpoint} returned unexpected status: {response.status_code}",
                        level="ERROR",
                    )
                    results[endpoint] = {
                        "status": "FAILED",
                        "response_code": response.status_code,
                    }
                    all_passed = False
            except Exception as e:
                self.log(
                    f"Failed to connect to endpoint {endpoint}: {str(e)}", level="ERROR"
                )
                results[endpoint] = {"status": "FAILED", "error": str(e)}
                all_passed = False

        if all_passed:
            self.checks_passed += 1
        else:
            self.checks_failed += 1

        self.results["api_endpoints"] = {
            "status": "PASSED" if all_passed else "FAILED",
            "endpoints": results,
        }

        return all_passed

    def check_required_tools(self):
        """Check if required tools are available"""
        self.log("Checking required tools...")

        tools = self.config["required_tools"]
        missing_tools = []

        for tool in tools:
            try:
                subprocess.run(
                    [tool, "--version"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    check=False,
                )
                self.log(f"Tool {tool} is available.")
            except Exception:
                self.log(f"Tool {tool} is not available.", level="ERROR")
                missing_tools.append(tool)

        if missing_tools:
            self.log(
                f"Missing required tools: {', '.join(missing_tools)}", level="ERROR"
            )
            self.checks_failed += 1
            self.results["required_tools"] = {
                "status": "FAILED",
                "missing": missing_tools,
            }
            return False

        self.log("All required tools are available.")
        self.checks_passed += 1
        self.results["required_tools"] = {"status": "PASSED"}
        return True

    def check_required_files(self):
        """Check if required files exist"""
        self.log("Checking required files...")

        files = self.config["required_files"]
        missing_files = []

        for file in files:
            if not os.path.exists(file):
                self.log(f"Required file {file} does not exist.", level="ERROR")
                missing_files.append(file)
            else:
                self.log(f"Required file {file} exists.")

        if missing_files:
            self.log(
                f"Missing required files: {', '.join(missing_files)}", level="ERROR"
            )
            self.checks_failed += 1
            self.results["required_files"] = {
                "status": "FAILED",
                "missing": missing_files,
            }
            return False

        self.log("All required files exist.")
        self.checks_passed += 1
        self.results["required_files"] = {"status": "PASSED"}
        return True

    def check_backup_capability(self):
        """Check if backup directory exists and is writable"""
        self.log("Checking backup capability...")

        if not self.config["backups"]["required"]:
            self.log("Backup check skipped as it's not required in the configuration.")
            self.checks_passed += 1
            self.results["backup_capability"] = {"status": "SKIPPED"}
            return True

        backup_dir = self.config["backups"]["backup_dir"]

        if not os.path.exists(backup_dir):
            try:
                os.makedirs(backup_dir)
                self.log(f"Created backup directory: {backup_dir}")
            except Exception as e:
                self.log(f"Failed to create backup directory: {str(e)}", level="ERROR")
                self.checks_failed += 1
                self.results["backup_capability"] = {
                    "status": "FAILED",
                    "error": str(e),
                }
                return False

        # Test if directory is writable
        test_file = os.path.join(backup_dir, "test_write.tmp")
        try:
            with open(test_file, "w") as f:
                f.write("Test")
            os.remove(test_file)
            self.log(f"Backup directory {backup_dir} is writable.")
            self.checks_passed += 1
            self.results["backup_capability"] = {"status": "PASSED"}
            return True
        except Exception as e:
            self.log(
                f"Backup directory {backup_dir} is not writable: {str(e)}",
                level="ERROR",
            )
            self.checks_failed += 1
            self.results["backup_capability"] = {"status": "FAILED", "error": str(e)}
            return False

    def check_migration_prerequisites(self):
        """Check if all migration script prerequisites are met"""
        self.log("Checking migration script prerequisites...")

        # Check if migration script exists
        migration_script = "Set Up/supabase_data_migration.py"
        if not os.path.exists(migration_script):
            self.log(
                f"Migration script {migration_script} does not exist.", level="ERROR"
            )
            self.checks_failed += 1
            self.results["migration_prerequisites"] = {
                "status": "FAILED",
                "error": f"Migration script {migration_script} does not exist",
            }
            return False

        # Try to run migration script with --help to verify it works
        try:
            result = subprocess.run(
                ["python", migration_script, "--help"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                check=False,
            )
            if result.returncode != 0:
                self.log(
                    f"Migration script returned non-zero exit code: {result.returncode}",
                    level="ERROR",
                )
                self.log(f"Error: {result.stderr.decode('utf-8')}", level="ERROR")
                self.checks_failed += 1
                self.results["migration_prerequisites"] = {
                    "status": "FAILED",
                    "error": f"Migration script test failed with exit code {result.returncode}",
                }
                return False

            self.log("Migration script test succeeded.")
            self.checks_passed += 1
            self.results["migration_prerequisites"] = {"status": "PASSED"}
            return True
        except Exception as e:
            self.log(f"Failed to test migration script: {str(e)}", level="ERROR")
            self.checks_failed += 1
            self.results["migration_prerequisites"] = {
                "status": "FAILED",
                "error": str(e),
            }
            return False

    def check_test_suite_health(self):
        """Check if test suite is healthy by running a quick test"""
        self.log("Checking test suite health...")

        try:
            # Run a quick test to verify the test suite is working
            test_env = os.environ.copy()
            test_env["SUPABASE_URL"] = self.config["supabase"]["url"]
            test_env["SUPABASE_KEY"] = self.config["supabase"]["key"]

            result = subprocess.run(
                [
                    "python",
                    "-m",
                    "pytest",
                    "tests/supabase",
                    "-k",
                    "test_supabase_client",
                    "-v",
                ],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=test_env,
                check=False,
            )

            if result.returncode != 0:
                self.log("Test suite health check failed.", level="WARNING")
                self.log(f"Error: {result.stderr.decode('utf-8')}", level="WARNING")
                self.warnings += 1
                self.results["test_suite_health"] = {
                    "status": "WARNING",
                    "error": "Basic test failed to run",
                }
                return False

            self.log("Test suite health check passed.")
            self.checks_passed += 1
            self.results["test_suite_health"] = {"status": "PASSED"}
            return True
        except Exception as e:
            self.log(
                f"Failed to run test suite health check: {str(e)}", level="WARNING"
            )
            self.warnings += 1
            self.results["test_suite_health"] = {"status": "WARNING", "error": str(e)}
            return False

    def run_all_checks(self):
        """Run all safety checks"""
        self.log("Running all safety checks...")

        checks = [
            ("Environment Variables", self.check_environment_variables),
            ("PostgreSQL Connection", self.check_postgres_connection),
            ("PostgreSQL Data", self.check_postgres_data),
            ("Supabase Connection", self.check_supabase_connection),
            ("API Endpoints", self.check_api_endpoints),
            ("Required Tools", self.check_required_tools),
            ("Required Files", self.check_required_files),
            ("Backup Capability", self.check_backup_capability),
            ("Migration Prerequisites", self.check_migration_prerequisites),
            ("Test Suite Health", self.check_test_suite_health),
        ]

        for name, check_func in checks:
            self.log(f"\n=== Running Check: {name} ===")
            check_func()

        return self.generate_report()

    def generate_report(self):
        """Generate a report of all checks"""
        self.log("\n=== Migration Safety Check Report ===")
        self.log(f"Checks passed: {self.checks_passed}")
        self.log(f"Checks failed: {self.checks_failed}")
        self.log(f"Warnings: {self.warnings}")

        # Determine overall status
        if self.checks_failed > 0:
            overall_status = "FAILED"
            self.log(
                "Overall status: FAILED - Some checks have failed. Migration is NOT safe to proceed.",
                level="ERROR",
            )
        elif self.warnings > 0:
            overall_status = "WARNING"
            self.log(
                "Overall status: WARNING - All checks passed but with warnings. Review before proceeding.",
                level="WARNING",
            )
        else:
            overall_status = "PASSED"
            self.log(
                "Overall status: PASSED - All checks passed. Migration is safe to proceed."
            )

        # Save report to JSON file
        report = {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "overall_status": overall_status,
            "checks_passed": self.checks_passed,
            "checks_failed": self.checks_failed,
            "warnings": self.warnings,
            "results": self.results,
        }

        report_file = (
            f"migration_safety_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)

        self.log(f"Report saved to {report_file}")

        # Generate a human-readable summary
        summary_file = (
            f"migration_safety_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        )
        with open(summary_file, "w") as f:
            f.write("======================================\n")
            f.write("MIGRATION SAFETY CHECK SUMMARY\n")
            f.write("======================================\n\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"Overall Status: {overall_status}\n")
            f.write(f"Checks Passed: {self.checks_passed}\n")
            f.write(f"Checks Failed: {self.checks_failed}\n")
            f.write(f"Warnings: {self.warnings}\n\n")

            f.write("CHECK RESULTS:\n")
            f.write("-------------\n\n")

            for check_name, check_result in self.results.items():
                status = check_result.get("status", "UNKNOWN")
                f.write(f"{check_name}: {status}\n")

                if status == "FAILED" and "error" in check_result:
                    f.write(f"  Error: {check_result['error']}\n")

                if status == "WARNING" and "error" in check_result:
                    f.write(f"  Warning: {check_result['error']}\n")

                if "missing" in check_result:
                    f.write(f"  Missing: {', '.join(check_result['missing'])}\n")

                f.write("\n")

            f.write("RECOMMENDATION:\n")
            f.write("--------------\n\n")

            if overall_status == "FAILED":
                f.write(
                    "DO NOT PROCEED WITH MIGRATION. Fix the issues reported above and run the check again.\n"
                )
            elif overall_status == "WARNING":
                f.write(
                    "Review warnings before proceeding with migration. If the warnings are acceptable, migration can proceed.\n"
                )
            else:
                f.write(
                    "All checks have passed. It is safe to proceed with the migration.\n"
                )

        self.log(f"Summary saved to {summary_file}")

        return overall_status == "PASSED" or overall_status == "WARNING"


def main():
    parser = argparse.ArgumentParser(
        description="Run safety checks before Supabase migration"
    )
    parser.add_argument("--config", help="Path to configuration JSON file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    args = parser.parse_args()

    checker = MigrationSafetyCheck(args.config, args.verbose)
    success = checker.run_all_checks()

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
