"""
Supabase client adapter for Writer v2 - FastAPI version.
This module provides both sync and async Supabase clients for different use cases.
- Sync client: For repository operations (database CRUD)
- Async client: For auth operations only

Migrated from Flask to use FastAPI dependency injection.
"""

import logging

from supabase import create_client, Client
from supabase._async.client import create_client as create_async_client, AsyncClient

logger = logging.getLogger(__name__)

# Global variables to store clients for testing purposes
_test_client: AsyncClient | None = None
_test_sync_client: Client | None = None


class SupabaseClientError(Exception):
    """Exception raised for Supabase client errors."""
    pass


def set_test_client(client: AsyncClient) -> None:
    """Set an async Supabase client instance for testing purposes.

    Args:
        client: Supabase async client instance to use for testing
    """
    global _test_client
    _test_client = client
    logger.info("Test async Supabase client set")


def set_test_sync_client(client: Client) -> None:
    """Set a sync Supabase client instance for testing purposes.

    Args:
        client: Supabase sync client instance to use for testing
    """
    global _test_sync_client
    _test_sync_client = client
    logger.info("Test sync Supabase client set")


def get_test_client() -> AsyncClient | None:
    """Get the test async client if set."""
    return _test_client


def get_test_sync_client() -> Client | None:
    """Get the test sync client if set."""
    return _test_sync_client


# Backward compatibility functions - these now use the FastAPI dependencies
async def get_supabase_client() -> AsyncClient:
    """
    Get an async Supabase client instance.
    This function is kept for backward compatibility.
    New code should use the FastAPI dependency from app.dependencies.
    """
    if _test_client is not None:
        return _test_client
    
    from app.dependencies import get_supabase_client as _get_client
    return await _get_client()


def get_sync_supabase_client() -> Client:
    """
    Get a sync Supabase client instance.
    This function is kept for backward compatibility.
    New code should use the FastAPI dependency from app.dependencies.
    """
    if _test_sync_client is not None:
        return _test_sync_client
    
    from app.dependencies import get_supabase_sync_client as _get_sync_client
    return _get_sync_client()


def get_table(table_name: str):
    """Get a table reference from the sync client for repository operations."""
    client = get_sync_supabase_client()
    return client.table(table_name)


async def get_async_table(table_name: str):
    """Get a table reference from the async client (rarely needed)."""
    client = await get_supabase_client()
    return client.table(table_name)


async def get_auth():
    """Get the Supabase auth client from the client."""
    client = await get_supabase_client()
    return client.auth


async def get_storage():
    """Get the Supabase storage client from the client."""
    client = await get_supabase_client()
    return client.storage


# Legacy Flask functions - no longer needed in FastAPI
def close_supabase_client(e=None):
    """
    Legacy Flask function - no longer needed in FastAPI.
    Kept for backward compatibility during migration.
    """
    logger.debug("close_supabase_client called (legacy function, no-op in FastAPI)")


def init_supabase(app):
    """
    Legacy Flask function - no longer needed in FastAPI.
    Kept for backward compatibility during migration.
    """
    logger.debug("init_supabase called (legacy function, no-op in FastAPI)")
    return True
