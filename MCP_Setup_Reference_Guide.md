# MCP Server Setup Reference Guide for Windows

This guide documents the process for setting up Model Context Protocol (MCP) servers on your Windows system based on your successful configuration.

## System Configuration

**Location:** `C:\Users\<USER>\.cursor\mcp.json`

**Working Configuration Pattern:**
```json
{
  "mcpServers": {
    "server_name": {
      "command": "command_or_path",
      "args": ["arg1", "arg2"],
      "type": "stdio"
    }
  }
}
```

## Successful MCP Server Configurations

### NPM-based Servers (Recommended Pattern)
```json
"Sequential Thinking": {
  "command": "C:\\Users\\<USER>\\AppData\\Local\\fnm_multishells\\12784_1743600085389\\node.exe C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@modelcontextprotocol\\server-sequential-thinking\\dist\\index.js",
  "type": "stdio"
},
"Playwright": {
  "command": "npx",
  "args": ["@playwright/mcp@latest"],
  "type": "stdio"
},
"supabase": {
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-supabase"],
  "type": "stdio"
},
"context7": {
  "command": "npx",
  "args": ["-y", "@context7/mcp-server"],
  "type": "stdio"
},
"mcp-installer": {
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-mcp-installer"],
  "type": "stdio"
}
```

### Local Build Servers (For Problem Packages)
```json
"folder_structure_mcp": {
  "command": "node",
  "args": ["C:\\MCP\\folder_structure_mcp\\dist\\index.js"],
  "type": "stdio"
}
```

## Installation Methods

### Method 1: NPM Global Install (Preferred)
```bash
npm install -g @modelcontextprotocol/server-name
```

**Configuration:**
```json
"server_name": {
  "command": "npx",
  "args": ["-y", "@modelcontextprotocol/server-name"],
  "type": "stdio"
}
```

### Method 2: Local Build (For Problematic Packages)
```bash
# Clone the repository
git clone https://github.com/owner/repo.git C:\MCP\repo_name

# Navigate to directory
cd C:\MCP\repo_name

# Install dependencies (use install, not ci if no package-lock.json)
npm install

# Build the project
npm run build

# Verify build
Get-ChildItem dist
```

**Configuration:**
```json
"server_name": {
  "command": "node",
  "args": ["C:\\MCP\\repo_name\\dist\\index.js"],
  "type": "stdio"
}
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Package Not Found in NPM Registry
**Error:** `npm 404 package not found`

**Solution:** Use local build method (Method 2)

#### 2. Post-Install Script Failures
**Error:** `spawn C:\WINDOWS\system32\cmd.exe ENOENT` during esbuild

**Solution:** Use local build method instead of npm install

#### 3. PowerShell Syntax Issues
**Wrong:** `cd path && command`
**Correct:** `cd path; command`

#### 4. Package Lock Issues
**Error:** `npm ci` requires package-lock.json

**Solution:** Use `npm install` instead of `npm ci`

### Diagnostic Commands

#### Check Cursor Logs
```powershell
Get-ChildItem "C:\Users\<USER>\AppData\Roaming\Cursor\logs" -Name
Get-Content "C:\Users\<USER>\AppData\Roaming\Cursor\logs\[latest_date]\main.log" | Select-String -Pattern "mcp|error" -Context 2
```

#### Verify NPM Packages
```bash
npm list -g | findstr mcp
node -e "console.log(require.resolve('package-name'))"
```

#### Test MCP Server Directly
```bash
node path\to\server\dist\index.js
```

## Best Practices

### 1. Configuration File Management
- Always backup `mcp.json` before changes
- Use absolute paths for reliability
- Follow the exact JSON structure of working servers

### 2. Installation Strategy
1. **Try NPM first:** Most MCP servers work with `npx` commands
2. **Use local build for failures:** When npm installation fails
3. **Create dedicated MCP directory:** `C:\MCP\` for local builds

### 3. Testing Workflow
1. Add server to `mcp.json`
2. Restart Cursor completely
3. Check Cursor logs for connection errors
4. Test server functionality in chat

### 4. Directory Structure for Local Builds
```
C:\MCP\
├── server1_name\
│   ├── src\
│   ├── dist\
│   └── package.json
└── server2_name\
    ├── src\
    ├── dist\
    └── package.json
```

## Environment Details

- **OS:** Windows 10.0.26100
- **Shell:** PowerShell
- **Node Version:** Via fnm (Fast Node Manager)
- **Cursor Config Location:** `C:\Users\<USER>\.cursor\mcp.json`
- **Log Location:** `C:\Users\<USER>\AppData\Roaming\Cursor\logs\`

## Quick Reference Commands

```powershell
# Check if MCP config exists
Test-Path "C:\Users\<USER>\.cursor\mcp.json"

# View current MCP servers
Get-Content "C:\Users\<USER>\.cursor\mcp.json" | ConvertFrom-Json | Select-Object -ExpandProperty mcpServers

# Check latest Cursor logs
$latestLog = Get-ChildItem "C:\Users\<USER>\AppData\Roaming\Cursor\logs" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
Get-Content "$($latestLog.FullName)\main.log" | Select-String -Pattern "mcp" -Context 1 | Select-Object -Last 10

# Test local MCP server
cd C:\MCP\server_name
node dist\index.js
```

## Success Indicators

✅ **Working MCP Server:**
- No errors in Cursor logs
- Server appears in MCP tools list
- Tools are accessible in chat

❌ **Failed MCP Server:**
- "No server info found" in logs
- Server not listed in tools
- Connection timeouts or spawn errors

Remember: After any configuration changes, **always restart Cursor completely** for changes to take effect. 