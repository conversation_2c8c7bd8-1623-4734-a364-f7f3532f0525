import React, { useState } from "react";
import {
  <PERSON>,
  Typography,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Autocomplete,
  TextField,
  IconButton,
  Divider,
  Tooltip,
  CircularProgress,
  Checkbox,
  Button,
  Stack,
} from "@mui/material";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import DeleteIcon from "@mui/icons-material/Delete";
import CancelIcon from "@mui/icons-material/Cancel";

interface BusinessContextStub {
  id: string;
  // For now, let's assume a 'name' field exists or we'll derive one
  name: string; // You might need to adjust this based on actual data
  // Add other fields if needed for display, e.g., created_at for sorting
}

interface BusinessContextSidebarProps {
  contexts: BusinessContextStub[];
  currentContextId: string | null;
  onContextSelect: (id: string | null) => void;
  onNewContextClick: () => void;
  onDeleteContexts: (ids: string[]) => void; // New prop for batch delete
  loading?: boolean;
  error?: string | null;
}

const BusinessContextSidebar: React.FC<BusinessContextSidebarProps> = ({
  contexts,
  currentContextId,
  onContextSelect,
  onNewContextClick,
  onDeleteContexts,
  loading = false,
  error = null,
}) => {
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  const multiSelectActive = selectedIds.size > 0;

  const currentContext = contexts.find((c) => c.id === currentContextId);

  // Handle selection change in Autocomplete
  const handleAutocompleteChange = (
    event: React.SyntheticEvent,
    value: BusinessContextStub | null
  ) => {
    if (multiSelectActive) return; // Don't change selection in multi-select mode via Autocomplete
    onContextSelect(value ? value.id : null);
  };

  // Function to get display label for Autocomplete options
  const getOptionLabel = (option: BusinessContextStub) => {
    // Customize this label as needed, e.g., include date
    return option.name || `Context ${option.id.substring(0, 6)}`;
  };

  const handleCheckboxChange = (contextId: string) => {
    setSelectedIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(contextId)) {
        newSet.delete(contextId);
      } else {
        newSet.add(contextId);
      }
      return newSet;
    });
  };

  const handleListItemClick = (contextId: string) => {
    if (multiSelectActive) {
      // If multi-select is active, clicking the item toggles the checkbox
      handleCheckboxChange(contextId);
    } else {
      // Otherwise, it selects the context
      onContextSelect(contextId);
    }
  };

  const handleCancelMultiSelect = () => {
    setSelectedIds(new Set());
  };

  const handleDeleteClick = () => {
    if (selectedIds.size > 0) {
      onDeleteContexts(Array.from(selectedIds));
      // Keep selection active until parent confirms deletion and clears via prop change
      // Or clear immediately: setSelectedIds(new Set());
    }
  };

  return (
    <Box
      sx={{
        width: 280, // Example width, adjust as needed
        height: "100%",
        borderRight: "1px solid",
        borderColor: "divider",
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper", // Ensure background for visibility
      }}
    >
      <Box
        sx={{
          p: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        <Typography variant="h6" component="div" sx={{ fontWeight: "bold" }}>
          Business Contexts
        </Typography>
        {!multiSelectActive && (
          <Tooltip title="Create New Context">
            {/* Prevent creating new while selecting for delete */}
            <IconButton
              onClick={onNewContextClick}
              color="primary"
              size="small"
            >
              <AddCircleOutlineIcon />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      <Divider />

      <Box sx={{ p: 2 }}>
        <Autocomplete
          options={contexts}
          getOptionLabel={getOptionLabel}
          value={currentContext || null}
          onChange={handleAutocompleteChange}
          isOptionEqualToValue={(option, value) => option.id === value.id}
          disabled={
            loading || !!error || contexts.length === 0 || multiSelectActive
          }
          renderInput={(params) => (
            <TextField
              {...params}
              label={
                multiSelectActive ? "Multi-select active" : "Select Context"
              }
              variant="outlined"
              size="small"
              fullWidth
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <React.Fragment>
                    {loading ? (
                      <CircularProgress color="inherit" size={20} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </React.Fragment>
                ),
              }}
            />
          )}
          loading={loading}
          sx={{ mb: multiSelectActive ? 0 : 2 }} // Remove margin if buttons are shown below
        />
        {error && (
          <Typography color="error" variant="caption">
            {error}
          </Typography>
        )}
      </Box>

      {/* Show Delete/Cancel buttons when multi-select is active */}
      {multiSelectActive && (
        <Box sx={{ px: 2, pb: 1 }}>
          <Stack direction="row" spacing={1} justifyContent="space-between">
            <Button
              variant="contained"
              color="error"
              size="small"
              startIcon={<DeleteIcon />}
              onClick={handleDeleteClick}
              disabled={loading || selectedIds.size === 0}
            >
              Delete ({selectedIds.size})
            </Button>
            <Button
              variant="outlined"
              size="small"
              startIcon={<CancelIcon />}
              onClick={handleCancelMultiSelect}
              disabled={loading}
            >
              Cancel
            </Button>
          </Stack>
        </Box>
      )}

      <Divider />

      <List sx={{ flexGrow: 1, overflowY: "auto", pt: 0 }}>
        {contexts.map((context) => {
          const isSelected = selectedIds.has(context.id);
          const showCheckbox = multiSelectActive || hoveredId === context.id;
          return (
            <ListItem
              key={context.id}
              disablePadding
              secondaryAction={
                showCheckbox && (
                  <Checkbox
                    edge="end"
                    onChange={() => handleCheckboxChange(context.id)}
                    checked={isSelected}
                    onClick={(e) => e.stopPropagation()} // Prevent item click when clicking checkbox
                    size="small"
                  />
                )
              }
              onMouseEnter={() => setHoveredId(context.id)}
              onMouseLeave={() => setHoveredId(null)}
              sx={{ bgcolor: isSelected ? "action.selected" : "transparent" }}
            >
              <ListItemButton
                // Don't visually select if in multi-select mode, rely on checkbox/bgcolor
                selected={!multiSelectActive && context.id === currentContextId}
                onClick={() => handleListItemClick(context.id)}
                sx={{ pr: showCheckbox ? 6 : 2 }} // Add padding to prevent text overlap with checkbox
              >
                <ListItemText primary={getOptionLabel(context)} />
              </ListItemButton>
            </ListItem>
          );
        })}
        {contexts.length === 0 && !loading && !error && (
          <ListItem>
            <ListItemText
              secondary="No contexts found. Create one!"
              sx={{ textAlign: "center" }}
            />
          </ListItem>
        )}
      </List>
    </Box>
  );
};

export default BusinessContextSidebar;
