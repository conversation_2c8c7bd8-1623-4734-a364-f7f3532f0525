# Test Database Configuration
TEST_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/writer_v2_test


# Supabase Configuration
# JWT Configuration
SUPABASE_URL=https://zhtmqikyqgawninsgiup.supabase.co
JWT_SECRET_KEY=test-jwt-secret-key-for-testing
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpodG1xaWt5cWdhd25pbnNnaXVwIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MjQyMjgwMSwiZXhwIjoyMDU3OTk4ODAxfQ.lrIBB6uOSIIcYNxEDaVigdxdgLCAf0zJDi04c_cJQM8
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpodG1xaWt5cWdhd25pbnNnaXVwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI0MjI4MDEsImV4cCI6MjA1Nzk5ODgwMX0.LrREivYGkJNJLxJynBOw2VM9nm7_MzpTEiJgX9PLbRY

JWT_ACCESS_TOKEN_EXPIRES=3600

# API Keys
OPENAI_API_KEY=test-key
YOUTUBE_API_KEY=test-key

# Application Configuration
# FastAPI Configuration (no Flask variables needed)
ENVIRONMENT=testing
DEBUG=True

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_FILE=logs/writer_v2_test.log

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000

# SSL Configuration
