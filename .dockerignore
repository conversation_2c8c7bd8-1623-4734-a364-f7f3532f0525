# Git
.git
.gitignore
.gitattributes
.github/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache/
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Virtual Environment
venv/
ENV/

# IDE specific files
.vscode/
.idea/
*.swp
*.swo
.cursor/

# Logs and databases
*.log
*.sqlite

# Docker temporary files
!Dockerfile
!docker-compose.yml
!docker-compose.*.yml
.docker/

# Node modules
node_modules/
npm-debug.log
.npm

# Other backup files
backup/
backup_files/

# Never ignore these files
!create_db.py
!supabase_health_check.py
!app.py
!start-backend.sh
!requirements.txt

# Frontend (since it has its own Dockerfile)
frontend/

# Documentation
docs/
# *.md
# !README.md

# Local development
Set Up/
*.bat
debug_*.py
test_*.sh
fix_issues.py
init_db.py
monitoring.py

# Misc
.DS_Store
Thumbs.db 