from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Literal
from datetime import datetime
import uuid


# Assuming TranscriptSegment is defined elsewhere or defined here
class TranscriptSegment(BaseModel):
    start_time: float
    end_time: float
    text: str

    model_config = ConfigDict(from_attributes=True)


class YouTubeVideo(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)  # Internal DB ID
    user_id: uuid.UUID
    youtube_id: str = Field(..., max_length=20)  # YouTube's video ID
    title: str
    channel_id: Optional[str] = None
    channel_title: Optional[str] = None
    description: Optional[str] = None
    published_at: Optional[datetime] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[str] = None  # ISO 8601 duration format (e.g., PT5M30S)
    view_count: Optional[int] = None
    like_count: Optional[int] = None
    comment_count: Optional[int] = None
    # Removed tags as per previous observation
    # tags: Optional[List[str]] = None
    transcript: Optional[str] = None
    transcript_segments: Optional[List[TranscriptSegment]] = None
    summary: Optional[str] = None
    fetched_at: datetime = Field(default_factory=datetime.now)
    processing_status: Literal["pending", "processing", "completed", "failed"] = (
        "pending"
    )
    content_item_id: Optional[uuid.UUID] = None  # Link to generated content
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    model_config = ConfigDict(from_attributes=True)
