"""
FastAPI authentication dependencies for Writer v2.
This module replaces the Flask supabase_auth_required decorator with FastAPI dependency injection.
"""

import logging
from typing import Annotated, Dict, Any, Optional

from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.repositories.supabase_user_repository import UserRepository, SupabaseAuthError
from app.utils.supabase_auth import verify_supabase_token, AuthenticationException
from app.dependencies import get_user_repository

logger = logging.getLogger(__name__)

# OAuth2 scheme for token extraction
oauth2_scheme = HTTPBearer()


class CurrentUser:
    """
    Current user model for dependency injection.
    Contains user data and authenticated Supabase client.
    """
    def __init__(self, user_data: Dict[str, Any], user_id: str):
        self.user_data = user_data
        self.user_id = user_id
        self.id = user_id  # For backward compatibility
    
    def get(self, key: str, default=None):
        """Dictionary-like access for backward compatibility."""
        return self.user_data.get(key, default)
    
    def __getitem__(self, key: str):
        """Dictionary-like access for backward compatibility."""
        return self.user_data[key]


async def get_current_user(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(oauth2_scheme)],
    user_repo: Annotated[UserRepository, Depends(get_user_repository)]
) -> CurrentUser:
    """
    FastAPI dependency to get the current authenticated user.
    
    This replaces the Flask supabase_auth_required decorator.
    It validates the JWT token and returns the authenticated user.
    
    Args:
        credentials: HTTP Bearer token from Authorization header
        user_repo: User repository for database operations
        
    Returns:
        CurrentUser: Authenticated user object
        
    Raises:
        HTTPException: If authentication fails
    """
    token = credentials.credentials
    
    if not token:
        logger.warning("No token provided in Authorization header")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authorization token required",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        logger.debug("Verifying Supabase token")
        user_data = verify_supabase_token(token)
        
        if not user_data:
            logger.warning("Token verification failed")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Extract user ID (handle both dict and object access patterns)
        user_id = user_data.get('id') if hasattr(user_data, 'get') else user_data['id']
        
        logger.info(f"User {user_id} authenticated successfully")
        
        # Return CurrentUser object for dependency injection
        return CurrentUser(user_data=user_data, user_id=user_id)
        
    except SupabaseAuthError as e:
        logger.error(f"SupabaseAuthError during token verification: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Authentication error: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except AuthenticationException as e:
        logger.error(f"AuthenticationException during token verification: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"Unexpected error during token verification: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication failed due to an internal server error",
        )


async def get_current_user_id(
    current_user: Annotated[CurrentUser, Depends(get_current_user)]
) -> str:
    """
    FastAPI dependency to get the current user ID.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        str: User ID
    """
    return current_user.user_id


async def get_optional_current_user(
    request: Request,
    user_repo: Annotated[UserRepository, Depends(get_user_repository)]
) -> Optional[CurrentUser]:
    """
    FastAPI dependency to get the current user if authenticated, None otherwise.
    
    This is useful for endpoints that work with or without authentication.
    
    Args:
        request: FastAPI request object
        user_repo: User repository for database operations
        
    Returns:
        Optional[CurrentUser]: Authenticated user object or None
    """
    auth_header = request.headers.get("Authorization")
    
    if not auth_header or not auth_header.startswith("Bearer "):
        return None
    
    token = auth_header.split(" ")[1]
    
    try:
        user_data = verify_supabase_token(token)
        if user_data:
            user_id = user_data.get('id') if hasattr(user_data, 'get') else user_data['id']
            return CurrentUser(user_data=user_data, user_id=user_id)
    except Exception as e:
        logger.debug(f"Optional authentication failed: {e}")
    
    return None


# Type aliases for cleaner dependency injection
CurrentUserType = Annotated[CurrentUser, Depends(get_current_user)]
CurrentUserIdType = Annotated[str, Depends(get_current_user_id)]
OptionalCurrentUserType = Annotated[Optional[CurrentUser], Depends(get_optional_current_user)]
