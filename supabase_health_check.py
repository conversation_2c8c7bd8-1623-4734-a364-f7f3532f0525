import os
import sys
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_supabase_health():
    """Enhanced health check to verify Supabase connectivity and required environment variables"""

    # Check for required environment variables
    required_vars = ["SUPABASE_URL", "SUPABASE_KEY"]
    missing_vars = []

    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"Health check failed: Missing required environment variables: {', '.join(missing_vars)}")
        print("Required variables: SUPABASE_URL, SUPABASE_KEY")
        return False

    supabase_url = os.environ.get("SUPABASE_URL")
    supabase_key = os.environ.get("SUPABASE_KEY")

    try:
        # Test connectivity to Supabase with proper API key
        headers = {
            "apikey": supabase_key,
            "Authorization": f"Bearer {supabase_key}",
            "Content-Type": "application/json"
        }
        response = requests.get(f"{supabase_url}/rest/v1/", headers=headers, timeout=10)

        # Check if we get a successful response
        if response.status_code == 200:
            print(f"✓ Supabase health check: SUCCESS - Connected and authenticated (status {response.status_code})")
            return True
        elif response.status_code == 401:
            print(f"✗ Supabase health check failed: Authentication failed (status {response.status_code})")
            print("Check your SUPABASE_KEY environment variable")
            return False
        else:
            print(f"⚠ Supabase health check: URL reachable but got status code {response.status_code}")
            # Still return True if we can reach Supabase, even with unexpected status
            return True

    except requests.exceptions.Timeout:
        print("✗ Supabase health check failed: Request timeout (>10s)")
        return False
    except requests.exceptions.ConnectionError:
        print("✗ Supabase health check failed: Cannot connect to Supabase URL")
        print(f"Check your SUPABASE_URL: {supabase_url}")
        return False
    except Exception as e:
        print(f"✗ Supabase health check failed: {str(e)}")
        return False

def check_basic_health():
    """Basic health check that always passes - for Docker health check"""
    try:
        # Just check if we can import the main app
        import main
        # Verify the app exists
        if hasattr(main, 'app'):
            print("✓ Basic health check: Application can be imported and app exists")
            return True
        else:
            print("✗ Basic health check failed: main.app not found")
            return False
    except Exception as e:
        print(f"✗ Basic health check failed: {str(e)}")
        return False

if __name__ == "__main__":
    # For Docker health check, use basic check first, then Supabase
    if "--basic" in sys.argv:
        sys.exit(0 if check_basic_health() else 1)
    else:
        # Full health check including Supabase
        basic_ok = check_basic_health()
        supabase_ok = check_supabase_health()

        if basic_ok and supabase_ok:
            print("✓ All health checks passed!")
            sys.exit(0)
        elif basic_ok:
            print("⚠ Basic health check passed, but Supabase check failed")
            sys.exit(0)  # Still allow container to be healthy
        else:
            print("✗ Health checks failed!")
            sys.exit(1)