import { Session, User } from '@supabase/supabase-js';
import authReducer, {
  initialState,
  setSupabaseSession,
  AuthState,
} from '../store/auth/authSlice';

// Mock Supabase Session and User for testing
const mockUser: User = {
  id: 'test-user-id',
  app_metadata: { provider: 'email' },
  user_metadata: { name: 'Test User' },
  aud: 'authenticated',
  created_at: new Date().toISOString(),
  // Add other required User properties if needed by your logic
};

const mockSession: Session = {
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  expires_in: 3600,
  token_type: 'bearer',
  user: mockUser,
  // Add other required Session properties if needed
};

describe('authSlice reducer', () => {
  it('should handle initial state', () => {
    expect(authReducer(undefined, { type: 'unknown' })).toEqual({
      supabaseSession: null,
      supabaseUser: null,
      isLoading: true,
      error: null,
      lastActivity: expect.any(Number), // Check if timestamp is roughly correct if needed
    });
  });

  it('should handle setSupabaseSession with a session payload', () => {
    const previousState: AuthState = {
      ...initialState,
      isLoading: true, // Simulate initial loading state
      error: 'Some previous error', // Simulate a previous error
    };
    const newState = authReducer(previousState, setSupabaseSession(mockSession));
    expect(newState.supabaseSession).toEqual(mockSession);
    expect(newState.supabaseUser).toEqual(mockUser);
    expect(newState.isLoading).toBe(false);
    expect(newState.error).toBeNull(); // Error should be cleared
  });

  it('should handle setSupabaseSession with a null payload', () => {
    const previousState: AuthState = {
      ...initialState,
      supabaseSession: mockSession, // Simulate a logged-in state
      supabaseUser: mockUser,
      isLoading: false,
    };
    const newState = authReducer(previousState, setSupabaseSession(null));
    expect(newState.supabaseSession).toBeNull();
    expect(newState.supabaseUser).toBeNull();
    expect(newState.isLoading).toBe(false); // Loading should remain false
    expect(newState.error).toBeNull();
  });

  // Add more tests here for other reducers like setLoading, setError, clearError, updateActivity if needed
}); 