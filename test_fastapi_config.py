#!/usr/bin/env python3
"""
Test FastAPI configuration and basic functionality after test fixes.
"""

import pytest
from fastapi.testclient import TestClient


def test_fastapi_app_import():
    """Test that we can import the FastAPI app without errors."""
    from main import app
    assert app is not None
    assert hasattr(app, 'title')


def test_fastapi_testclient_creation():
    """Test that we can create a TestClient."""
    from main import app
    client = TestClient(app)
    assert client is not None


def test_health_endpoints():
    """Test basic health endpoints work."""
    from main import app
    client = TestClient(app)
    
    # Test /api/health endpoint
    response = client.get("/api/health")
    assert response.status_code == 200
    
    # Test root health endpoint if it exists
    response = client.get("/health")
    # Should be either 200 (exists) or 404 (doesn't exist), but not 500 (server error)
    assert response.status_code in [200, 404]


def test_environment_configuration():
    """Test that environment configuration is working."""
    import os
    from dotenv import load_dotenv
    
    # Load test environment
    load_dotenv(dotenv_path=".env.test", override=True)
    
    # Check that Flask variables are not present
    assert "FLASK_APP" not in os.environ or os.environ.get("FLASK_APP") != "app.py"
    
    # Check that required variables are present
    assert "SUPABASE_URL" in os.environ
    assert "SUPABASE_KEY" in os.environ


@pytest.mark.asyncio
async def test_async_support():
    """Test that async support is working in pytest."""
    import asyncio
    
    # Simple async test
    await asyncio.sleep(0.001)
    assert True


def test_openai_adapter_import():
    """Test that OpenAI adapter can be imported and used without Flask context."""
    from app.ai_assistant.openai_adapter import OpenAIAdapter
    
    # Should be able to create adapter without Flask app context
    adapter = OpenAIAdapter(api_key="test-key")
    assert adapter is not None
    assert adapter.api_key == "test-key"


if __name__ == "__main__":
    print("🔍 Running FastAPI configuration tests...")
    
    try:
        test_fastapi_app_import()
        print("✅ FastAPI app import: PASSED")
        
        test_fastapi_testclient_creation()
        print("✅ TestClient creation: PASSED")
        
        test_health_endpoints()
        print("✅ Health endpoints: PASSED")
        
        test_environment_configuration()
        print("✅ Environment configuration: PASSED")
        
        test_openai_adapter_import()
        print("✅ OpenAI adapter import: PASSED")
        
        print("\n🎉 All FastAPI configuration tests passed!")
        
    except Exception as e:
        print(f"\n❌ FastAPI configuration tests failed: {e}")
        import traceback
        traceback.print_exc()
