"""
Configuration file for Supabase repository tests
Provides common fixtures and utilities
"""

import os
import pytest
import uuid
from datetime import datetime, timezone
from dotenv import load_dotenv
from supabase import create_client
from unittest.mock import MagicMock  # Import MagicMock

# Load environment variables
load_dotenv()

# Supabase configuration
supabase_url = os.environ.get("SUPABASE_URL")
supabase_key = os.environ.get("SUPABASE_KEY")
skip_supabase_tests = not (supabase_url and supabase_key)


@pytest.fixture(scope="session")  # Keep service client session-scoped
def supabase_client():
    """Fixture to provide a Supabase client (service key) for tests"""
    if skip_supabase_tests:
        pytest.skip("Supabase not configured")

    # Use service key for potential admin actions if needed directly
    service_key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
    if not service_key:
        pytest.fail("SUPABASE_SERVICE_ROLE_KEY not found for supabase_client fixture")

    client = create_client(supabase_url, service_key)
    # set_test_client(client) # Don't set the service client globally by default
    return client


# Import load_dotenv and os if not already present at top level
from dotenv import load_dotenv
import os


@pytest.fixture(scope="function")  # Function scope for isolation
# Add back test_login_response dependency
def authenticated_supabase_client(test_login_response):
    """
    Provides a Supabase client authenticated as the test user via JWT header.
    This client respects RLS policies for the logged-in test user.
    """
    if skip_supabase_tests:
        pytest.skip("Supabase not configured")

    # Extract access token from the nested 'session' object in the login response
    session_data = test_login_response.get("session")
    if not session_data:
        pytest.fail("Login response did not contain a 'session' object.")

    access_token = session_data.get("access_token")
    if not access_token:
        pytest.fail(
            "Failed to get access_token from session data for authenticated_supabase_client"
        )

    # Force reload env vars to ensure ANON_KEY is picked up
    # (Keep this as it ensures the correct key is used for client creation)
    load_dotenv(dotenv_path=".env.test", override=True)
    # Use the ANON KEY for client creation
    anon_key = os.environ.get("SUPABASE_ANON_KEY")
    if not anon_key:
        pytest.fail(
            "SUPABASE_ANON_KEY not found after reloading .env.test in authenticated_supabase_client"
        )

    # Create a new client instance using the ANON key
    auth_client = create_client(supabase_url, anon_key)

    # --- Explicitly set Authorization header for all requests from this client's PostgREST session ---
    auth_header = {"Authorization": f"Bearer {access_token}"}
    auth_client.postgrest.session.headers.update(auth_header)
    # Note: If other client parts (auth, storage) need auth for different operations,
    # their headers might need separate updates.

    yield auth_client


# Removed duplicate yield line

# --- Existing fixtures continue below ---


# Removed conflicting test_user_id fixture.
# The correct test_user_id fixture (from tests/conftest.py, based on login)
# should now be injected automatically by pytest where needed.
@pytest.fixture
def test_user(
    authenticated_supabase_client, test_user_id
):  # MODIFIED: Use authenticated client
    """Fixture to create a test user in Supabase (public users table) and clean up after test"""
    # Create test user data (assuming this mirrors auth.users or is a public profile)
    user_data = {
        "id": test_user_id,  # This ID should come from the authenticated user (test_login_response)
        "email": f"test_{test_user_id}@example.com",  # Email might be redundant if linked via ID
        "name": "Test User",
        "created_at": datetime.now(timezone.utc).isoformat(),
    }

    try:
        # Insert test user using the authenticated client
        response = (
            authenticated_supabase_client.table("users").insert(user_data).execute()
        )
        # Add basic check
        if not response.data:
            pytest.fail(
                f"Failed to insert test user profile for {test_user_id} using authenticated client. Response: {response}"
            )

        yield response.data[0]  # Yield created data
    finally:
        # Clean up test user using the authenticated client
        try:
            authenticated_supabase_client.table("users").delete().eq(
                "id", test_user_id
            ).execute()
        except Exception as e:
            # Log potential cleanup errors but don't fail the test run
            print(
                f"Info: Error cleaning up test user profile {test_user_id} (might be expected if test deleted it): {e}"
            )


@pytest.fixture
def test_content_item_id():
    """Fixture to provide a test content item ID"""
    # Use integer ID if that's the actual schema type - CHANGED TO UUID
    return str(uuid.uuid4())
    # return 99999 # Example integer ID for testing - REMOVED


@pytest.fixture
def test_content_item(
    authenticated_supabase_client, test_user_id, test_content_item_id
):  # MODIFIED: Use authenticated client
    """Fixture to create a test content item in Supabase using authenticated client and clean up after test"""
    # Create test content item
    content_data = {
        "id": test_content_item_id,
        "user_id": test_user_id,  # Should be the authenticated user's ID
        "title": "Test Content Item",
        "content": "This is a test content item",
        "content_type": "text",  # ADDED: Provide required content_type
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat(),
    }

    try:
        # Insert test content item using the authenticated client
        response = (
            authenticated_supabase_client.table("content_items")
            .insert(content_data)
            .execute()
        )
        # Add basic check
        if not response.data:
            pytest.fail(
                f"Failed to insert test content item {test_content_item_id} using authenticated client. Response: {response}"
            )

        yield response.data[0]  # Yield created data
    finally:
        # Clean up test content item using the authenticated client
        try:
            # Ensure delete also respects RLS if applicable (e.g., user can only delete their own)
            authenticated_supabase_client.table("content_items").delete().eq(
                "id", test_content_item_id
            ).eq("user_id", test_user_id).execute()
        except Exception as e:
            print(
                f"Info: Error cleaning up test content item {test_content_item_id} (might be expected if test deleted it): {e}"
            )


@pytest.fixture
def test_business_context_id():
    """Fixture to provide a test business context ID"""
    # Use integer ID if that's the actual schema type - CHANGED TO UUID
    return str(uuid.uuid4())
    # return 88888 # Example integer ID for testing - REMOVED


@pytest.fixture
def test_business_context(
    authenticated_supabase_client, test_user_id, test_business_context_id
):  # MODIFIED: Use authenticated client
    """Fixture to create a test business context in Supabase using authenticated client and clean up after test"""
    # Create test business context
    context_data = {
        "id": test_business_context_id,
        "user_id": test_user_id,  # Should be the authenticated user's ID
        "offer_description": "Test offer description",
        "target_audience": "Test target audience",
        "brand_voice": "Professional and friendly",
        "key_benefits": "Test key benefits",
        "unique_value_proposition": "Test unique value proposition",
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat(),
    }

    try:
        # Insert test business context using the authenticated client
        response = (
            authenticated_supabase_client.table("business_contexts")
            .insert(context_data)
            .execute()
        )
        # Add basic check
        if not response.data:
            pytest.fail(
                f"Failed to insert test business context {test_business_context_id} using authenticated client. Response: {response}"
            )

        yield response.data[0]  # Yield created data
    finally:
        # Clean up test business context using the authenticated client
        try:
            # Ensure delete also respects RLS if applicable (e.g., user can only delete their own)
            authenticated_supabase_client.table("business_contexts").delete().eq(
                "id", test_business_context_id
            ).eq("user_id", test_user_id).execute()
        except Exception as e:
            print(
                f"Info: Error cleaning up test business context {test_business_context_id} (might be expected if test deleted it): {e}"
            )


@pytest.fixture
def test_chat_session_id():
    """Fixture to provide a test chat session ID"""
    # Use integer ID if that's the actual schema type - CHANGED TO UUID
    return str(uuid.uuid4())
    # return 77777 # Example integer ID for testing - REMOVED


@pytest.fixture
def test_chat_session(
    authenticated_supabase_client, test_user_id, test_chat_session_id
):  # Use authenticated client and correct user_id
    """Fixture to create a test chat session using the authenticated client and clean up."""
    # Create test chat session data
    session_data = {
        "id": test_chat_session_id,
        "user_id": test_user_id,  # This should now be the ID from the login
        "title": "Test Auth Chat Session",  # Changed title for clarity
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat(),
        "session_type": "test_auth",  # Add required field if necessary based on table schema
    }

    try:
        # Insert test chat session using the authenticated client
        response = (
            authenticated_supabase_client.table("chat_sessions")
            .insert(session_data)
            .execute()
        )

        # Basic check if insert seemed successful (might need more robust check)
        if not response.data:
            pytest.fail(
                f"Failed to insert test chat session using authenticated client. Response: {response}"
            )

        yield response.data[0]  # Yield the actual created data
    finally:
        # Clean up test chat session using the authenticated client
        try:
            # Use the authenticated client for cleanup as well, respecting RLS if applicable during delete
            authenticated_supabase_client.table("chat_sessions").delete().eq(
                "id", test_chat_session_id
            ).eq("user_id", test_user_id).execute()
        except Exception as e:
            # It might fail if the test deleted it already, which is fine.
            print(
                f"Info: Error during test_chat_session cleanup (might be expected if test deleted it): {e}"
            )


@pytest.fixture
def test_activity_log_id():
    """Fixture to provide a test activity log ID"""
    # Use integer ID if that's the actual schema type - CHANGED TO UUID
    return str(uuid.uuid4())
    # return 66666 # Example integer ID for testing - REMOVED


@pytest.fixture
def mock_request_context(
    supabase_client, test_login_response
):  # ADD test_login_response dependency
    """Fixture to provide a mock Flask request context with Supabase repositories in g,
    initialized with an AUTHENTICATED client context."""
    from flask import g, Flask
    from supabase import create_client  # Ensure create_client is available
    import os
    from dotenv import load_dotenv

    if skip_supabase_tests:
        pytest.skip("Supabase not configured")

    # --- Get Auth Token ---
    # Use 'session' key based on test_login_response structure in tests/conftest.py
    # Adjust key access if the structure differs (e.g., directly under login_data)
    login_data = (
        test_login_response  # Assuming test_login_response returns the dict directly
    )
    session_data = login_data.get("session")  # Get session object
    if not session_data:
        pytest.fail(
            "Login response did not contain a 'session' object in mock_request_context."
        )
    access_token = session_data.get("access_token")  # Get token from session
    if not access_token:
        pytest.fail(
            "Failed to get access_token from test_login_response for mock_request_context"
        )

    # --- Create Authenticated Client for Repositories ---
    # Force reload env vars to ensure ANON_KEY is picked up from .env.test
    load_dotenv(dotenv_path=".env.test", override=True)
    repo_supabase_url = os.environ.get("SUPABASE_URL")
    repo_anon_key = os.environ.get("SUPABASE_ANON_KEY")
    if not repo_supabase_url or not repo_anon_key:
        pytest.fail(
            "SUPABASE_URL or SUPABASE_ANON_KEY not found for repository client in mock_request_context"
        )

    # Create a new client instance specifically for repositories, using ANON key
    auth_repo_client = create_client(repo_supabase_url, repo_anon_key)

    # Set the Authorization header for this client's PostgREST interactions
    auth_header = {"Authorization": f"Bearer {access_token}"}
    auth_repo_client.postgrest.session.headers.update(auth_header)
    # --- End Authenticated Client Setup ---

    app = Flask(__name__)
    app.config["USE_SUPABASE"] = True
    # Set config for potential internal calls to get_supabase_client() within repositories
    # These might still pick up the service key client if not careful, but repo instances below use the auth'd one.
    app.config["SUPABASE_URL"] = supabase_url  # Keep original service URL
    app.config["SUPABASE_KEY"] = supabase_key  # Keep original service key
    app.config["SUPABASE_ANON_KEY"] = repo_anon_key  # Store anon key too if needed

    with app.test_request_context():
        # Keep the original service client available if needed for specific tasks
        g.supabase_service_client = supabase_client  # Rename original for clarity
        # Store the authenticated client used by repos
        g.supabase_auth_client = auth_repo_client

        # Initialize repositories (ensure these exist)
        # It's safer to import within the context if module structure might change
        try:
            # Updated import paths to app.repositories
            from app.repositories.supabase_activity_log_repository import (
                ActivityLogRepository,
            )
            from app.repositories.supabase_content_item_repository import (
                ContentItemRepository,
            )
            from app.repositories.supabase_business_context_repository import (
                BusinessContextRepository,
            )
            from app.repositories.supabase_chat_session_repository import (
                ChatSessionRepository,
            )
            from app.repositories.supabase_feedback_repository import FeedbackRepository
            from app.repositories.supabase_video_data_repository import (
                VideoDataRepository,
            )
            from app.repositories.supabase_user_repository import (
                UserRepository,
            )  # Added user repository

            # *** Initialize repositories with the AUTHENTICATED client ***
            g.activity_log_repository = ActivityLogRepository(auth_repo_client)
            g.content_item_repository = ContentItemRepository(auth_repo_client)
            g.business_context_repository = BusinessContextRepository(auth_repo_client)
            g.chat_session_repository = ChatSessionRepository(auth_repo_client)
            g.feedback_repository = FeedbackRepository(auth_repo_client)
            g.video_data_repository = VideoDataRepository(auth_repo_client)
            g.user_repository = UserRepository(
                auth_repo_client
            )  # Added user repository to g

        except ImportError as e:
            # Make this a failure because tests relying on these repos will fail anyway
            pytest.fail(f"Failed to import repository in mock_request_context: {e}")

        yield app  # Yield the app context


# --- Mocked Request Context for Unit Tests ---


@pytest.fixture
def mock_authenticated_request_context():
    """Fixture to provide a mock Flask request context with MOCKED Supabase repositories in g.
    This is intended for pure unit tests that should NOT hit a real Supabase instance.
    It simulates an authenticated context without performing real login.
    """
    from flask import g, Flask

    # Create a completely mock Supabase client
    mock_client = MagicMock(name="MockSupabaseClientForUnitTests")
    # Configure basic mock behaviors if needed by repositories
    # Example: mock_client.table().select().eq().execute.return_value = MagicMock(data=[...])
    # Often, the repositories themselves will be mocked in the unit tests using this context,
    # so deep configuration of the client mock might not be necessary here.

    app = Flask(__name__)
    app.config["USE_SUPABASE"] = True  # Keep consistent config
    app.config["SUPABASE_URL"] = "mock_url"
    app.config["SUPABASE_KEY"] = "mock_key"
    app.config["SUPABASE_ANON_KEY"] = "mock_anon_key"

    with app.test_request_context():
        # Store the mock client in g for potential use
        g.supabase_service_client = mock_client  # Use a consistent name
        g.supabase_auth_client = mock_client  # Use the same mock for simplicity

        # Initialize repositories with the MOCK client
        try:
            from app.repositories.supabase_activity_log_repository import (
                ActivityLogRepository,
            )
            from app.repositories.supabase_content_item_repository import (
                ContentItemRepository,
            )
            from app.repositories.supabase_business_context_repository import (
                BusinessContextRepository,
            )
            from app.repositories.supabase_chat_session_repository import (
                ChatSessionRepository,
            )
            from app.repositories.supabase_feedback_repository import FeedbackRepository
            from app.repositories.supabase_video_data_repository import (
                VideoDataRepository,
            )
            from app.repositories.supabase_user_repository import UserRepository

            # Create MOCK instances of repositories, passing the MOCK client
            # Often, the tests using this fixture will *also* patch the repository classes themselves,
            # but initializing them here provides a basic structure.
            g.activity_log_repository = ActivityLogRepository(mock_client)
            g.content_item_repository = ContentItemRepository(mock_client)
            g.business_context_repository = BusinessContextRepository(mock_client)
            g.chat_session_repository = ChatSessionRepository(mock_client)
            g.feedback_repository = FeedbackRepository(mock_client)
            g.video_data_repository = VideoDataRepository(mock_client)
            g.user_repository = UserRepository(mock_client)

            # Add a mock user ID to g, simulating an authenticated user for the context
            g.user_id = "mock-user-id-for-unit-test"

        except ImportError as e:
            pytest.fail(
                f"Failed to import repository in mock_authenticated_request_context: {e}"
            )

        yield app  # Yield the app context
