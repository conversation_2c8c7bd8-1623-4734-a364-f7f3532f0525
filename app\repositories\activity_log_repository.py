"""
Supabase repository for activity logs (<PERSON><PERSON><PERSON> <PERSON>tern Implemented)
Handles tracking and retrieving user activity data.

Adapter Pattern & Migration Plan:
This repository currently acts as an adapter to an older database schema.

Phase 1: Adapter Implementation (Current)
- API uses fields like `activity_type`, `title`, `description`, `link_path`, `ip_address`.
- Database stores `action_type` (for `activity_type`), and `details` (JSONB for `title`, `description`, `link_path`),
  and `ip_address` directly.
- This repository handles the mapping between the API representation and the DB schema.

Phase 2: Database Migration (Future)
- Migrate the `user_activities` table schema to directly match the API fields:
  - Rename `action_type` column to `activity_type`.
  - Extract `title`, `description`, `link_path` from the `details` JSONB field into their own dedicated columns.
  - Ensure `ip_address`, `resource_type`, `resource_id` columns are correctly typed and indexed.

Phase 3: Remove Adapter Code (Future)
- Once the database schema matches the API, remove the transformation logic
  (mapping and packing/unpacking of `details`) from this repository.
- The repository methods will then interact directly with the new schema columns.
"""

from datetime import datetime, timezone

from app.repositories.supabase_repository import SupabaseRepository
from app.models.supabase_client import get_supabase_client
from app.utils.errors import SupabaseRepositoryError


class ActivityLogRepository(SupabaseRepository):
    """Repository for managing activity logs in Supabase"""

    def __init__(self):
        """Initialize with Supabase client instance"""
        super().__init__(
            table_name="user_activities"
        )  # Use correct table name, removed supabase_client

    async def log_activity(
        self,
        user_id,
        activity_type,  # Kept as activity_type for API
        title,
        description=None,
        resource_type=None,
        resource_id=None,
        link_path=None,
        ip_address=None,  # Added ip_address parameter
    ):
        """
        Log a user activity (Adapter for existing DB schema)

        Args:
            user_id: User ID
            activity_type: Type of activity (e.g., 'login', 'create_content'). Will be stored as 'action_type'.
            title: A short title describing the activity. Stored in 'details' JSONB field.
            description: Optional longer description of the activity. Stored in 'details' JSONB field.
            resource_type: Optional type of the related resource (e.g., 'content_item', 'chat_session').
            resource_id: Optional ID of the related resource.
            link_path: Optional path to view the related resource (e.g., '/content/123'). Stored in 'details' JSONB field.
            ip_address: Optional IP address of the user.

        Returns:
            Activity log data if successful, None otherwise (or raises SupabaseRepositoryError)
        """
        try:
            client = await get_supabase_client()
            
            details_json = {}
            if title is not None: # Ensure title is not None before adding
                details_json["title"] = title
            if description is not None:
                details_json["description"] = description
            if link_path is not None:
                details_json["link_path"] = link_path

            activity_data = {
                "user_id": user_id,
                "action_type": activity_type,  # Map to DB 'action_type'
                "details": details_json,       # Pack into DB 'details'
                "created_at": datetime.utcnow().isoformat(),
            }

            if resource_type is not None:
                activity_data["resource_type"] = resource_type
            if resource_id is not None:
                activity_data["resource_id"] = resource_id
            if ip_address is not None:
                activity_data["ip_address"] = ip_address

            result = (
                await client.table(self.table_name).insert(activity_data).execute()
            )

            if result.data:
                # Transform DB result back to API representation before returning
                db_row = result.data[0]
                api_row = {
                    "id": db_row.get("id"), # Assuming id is returned by insert
                    "user_id": db_row.get("user_id"),
                    "activity_type": db_row.get("action_type"), # Map back
                    "title": db_row.get("details", {}).get("title"),
                    "description": db_row.get("details", {}).get("description"),
                    "link_path": db_row.get("details", {}).get("link_path"),
                    "resource_type": db_row.get("resource_type"),
                    "resource_id": db_row.get("resource_id"),
                    "ip_address": db_row.get("ip_address"),
                    "created_at": db_row.get("created_at"),
                }
                return api_row
            # Original code returned None, but now we raise SupabaseRepositoryError
            # Raising an error if no data is returned from insert is generally better.
            raise SupabaseRepositoryError(f"Error logging activity for user {user_id}: No data returned from insert", response=result)

        except Exception as e:
            # Ensure any exception is wrapped in SupabaseRepositoryError if not already
            if not isinstance(e, SupabaseRepositoryError):
                raise SupabaseRepositoryError(f"Error logging activity for user {user_id}: {str(e)}", original_exception=e)
            raise e # Re-raise if already SupabaseRepositoryError

    async def get_user_activities(
        self,
        user_id,
        limit=50,
        offset=0,
        activity_type=None,  # API uses activity_type
        resource_type=None,
        from_date=None,
        to_date=None,
    ):
        """
        Get activities for a specific user with optional filtering (Adapter for existing DB schema)

        Args:
            user_id: User ID
            limit: Maximum number of results to return
            offset: Pagination offset
            activity_type: Filter by specific activity type (maps to 'action_type' in DB).
            resource_type: Filter by specific resource type
            from_date: Filter from date (ISO format)
            to_date: Filter to date (ISO format)

        Returns:
            List of activity records (API representation)
        """
        try:
            client = await get_supabase_client()
            query = (
                client.table(self.table_name).select("*").eq("user_id", user_id)
            )

            if activity_type: # API parameter
                query = query.eq("action_type", activity_type)  # DB column is action_type

            if resource_type:
                query = query.eq("resource_type", resource_type)

            if from_date:
                query = query.gte("created_at", from_date)  # Use created_at

            if to_date:
                query = query.lte("created_at", to_date)  # Use created_at

            # Order by timestamp descending (newest first)
            query = query.order("created_at", desc=True)  # Use created_at

            # Apply pagination
            query = query.range(offset, offset + limit - 1)

            # Execute query
            result = await query.execute()

            # Transform DB results to API representation
            api_results = []
            if result.data:
                for db_row in result.data:
                    details = db_row.get("details", {})
                    api_row = {
                        "id": db_row.get("id"),
                        "user_id": db_row.get("user_id"),
                        "activity_type": db_row.get("action_type"), # Map DB 'action_type' to API 'activity_type'
                        "title": details.get("title"),
                        "description": details.get("description"),
                        "link_path": details.get("link_path"),
                        "resource_type": db_row.get("resource_type"),
                        "resource_id": db_row.get("resource_id"),
                        "ip_address": db_row.get("ip_address"),
                        "created_at": db_row.get("created_at"),
                    }
                    api_results.append(api_row)
            return api_results

        except Exception as e:
            if not isinstance(e, SupabaseRepositoryError):
                 raise SupabaseRepositoryError(f"Error getting activities for user {user_id}: {str(e)}", original_exception=e)
            raise e

    async def get_resource_activities(self, user_id, resource_type, resource_id, limit=20):
        """
        Get activities for a specific resource (Adapter for existing DB schema)

        Args:
            user_id: User ID
            resource_type: Resource type
            resource_id: Resource ID
            limit: Maximum number of results

        Returns:
            List of activity records (API representation)
        """
        try:
            client = await get_supabase_client()
            result = (
                await client.table(self.table_name)
                .select("*")
                .eq("user_id", user_id)
                .eq("resource_type", resource_type)
                .eq("resource_id", resource_id)
                .order("created_at", desc=True)
                .limit(limit)
                .execute()
            )

            # Transform DB results to API representation
            api_results = []
            if result.data:
                for db_row in result.data:
                    details = db_row.get("details", {})
                    api_row = {
                        "id": db_row.get("id"),
                        "user_id": db_row.get("user_id"),
                        "activity_type": db_row.get("action_type"), # Map DB 'action_type' to API 'activity_type'
                        "title": details.get("title"),
                        "description": details.get("description"),
                        "link_path": details.get("link_path"),
                        "resource_type": db_row.get("resource_type"),
                        "resource_id": db_row.get("resource_id"),
                        "ip_address": db_row.get("ip_address"),
                        "created_at": db_row.get("created_at"),
                    }
                    api_results.append(api_row)
            return api_results

        except Exception as e:
            if not isinstance(e, SupabaseRepositoryError):
                raise SupabaseRepositoryError(
                    f"Error getting resource activities for {resource_type}/{resource_id}: {str(e)}", original_exception=e
                )
            raise e

    async def get_login_activities(self, user_id, limit=10):
        """
        Get login activities for a user (Adapter for existing DB schema)

        Args:
            user_id: User ID
            limit: Maximum number of results

        Returns:
            List of login activity records (API representation)
        """
        try:
            client = await get_supabase_client()
            result = (
                await client.table(self.table_name)
                .select("*")
                .eq("user_id", user_id)
                .eq("action_type", "login")  # DB column is action_type
                .order("created_at", desc=True)
                .limit(limit)
                .execute()
            )

            # Transform DB results to API representation
            api_results = []
            if result.data:
                for db_row in result.data:
                    details = db_row.get("details", {})
                    api_row = {
                        "id": db_row.get("id"),
                        "user_id": db_row.get("user_id"),
                        "activity_type": db_row.get("action_type"), # Map DB 'action_type' to API 'activity_type'
                        "title": details.get("title"),
                        "description": details.get("description"),
                        "link_path": details.get("link_path"),
                        "resource_type": db_row.get("resource_type"),
                        "resource_id": db_row.get("resource_id"),
                        "ip_address": db_row.get("ip_address"),
                        "created_at": db_row.get("created_at"),
                    }
                    api_results.append(api_row)
            return api_results

        except Exception as e:
            if not isinstance(e, SupabaseRepositoryError):
                raise SupabaseRepositoryError(f"Error getting login activities for user {user_id}: {str(e)}", original_exception=e)
            raise e

    async def get_activity_counts_by_type(self, user_id, from_date=None):
        """
        Get count of activities grouped by type (Adapter for existing DB schema)

        Args:
            user_id: User ID
            from_date: Optional start date in ISO format

        Returns:
            Dictionary with activity types (API representation) as keys and counts as values
        """
        try:
            client = await get_supabase_client()
            query_string = f"""
                SELECT action_type, COUNT(*) as count
                FROM {self.table_name}
                WHERE user_id = '{user_id}'
            """

            if from_date:
                query_string += f" AND created_at >= '{from_date}'"

            query_string += " GROUP BY action_type ORDER BY count DESC" # Group by DB 'action_type'

            result = await client.rpc("execute_sql", {"query": query_string}).execute()

            # Transform to dictionary with API representation of activity_type
            api_counts = {}
            if result.data:
                for row in result.data:
                    # row["action_type"] is from DB, map to API "activity_type" for keys
                    api_counts[row["action_type"]] = row["count"]
            return api_counts

        except Exception as e:
            if not isinstance(e, SupabaseRepositoryError):
                raise SupabaseRepositoryError(f"Error getting activity counts for user {user_id}: {str(e)}", original_exception=e)
            raise e
