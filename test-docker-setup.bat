@echo off
echo ========================================
echo Docker Compose Setup Test Script
echo ========================================

echo.
echo Step 1: Checking Docker Desktop status...
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Desktop is not running!
    echo Please start Docker Desktop and wait for it to fully initialize.
    echo Then run this script again.
    pause
    exit /b 1
)
echo ✓ Docker Desktop is running

echo.
echo Step 2: Validating Docker Compose configuration...
docker-compose config >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Compose configuration is invalid!
    echo Running detailed validation...
    docker-compose config
    pause
    exit /b 1
)
echo ✓ Docker Compose configuration is valid

echo.
echo Step 3: Checking required files...
if not exist "Dockerfile.backend" (
    echo ERROR: Dockerfile.backend not found!
    exit /b 1
)
if not exist "frontend\Dockerfile.dev" (
    echo ERROR: frontend\Dockerfile.dev not found!
    exit /b 1
)
if not exist ".env.local" (
    echo ERROR: .env.local not found!
    exit /b 1
)
if not exist "requirements.txt" (
    echo ERROR: requirements.txt not found!
    exit /b 1
)
if not exist "main.py" (
    echo ERROR: main.py not found!
    exit /b 1
)
echo ✓ All required files are present

echo.
echo Step 4: Testing Supabase health check...
python supabase_health_check.py
if %errorlevel% neq 0 (
    echo WARNING: Supabase health check failed!
    echo This might cause issues with the backend service.
    echo Please check your Supabase configuration in .env.local
)

echo.
echo Step 5: Building Docker images (this may take a few minutes)...
docker-compose build --no-cache
if %errorlevel% neq 0 (
    echo ERROR: Docker build failed!
    echo Check the output above for specific errors.
    pause
    exit /b 1
)
echo ✓ Docker images built successfully

echo.
echo Step 6: Starting services...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ERROR: Failed to start services!
    echo Check the output above for specific errors.
    pause
    exit /b 1
)

echo.
echo Step 7: Waiting for services to be ready...
timeout /t 30 /nobreak >nul

echo.
echo Step 8: Checking service status...
docker-compose ps

echo.
echo Step 9: Testing backend health...
curl -f http://localhost:5000/health >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Backend health check failed!
    echo Backend might still be starting up.
)

echo.
echo Step 10: Testing frontend...
curl -f http://localhost:3000 >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Frontend health check failed!
    echo Frontend might still be starting up.
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Services should be available at:
echo - Backend API: http://localhost:5000
echo - Frontend: http://localhost:3000
echo - API Documentation: http://localhost:5000/docs
echo.
echo To view logs: docker-compose logs -f
echo To stop services: docker-compose down
echo.
pause
