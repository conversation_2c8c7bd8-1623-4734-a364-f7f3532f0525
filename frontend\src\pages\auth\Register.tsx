import React, { useState } from "react";
import { Link as RouterLink, useNavigate } from "react-router-dom";
import {
  Box,
  Button,
  TextField,
  Typography,
  Link,
  InputAdornment,
  IconButton,
  CircularProgress,
  <PERSON>ert,
  <PERSON>rid,
  <PERSON><PERSON>,
  <PERSON>,
  StepLabel,
} from "@mui/material";
import {
  Visibility,
  VisibilityOff,
  PersonAdd as PersonAddIcon,
  <PERSON>For<PERSON> as ArrowForwardIcon,
  ArrowBack as ArrowBackIcon,
} from "@mui/icons-material";
import { motion, AnimatePresence } from "framer-motion";
import {
  selectAuthLoading,
  selectAuthError,
  clearError,
} from "../../store/auth/authSlice";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { useAuth } from "../../contexts/AuthContext";

const Register: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { signUp, error: contextError, loading: contextLoading } = useAuth();
  const authLoading = useAppSelector(selectAuthLoading);
  const authError = useAppSelector(selectAuthError);
  const isLoading = authLoading || contextLoading;
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [formErrors, setFormErrors] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // Clear field-specific error when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors({
        ...formErrors,
        [name]: "",
      });
    }
  };

  // Validate first step (credentials)
  const validateFirstStep = () => {
    const errors = {
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
    };
    let isValid = true;

    // Username validation
    if (!formData.username.trim()) {
      errors.username = "Username is required";
      isValid = false;
    } else if (formData.username.length < 3) {
      errors.username = "Username must be at least 3 characters";
      isValid = false;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email.trim()) {
      errors.email = "Email is required";
      isValid = false;
    } else if (!emailRegex.test(formData.email)) {
      errors.email = "Please enter a valid email address";
      isValid = false;
    }

    // Password validation
    if (!formData.password) {
      errors.password = "Password is required";
      isValid = false;
    } else if (formData.password.length < 8) {
      errors.password = "Password must be at least 8 characters";
      isValid = false;
    }

    // Confirm password validation
    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Handle next step
  const handleNext = () => {
    if (activeStep === 0 && !validateFirstStep()) {
      return;
    }
    setActiveStep((prevStep) => prevStep + 1);
  };

  // Handle back step
  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    dispatch(clearError()); // Clear previous Redux errors
    setError(null); // Clear local error state

    if (activeStep !== 2) return; // Should only submit on the last step

    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    // Use only the AuthContext signUp method
    try {
      const result = await signUp(formData.email, formData.password, {
        username: formData.username,
        firstName: formData.firstName,
        lastName: formData.lastName,
      });

      if (result.success) {
        // Handle success - maybe navigate or show message
        // Removed registration success log
        // Check if there was a non-blocking error message (like email confirmation needed)
        if (result.error) {
          setError(result.error);
        } else {
          setError("Registration successful! Please log in.");
          setTimeout(() => navigate("/login"), 3000);
        }
      } else {
        // Handle specific sign-up failure from context
        setError(result.error || "Registration failed. Please try again.");
      }
    } catch (contextErr: any) {
      // Catch any unexpected errors from the context method itself
      console.error("Context signUp error:", contextErr);
      setError(
        contextErr.message ||
          "An unexpected error occurred during registration."
      );
    }
  };

  // Toggle password visibility
  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Step content
  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Username"
              name="username"
              autoComplete="username"
              autoFocus
              value={formData.username}
              onChange={handleChange}
              error={!!formErrors.username}
              helperText={formErrors.username}
              disabled={isLoading}
              InputProps={{
                sx: {
                  borderRadius: 2,
                  "&.Mui-focused": {
                    boxShadow: "0 0 0 2px rgba(99, 102, 241, 0.2)",
                  },
                },
              }}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="Email Address"
              name="email"
              autoComplete="email"
              value={formData.email}
              onChange={handleChange}
              error={!!formErrors.email}
              helperText={formErrors.email}
              disabled={isLoading}
              InputProps={{
                sx: {
                  borderRadius: 2,
                  "&.Mui-focused": {
                    boxShadow: "0 0 0 2px rgba(99, 102, 241, 0.2)",
                  },
                },
              }}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type={showPassword ? "text" : "password"}
              id="password"
              autoComplete="new-password"
              value={formData.password}
              onChange={handleChange}
              error={!!formErrors.password}
              helperText={formErrors.password}
              disabled={isLoading}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
                sx: {
                  borderRadius: 2,
                  "&.Mui-focused": {
                    boxShadow: "0 0 0 2px rgba(99, 102, 241, 0.2)",
                  },
                },
              }}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              name="confirmPassword"
              label="Confirm Password"
              type={showPassword ? "text" : "password"}
              id="confirmPassword"
              autoComplete="new-password"
              value={formData.confirmPassword}
              onChange={handleChange}
              error={!!formErrors.confirmPassword}
              helperText={formErrors.confirmPassword}
              disabled={isLoading}
              InputProps={{
                sx: {
                  borderRadius: 2,
                  "&.Mui-focused": {
                    boxShadow: "0 0 0 2px rgba(99, 102, 241, 0.2)",
                  },
                },
              }}
            />
          </motion.div>
        );
      case 1:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  margin="normal"
                  fullWidth
                  id="firstName"
                  label="First Name"
                  name="firstName"
                  autoComplete="given-name"
                  value={formData.firstName}
                  onChange={handleChange}
                  disabled={isLoading}
                  InputProps={{
                    sx: {
                      borderRadius: 2,
                      "&.Mui-focused": {
                        boxShadow: "0 0 0 2px rgba(99, 102, 241, 0.2)",
                      },
                    },
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  margin="normal"
                  fullWidth
                  id="lastName"
                  label="Last Name"
                  name="lastName"
                  autoComplete="family-name"
                  value={formData.lastName}
                  onChange={handleChange}
                  disabled={isLoading}
                  InputProps={{
                    sx: {
                      borderRadius: 2,
                      "&.Mui-focused": {
                        boxShadow: "0 0 0 2px rgba(99, 102, 241, 0.2)",
                      },
                    },
                  }}
                />
              </Grid>
            </Grid>

            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              By clicking "Create Account", you agree to our Terms of Service
              and Privacy Policy.
            </Typography>
          </motion.div>
        );
      default:
        return "Unknown step";
    }
  };

  return (
    <Box
      sx={{
        marginTop: 4,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        p: 3,
        maxWidth: 600,
        mx: "auto",
      }}
    >
      <Typography component="h1" variant="h4" gutterBottom>
        Create an Account
      </Typography>

      <Stepper activeStep={activeStep} sx={{ width: "100%", mb: 4 }}>
        <Step>
          <StepLabel>Account Details</StepLabel>
        </Step>
        <Step>
          <StepLabel>Profile Information</StepLabel>
        </Step>
      </Stepper>

      {(error || authError) && (
        <Alert severity="error" sx={{ width: "100%", mb: 2 }}>
          {error || authError}
        </Alert>
      )}

      <Box
        component="form"
        onSubmit={handleSubmit}
        sx={{ mt: 1, width: "100%" }}
      >
        <AnimatePresence mode="wait">
          {getStepContent(activeStep)}
        </AnimatePresence>

        <Box sx={{ display: "flex", justifyContent: "space-between", mt: 3 }}>
          {activeStep > 0 && (
            <Button
              onClick={handleBack}
              disabled={isLoading}
              startIcon={<ArrowBackIcon />}
            >
              Back
            </Button>
          )}

          <Box sx={{ flexGrow: 1 }} />

          <Button
            type="submit"
            variant="contained"
            color="primary"
            disabled={isLoading}
            endIcon={activeStep < 1 ? <ArrowForwardIcon /> : <PersonAddIcon />}
            sx={{ minWidth: 120 }}
          >
            {isLoading ? (
              <CircularProgress size={24} />
            ) : activeStep < 1 ? (
              "Next"
            ) : (
              "Register"
            )}
          </Button>
        </Box>

        <Grid container justifyContent="center" sx={{ mt: 3 }}>
          <Grid item>
            <Link component={RouterLink} to="/login" variant="body2">
              Already have an account? Sign in
            </Link>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default Register;
