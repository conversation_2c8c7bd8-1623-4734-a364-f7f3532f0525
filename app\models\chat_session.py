from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any, Literal
from datetime import datetime
import uuid


class ChatMessage(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    chat_session_id: uuid.UUID
    # user_id might be implicit via session, but good to have if needed directly
    user_id: Optional[uuid.UUID] = None
    message_index: Optional[int] = None  # Assuming this exists based on repo usage
    role: Literal["system", "user", "assistant"]
    content: str
    token_count: int = 0
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime = Field(default_factory=datetime.now)

    model_config = ConfigDict(from_attributes=True)


class ChatSession(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID
    title: str
    model_name: Optional[str] = None
    system_prompt: Optional[str] = None
    temperature: Optional[float] = 0.7
    max_tokens: Optional[int] = 500000
    content_item_id: Optional[uuid.UUID] = None
    business_context_id: Optional[uuid.UUID] = None
    session_type: str = "general"
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    messages: Optional[List[ChatMessage]] = (
        None  # For holding messages when fetched together
    )

    model_config = ConfigDict(from_attributes=True)
