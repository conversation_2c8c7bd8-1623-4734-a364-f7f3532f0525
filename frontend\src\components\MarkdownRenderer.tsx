import React from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import type { Components } from "react-markdown";

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

interface CodeProps {
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
  [key: string]: any;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className,
}) => {
  // Pre-processing REMOVED
  /*
  const processedContent = content
    // Replace single newlines with double newlines to create paragraphs
    .replace(/([^\n])\n([^\n])/g, '$1\n\n$2')
    // Preserve consecutive newlines
    .replace(/\n\n+/g, '\n\n');
  */

  const components: Components = {
    // Handle code blocks (Keep this for syntax highlighting)
    code({ inline, className, children, ...props }: CodeProps) {
      const match = /language-(\w+)/.exec(className || "");
      return !inline && match ? (
        <SyntaxHighlighter
          style={vscDarkPlus as any}
          language={match[1]}
          PreTag="div"
        >
          {String(children).replace(/\n$/, "")}
        </SyntaxHighlighter>
      ) : (
        <code className={className} {...props}>
          {children}
        </code>
      );
    },
    // Custom paragraph component REMOVED
    /*
    p({ children }) {
      return (
        <p style={{ whiteSpace: 'pre-wrap', marginBottom: '1em' }}>
          {children}
        </p>
      );
    },
    */
    // Custom br component REMOVED (Let react-markdown handle breaks)
    /*
    br() {
      return <br />;
    },
    */
  };

  return (
    // Removed style={{ whiteSpace: 'pre-wrap' }} from outer div
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={components}
        children={content}
      />
    </div>
  );
};

export default MarkdownRenderer;
