"""
VideoDataRepository for Supabase implementation
Handles operations for YouTube video data
"""

import logging
from datetime import datetime, timezone
from app.repositories.supabase_repository import SupabaseRepository

logger = logging.getLogger(__name__)


class VideoDataRepository(SupabaseRepository):
    """Repository for managing YouTube video data in Supabase"""

    def __init__(self):
        """Initialize with Supabase client"""
        super().__init__(table_name="video_data")

    async def get_by_user_id(self, user_id, limit=20, offset=0):
        """Get video data for a specific user with pagination asynchronously."""
        try:
            table = await self._get_table()
            result = await (
                table
                .select("*")
                .eq("user_id", user_id)
                .order("fetched_at", desc=True)
                .range(offset, offset + limit - 1)
                .execute()
            )
            return result.data
        except Exception as e:
            self.handle_error(f"Error getting videos for user {user_id}", e)
            raise # Re-raise

    async def get_by_id(self, video_id, user_id):
        """Get a specific video by ID asynchronously, ensuring it belongs to the user."""
        try:
            table = await self._get_table()
            result = await (
                table
                .select("*")
                .eq("id", video_id)
                .eq("user_id", user_id)
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None
        except Exception as e:
            self.handle_error(f"Error getting video {video_id} for user {user_id}", e)
            raise # Re-raise

    async def get_by_youtube_id(self, youtube_id, user_id):
        """Get video data by YouTube ID asynchronously."""
        try:
            table = await self._get_table()
            result = await (
                table
                .select("*")
                .eq("youtube_id", youtube_id)
                .eq("user_id", user_id)
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None
        except Exception as e:
            self.handle_error(
                f"Error getting video with YouTube ID {youtube_id} for user {user_id}", e)
            raise # Re-raise

    async def create_video_data(self, *args, **kwargs):
        """Create a new video data entry asynchronously."""
        data_dict_provided = len(args) == 1 and isinstance(args[0], dict)
        actual_data_to_insert = {}
        youtube_id_for_error = "unknown"

        try:
            if data_dict_provided:
                provided_data = args[0]
                user_id = provided_data.get("user_id")
                youtube_id = provided_data.get("youtube_id")
                youtube_id_for_error = youtube_id or "unknown_in_dict"

                if user_id and youtube_id:
                    existing = await self.get_by_youtube_id(youtube_id, user_id)
                    if existing:
                        return existing
                
                actual_data_to_insert = provided_data.copy() # Use a copy
                now = datetime.now(timezone.utc).isoformat()
                actual_data_to_insert.setdefault("created_at", now)
                actual_data_to_insert.setdefault("updated_at", now)
                actual_data_to_insert.setdefault("fetched_at", now) 
                actual_data_to_insert.setdefault("processing_status", "pending")

            elif len(args) >= 2 or ("user_id" in kwargs and "youtube_id" in kwargs):
                if len(args) >= 2:
                    user_id = args[0]
                    youtube_id = args[1]
                    title = args[2] if len(args) > 2 else kwargs.get("title", "Untitled Video")
                else:
                    user_id = kwargs["user_id"]
                    youtube_id = kwargs["youtube_id"]
                    title = kwargs.get("title", "Untitled Video")
                
                youtube_id_for_error = youtube_id or "unknown_in_kwargs"
                metadata = kwargs.get("metadata", {})

                existing = await self.get_by_youtube_id(youtube_id, user_id)
                if existing:
                    return existing

                now = datetime.now(timezone.utc).isoformat()
                actual_data_to_insert = {
                    "user_id": user_id,
                    "youtube_id": youtube_id,
                    "title": title,
                    "fetched_at": now,
                    "created_at": now,
                    "updated_at": now,
                    "processing_status": "pending",
                }
                # Merge metadata carefully, avoiding overwriting core fields
                for key, value in metadata.items():
                    if key not in ["id", "user_id", "youtube_id", "fetched_at", "created_at", "updated_at", "processing_status"]:
                        actual_data_to_insert[key] = value
            else:
                raise ValueError("Invalid arguments provided to create_video_data")

            table = await self._get_table()
            result = await table.insert(actual_data_to_insert).execute()

            if result.data and len(result.data) > 0:
                return result.data[0]
            # If insert returns no data, it's an error condition
            raise SupabaseRepositoryError(f"Failed to create video data for YouTube ID {youtube_id_for_error}: No data returned from insert")
        except Exception as e:
            # Ensure youtube_id_for_error is set before this point if possible
            # The error message construction was a bit complex, simplifying slightly
            self.handle_error(f"Error creating video data for YouTube ID {youtube_id_for_error}", e)
            raise # Re-raise

    async def update_video_data(self, video_id, user_id, data=None, **kwargs):
        """Update an existing video data entry asynchronously."""
        try:
            update_payload = {}
            if data is not None:
                update_payload.update(data)
            if kwargs:
                update_payload.update(kwargs)

            if not update_payload: # Nothing to update
                # Optionally, fetch and return existing if no error is desired
                # For now, consider it a no-op or raise error if update was expected
                return await self.get_by_id(video_id, user_id) 

            if "user_id" in update_payload:
                del update_payload["user_id"]
            if "updated_at" not in update_payload:
                update_payload["updated_at"] = datetime.now(timezone.utc).isoformat()

            table = await self._get_table()
            result = await (
                table.update(update_payload)
                .eq("id", video_id)
                .eq("user_id", user_id)
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            # If update returns no data, it might mean the row wasn't found or RLS issue
            # Check if it exists first to give a clearer error
            existing = await self.get_by_id(video_id, user_id)
            if not existing:
                raise SupabaseRepositoryError(f"Video data {video_id} not found for user {user_id} during update.", status_code=404)
            raise SupabaseRepositoryError(f"Failed to update video data {video_id}: No data returned, RLS or other issue.")

        except Exception as e:
            self.handle_error(f"Error updating video {video_id} for user {user_id}", e)
            raise # Re-raise

    async def update_transcript(
        self, video_id, user_id, transcript, transcript_segments=None
    ):
        """Update video transcript and segments asynchronously."""
        update_data = {"transcript": transcript}
        if transcript_segments is not None:
            update_data["transcript_segments"] = transcript_segments
        return await self.update_video_data(video_id, user_id, data=update_data)

    async def update_processing_status(self, video_id, user_id, status):
        """Update video processing status asynchronously."""
        return await self.update_video_data(video_id, user_id, processing_status=status)

    async def delete_video_data(self, video_id, user_id):
        """Delete video data asynchronously, ensuring it belongs to the user."""
        try:
            # Verify ownership before delete
            existing = await self.get_by_id(video_id, user_id)
            if not existing:
                logger.warning(f"Video data {video_id} not found for user {user_id} during delete attempt.")
                return False # Or raise NotFoundException

            table = await self._get_table()
            await (
                table.delete()
                .eq("id", video_id)
                .eq("user_id", user_id)
                .execute()
            )
            return True
        except Exception as e:
            self.handle_error(f"Error deleting video {video_id} for user {user_id}", e)
            raise # Re-raise

    async def search_transcripts(self, user_id, search_term, limit=10):
        """Search video transcripts using full-text search asynchronously."""
        # Assuming a 'transcript_tsv' tsvector column on video_data table
        try:
            table = await self._get_table()
            response = await (
                table.select("*, content_items(id, title, content_type)") # Example of joining related data
                .text_search("transcript_tsv", f"'{search_term}'", config="english", ts_type="plainto")
                .eq("user_id", user_id)
                .limit(limit)
                .execute()
            )
            return response.data
        except Exception as e:
            self.handle_error(f"Error searching transcripts for user {user_id} with term '{search_term}'", e)
            raise # Re-raise

    async def link_to_content_item(self, video_id, user_id, content_item_id):
        """Link video data to a content item asynchronously."""
        return await self.update_video_data(video_id, user_id, content_item_id=content_item_id)

    async def get_videos_by_processing_status(self, status, limit=50):
        """Get videos by processing status asynchronously (admin/system use)."""
        try:
            table = await self._get_table()
            response = await (
                table.select("*")
                .eq("processing_status", status)
                .order("created_at", desc=True)
                .limit(limit)
                .execute()
            )
            return response.data
        except Exception as e:
            self.handle_error(f"Error getting videos by status '{status}'", e)
            raise # Re-raise
