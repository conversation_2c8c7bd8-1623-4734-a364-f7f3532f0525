import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

from app.repositories.supabase_repository import SupabaseRepository, SupabaseRepositoryError

logger = logging.getLogger(__name__)

class AudienceAnalysisRepository(SupabaseRepository):
    """Repository for managing audience analysis in Supabase"""

    def __init__(self):
        """Initialize with Supabase client"""
        super().__init__(table_name="audience_analysis")

    def get_by_business_context_id(self, business_context_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get audience analysis for a specific business context synchronously."""
        try:
            table = self._get_table()
            result = (
                table
                .select("*")
                .eq("business_context_id", business_context_id)
                .eq("user_id", user_id)
                .limit(1) # Assuming one analysis per context per user, or use maybe_single()
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None
        except Exception as e:
            self.handle_error(f"Error getting audience analysis for business context {business_context_id}", e)
            raise # Re-raise

    def save_audience_analysis(
        self, user_id: str, business_context_id: str, analysis_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Save audience analysis for a business context synchronously."""
        try:
            existing_analysis = self.get_by_business_context_id(business_context_id, user_id)
            now = datetime.utcnow().isoformat()
            table = self._get_table()

            if existing_analysis:
                result = (
                    table
                    .update({
                        "analysis_data": analysis_data,
                        "updated_at": now
                    })
                    .eq("id", existing_analysis["id"])
                    .execute()
                )
            else:
                result = (
                    table
                    .insert({
                        "user_id": user_id,
                        "business_context_id": business_context_id,
                        "analysis_data": analysis_data,
                        "created_at": now,
                        "updated_at": now
                    })
                    .execute()
                )

            if result.data and len(result.data) > 0:
                return result.data[0]
            logger.warning(
                f"Save audience analysis for user {user_id}, context {business_context_id} returned no data."
            )
            # This case implies an issue if an insert/update was expected to return data.
            raise SupabaseRepositoryError("Failed to save audience analysis: No data returned from operation")
        except Exception as e:
            # self.handle_error is called by the base class if this is a direct Supabase client error
            # For other errors, or to ensure it's logged with this context:
            self.handle_error(f"Error saving audience analysis for business context {business_context_id}", e)
            raise # Re-raise SupabaseRepositoryError

    def upsert_analysis(self, business_context_id: str, user_id: str, analysis_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Upsert (insert or update) audience analysis for a business context synchronously."""
        return self.save_audience_analysis(user_id, business_context_id, analysis_data)

    def delete_by_business_context_id(self, business_context_id: str, user_id: str) -> bool:
        """Delete audience analysis for a specific business context synchronously."""
        try:
            table = self._get_table()
            # Ensure the item belongs to the user before deleting if RLS is not solely relied upon
            # existing = self.get_by_business_context_id(business_context_id, user_id)
            # if not existing:
            #     logger.warning(f"Audience analysis for context {business_context_id} not found for user {user_id}.")
            #     return False

            response = (
                table
                .delete()
                .eq("business_context_id", business_context_id)
                .eq("user_id", user_id)
                .execute()
            )
            # Delete operation might return the deleted items in response.data or just confirm success via no error.
            # If response.data is not empty, it means items were deleted.
            # If RLS prevents deletion or no items match, data might be empty.
            # Assuming success if no error is raised is common for delete.
            # To be more precise, one might check response.count if available or if response.data contains expected items.
            logger.info(f"Delete audience analysis for context {business_context_id} attempted. Response: {response}")
            return True # Assume success if no exception, or refine based on response inspection
        except Exception as e:
            self.handle_error(f"Error deleting audience analysis for business context {business_context_id}", e)
            raise # Re-raise
