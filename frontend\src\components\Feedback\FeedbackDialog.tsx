import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Rating,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Box,
  Snackbar,
  Alert,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { api } from "../../services/api";

interface FeedbackDialogProps {
  open: boolean;
  onClose: () => void;
}

const FeedbackDialog: React.FC<FeedbackDialogProps> = ({ open, onClose }) => {
  const theme = useTheme();

  // Feedback state
  const [feedbackType, setFeedbackType] = useState<string>("general");
  const [rating, setRating] = useState<number | null>(null);
  const [comment, setComment] = useState<string>("");
  const [featureRequest, setFeatureRequest] = useState<string>("");
  const [bugReport, setBugReport] = useState<string>("");

  // UI state
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>("");
  const [snackbarSeverity, setSnackbarSeverity] = useState<"success" | "error">(
    "success"
  );

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      const feedbackData = {
        feedback_type: feedbackType,
        rating: rating,
        comment: comment || null,
        feature_request: feedbackType === "feature" ? featureRequest : null,
        bug_report: feedbackType === "bug" ? bugReport : null,
      };

      const response = await api.post("/feedback", feedbackData);

      setSnackbarMessage("Thank you for your feedback!");
      setSnackbarSeverity("success");
      setSnackbarOpen(true);

      // Reset form
      setFeedbackType("general");
      setRating(null);
      setComment("");
      setFeatureRequest("");
      setBugReport("");

      // Close dialog after a short delay
      setTimeout(() => {
        onClose();
      }, 1000);
    } catch (error) {
      console.error("Error submitting feedback:", error);
      setSnackbarMessage("Failed to submit feedback. Please try again.");
      setSnackbarSeverity("error");
      setSnackbarOpen(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>
          <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
            We Value Your Feedback
          </Typography>
        </DialogTitle>

        <DialogContent dividers>
          <Box sx={{ mb: 3 }}>
            <FormControl component="fieldset">
              <FormLabel component="legend">Feedback Type</FormLabel>
              <RadioGroup
                row
                value={feedbackType}
                onChange={(e) => setFeedbackType(e.target.value)}
              >
                <FormControlLabel
                  value="general"
                  control={<Radio />}
                  label="General"
                />
                <FormControlLabel
                  value="feature"
                  control={<Radio />}
                  label="Feature Request"
                />
                <FormControlLabel
                  value="bug"
                  control={<Radio />}
                  label="Bug Report"
                />
              </RadioGroup>
            </FormControl>
          </Box>

          {feedbackType === "general" && (
            <>
              <Box sx={{ mb: 3 }}>
                <Typography component="legend">
                  How would you rate your experience?
                </Typography>
                <Rating
                  name="feedback-rating"
                  value={rating}
                  onChange={(event, newValue) => {
                    setRating(newValue);
                  }}
                  size="large"
                  sx={{ mt: 1 }}
                />
              </Box>

              <TextField
                label="Comments"
                multiline
                rows={4}
                fullWidth
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder="Tell us what you think about the application..."
                variant="outlined"
                sx={{ mb: 2 }}
              />
            </>
          )}

          {feedbackType === "feature" && (
            <TextField
              label="Feature Request"
              multiline
              rows={4}
              fullWidth
              value={featureRequest}
              onChange={(e) => setFeatureRequest(e.target.value)}
              placeholder="Describe the feature you'd like to see..."
              variant="outlined"
              sx={{ mb: 2 }}
            />
          )}

          {feedbackType === "bug" && (
            <TextField
              label="Bug Report"
              multiline
              rows={4}
              fullWidth
              value={bugReport}
              onChange={(e) => setBugReport(e.target.value)}
              placeholder="Please describe the issue you encountered in detail..."
              variant="outlined"
              sx={{ mb: 2 }}
            />
          )}
        </DialogContent>

        <DialogActions sx={{ p: 2 }}>
          <Button onClick={onClose} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            color="primary"
            disabled={
              isSubmitting ||
              (feedbackType === "general" && !rating && !comment) ||
              (feedbackType === "feature" && !featureRequest) ||
              (feedbackType === "bug" && !bugReport)
            }
          >
            Submit Feedback
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        <Alert
          onClose={handleSnackbarClose}
          severity={snackbarSeverity}
          sx={{ width: "100%" }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default FeedbackDialog;
