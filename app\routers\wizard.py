"""
Script Wizard routes for FastAPI.
Migrated from Flask app/ai_assistant/wizard_routes.py to use FastAPI dependency injection.
"""

import logging
import json
import re
from typing import Dict, Any, List, Optional
from uuid import UUID

from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field

from app.auth.dependencies import CurrentUserType, CurrentUserIdType
from app.dependencies import get_wizard_session_repository, get_business_context_repository
from app.repositories.wizard_session_repository import WizardSessionRepository
from app.repositories.supabase_business_context_repository import BusinessContextRepository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ai/wizard", tags=["wizard"])


# Pydantic models for request/response
class BrainstormRequest(BaseModel):
    business_context_id: str = Field(..., description="Business context UUID")
    content_idea: str = Field(..., min_length=1, description="Content idea for brainstorming")


class HookRequest(BaseModel):
    session_id: str = Field(..., description="Wizard session UUID")
    selected_index: int = Field(..., ge=0, description="Index of selected title")


class IntroRequest(BaseModel):
    session_id: str = Field(..., description="Wizard session UUID")
    selected_index: int = Field(..., ge=0, description="Index of selected hook")


class OutlineRequest(BaseModel):
    session_id: str = Field(..., description="Wizard session UUID")
    selected_index: int = Field(..., ge=0, description="Index of selected intro")


class EditRequest(BaseModel):
    session_id: str = Field(..., description="Wizard session UUID")
    content_to_edit: str = Field(..., min_length=1, description="Content to edit")
    edit_instructions: str = Field(..., min_length=1, description="Edit instructions")


class DraftRequest(BaseModel):
    session_id: str = Field(..., description="Wizard session UUID")


class WizardResponse(BaseModel):
    message: str
    data: Dict[str, Any]
    session_id: Optional[str] = None


async def get_wizard_session_repository() -> WizardSessionRepository:
    """Get wizard session repository dependency"""
    from app.models.supabase_client import get_supabase_client
    from app.repositories.wizard_session_repository import WizardSessionRepository
    
    supabase = await get_supabase_client()
    if not supabase:
        raise HTTPException(
            status_code=503,
            detail="Wizard service temporarily unavailable - Failed to get Supabase client"
        )
    
    return WizardSessionRepository(supabase_client=supabase)


async def get_business_context_repository() -> BusinessContextRepository:
    """Get business context repository dependency"""
    from app.models.supabase_client import get_supabase_client
    from app.repositories.supabase_business_context_repository import BusinessContextRepository
    
    supabase = await get_supabase_client()
    if not supabase:
        raise HTTPException(
            status_code=503,
            detail="Business context service temporarily unavailable - Failed to get Supabase client"
        )
    
    return BusinessContextRepository(supabase_client=supabase)


@router.post("/brainstorm", response_model=WizardResponse)
async def brainstorm_stage(
    brainstorm_request: BrainstormRequest,
    current_user_id: CurrentUserIdType,
    wizard_repo: WizardSessionRepository = Depends(get_wizard_session_repository),
    business_repo: BusinessContextRepository = Depends(get_business_context_repository)
) -> WizardResponse:
    """Stage 1: Brainstorm titles based on content idea and selected business context."""
    
    # Validate UUID format
    try:
        business_context_id = UUID(brainstorm_request.business_context_id)
    except (ValueError, TypeError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid business_context_id format"
        )

    try:
        # 1. Fetch the selected business context
        business_context = await business_repo.get_by_id(business_context_id, user_id=current_user_id)
        if not business_context:
            logger.warning(f"Business context {business_context_id} not found or access denied for user {current_user_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Selected business context not found"
            )

        # 2. Create a new wizard session
        try:
            session = await wizard_repo.create_session(
                user_id=current_user_id,
                business_context_id=business_context_id,
                content_idea=brainstorm_request.content_idea
            )
            if not session:
                logger.error(f"Failed to create wizard session for user {current_user_id}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to initialize wizard session"
                )

            # Extract session ID, handling both dict and object types
            session_id = session.get('id') if isinstance(session, dict) else session['id']
        except Exception as e:
            logger.error(f"Unexpected error creating wizard session: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"An unexpected error occurred during session creation: {str(e)}"
            )

        # 3. Get AI Generator
        from app.utils.ai_integration import get_ai_generator
        from app.ai_assistant.prompts.wizard_prompts import get_brainstorm_prompt
        
        ai_generator = get_ai_generator()

        # 4. Build the prompt using the fetched business context
        prompt_text = get_brainstorm_prompt(
            content_idea=brainstorm_request.content_idea, 
            business_context=business_context
        )

        # 5. Call the AI
        logger.info(f"Calling AI for brainstorm stage, session {session_id}")
        logger.debug(f"Using prompt: {prompt_text[:100]}...")
        ai_result = await ai_generator.generate_content(prompt=prompt_text)

        if ai_result is None:
            logger.error(f"AI generator returned None for session {session_id}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="AI generation failed: AI generator returned None"
            )

        logger.debug(f"AI response received for brainstorm stage: {ai_result.get('generated_text', '')[:100]}...")

        if ai_result.get("error"):
            error_msg = ai_result.get('error_message', 'Unknown error')
            logger.error(f"AI generation failed for session {session_id}: {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI generation failed: {error_msg}"
            )

        # 6. Parse AI response (expecting JSON list of strings)
        try:
            # Log the raw AI response for debugging
            logger.info(f"Raw AI brainstorm response for session {session_id}: {ai_result}")

            # Get the generated text from the response
            generated_text = ai_result.get("generated_text", "[]")
            logger.info(f"Generated text from AI response: {generated_text}")

            # Try to parse the JSON
            titles_data = json.loads(generated_text)
            logger.info(f"Parsed titles_data: {titles_data}")

            # Validate the format
            if not isinstance(titles_data, list):
                logger.warning(f"AI brainstorm response for session {session_id} was not a valid JSON list. Raw: {generated_text}")
                # Try to extract a list from the response if possible
                if isinstance(titles_data, dict) and "titles" in titles_data:
                    titles_data = titles_data["titles"]
                    logger.info(f"Extracted titles from dictionary: {titles_data}")
                else:
                    titles_data = [] # Fallback to empty list

            # Ensure all items are strings
            if not all(isinstance(item, str) for item in titles_data):
                logger.warning(f"Not all items in the brainstorm response are strings. Converting to strings.")
                titles_data = [str(item) for item in titles_data]

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse AI JSON response for session {session_id}. Raw: {ai_result.get('generated_text')}. Error: {str(e)}")

            # Try to extract JSON from markdown code blocks if present
            generated_text = ai_result.get("generated_text", "")
            json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', generated_text)
            if json_match:
                try:
                    json_content = json_match.group(1).strip()
                    logger.info(f"Extracted JSON from markdown: {json_content}")
                    titles_data = json.loads(json_content)
                    if isinstance(titles_data, list):
                        logger.info(f"Successfully parsed JSON from markdown: {titles_data}")
                    else:
                        logger.warning(f"Extracted JSON is not a list: {titles_data}")
                        titles_data = []
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse extracted JSON from markdown")
                    titles_data = []
            else:
                # If all else fails, try to create a list from the text
                logger.warning(f"Attempting to create a list from raw text")
                lines = generated_text.strip().split('\n')
                titles_data = [line.strip() for line in lines if line.strip()]
                if not titles_data:
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Failed to process AI response structure"
                    )

        # 7. Update the session with results
        updated_session = await wizard_repo.update_stage(
            session_id=session_id,
            user_id=current_user_id,
            stage_data={"brainstorm_results": titles_data} # Storing the list of titles
        )

        if not updated_session:
            logger.error(f"Failed to update wizard session {session_id} after AI call.")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update wizard session state"
            )

        # 8. Return success response with standardized format
        return WizardResponse(
            message="Brainstorm stage completed successfully.",
            data={
                "session_id": str(session_id),
                "titles": titles_data
            },
            session_id=str(session_id)
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error during brainstorm stage for user {current_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred during the brainstorm stage: {str(e)}"
        )
