from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid

# Assuming 'db' is imported from a central location as indicated by other files
# This import path is based on app/app.py and create_db.py
# from .database import db


class ContentItem(BaseModel):
    """
    Represents a piece of content generated or managed by the application.
    """

    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID
    title: str
    content: str
    status: str  # Assuming status was intended (e.g., 'draft', 'published')
    content_type: Optional[str] = None  # e.g., 'blog', 'email'
    prompt_id: Optional[uuid.UUID] = None
    business_context_id: Optional[uuid.UUID] = None
    video_id: Optional[uuid.UUID] = None
    is_business_context: Optional[bool] = False  # Added based on checklist

    tags: Optional[List[str]] = Field(default_factory=list)
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    model_config = ConfigDict(from_attributes=True)

    # Removed SQLAlchemy-specific attributes like __tablename__, db.Column, relationship
    # __tablename__ = 'content_items'
    # user_id = db.Column(db.Integer, ForeignKey('users.id'), nullable=False)
    # title = db.Column(db.String(255), nullable=False)
    # content = db.Column(db.Text, nullable=False)
    # content_type = db.Column(db.String(50), nullable=True)
    # tags = db.Column(ARRAY(db.String), nullable=True, default=[])
    # created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    # updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    # metadata = db.Column(JSONB, nullable=True)
    # user = relationship("User", back_populates="content_items")

    # def __repr__(self):
    #     return f'<ContentItem {self.id}: {self.title[:30]}>'


# Ensure User model has the corresponding relationship if not already present
# (This part is outside the scope of modifying content_item.py but important for ORM)
# class User(db.Model):
#    ...
#    content_items = relationship("ContentItem", back_populates="user", lazy=True)
#    ...
