"""
YouTube integration routes for FastAPI.
Migrated from Flask app/youtube/routes.py to use FastAPI dependency injection.
"""

import os
import re
import logging
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from googleapiclient.discovery import build
from youtube_transcript_api import (
    YouTubeTranscriptApi,
    TranscriptsDisabled,
    NoTranscriptFound,
)

from app.auth.dependencies import CurrentUserType, CurrentUserIdType
from app.dependencies import get_video_data_repository
from app.repositories.supabase_video_data_repository import VideoDataRepository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/youtube", tags=["youtube"])

# Initialize YouTube API client
YOUTUBE_API_KEY = os.environ.get("YOUTUBE_API_KEY")
youtube_client = build("youtube", "v3", developerKey=YOUTUBE_API_KEY) if YOUTUBE_API_KEY else None


# Pydantic models for request/response
class VideoSearchRequest(BaseModel):
    query: str = Field(..., min_length=1, description="Search query or YouTube URL")
    max_results: int = Field(50, ge=1, le=100, description="Maximum number of results")


class VideoSearchResponse(BaseModel):
    message: str
    results: List[Dict[str, Any]]


class VideoDetailsResponse(BaseModel):
    message: str
    video: Dict[str, Any]


class TranscriptResponse(BaseModel):
    message: str
    video_id: str
    transcript: str


class OutlierAnalysisRequest(BaseModel):
    video_ids: List[str] = Field(..., min_items=1, description="List of video IDs or URLs to analyze")


class OutlierAnalysisResponse(BaseModel):
    message: str
    results: Dict[str, Any]


def extract_video_id(url_or_id: str) -> Optional[str]:
    """Extract YouTube video ID from URL or return the ID if already in correct format"""
    # Check if it's already a video ID (typically 11 characters)
    if re.match(r"^[A-Za-z0-9_-]{11}$", url_or_id):
        return url_or_id

    # Extract from URL patterns
    patterns = [
        r"(?:youtube\.com\/watch\?v=)([A-Za-z0-9_-]{11})",  # Standard watch URL
        r"(?:youtu\.be\/)([A-Za-z0-9_-]{11})",  # Shortened URL
        r"(?:youtube\.com\/embed\/)([A-Za-z0-9_-]{11})",  # Embed URL
        r"(?:youtube\.com\/v\/)([A-Za-z0-9_-]{11})",  # Old embed URL
    ]

    for pattern in patterns:
        match = re.search(pattern, url_or_id)
        if match:
            return match.group(1)

    return None


def get_channel_statistics(channel_id: str) -> Optional[Dict[str, Any]]:
    """Get YouTube channel statistics"""
    if not youtube_client:
        return None
        
    try:
        response = (
            youtube_client.channels().list(part="statistics", id=channel_id).execute()
        )

        if response.get("items"):
            return response["items"][0]["statistics"]
        return None
    except Exception as e:
        logger.error("Error getting channel statistics: %s", str(e))
        return None


def get_video_transcript(video_id: str) -> Optional[str]:
    """Get transcript for a YouTube video"""
    try:
        transcript_list = YouTubeTranscriptApi.get_transcript(video_id)

        # Format the transcript
        if transcript_list:
            full_text = " ".join([item["text"] for item in transcript_list])
            return full_text
        return None
    except (TranscriptsDisabled, NoTranscriptFound):
        return None
    except Exception as e:
        logger.error("Error getting transcript: %s", str(e))
        return None


async def get_video_data_repository() -> VideoDataRepository:
    """Get video data repository dependency"""
    from app.models.supabase_client import get_supabase_client
    from app.repositories.supabase_video_data_repository import VideoDataRepository
    
    supabase = await get_supabase_client()
    if not supabase:
        raise HTTPException(
            status_code=503,
            detail="YouTube service temporarily unavailable - Failed to get Supabase client"
        )
    
    return VideoDataRepository(supabase_client=supabase)


@router.post("/search", response_model=VideoSearchResponse)
async def search_videos(
    search_request: VideoSearchRequest,
    current_user_id: CurrentUserIdType
) -> VideoSearchResponse:
    """Search for YouTube videos by keyword or URL"""
    logger.info("Search request received:")
    logger.info("User ID: %s", current_user_id)
    logger.info("Request data: %s", search_request.model_dump())

    if not YOUTUBE_API_KEY:
        raise HTTPException(
            status_code=500,
            detail="YouTube API key not configured"
        )

    query = search_request.query.strip()
    max_results = search_request.max_results

    try:
        # Check if query is a URL or video ID
        video_id = extract_video_id(query)
        logger.info("Extracted video ID: %s", video_id)

        if video_id:
            logger.info("Fetching specific video: %s", video_id)
            # If it's a video ID or URL, get that specific video
            response = (
                youtube_client.videos()
                .list(part="snippet,statistics,contentDetails", id=video_id)
                .execute()
            )
            items = response.get("items", [])
        else:
            logger.info("Performing search for: %s", query)
            # Otherwise perform a search
            response = (
                youtube_client.search()
                .list(q=query, part="snippet", type="video", maxResults=max_results)
                .execute()
            )

            # Get video IDs from search results with proper type checking
            video_ids = []
            try:
                for item in response.get("items", []):
                    if (
                        "id" in item
                        and isinstance(item["id"], dict)
                        and "videoId" in item["id"]
                    ):
                        video_ids.append(item["id"]["videoId"])
                    else:
                        logger.warning(
                            f"Skipping item with unexpected structure: {item}"
                        )
            except Exception as e:
                logger.error("Error extracting videoId: %s", str(e))
                # Continue with any valid IDs we found

            logger.info("Found video IDs: %s", video_ids)

            # If no video IDs found, return empty results
            if not video_ids:
                return VideoSearchResponse(
                    message="No videos found",
                    results=[]
                )

            # Get details for each video in a single batch request
            response = (
                youtube_client.videos()
                .list(part="snippet,statistics,contentDetails", id=",".join(video_ids))
                .execute()
            )

            items = response.get("items", [])

        # Process results
        results = []

        for item in items:
            try:
                # Handle different item structures
                if isinstance(item["id"], dict) and "videoId" in item["id"]:
                    video_id = item["id"]["videoId"]
                else:
                    video_id = item["id"]

                # Ensure video_id is a string
                if not isinstance(video_id, str):
                    logger.info("Converting video_id to string: %s", video_id)
                    video_id = str(video_id)

                snippet = item["snippet"]
                statistics = item.get("statistics", {})

                # Get channel statistics for subscriber count
                channel_id = snippet["channelId"]
                channel_stats = get_channel_statistics(channel_id)
                subscriber_count = (
                    int(channel_stats.get("subscriberCount", 0)) if channel_stats else 0
                )

                # Create result object with default values for missing data
                video_result = {
                    "video_id": video_id,
                    "title": snippet.get("title", ""),
                    "description": snippet.get("description", ""),
                    "channel_id": channel_id,
                    "channel_title": snippet.get("channelTitle", ""),
                    "publish_date": snippet.get("publishedAt", ""),
                    "views": int(statistics.get("viewCount", 0)),
                    "likes": int(statistics.get("likeCount", 0)),
                    "comments": int(statistics.get("commentCount", 0)),
                    "channel_subscribers": subscriber_count,
                }
                results.append(video_result)
            except Exception as e:
                logger.error(
                    f"Error processing video {item.get('id', 'unknown')}: {str(e)}"
                )
                continue

        logger.info("Returning results: %s", len(results))
        return VideoSearchResponse(
            message="Videos retrieved successfully",
            results=results
        )

    except Exception as e:
        logger.error("Error searching videos: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to search videos: {str(e)}"
        )


@router.get("/video/{video_id}", response_model=VideoDetailsResponse)
async def get_video_details(
    video_id: str,
    current_user_id: CurrentUserIdType,
    video_repo: VideoDataRepository = Depends(get_video_data_repository)
) -> VideoDetailsResponse:
    """Get detailed information about a specific YouTube video"""
    try:
        # Extract video ID if a URL was provided
        extracted_id = extract_video_id(video_id)
        if not extracted_id:
            raise HTTPException(
                status_code=400,
                detail="Invalid YouTube video ID or URL"
            )

        video_id = extracted_id

        # Check if we already have this video in our database
        existing_video = await video_repo.get_by_youtube_id(
            youtube_id=video_id, user_id=current_user_id
        )
        if existing_video:
            return VideoDetailsResponse(
                message="Video found in database",
                video=existing_video
            )

        if not youtube_client:
            raise HTTPException(
                status_code=500,
                detail="YouTube API not configured"
            )

        # Get video details from YouTube API
        response = (
            youtube_client.videos()
            .list(part="snippet,statistics,contentDetails", id=video_id)
            .execute()
        )

        if not response.get("items"):
            raise HTTPException(
                status_code=404,
                detail="Video not found"
            )

        item = response["items"][0]
        snippet = item["snippet"]
        statistics = item.get("statistics", {})

        # Get channel statistics for subscriber count
        channel_id = snippet["channelId"]
        channel_stats = get_channel_statistics(channel_id)
        subscriber_count = (
            int(channel_stats.get("subscriberCount", 0)) if channel_stats else 0
        )

        # Get video transcript
        transcript = get_video_transcript(video_id)

        # Prepare metadata
        metadata = {
            "description": snippet.get("description", ""),
            "channel_id": channel_id,
            "channel_title": snippet.get("channelTitle", ""),
            "views": int(statistics.get("viewCount", 0)),
            "likes": int(statistics.get("likeCount", 0)),
            "comments": int(statistics.get("commentCount", 0)),
            "channel_subscribers": subscriber_count,
            "publish_date": snippet.get("publishedAt", ""),
            "transcript": transcript,
        }

        # Create new video data
        new_video = await video_repo.create_video_data(
            user_id=current_user_id,
            youtube_id=video_id,
            title=snippet["title"],
            metadata=metadata,
        )

        # Update processing status if transcript was fetched
        if transcript:
            await video_repo.update_processing_status(
                video_id=new_video["id"],
                user_id=current_user_id,
                status="completed",
            )

        return VideoDetailsResponse(
            message="Video details retrieved successfully",
            video=new_video
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting video details for %s: %s", video_id, str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get video details: {str(e)}"
        )


@router.get("/transcript/{video_id}", response_model=TranscriptResponse)
async def get_transcript(
    video_id: str,
    current_user_id: CurrentUserIdType,
    video_repo: VideoDataRepository = Depends(get_video_data_repository)
) -> TranscriptResponse:
    """Get transcript for a specific YouTube video"""
    try:
        # Extract video ID if a URL was provided
        extracted_id = extract_video_id(video_id)
        if not extracted_id:
            raise HTTPException(
                status_code=400,
                detail="Invalid YouTube video ID or URL"
            )

        video_id = extracted_id

        # Check if we already have this video in our database
        existing_video = await video_repo.get_by_youtube_id(
            youtube_id=video_id, user_id=current_user_id
        )
        if existing_video and existing_video.get("transcript"):
            return TranscriptResponse(
                message="Transcript found in database",
                video_id=video_id,
                transcript=existing_video.get("transcript")
            )

        # Get fresh transcript from YouTube
        transcript = get_video_transcript(video_id)

        if not transcript:
            raise HTTPException(
                status_code=404,
                detail="Transcript not available for this video"
            )

        # If we have the video in our database, update the transcript
        if existing_video:
            await video_repo.update_transcript(
                video_id=existing_video["id"],
                user_id=current_user_id,
                transcript=transcript,
            )

        return TranscriptResponse(
            message="Transcript retrieved successfully",
            video_id=video_id,
            transcript=transcript
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting transcript for %s: %s", video_id, str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get transcript: {str(e)}"
        )


@router.post("/analyze/outliers", response_model=OutlierAnalysisResponse)
async def analyze_outliers(
    analysis_request: OutlierAnalysisRequest,
    current_user_id: CurrentUserIdType,
    video_repo: VideoDataRepository = Depends(get_video_data_repository)
) -> OutlierAnalysisResponse:
    """Analyze videos to detect outliers based on the new outlier score algorithm"""
    logger.info("Analyze outliers request received:")
    logger.info("User ID: %s", current_user_id)
    logger.info("Request data: %s", analysis_request.model_dump())

    video_ids = analysis_request.video_ids

    # Import necessary functions and classes
    from app.youtube.analytics import calculate_video_outlier_score
    from app.youtube.youtube_service import YouTubeService

    youtube_service = YouTubeService()
    channel_history_cache = {}  # Cache historical data per channel_id for this request
    results = {}  # Store results keyed by video_id

    try:
        # --- Step 1: Fetch data for all requested videos ---
        videos_to_process = []
        for vid_input in video_ids:
            try:
                extracted_id = extract_video_id(vid_input)
                logger.debug(
                    f"Processing requested ID: {vid_input}, Extracted ID: {extracted_id}"
                )

                if not extracted_id:
                    results[vid_input] = {"error": "Invalid YouTube video ID or URL"}
                    continue

                # Check DB first
                existing_video = None
                try:
                    existing_video = await video_repo.get_by_youtube_id(
                        youtube_id=extracted_id, user_id=current_user_id
                    )
                    logger.debug(
                        f"DB lookup result for {extracted_id}: {'Found' if existing_video else 'Not Found'}"
                    )
                except Exception as db_error:
                    logger.warning(
                        f"DB lookup failed for {extracted_id}: {str(db_error)}"
                    )
                    # Continue to API fetch

                if existing_video:
                    # Ensure necessary fields are present from DB record
                    video_obj = {
                        "user_id": current_user_id,
                        "youtube_id": extracted_id,
                        "title": existing_video.get("title", "N/A"),
                        "description": existing_video.get("metadata", {}).get(
                            "description", ""
                        ),
                        "channel_id": existing_video.get("metadata", {}).get(
                            "channel_id"
                        ),
                        "channel_title": existing_video.get("metadata", {}).get(
                            "channel_title", "N/A"
                        ),
                        "views": existing_video.get("metadata", {}).get("views", 0),
                        "likes": existing_video.get("metadata", {}).get("likes", 0),
                        "comments": existing_video.get("metadata", {}).get(
                            "comments", 0
                        ),
                        "channel_subscribers": existing_video.get("metadata", {}).get(
                            "channel_subscribers", 0
                        ),
                        "publish_date": existing_video.get("metadata", {}).get(
                            "publish_date"
                        ),
                    }
                    videos_to_process.append(video_obj)
                    logger.debug(f"Added video from DB: {extracted_id}")
                else:
                    # Fetch from YouTube API
                    if not youtube_client:
                        results[vid_input] = {"error": "YouTube API not configured"}
                        continue

                    try:
                        response = (
                            youtube_client.videos()
                            .list(part="snippet,statistics", id=extracted_id)
                            .execute()
                        )

                        if response.get("items"):
                            item = response["items"][0]
                            snippet = item["snippet"]
                            statistics = item.get("statistics", {})

                            # Get channel statistics
                            channel_id = snippet["channelId"]
                            channel_stats = get_channel_statistics(channel_id)
                            subscriber_count = (
                                int(channel_stats.get("subscriberCount", 0))
                                if channel_stats
                                else 0
                            )

                            video_obj = {
                                "user_id": current_user_id,
                                "youtube_id": extracted_id,
                                "title": snippet.get("title", "N/A"),
                                "description": snippet.get("description", ""),
                                "channel_id": channel_id,
                                "channel_title": snippet.get("channelTitle", "N/A"),
                                "views": int(statistics.get("viewCount", 0)),
                                "likes": int(statistics.get("likeCount", 0)),
                                "comments": int(statistics.get("commentCount", 0)),
                                "channel_subscribers": subscriber_count,
                                "publish_date": snippet.get("publishedAt"),
                            }
                            videos_to_process.append(video_obj)
                            logger.debug(f"Added video from API: {extracted_id}")
                        else:
                            results[vid_input] = {"error": "Video not found on YouTube"}
                    except Exception as api_error:
                        logger.error(f"API fetch failed for {extracted_id}: {str(api_error)}")
                        results[vid_input] = {"error": f"API fetch failed: {str(api_error)}"}

            except Exception as e:
                logger.error(f"Error processing video {vid_input}: {str(e)}")
                results[vid_input] = {"error": f"Processing failed: {str(e)}"}

        # --- Step 2: Calculate outlier scores for successfully fetched videos ---
        for video_obj in videos_to_process:
            try:
                channel_id = video_obj["channel_id"]

                # Get or fetch channel history
                if channel_id not in channel_history_cache:
                    try:
                        channel_history = await youtube_service.get_channel_history(channel_id)
                        channel_history_cache[channel_id] = channel_history
                    except Exception as history_error:
                        logger.warning(f"Failed to get channel history for {channel_id}: {str(history_error)}")
                        channel_history_cache[channel_id] = []

                channel_history = channel_history_cache[channel_id]

                # Calculate outlier score
                outlier_score, performance_category = calculate_video_outlier_score(
                    video_obj, channel_history
                )

                results[video_obj["youtube_id"]] = {
                    "video_id": video_obj["youtube_id"],
                    "title": video_obj["title"],
                    "outlier_score": outlier_score,
                    "performance_category": performance_category,
                    "views": video_obj["views"],
                    "likes": video_obj["likes"],
                    "comments": video_obj["comments"],
                    "channel_title": video_obj["channel_title"],
                    "channel_subscribers": video_obj["channel_subscribers"],
                }

            except Exception as e:
                logger.error(f"Error calculating outlier score for {video_obj['youtube_id']}: {str(e)}")
                results[video_obj["youtube_id"]] = {
                    "error": f"Outlier calculation failed: {str(e)}"
                }

        return OutlierAnalysisResponse(
            message="Outlier analysis completed",
            results=results
        )

    except Exception as e:
        logger.error("Error in analyze_outliers: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Outlier analysis failed: {str(e)}"
        )
