# app/ai_assistant/prompts/__init__.py
"""Main entry point for the prompts package."""

# Import formatters from the new module
# (Removed import of private helpers: _format_business_context, _format_content_examples, _format_content_items, _format_video_data)
# These are now only used internally within the package submodules.

# Import transcript formatter from the new module
# from .transcript import get_transcript_format_prompt # REMOVED as per refactoring
from .transcript import format_transcript # Keep this if it exists and is needed

# Import generic functions/constants from the new module
from .generic import (
    build_system_prompt,
    # get_default_system_prompt, # REMOVED as per refactoring
    DEFAULT_YOUTUBE_ANALYSIS_USER_PROMPT,
    DEFAULT_TRANSCRIPT_SUMMARY_USER_PROMPT,
    logger,  # Re-exporting logger from generic for now
)

# Import YouTube functions from the new module
from .youtube import (
    get_youtube_system_prompt,
    get_youtube_content_prompt,
    get_youtube_user_prompt,
)

# Import audience analysis functions
from .audience_analysis import (
    build_audience_analysis_prompt,
)


# Define __all__ to control what `from app.ai_assistant.prompts import *` imports
# List all the functions/constants intended to be public
__all__ = [
    # Formatters (Internal helpers removed from public API)
    # Transcript formatter
    # "get_transcript_format_prompt",
    # Generic items
    "build_system_prompt",
    # "get_default_system_prompt",
    "DEFAULT_YOUTUBE_ANALYSIS_USER_PROMPT",
    "DEFAULT_TRANSCRIPT_SUMMARY_USER_PROMPT",
    "logger",
    # YouTube functions
    "get_youtube_system_prompt",
    "get_youtube_content_prompt",
    "get_youtube_user_prompt",
    # Audience analysis functions
    "build_audience_analysis_prompt",
]

# The original try...except block for importing from the old file is no longer needed
# as all functions are now sourced from submodules within this package.
