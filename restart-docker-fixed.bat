@echo off
echo Stopping Docker containers...
docker-compose down

echo Removing any dangling volumes...
docker volume prune -f

echo Removing any existing containers...
docker-compose rm -f

echo Building and starting Docker containers with new configuration...
docker-compose up -d --build

echo Containers started in detached mode. Use 'docker-compose logs -f' to view logs.
echo Frontend should be available at http://localhost:3000
echo Backend should be available at http://localhost:5000
