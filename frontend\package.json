{"name": "writer-v2-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.165", "@mui/material": "^5.13.0", "@reduxjs/toolkit": "^2.0.0", "@supabase/supabase-js": "^2.49.1", "@types/react-syntax-highlighter": "^15.5.13", "axios": "^1.7.2", "chart.js": "^4.4.8", "framer-motion": "^10.12.10", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "writer-v2-test": "file:.."}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/chart.js": "^2.9.41", "@types/jest": "^27.5.2", "@types/node": "^16.18.30", "@types/react": "^18.2.6", "@types/react-dom": "^18.3.0", "@types/redux-mock-store": "^1.5.0", "eslint": "^8.40.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.8", "redux-mock-store": "^1.5.5", "source-map-explorer": "^2.5.3"}, "scripts": {"start": "HOST=0.0.0.0 PORT=3000 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "analyze": "source-map-explorer 'build/static/js/*.js'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}