"""
Supabase user repository for Writer v2.
This module provides a repository for interacting with Supa<PERSON> auth and users.
"""

from typing import Dict, Any, Optional
import logging
from datetime import datetime

from app.repositories.supabase_repository import (
    SupabaseRepository,
    SupabaseRepositoryError,
)
from app.models.supabase_client import get_supabase_client, get_auth
from app.utils.caching import cache_supabase_query, supabase_query_cache

logger = logging.getLogger(__name__)


class SupabaseAuthError(Exception):
    """Exception raised for Supabase authentication errors."""

    pass


class UserRepository(SupabaseRepository):
    """Repository for Supabase users."""

    def __init__(self):
        """Initialize the repository."""
        super().__init__(table_name="users")
        # self.auth will be fetched asynchronously in methods that need it

    async def register(
        self,
        email: str,
        password: str,
        username: str,
        first_name: str = "",
        last_name: str = "",
    ) -> Dict[str, Any]:
        """Register a new user with <PERSON><PERSON><PERSON> Auth and create a user profile asynchronously."""
        try:
            auth_client = await get_auth()
            auth_response = await auth_client.sign_up({"email": email, "password": password})

            if not auth_response.user:
                raise SupabaseAuthError("Failed to register user with Supabase Auth")

            user_id = auth_response.user.id
            created_at = auth_response.user.created_at

            user_data = {
                "id": user_id,
                "email": email,
                "username": username,
                "first_name": first_name,
                "last_name": last_name,
                "created_at": created_at,
                "updated_at": created_at,
                "is_active": True,
            }

            table = await self._get_table()
            profile_response = await table.insert(user_data).execute()

            if not profile_response.data:
                logger.warning(
                    f"User authenticated but profile creation failed for {email}"
                )
                return {
                    "user": {
                        "id": user_id,
                        "email": email,
                        "username": username,
                        "created_at": created_at,
                    },
                    "session": auth_response.session,
                }

            return {"user": profile_response.data[0], "session": auth_response.session}

        except Exception as e:
            logger.error("Failed to register user: %s", e)
            raise SupabaseAuthError(f"Failed to register user: {e}")

    async def login(self, email: str, password: str) -> Dict[str, Any]:
        """Log in a user with Supabase Auth asynchronously."""
        try:
            auth_client = await get_auth()
            auth_response = await auth_client.sign_in_with_password(
                {"email": email, "password": password}
            )

            if not auth_response.user:
                raise SupabaseAuthError("Failed to log in user with Supabase Auth")

            user_profile = {
                "id": auth_response.user.id,
                "email": email,
                "username": email.split("@")[0],
                "created_at": auth_response.user.created_at,
                "is_active": True,
            }

            try:
                db_user = await self.get_by_id(auth_response.user.id)
                if db_user:
                    user_profile = db_user
            except Exception as e:
                logger.warning("Could not get user profile from database: %s", e)

            return {
                "user": user_profile,
                "session": auth_response.session,
                "access_token": (
                    auth_response.session.access_token
                    if auth_response.session
                    else None
                ),
            }
        except Exception as e:
            logger.error("Failed to log in user: %s", e)
            raise SupabaseAuthError(f"Failed to log in user: {e}")

    async def logout(self) -> bool: # Removed access_token as sign_out() doesn't need it for current user
        """Log out a user with Supabase Auth asynchronously."""
        try:
            auth_client = await get_auth()
            await auth_client.sign_out()
            return True
        except Exception as e:
            logger.error("Failed to log out user: %s", e)
            raise SupabaseAuthError(f"Failed to log out user: {e}")

    @cache_supabase_query() # This decorator needs to be async compatible
    async def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get a user by email asynchronously."""
        try:
            logger.debug(f"Fetching user by email from DB: {email}")
            table = await self._get_table()
            response = await table.select("*").eq("email", email).execute()
            if response.data:
                return response.data[0]
            return None
        except Exception as e:
            logger.error("Failed to get user by email: %s", e)
            raise SupabaseRepositoryError(f"Failed to get user by email: {e}")

    async def get_current_session(self) -> Optional[Dict[str, Any]]:
        """Get the current user session from Supabase asynchronously."""
        try:
            auth_client = await get_auth()
            session_response = await auth_client.get_session()
            return session_response.session # Accessing .session attribute
        except Exception as e:
            logger.error("Failed to get current session: %s", e)
            return None # Or raise SupabaseAuthError

    async def get_session(self, access_token: Optional[str] = None) -> Optional[Any]: # Renamed from get_session to avoid conflict, param for token
        """Get the current session with user information asynchronously. If token is provided, gets user for that token."""
        try:
            auth_client = await get_auth()
            if access_token:
                 # Assuming get_user() is the method to validate a token and get user data
                session_response = await auth_client.get_user(jwt=access_token) 
            else:
                session_response = await auth_client.get_session()
            
            # The structure of session_response might differ between get_user and get_session
            # get_session returns a SessionResponse object which has .session and .user
            # get_user returns a UserResponse object which has .user
            # We need to standardize or handle appropriately
            if session_response and hasattr(session_response, 'user') and session_response.user:
                 # For get_user, session might not be directly available in the same way
                 # For get_session, session_response.session is the session object
                return session_response # Return the whole response for now, caller can unpack
            return None
        except Exception as e:
            logger.error("Failed to get session: %s", e)
            raise SupabaseAuthError(f"Failed to get session: {e}")

    @cache_supabase_query() # This decorator needs to be async compatible
    async def get_by_id(self, entity_id: str) -> Optional[Dict[str, Any]]: # Overriding to ensure it's async
        """Get a user by ID asynchronously."""
        return await super().get_by_id(entity_id)

    async def update_profile(self, user_id: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update a user profile asynchronously."""
        # Add 'updated_at' timestamp
        data["updated_at"] = datetime.utcnow().isoformat()
        try:
            table = await self._get_table()
            response = await table.update(data).eq("id", user_id).execute()
            if response.data:
                # Invalidate cache for get_by_id and get_user_by_email if data contains email
                self.get_by_id.invalidate(self, user_id)
                if 'email' in data:
                    self.get_user_by_email.invalidate(self, data['email'])
                return response.data[0]
            return None # Or raise error if update expected to always find a row
        except Exception as e:
            logger.error("Failed to update user profile: %s", e)
            raise SupabaseRepositoryError(f"Failed to update user profile for {user_id}: {e}")

    async def change_password(
        self, user_id: str, new_password: str # current_password removed, handled by Supabase
    ) -> bool:
        """Change a user's password asynchronously. Assumes user is authenticated."""
        try:
            auth_client = await get_auth()
            # The update_user method is used for changing password when user is authenticated
            await auth_client.update_user({"password": new_password})
            return True
        except Exception as e:
            logger.error("Failed to change password for user %s: %s", user_id, e)
            raise SupabaseAuthError(f"Failed to change password: {e}")

    async def request_password_reset(self, email: str) -> bool:
        """Request a password reset for an email asynchronously."""
        try:
            auth_client = await get_auth()
            await auth_client.reset_password_email(email)
            return True
        except Exception as e:
            logger.error("Failed to request password reset for %s: %s", email, e)
            raise SupabaseAuthError(f"Failed to request password reset: {e}")

    async def verify_session(self, access_token: str) -> Optional[Dict[str, Any]]: # Renamed to verify_token_get_user
        """Verify a session token and get user details asynchronously."""
        try:
            auth_client = await get_auth()
            user_response = await auth_client.get_user(jwt=access_token)
            if user_response and user_response.user:
                # Convert user to dict. Assuming Pydantic model or similar with model_dump
                if hasattr(user_response.user, 'model_dump') and callable(user_response.user.model_dump):
                    return user_response.user.model_dump(exclude_none=True)
                elif hasattr(user_response.user, 'dict') and callable(user_response.user.dict):
                     return user_response.user.dict(exclude_none=True) # For older Pydantic or similar
                else: # Fallback for other object types
                    return vars(user_response.user) 
            return None
        except Exception as e:
            logger.error("Failed to verify session token: %s", e)
            # Distinguish between invalid token and other errors if possible
            if "invalid token" in str(e).lower() or "expired" in str(e).lower():
                 raise SupabaseAuthError(f"Invalid or expired token: {e}")
            raise SupabaseAuthError(f"Failed to verify session: {e}")

    async def update_user_metadata(self, user_id: str, metadata: Dict[str, Any]) -> bool:
        """Update user metadata asynchronously."""
        try:
            auth_client = await get_auth()
            # Supabase uses user_metadata for custom fields in the auth.users table
            # or app_metadata for admin-settable, non-user-editable fields.
            # Assuming we mean user_metadata here.
            await auth_client.update_user({"data": metadata}) # 'data' key for user_metadata
            return True
        except Exception as e:
            logger.error("Failed to update user metadata for user %s: %s", user_id, e)
            raise SupabaseAuthError(f"Failed to update user metadata: {e}")

    async def refresh_session(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """Refresh a user session using a refresh token asynchronously."""
        try:
            auth_client = await get_auth()
            session_response = await auth_client.refresh_session(refresh_token)
            if session_response and session_response.session:
                # Convert session and user to dicts for consistent return type
                session_dict = {
                    "access_token": session_response.session.access_token,
                    "refresh_token": session_response.session.refresh_token,
                    "expires_in": session_response.session.expires_in,
                    "expires_at": session_response.session.expires_at,
                    "token_type": session_response.session.token_type,
                }
                user_dict = None
                if session_response.user:
                    if hasattr(session_response.user, 'model_dump') and callable(session_response.user.model_dump):
                        user_dict = session_response.user.model_dump(exclude_none=True)
                    elif hasattr(session_response.user, 'dict') and callable(session_response.user.dict):
                        user_dict = session_response.user.dict(exclude_none=True)
                    else:
                        user_dict = vars(session_response.user)
                
                session_dict['user'] = user_dict
                return session_dict
            return None
        except Exception as e:
            logger.error("Failed to refresh session: %s", e)
            # Distinguish invalid refresh token from other errors if possible
            if "invalid refresh token" in str(e).lower():
                raise SupabaseAuthError(f"Invalid refresh token: {e}")    
            raise SupabaseAuthError(f"Failed to refresh session: {e}")

    # Add other user-specific methods as needed
