# tests/utils/test_supabase_auth.py
from unittest.mock import patch, MagicMock
from flask import g, current_app

# Import the function to test
from app.utils.supabase_auth import verify_supabase_token, FallbackUser

# Import relevant exceptions
from jwt import ExpiredSignatureError, InvalidTokenError

# Import a placeholder for the user model/object if needed, or use MagicMock
# from app.models.user import User # Assuming a user model exists

# Mock user data for testing
MOCK_USER_ID = "user-abc-123"
# Updated payload to include user_metadata for fallback test
MOCK_TOKEN_PAYLOAD = {
    "sub": MOCK_USER_ID,
    "email": "<EMAIL>",
    "user_metadata": {"name": "Test User"},
    # Add other claims if needed by tests
}
VALID_TOKEN = "valid.token.string"
MOCK_JWT_SECRET = "test-jwt-secret"  # Matches conftest.py for consistency


# Test Suite for verify_supabase_token
class TestVerifySupabaseToken:
    def test_verify_supabase_token_invalid_or_expired(self, app):
        """
        GIVEN an invalid or expired token
        WHEN verify_supabase_token is called
        THEN it should handle the JW<PERSON> exception gracefully and return None
        AND g.user should not be set.
        """
        invalid_token = "this.is.invalid"
        # Mock jwt.decode to raise an exception
        with patch(
            "app.utils.supabase_auth.jwt.decode",
            side_effect=InvalidTokenError("Invalid token"),
        ) as mock_decode:
            with app.app_context():
                # Clear g.user just in case it was set by a previous test/fixture
                g.user = None
                result = verify_supabase_token(invalid_token)

                # Assertions
                assert result is None
                assert not hasattr(g, "user") or g.user is None
                mock_decode.assert_called_once()  # Ensure decode was attempted

        # Test with ExpiredSignatureError
        expired_token = "this.is.expired"
        with patch(
            "app.utils.supabase_auth.jwt.decode",
            side_effect=ExpiredSignatureError("Token expired"),
        ) as mock_decode_expired:
            with app.app_context():
                g.user = None
                result_expired = verify_supabase_token(expired_token)

                # Assertions
                assert result_expired is None
                assert not hasattr(g, "user") or g.user is None
                mock_decode_expired.assert_called_once()

    def test_verify_supabase_token_uses_correct_secret(self, app):
        """
        GIVEN a valid token
        WHEN verify_supabase_token is called
        THEN it should attempt to decode the token using the JWT_SECRET_KEY from app config.
        """
        with (
            patch(
                "app.utils.supabase_auth.jwt.decode", return_value=MOCK_TOKEN_PAYLOAD
            ) as mock_decode,
            patch("app.utils.supabase_auth.get_supabase_client") as _mock_get_client,
            patch("app.utils.supabase_hooks.init_supabase_repositories"),
        ):
            # Mock repository on g to avoid errors
            with app.app_context():
                # Setup mock repository
                mock_repo = MagicMock()
                mock_repo.get_by_id.return_value = MagicMock(
                    id=MOCK_USER_ID
                )  # Simulate found user
                g.user_repository = mock_repo
                g.user = None  # Ensure g.user is not set initially

                # Override config for this test if needed, or rely on conftest
                current_app.config["JWT_SECRET_KEY"] = MOCK_JWT_SECRET

                verify_supabase_token(VALID_TOKEN)

                # Assertion
                mock_decode.assert_called_once_with(
                    VALID_TOKEN,
                    MOCK_JWT_SECRET,  # Check the secret from config is used
                    algorithms=["HS256"],
                    options={"verify_aud": False},
                )

    @patch("app.utils.supabase_auth.jwt.decode", return_value=MOCK_TOKEN_PAYLOAD)
    @patch("app.utils.supabase_auth.get_supabase_client")
    @patch("app.utils.supabase_hooks.init_supabase_repositories")  # CORRECTED PATH
    def test_verify_supabase_token_success_db_lookup(
        self, mock_init_repos, mock_get_client, mock_decode, app
    ):
        """
        GIVEN a valid token and a successful database lookup
        WHEN verify_supabase_token is called
        THEN it should return the user object from the database
        AND set g.user to the database user object.
        """
        mock_db_user = MagicMock(
            id=MOCK_USER_ID, email="<EMAIL>", name="DB User"
        )
        # Mock repository behavior
        mock_repo = MagicMock()
        mock_repo.get_by_id.return_value = mock_db_user
        # Mock Supabase client (needed for repo initialization potentially)
        mock_supabase_client = MagicMock()
        mock_get_client.return_value = mock_supabase_client

        with app.app_context():
            g.user_repository = mock_repo
            g.user = None  # Ensure g.user is not set initially

            result = verify_supabase_token(VALID_TOKEN)

            # Assertions
            mock_decode.assert_called_once()
            mock_repo.get_by_id.assert_called_once_with(MOCK_USER_ID)
            assert result == mock_db_user
            assert g.user == mock_db_user

    @patch("app.utils.supabase_auth.jwt.decode", return_value=MOCK_TOKEN_PAYLOAD)
    @patch("app.utils.supabase_auth.get_supabase_client")
    @patch("app.utils.supabase_hooks.init_supabase_repositories")  # CORRECTED PATH
    def test_verify_supabase_token_success_db_fail_fallback(
        self, mock_init_repos, mock_get_client, mock_decode, app
    ):
        """
        GIVEN a valid token but a failed database lookup (user not found)
        WHEN verify_supabase_token is called
        THEN it should return a fallback user object (FallbackUser instance)
        AND set g.user to the fallback user object.
        """
        # Mock repository behavior (user not found)
        mock_repo = MagicMock()
        mock_repo.get_by_id.return_value = None
        # Mock Supabase client
        mock_supabase_client = MagicMock()
        mock_get_client.return_value = mock_supabase_client

        with app.app_context():
            g.user_repository = mock_repo
            g.user = None  # Ensure g.user is not set initially

            result = verify_supabase_token(VALID_TOKEN)

            # Assertions
            mock_decode.assert_called_once()
            mock_repo.get_by_id.assert_called_once_with(MOCK_USER_ID)
            assert result is not None
            # Check it's the correct fallback type
            assert isinstance(result, FallbackUser)
            # Check attributes
            assert result.id == MOCK_USER_ID
            assert result.email == MOCK_TOKEN_PAYLOAD["email"]
            assert result.name == MOCK_TOKEN_PAYLOAD["user_metadata"]["name"]
            # Check g.user is set correctly
            assert g.user == result

    @patch("app.utils.supabase_auth.jwt.decode", return_value=MOCK_TOKEN_PAYLOAD)
    @patch("app.utils.supabase_auth.get_supabase_client")
    @patch("app.utils.supabase_hooks.init_supabase_repositories")  # CORRECTED PATH
    def test_verify_supabase_token_sets_and_resets_postgrest_auth(
        self, mock_init_repos, mock_get_client, mock_decode, app
    ):
        """
        GIVEN a valid token
        WHEN verify_supabase_token is called
        THEN it should set PostgREST auth context with the token before the DB call
        AND reset PostgREST auth context with the service key after the DB call.
        """
        mock_db_user = MagicMock(id=MOCK_USER_ID)
        # Mock repository
        mock_repo = MagicMock()
        mock_repo.get_by_id.return_value = mock_db_user  # Simulate successful find
        # Mock Supabase client and its postgrest attribute
        mock_supabase_client = MagicMock()
        mock_postgrest_builder = MagicMock()
        mock_supabase_client.postgrest = mock_postgrest_builder
        mock_get_client.return_value = mock_supabase_client

        # Assume service key is retrieved from config
        MOCK_SERVICE_KEY = "test-service-key"
        with app.app_context():
            current_app.config["SUPABASE_SERVICE_ROLE_KEY"] = MOCK_SERVICE_KEY
            g.user_repository = mock_repo
            g.user = None

            verify_supabase_token(VALID_TOKEN)

            # Assertions
            mock_decode.assert_called_once()
            # Check that postgrest.auth() was called twice (set and reset)
            assert mock_postgrest_builder.auth.call_count == 2
            # Check the calls were made with the correct arguments
            calls = mock_postgrest_builder.auth.call_args_list
            # Call 1: Set auth with user token
            assert calls[0][0] == (VALID_TOKEN,)
            # Call 2: Reset auth with service key
            assert calls[1][0] == (MOCK_SERVICE_KEY,)

            # Ensure the DB call happened *between* the auth calls
            # This relies on the call order within the patched function
            # We know get_by_id is called, checking its position relative to auth calls is complex with mocks
            # A simpler check is that both auth calls and the DB call happened.
            mock_repo.get_by_id.assert_called_once_with(MOCK_USER_ID)

    # --- Additional tests will be added here ---
    # test_verify_supabase_token_success_db_lookup
    # test_verify_supabase_token_success_db_fail_fallback
    # test_verify_supabase_token_uses_correct_secret
    # test_verify_supabase_token_sets_and_resets_postgrest_auth
