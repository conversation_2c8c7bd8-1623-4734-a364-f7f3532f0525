"""
Supabase Authentication Utilities
This module provides minimal authentication utilities for Supabase integration.
"""

import jwt
import logging
from dataclasses import dataclass

from app.config import get_settings
from app.models.supabase_client import SupabaseClientError
from app.utils.errors import AuthenticationException

logger = logging.getLogger(__name__)


class SupabaseAuthError(Exception):
    """Exception raised for Supabase authentication errors."""
    pass


# Define a simple dataclass for the fallback user
@dataclass
class FallbackUser:
    id: str
    email: str
    name: str

    def get(self, key, default=None):
        """
        Make FallbackUser compatible with dictionary-style access.
        This allows code to access user attributes with user.get('id') instead of user.id
        """
        if hasattr(self, key):
            return getattr(self, key)
        return default

    def __getitem__(self, key):
        """
        Support dictionary-style access with user['id']
        Raises KeyError if attribute doesn't exist
        """
        if hasattr(self, key):
            return getattr(self, key)
        raise KeyError(key)

    def __contains__(self, key):
        """
        Support 'in' operations like 'id' in user
        """
        return hasattr(self, key)

    def __iter__(self):
        """
        Support iteration over keys, e.g., for key in user
        """
        for field in self.__dataclass_fields__:
            yield field

    def keys(self):
        """
        Return the attribute names, like dict.keys()
        """
        return list(self.__dataclass_fields__.keys())

    def values(self):
        """
        Return the attribute values, like dict.values()
        """
        return [getattr(self, field) for field in self.__dataclass_fields__]

    def items(self):
        """
        Return (key, value) pairs, like dict.items()
        """
        return [(field, getattr(self, field)) for field in self.__dataclass_fields__]


def verify_supabase_token(token):
    """
    Verify a Supabase JWT token and return the user object.
    Handles fetching user from DB or falling back to token claims.
    """
    logger.debug("[VERIFY_TOKEN] Entered verify_supabase_token")

    try:
        logger.debug("[VERIFY_TOKEN] Calling get_sync_supabase_client")
        # Use sync client since verify_supabase_token is a synchronous function
        from app.models.supabase_client import get_sync_supabase_client
        supabase = get_sync_supabase_client()
        logger.debug(f"[VERIFY_TOKEN] Supabase sync client obtained: {supabase is not None}")

        # Get settings instead of current_app.config
        settings = get_settings()
        supabase_jwt_secret = settings.supabase_jwt_secret

        if not supabase_jwt_secret:
            logger.error("[VERIFY_TOKEN] Config Error: SUPABASE_JWT_SECRET is not configured!")
            raise SupabaseAuthError("Server configuration error: JWT Secret missing.")

        service_key = settings.supabase_service_role_key
        if not service_key:
            logger.critical(
                "[VERIFY_TOKEN] Config Error: SUPABASE_SERVICE_ROLE_KEY not configured! Cannot reset auth context."
            )

        # Decode the token
        decoded = None
        decode_exception = None

        # Try to decode with the definitive SUPABASE_JWT_SECRET
        try:
            logger.debug("[VERIFY_TOKEN] Attempting JWT decode with SUPABASE_JWT_SECRET...")
            decoded = jwt.decode(
                token,
                supabase_jwt_secret,
                algorithms=["HS256"],
                options={"verify_aud": False},
            )
            logger.debug(f"[VERIFY_TOKEN] JWT decode successful for sub: {decoded.get('sub')}")
        except Exception as e:
            logger.warning(f"[VERIFY_TOKEN] JWT decode failed with SUPABASE_JWT_SECRET: {str(e)}")
            decode_exception = e

        # Development mode fallback - only if no production JWT secret
        if decoded is None and settings.log_level.upper() == "DEBUG":
            try:
                logger.debug("[VERIFY_TOKEN] Development mode: Attempting JWT decode without verification...")
                decoded = jwt.decode(
                    token,
                    options={"verify_signature": False, "verify_aud": False},
                )
                logger.warning(
                    "[VERIFY_TOKEN] INSECURE: JWT signature verification bypassed in development mode. "
                    "This should NOT happen in production. Ensure SUPABASE_JWT_SECRET is correct."
                )
            except Exception as e:
                logger.debug(f"[VERIFY_TOKEN] JWT decode failed even without verification: {str(e)}")
                decode_exception = e

        if not decoded:
            if decode_exception:
                logger.warning(f"[VERIFY_TOKEN] JWT Verification Failed: {type(decode_exception).__name__} - {str(decode_exception)}")
            else:
                logger.error("[VERIFY_TOKEN] Internal Error: Token decoded as None unexpectedly.")
            return None

        user_id = decoded.get("sub")
        if not user_id:
            logger.warning("[VERIFY_TOKEN] JWT Verification Failed: 'sub' (user ID) claim missing.")
            return None

        # Reset Supabase auth context to service role
        if service_key:
            logger.debug("[VERIFY_TOKEN] Resetting Supabase auth context to service role")
            try:
                # For sync client, we don't need to reset session since we're using service role key
                # The sync client will use the service role key for database operations
                logger.debug("[VERIFY_TOKEN] Using service role for database operations")
            except Exception as e:
                logger.debug(f"[VERIFY_TOKEN] Auth context reset not needed for sync client: {e}")

        # Try to fetch user from database
        try:
            logger.debug(f"[VERIFY_TOKEN] Fetching user data from database for user_id: {user_id}")
            response = supabase.table("profiles").select("*").eq("id", user_id).single().execute()
            
            if response.data:
                logger.debug(f"[VERIFY_TOKEN] User data fetched from database")
                return response.data
            else:
                logger.warning(f"[VERIFY_TOKEN] No user profile found in database for user_id: {user_id}")
        except Exception as e:
            logger.warning(f"[VERIFY_TOKEN] Database user fetch failed: {e}")

        # Fall back to token claims
        logger.debug("[VERIFY_TOKEN] Using fallback user from token claims")
        email = decoded.get("email", "")
        name = decoded.get("user_metadata", {}).get("full_name", email.split("@")[0] if email else "Unknown")
        
        # Return a FallbackUser instance that can be accessed like a dict
        fallback_user = FallbackUser(id=user_id, email=email, name=name)
        logger.debug(f"[VERIFY_TOKEN] Created fallback user: {fallback_user.id}")
        return fallback_user

    except SupabaseAuthError:
        raise  # Re-raise SupabaseAuthError as-is
    except Exception as e:
        logger.error(f"[VERIFY_TOKEN] Unexpected error during token verification: {e}", exc_info=True)
        raise SupabaseAuthError(f"Token verification failed: {e}")
