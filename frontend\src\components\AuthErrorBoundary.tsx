import React, { Component, ReactNode } from 'react';
import { Box, Typography, Button, Alert, Paper } from '@mui/material';
import { Refresh as RefreshIcon, Login as LoginIcon } from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onRetry?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
  isAuthError: boolean;
}

class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isAuthError: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Check if this is an authentication-related error
    const isAuthError = 
      error.message.includes('Authentication') ||
      error.message.includes('401') ||
      error.message.includes('Unauthorized') ||
      error.message.includes('session') ||
      error.message.includes('token') ||
      error.message.includes('rate limit');

    return {
      hasError: true,
      error,
      isAuthError,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('AuthErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      errorInfo,
    });

    // Log authentication errors for monitoring
    if (this.state.isAuthError) {
      console.error('Authentication error caught by boundary:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
      });
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isAuthError: false,
    });

    if (this.props.onRetry) {
      this.props.onRetry();
    }
  };

  handleLogin = () => {
    window.location.href = '/login';
  };

  render() {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '200px',
            p: 3,
          }}
        >
          <Paper
            elevation={2}
            sx={{
              p: 4,
              maxWidth: 500,
              textAlign: 'center',
            }}
          >
            {this.state.isAuthError ? (
              <>
                <Alert severity="warning" sx={{ mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Authentication Issue
                  </Typography>
                  <Typography variant="body2">
                    There was a problem with your authentication session. This might be due to:
                  </Typography>
                  <Box component="ul" sx={{ textAlign: 'left', mt: 1, mb: 0 }}>
                    <li>Session expired</li>
                    <li>Network connectivity issues</li>
                    <li>Too many authentication attempts</li>
                  </Box>
                </Alert>

                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                  <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={this.handleRetry}
                  >
                    Try Again
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<LoginIcon />}
                    onClick={this.handleLogin}
                  >
                    Sign In
                  </Button>
                </Box>
              </>
            ) : (
              <>
                <Alert severity="error" sx={{ mb: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Something went wrong
                  </Typography>
                  <Typography variant="body2">
                    An unexpected error occurred. Please try refreshing the page.
                  </Typography>
                </Alert>

                <Button
                  variant="contained"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleRetry}
                >
                  Try Again
                </Button>

                {process.env.NODE_ENV === 'development' && (
                  <Box sx={{ mt: 3, textAlign: 'left' }}>
                    <Typography variant="caption" color="text.secondary">
                      Error Details (Development):
                    </Typography>
                    <Box
                      component="pre"
                      sx={{
                        fontSize: '0.75rem',
                        backgroundColor: 'grey.100',
                        p: 1,
                        borderRadius: 1,
                        overflow: 'auto',
                        maxHeight: 200,
                        mt: 1,
                      }}
                    >
                      {this.state.error?.message}
                      {this.state.errorInfo?.componentStack}
                    </Box>
                  </Box>
                )}
              </>
            )}
          </Paper>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default AuthErrorBoundary;
