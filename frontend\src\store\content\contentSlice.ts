import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";

// Define types
interface ContentItem {
  id: string;
  title: string;
  content: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

interface ContentState {
  items: ContentItem[];
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: ContentState = {
  items: [],
  isLoading: false,
  error: null,
};

// Slice
const contentSlice = createSlice({
  name: "content",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Add extra reducers when implementing API calls
  },
});

// Actions
export const { clearError } = contentSlice.actions;

// Selectors
export const selectContent = (state: RootState) => state.content;
export const selectContentItems = (state: RootState) => state.content.items;
export const selectContentLoading = (state: RootState) =>
  state.content.isLoading;
export const selectContentError = (state: RootState) => state.content.error;

// Reducer
export default contentSlice.reducer;
