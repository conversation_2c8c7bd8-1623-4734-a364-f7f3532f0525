#!/usr/bin/env python3
"""
Test script to verify frontend-backend communication is working after the API URL fix.
This simulates exactly what the frontend will do with the new configuration.
"""

import requests
import json
import time

def test_api_communication():
    """Test the API communication after the fix"""
    print("🚀 Testing Frontend-Backend Communication After Fix")
    print("=" * 60)
    
    # This is what the frontend will do now with baseURL: 'http://localhost:5000'
    base_url = "http://localhost:5000"
    
    # Test endpoints that should work
    test_cases = [
        {
            "name": "Health Check",
            "url": f"{base_url}/api/health",
            "method": "GET",
            "expected_status": 200,
            "description": "Basic health check endpoint"
        },
        {
            "name": "API Root",
            "url": f"{base_url}/api/",
            "method": "GET", 
            "expected_status": 200,
            "description": "API root endpoint"
        },
        {
            "name": "Auth Ping",
            "url": f"{base_url}/api/auth/ping",
            "method": "GET",
            "expected_status": 200,
            "description": "Auth ping endpoint (no authentication required)"
        },
        {
            "name": "AI Assistant Health",
            "url": f"{base_url}/api/ai/health",
            "method": "GET",
            "expected_status": 200,
            "description": "AI assistant health check"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n🔍 Testing: {test_case['name']}")
        print(f"   URL: {test_case['url']}")
        print(f"   Expected: {test_case['description']}")
        
        try:
            if test_case['method'] == 'GET':
                response = requests.get(test_case['url'], timeout=10)
            else:
                response = requests.post(test_case['url'], timeout=10)
                
            success = response.status_code == test_case['expected_status']
            
            if success:
                print(f"   ✅ SUCCESS: Status {response.status_code}")
                try:
                    data = response.json()
                    print(f"   📄 Response: {json.dumps(data, indent=6)}")
                except:
                    print(f"   📄 Response: {response.text}")
            else:
                print(f"   ❌ FAILED: Status {response.status_code} (expected {test_case['expected_status']})")
                print(f"   📄 Error: {response.text}")
                
            results.append({
                "name": test_case['name'],
                "success": success,
                "status": response.status_code,
                "expected": test_case['expected_status']
            })
            
        except requests.exceptions.ConnectionError:
            print(f"   ❌ CONNECTION FAILED: Backend not running or unreachable")
            results.append({
                "name": test_case['name'],
                "success": False,
                "status": "Connection Error",
                "expected": test_case['expected_status']
            })
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
            results.append({
                "name": test_case['name'],
                "success": False,
                "status": f"Error: {str(e)}",
                "expected": test_case['expected_status']
            })
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMMUNICATION TEST RESULTS")
    print("=" * 60)
    
    successful_tests = sum(1 for r in results if r['success'])
    total_tests = len(results)
    
    for result in results:
        status_icon = "✅" if result['success'] else "❌"
        print(f"{status_icon} {result['name']}: {result['status']}")
    
    print(f"\n🎯 OVERALL RESULT: {successful_tests}/{total_tests} tests passed")
    
    if successful_tests == total_tests:
        print("🎉 SUCCESS: Frontend-Backend communication is working!")
        print("   The API URL fix has resolved the double /api issue.")
        print("   Frontend can now successfully communicate with backend.")
    elif successful_tests > 0:
        print("⚠️  PARTIAL SUCCESS: Some endpoints working, others may need attention.")
    else:
        print("❌ FAILURE: No endpoints working. Check backend status and configuration.")
    
    return successful_tests == total_tests

def test_authentication_flow():
    """Test authentication endpoints specifically"""
    print("\n" + "=" * 60)
    print("🔐 Testing Authentication Flow")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    # Test auth endpoints
    auth_tests = [
        {
            "name": "Auth Ping",
            "url": f"{base_url}/api/auth/ping",
            "description": "Test auth service availability"
        },
        {
            "name": "AI Assistant Auth Test",
            "url": f"{base_url}/api/ai/test-connection",
            "description": "Test AI assistant auth (should fail without token - expected)"
        }
    ]
    
    for test in auth_tests:
        print(f"\n🔍 {test['name']}")
        print(f"   URL: {test['url']}")
        
        try:
            response = requests.get(test['url'], timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ SUCCESS: {response.json()}")
            elif response.status_code == 401:
                print(f"   ✅ EXPECTED: Authentication required (this is correct)")
            else:
                print(f"   📄 Response: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")

if __name__ == "__main__":
    print("Starting comprehensive frontend-backend communication test...")
    time.sleep(1)
    
    # Test basic API communication
    communication_success = test_api_communication()
    
    # Test authentication flow
    test_authentication_flow()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL CONCLUSION")
    print("=" * 60)
    
    if communication_success:
        print("✅ FRONTEND-BACKEND COMMUNICATION FIX: SUCCESS")
        print("   • API URL mismatch resolved")
        print("   • Frontend can reach backend endpoints")
        print("   • Ready for authentication and feature testing")
        print("\n🎯 NEXT STEPS:")
        print("   1. Test frontend login functionality")
        print("   2. Verify authenticated API calls work")
        print("   3. Test all major application features")
    else:
        print("❌ FRONTEND-BACKEND COMMUNICATION FIX: NEEDS ATTENTION")
        print("   • Some endpoints still not working")
        print("   • May need additional configuration")
        print("   • Check backend logs for errors")
