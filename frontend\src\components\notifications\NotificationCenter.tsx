import React from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  Popover,
  List,
  ListItem,
  ListItemText,
  Typography,
  IconButton,
  Divider,
  Badge,
  Button,
} from "@mui/material";
import {
  Close as CloseIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
} from "@mui/icons-material";
import {
  selectNotifications,
  selectUnreadNotificationsCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  clearNotifications,
} from "../../store/ui/uiSlice";

interface NotificationCenterProps {
  anchorEl: HTMLElement | null;
  onClose: () => void;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({
  anchorEl,
  onClose,
}) => {
  const dispatch = useDispatch();
  const notifications = useSelector(selectNotifications);
  const unreadCount = useSelector(selectUnreadNotificationsCount);

  const handleMarkAsRead = (id: string) => {
    dispatch(markNotificationAsRead(id));
  };

  const handleMarkAllAsRead = () => {
    dispatch(markAllNotificationsAsRead());
  };

  const handleClearAll = () => {
    dispatch(clearNotifications());
    onClose();
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return <SuccessIcon color="success" />;
      case "error":
        return <ErrorIcon color="error" />;
      case "warning":
        return <WarningIcon color="warning" />;
      case "info":
      default:
        return <InfoIcon color="info" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <Popover
      open={Boolean(anchorEl)}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
      PaperProps={{
        sx: {
          width: 320,
          maxHeight: 400,
          borderRadius: 2,
          boxShadow: 4,
        },
      }}
    >
      <Box
        sx={{
          p: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h6" fontWeight="bold">
          Notifications
          {unreadCount > 0 && (
            <Badge color="primary" badgeContent={unreadCount} sx={{ ml: 1 }} />
          )}
        </Typography>
        <Box>
          {unreadCount > 0 && (
            <Button size="small" onClick={handleMarkAllAsRead} sx={{ mr: 1 }}>
              Mark all read
            </Button>
          )}
          <Button size="small" onClick={handleClearAll}>
            Clear all
          </Button>
        </Box>
      </Box>

      <Divider />

      {notifications.length === 0 ? (
        <Box sx={{ p: 4, textAlign: "center" }}>
          <Typography color="text.secondary">No notifications</Typography>
        </Box>
      ) : (
        <List sx={{ p: 0 }}>
          {notifications.map((notification) => (
            <React.Fragment key={notification.id}>
              <ListItem
                secondaryAction={
                  <IconButton
                    edge="end"
                    aria-label="mark as read"
                    onClick={() => handleMarkAsRead(notification.id)}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                }
                sx={{
                  bgcolor: notification.read ? "transparent" : "action.hover",
                  transition: "background-color 0.2s",
                }}
              >
                <Box sx={{ display: "flex", alignItems: "flex-start", pr: 2 }}>
                  <Box sx={{ mr: 2, mt: 0.5 }}>
                    {getNotificationIcon(notification.type)}
                  </Box>
                  <Box>
                    <ListItemText
                      primary={notification.message}
                      secondary={formatTimestamp(notification.timestamp)}
                      primaryTypographyProps={{
                        variant: "body2",
                        fontWeight: notification.read ? "normal" : "bold",
                      }}
                      secondaryTypographyProps={{
                        variant: "caption",
                        color: "text.secondary",
                      }}
                    />
                  </Box>
                </Box>
              </ListItem>
              <Divider component="li" />
            </React.Fragment>
          ))}
        </List>
      )}
    </Popover>
  );
};

export default NotificationCenter;
