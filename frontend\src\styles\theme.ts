import { PaletteMode } from "@mui/material";
import {
  ThemeOptions,
  experimental_extendTheme as extendTheme,
  alpha,
  Theme,
  // responsiveFontSizes, // Keep commented
} from "@mui/material/styles";
import type {} from "@mui/material/themeCssVarsAugmentation";
import { common } from "@mui/material/colors";

// Common color definitions
const commonColors = {
  primary: {
    main: "#6366F1",
    light: "#818CF8",
    dark: "#4F46E5",
    contrastText: "#FFFFFF",
  },
  secondary: {
    main: "#EC4899",
    light: "#F472B6",
    dark: "#DB2777",
    contrastText: "#FFFFFF",
  },
  success: { main: "#10B981", light: "#34D399", dark: "#059669", contrastText: "#FFFFFF" },
  error: { main: "#EF4444", light: "#F87171", dark: "#DC2626", contrastText: "#FFFFFF" },
  warning: { main: "#F59E0B", light: "#FBBF24", dark: "#D97706", contrastText: "#FFFFFF" },
  info: { main: "#3B82F6", light: "#60A5FA", dark: "#2563EB", contrastText: "#FFFFFF" },
};

const lightPalette = {
  mode: "light" as PaletteMode,
  ...commonColors,
  background: { default: "#F9FAFB", paper: "#FFFFFF" },
  text: { primary: "#111827", secondary: "#6B7280", disabled: "#9CA3AF" },
  divider: "rgba(0, 0, 0, 0.08)",
  // Ensure all required palette properties are present if extendTheme is strict
  common: { black: common.black, white: common.white },
  grey: {
    50: "#F9FAFB",
    100: "#F3F4F6",
    200: "#E5E7EB",
    300: "#D1D5DB",
    400: "#9CA3AF",
    500: "#6B7280",
    600: "#4B5563",
    700: "#374151",
    800: "#1F2937",
    900: "#111827",
    A100: "#F3F4F6",
    A200: "#E5E7EB",
    A400: "#9CA3AF",
    A700: "#374151",
  },
  action: {
    active: alpha("#111827", 0.54),
    hover: alpha("#111827", 0.04),
    selected: alpha("#111827", 0.08),
    disabled: alpha("#111827", 0.26),
    disabledBackground: alpha("#111827", 0.12),
    focus: alpha("#111827", 0.12),
  },
};

const darkPalette = {
  mode: "dark" as PaletteMode,
  ...commonColors,
  background: { default: "#111827", paper: "#1F2937" },
  text: { primary: "#F9FAFB", secondary: "#D1D5DB", disabled: "#6B7280" },
  divider: "rgba(255, 255, 255, 0.08)",
  common: { black: common.black, white: common.white },
  grey: {
    50: "#F9FAFB", // These might need to be dark mode specific greys
    100: "#F3F4F6",
    200: "#E5E7EB",
    300: "#D1D5DB",
    400: "#9CA3AF",
    500: "#6B7280",
    600: "#4B5563",
    700: "#374151",
    800: "#1F2937",
    900: "#111827",
    A100: "#F3F4F6",
    A200: "#E5E7EB",
    A400: "#9CA3AF",
    A700: "#374151",
  }, // Example, fill appropriately
  action: {
    active: "#F9FAFB",
    hover: alpha("#F9FAFB", 0.08),
    selected: alpha("#F9FAFB", 0.16),
    disabled: alpha("#F9FAFB", 0.3),
    disabledBackground: alpha("#F9FAFB", 0.12),
    focus: alpha("#F9FAFB", 0.12),
  },
};

// Define shadows once, as they are not mode-dependent in this setup
// MUI expects exactly 25 shadow values
const shadows: ThemeOptions['shadows'] = [
  "none",
  "0px 2px 4px rgba(0, 0, 0, 0.05)",
  "0px 4px 6px rgba(0, 0, 0, 0.07)",
  "0px 6px 8px rgba(0, 0, 0, 0.08)",
  "0px 8px 12px rgba(0, 0, 0, 0.09)",
  "0px 10px 14px rgba(0, 0, 0, 0.1)",
  "0px 12px 16px rgba(0, 0, 0, 0.11)",
  "0px 14px 18px rgba(0, 0, 0, 0.12)",
  "0px 16px 20px rgba(0, 0, 0, 0.13)",
  "0px 18px 22px rgba(0, 0, 0, 0.14)",
  "0px 20px 24px rgba(0, 0, 0, 0.15)",
  "0px 22px 26px rgba(0, 0, 0, 0.16)",
  "0px 24px 28px rgba(0, 0, 0, 0.17)",
  "0px 26px 30px rgba(0, 0, 0, 0.18)",
  "0px 28px 32px rgba(0, 0, 0, 0.19)",
  "0px 30px 34px rgba(0, 0, 0, 0.2)",
  "0px 32px 36px rgba(0, 0, 0, 0.21)",
  "0px 34px 38px rgba(0, 0, 0, 0.22)",
  "0px 36px 40px rgba(0, 0, 0, 0.23)",
  "0px 38px 42px rgba(0, 0, 0, 0.24)",
  "0px 40px 44px rgba(0, 0, 0, 0.25)",
  "0px 42px 46px rgba(0, 0, 0, 0.26)",
  "0px 44px 48px rgba(0, 0, 0, 0.27)",
  "0px 46px 50px rgba(0, 0, 0, 0.28)",
  "0px 48px 52px rgba(0, 0, 0, 0.29)"
];

// Example of setting cssVarPrefix, to be properly implemented in a comprehensive refactoring
// const THEME_PREFIX = 'writer';

export const getTheme = (mode: PaletteMode) => {
  // The `mode` parameter is still used by extendTheme to know the initial mode,
  // but component styles will rely on `theme.palette.mode` from the theme object passed to them.
  const theme = extendTheme({
    colorSchemes: {
      light: { palette: lightPalette },
      dark: { palette: darkPalette },
    },
    typography: {
      fontFamily: "\"Inter\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\"",
      h1: { fontWeight: 700, fontSize: "3.5rem", lineHeight: 1.2 },
      h2: { fontWeight: 700, fontSize: "3rem", lineHeight: 1.2 },
      h3: { fontWeight: 700, fontSize: "2.25rem", lineHeight: 1.2 },
      h4: { fontWeight: 600, fontSize: "1.75rem", lineHeight: 1.2 },
      h5: { fontWeight: 600, fontSize: "1.5rem", lineHeight: 1.2 },
      h6: { fontWeight: 600, fontSize: "1.25rem", lineHeight: 1.2 },
      subtitle1: { fontSize: "1rem", fontWeight: 500 },
      subtitle2: { fontSize: "0.875rem", fontWeight: 500 },
      body1: { fontSize: "1rem", lineHeight: 1.6 },
      body2: { fontSize: "0.875rem", lineHeight: 1.5 },
      button: { fontWeight: 600, textTransform: "none" },
      caption: { fontSize: "0.75rem" },
      overline: { fontSize: "0.75rem", textTransform: "uppercase", letterSpacing: "0.5px" },
    },
    shape: {
      borderRadius: 12,
    },
    shadows: shadows, // Use the predefined shadows array
    components: { // Components defined at the top level
      MuiButton: {
        styleOverrides: {
          root: ({ theme }: { theme: Theme }) => ({
            borderRadius: theme.shape.borderRadius / 1.5,
            padding: theme.spacing(1.25, 2.5),
            boxShadow: "none",
            "&:hover": {
              boxShadow: theme.shadows[2],
              transform: "translateY(-1px)",
              backgroundColor: alpha(theme.palette.primary.main, 0.08),
            },
            transition: "all 0.2s ease-in-out",
          }),
          contained: ({ theme }: { theme: Theme }) => ({
            "&:hover": {
              boxShadow: theme.shadows[4],
            },
          }),
        },
      },
      MuiCard: {
        styleOverrides: {
          root: ({ theme }: { theme: Theme }) => ({
            borderRadius: theme.shape.borderRadius * 4 / 3,
            boxShadow: `0px 4px 6px ${alpha(theme.palette.divider, 0.5)}`,
          }),
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: ({ theme }: { theme: Theme }) => ({
            "& .MuiOutlinedInput-root": {
              borderRadius: theme.shape.borderRadius / 1.5,
              "& fieldset": {
                borderColor: alpha(theme.palette.primary.main, 0.3),
              },
              "&:hover fieldset": {
                borderColor: theme.palette.primary.main,
              },
              "&.Mui-focused fieldset": {
                borderColor: theme.palette.primary.dark,
                borderWidth: '1px',
              },
            },
            "& .MuiInputLabel-outlined": {
              transform: "translate(14px, 12px) scale(1)",
              "&.MuiInputLabel-shrink": { transform: "translate(14px, -9px) scale(0.75)" },
            },
            "& .MuiOutlinedInput-input": { padding: theme.spacing(1.5, 1.75) },
          }),
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundImage: "none",
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: ({ theme }: { theme: Theme }) => ({
            boxShadow: "none",
            borderBottom: `1px solid ${theme.palette.divider}`,
            backgroundColor: theme.palette.background.paper,
            color: theme.palette.text.primary,
          }),
        },
      },
      MuiDrawer: {
        styleOverrides: {
          paper: ({ theme }: { theme: Theme }) => ({
            borderRight: `1px solid ${theme.palette.divider}`,
          }),
        }
      },
      // ... other component overrides can be added here
    },
  });

  // Responsive font sizes may need to be handled differently with extendTheme.
  // For now, we are not applying it.
  // return responsiveFontSizes(theme);
  return theme;
};
