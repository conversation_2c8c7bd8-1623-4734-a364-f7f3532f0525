import React, { Suspense, lazy, useEffect, useState } from "react";
import {
  Routes,
  Route,
  Navigate,
  useLocation,
} from "react-router-dom";
import { Box, Snackbar, Alert } from "@mui/material";
import { useSelector } from "react-redux";
import {
  selectIsAuthenticated,
} from "./store/auth/authSlice";
import { ContentSelectionProvider } from "./contexts/ContentSelectionContext";
import { AuthProvider } from "./contexts/AuthContext";
import { BusinessContextSelectionProvider } from "./contexts/BusinessContextSelectionContext";

// Import auth debugger for development
import './utils/authDebugger';

// Layout components
import MainLayout from "./components/layouts/MainLayout";
import AuthLayout from "./components/layouts/AuthLayout";

// Pages
import RequestPasswordReset from "./pages/auth/RequestPasswordReset";
import ResetPassword from "./pages/auth/ResetPassword";
import Dashboard from "./pages/Dashboard";
import TranscriptFormatterTest from "./components/TranscriptFormatterTest";
import EnvironmentTest from "./components/EnvironmentTest";
import ApiTest from "./components/ApiTest";

// Lazy-loaded Pages
const Login = lazy(() => import("./pages/auth/Login"));
const Register = lazy(() => import("./pages/auth/Register"));
const ContentLibrary = lazy(() => import("./pages/ContentLibrary"));
const BusinessContextPage = lazy(() => import("./pages/BusinessContextPage"));
const AIAssistant = lazy(() => import("./pages/AIAssistant"));
const YouTubeAnalytics = lazy(() => import("./pages/YouTubeAnalytics"));
const UserProfile = lazy(() => import("./pages/UserProfile"));
const WizardPage = lazy(() => import("./pages/WizardPage"));
const NotFound = lazy(() => import("./pages/NotFound"));

// Time synchronization component
const TimeSynchronization: React.FC = () => {
  const [timeWarning, setTimeWarning] = useState(false);

  useEffect(() => {
    // Check for time/date issues
    const checkTimeSynchronization = () => {
      const yearDifference = localStorage.getItem("yearDifference");
      if (yearDifference) {
        console.warn(
          `Detected potential time synchronization issue: ${yearDifference} year difference`
        );
        setTimeWarning(true);
      }
    };

    // Run on mount and whenever the token changes
    window.addEventListener("storage", (e) => {
      if (e.key === "token" || e.key === "yearDifference") {
        checkTimeSynchronization();
      }
    });

    checkTimeSynchronization();

    return () => {
      window.removeEventListener("storage", checkTimeSynchronization);
    };
  }, []);

  return (
    <Snackbar
      open={timeWarning}
      anchorOrigin={{ vertical: "top", horizontal: "center" }}
      autoHideDuration={10000}
      onClose={() => setTimeWarning(false)}
    >
      <Alert severity="warning" onClose={() => setTimeWarning(false)}>
        System time difference detected which may affect authentication. Please
        check your device's date and time settings.
      </Alert>
    </Snackbar>
  );
};

// Protected route wrapper - enforces authentication
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const isAuthenticated = useSelector(selectIsAuthenticated);

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

// Auth route wrapper - redirects authenticated users away from login pages
const AuthRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const location = useLocation();

  // If authenticated and trying to access auth pages (login, register, etc.), redirect to dashboard
  if (
    isAuthenticated &&
    (location.pathname === "/login" ||
     location.pathname === "/register" ||
     location.pathname === "/forgot-password" ||
     location.pathname === "/reset-password") &&
    !location.state?.justLoggedIn
  ) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  // Removed unused location variable
  // const location = useLocation();
  // Removed unused isAuthenticated variable
  // const isAuthenticated = useSelector(selectIsAuthenticated);

  // Effect to check session expiration (if needed, might be handled by Supabase client)

  return (
    <AuthProvider>
      <Box sx={{ height: "100vh", display: "flex", flexDirection: "column" }}>
        {/* Session Manager for basic session monitoring */}
        {/* <SessionManager /> */}

        {/* Time synchronization warning */}
        <TimeSynchronization />

        <Suspense fallback={<div className="loading-spinner">Loading...</div>}>
          <Routes>
            {/* Auth routes */}
            <Route element={<AuthLayout />}>
              <Route
                path="/login"
                element={
                  <AuthRoute>
                    <Login />
                  </AuthRoute>
                }
              />
              <Route
                path="/register"
                element={
                  <AuthRoute>
                    <Register />
                  </AuthRoute>
                }
              />
              <Route
                path="/forgot-password"
                element={
                  <AuthRoute>
                    <RequestPasswordReset />
                  </AuthRoute>
                }
              />
              <Route
                path="/reset-password"
                element={
                  <AuthRoute>
                    <ResetPassword />
                  </AuthRoute>
                }
              />
            </Route>

            {/* Protected routes */}
            <Route
              element={
                <BusinessContextSelectionProvider>
                  <ContentSelectionProvider>
                    <MainLayout />
                  </ContentSelectionProvider>
                </BusinessContextSelectionProvider>
              }
            >
              <Route
                index
                element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path="content-library"
                element={
                  <ProtectedRoute>
                    <ContentLibrary />
                  </ProtectedRoute>
                }
              />
              <Route
                path="business-context"
                element={
                  <ProtectedRoute>
                    <BusinessContextPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="ai-assistant"
                element={
                  <ProtectedRoute>
                    <AIAssistant />
                  </ProtectedRoute>
                }
              />
              <Route
                path="youtube-analytics"
                element={
                  <ProtectedRoute>
                    <YouTubeAnalytics />
                  </ProtectedRoute>
                }
              />
              <Route
                path="profile"
                element={
                  <ProtectedRoute>
                    <UserProfile />
                  </ProtectedRoute>
                }
              />
              <Route
                path="wizard"
                element={
                  <ProtectedRoute>
                    <WizardPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/transcript-formatter-test"
                element={<TranscriptFormatterTest />}
              />
              <Route path="/env-test" element={<EnvironmentTest />} />
              <Route path="/test/env" element={<EnvironmentTest />} />
              <Route
                path="/test/transcript"
                element={<TranscriptFormatterTest />}
              />
              <Route path="/test/api" element={<ApiTest />} />
            </Route>

            {/* 404 route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Suspense>
      </Box>
    </AuthProvider>
  );
};

export default App;
