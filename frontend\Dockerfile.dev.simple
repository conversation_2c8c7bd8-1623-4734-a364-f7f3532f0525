# Simple frontend Dockerfile - copies all source code
FROM node:18-alpine

WORKDIR /app

# Install curl for health checks
RUN apk add --no-cache curl

# Copy package files and install dependencies
COPY package*.json ./
RUN npm ci --silent --no-audit --no-fund && npm cache clean --force

# Copy all source code
COPY . .

# Set environment for Docker networking
ENV HOST=0.0.0.0
ENV PORT=3000
ENV GENERATE_SOURCEMAP=false
ENV CHOKIDAR_USEPOLLING=true

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

CMD ["npm", "start"]