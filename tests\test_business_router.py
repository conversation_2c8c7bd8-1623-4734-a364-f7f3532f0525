"""
Tests for FastAPI business router.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, AsyncMock

from main import app
from app.auth.dependencies import get_current_user
from app.dependencies import get_business_context_repository


@pytest.fixture
def mock_current_user():
    """Mock current user for testing."""
    user = Mock()
    user.user_id = "test-user-123"
    user.id = "test-user-123"
    return user


@pytest.fixture
def mock_business_repo():
    """Mock business context repository."""
    repo = Mock()
    repo.create_business_context = AsyncMock()
    repo.get_by_user_id = AsyncMock()
    repo.get_by_id = AsyncMock()
    repo.update_business_context = AsyncMock()
    repo.delete_business_context = AsyncMock()
    return repo


@pytest.fixture
def client(mock_current_user, mock_business_repo):
    """Test client with mocked dependencies."""
    app.dependency_overrides[get_current_user] = lambda: mock_current_user
    app.dependency_overrides[get_business_context_repository] = lambda: mock_business_repo
    
    with TestClient(app) as client:
        yield client
    
    # Clean up
    app.dependency_overrides.clear()


@pytest.fixture
def sample_business_context():
    """Sample business context data."""
    return {
        "profile_overview": "A tech startup focused on AI solutions",
        "content_focus": ["AI", "Technology", "Innovation"],
        "target_audience": "Tech entrepreneurs and developers",
        "brand_voice": "Professional yet approachable"
    }


@pytest.fixture
def sample_business_response():
    """Sample business context response."""
    return {
        "id": "context-123",
        "user_id": "test-user-123",
        "profile_overview": "A tech startup focused on AI solutions",
        "content_focus": ["AI", "Technology", "Innovation"],
        "target_audience": "Tech entrepreneurs and developers",
        "brand_voice": "Professional yet approachable",
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z"
    }


def test_create_business_context_success(client, mock_business_repo, sample_business_context, sample_business_response):
    """Test successful business context creation."""
    mock_business_repo.create_business_context.return_value = sample_business_response
    
    response = client.post("/api/business/", json=sample_business_context)
    
    assert response.status_code == 201
    data = response.json()
    assert data["id"] == "context-123"
    assert data["profile_overview"] == sample_business_context["profile_overview"]
    
    # Verify repository was called correctly
    mock_business_repo.create_business_context.assert_called_once()


def test_get_business_contexts_success(client, mock_business_repo, sample_business_response):
    """Test successful retrieval of business contexts."""
    mock_business_repo.get_by_user_id.return_value = [sample_business_response]
    
    response = client.get("/api/business/")
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["id"] == "context-123"
    
    # Verify repository was called correctly
    mock_business_repo.get_by_user_id.assert_called_once_with("test-user-123")


def test_get_business_context_success(client, mock_business_repo, sample_business_response):
    """Test successful retrieval of a specific business context."""
    mock_business_repo.get_by_id.return_value = sample_business_response
    
    response = client.get("/api/business/context-123")
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == "context-123"
    
    # Verify repository was called correctly
    mock_business_repo.get_by_id.assert_called_once_with("context-123", "test-user-123")


def test_get_business_context_not_found(client, mock_business_repo):
    """Test business context not found."""
    mock_business_repo.get_by_id.return_value = None
    
    response = client.get("/api/business/nonexistent")
    
    assert response.status_code == 404
    data = response.json()
    assert "not found" in data["detail"].lower()


def test_update_business_context_success(client, mock_business_repo, sample_business_response):
    """Test successful business context update."""
    # Mock existing context
    mock_business_repo.get_by_id.return_value = sample_business_response
    
    # Mock updated context
    updated_response = sample_business_response.copy()
    updated_response["profile_overview"] = "Updated overview"
    mock_business_repo.update_business_context.return_value = updated_response
    
    update_data = {"profile_overview": "Updated overview"}
    response = client.put("/api/business/context-123", json=update_data)
    
    assert response.status_code == 200
    data = response.json()
    assert data["profile_overview"] == "Updated overview"
    
    # Verify repository was called correctly
    mock_business_repo.update_business_context.assert_called_once()


def test_delete_business_context_success(client, mock_business_repo, sample_business_response):
    """Test successful business context deletion."""
    mock_business_repo.get_by_id.return_value = sample_business_response
    mock_business_repo.delete_business_context.return_value = None
    
    response = client.delete("/api/business/context-123")
    
    assert response.status_code == 200
    data = response.json()
    assert "deleted successfully" in data["message"]
    
    # Verify repository was called correctly
    mock_business_repo.delete_business_context.assert_called_once_with("context-123", "test-user-123")


def test_create_business_context_validation_error(client):
    """Test business context creation with validation error."""
    # Missing required fields
    invalid_data = {"profile_overview": "Test"}
    
    response = client.post("/api/business/", json=invalid_data)
    
    assert response.status_code == 422  # Validation error
    data = response.json()
    assert "detail" in data
