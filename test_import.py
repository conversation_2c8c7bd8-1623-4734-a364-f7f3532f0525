#!/usr/bin/env python3
"""
Test script to check if we can import the ai_assistant router
"""

try:
    print("Testing imports...")
    
    print("1. Importing app.routers.ai_assistant...")
    from app.routers import ai_assistant
    print("   ✓ Successfully imported ai_assistant")
    
    print("2. Checking router object...")
    print(f"   Router: {ai_assistant.router}")
    print(f"   Router prefix: {ai_assistant.router.prefix}")
    print(f"   Router tags: {ai_assistant.router.tags}")
    
    print("3. Checking routes...")
    routes = ai_assistant.router.routes
    print(f"   Number of routes: {len(routes)}")
    for route in routes:
        if hasattr(route, 'path') and hasattr(route, 'methods'):
            print(f"   - {list(route.methods)[0] if route.methods else 'UNKNOWN'} {route.path}")
    
    print("\n✓ All imports successful!")
    
except Exception as e:
    print(f"✗ Import failed: {e}")
    import traceback
    traceback.print_exc()
