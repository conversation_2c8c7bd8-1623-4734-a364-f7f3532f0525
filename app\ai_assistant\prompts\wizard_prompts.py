# app/ai_assistant/prompts/wizard_prompts.py
from typing import List, Dict, Any, Optional

# --- System Prompt ---
# Define a consistent system prompt if needed for the wizard context
WIZARD_SYSTEM_PROMPT = """
You are an expert video script writing assistant. Follow the specific instructions for each stage precisely.
Be concise and creative.
""" # Optional: refine as needed

# --- Helper to format context ---
def _format_context_for_prompt(business_context: Optional[Dict[str, Any]]) -> str:
    if not business_context:
        return ""

    lines = ["\n\nConsider this Business Context:"]
    field_mapping = [
        ("name", "Profile Name"),
        ("offer_description", "Offer"),
        ("target_audience", "Target Audience"),
        ("brand_voice", "Brand Voice"),
        ("key_benefits", "Key Benefits"),
        ("unique_value_proposition", "Unique Value Proposition"),
        ("primary_objectives", "Objectives"),
        ("content_focus", "Content Focus")
    ]

    for key, label in field_mapping:
        value = business_context.get(key)
        if value:
            if isinstance(value, list):
                lines.append(f"- {label}: {', '.join(value)}")
            else:
                lines.append(f"- {label}: {value}")

    return "\n".join(lines)


# --- Stage-Specific Prompt Builders ---

def brainstorm_titles_prompt(topic: str, target_audience: str) -> str:
    """Generates the prompt for the brainstorm stage."""
    return f"""
    You are an expert video script writer specializing in creating engaging content for {target_audience}.

    Generate 5 creative and compelling video title ideas about '{topic}' that would resonate with this audience.

    Your titles should be:
    - Attention-grabbing
    - Clear and concise (under 60 characters)
    - Relevant to the topic
    - Tailored to the target audience
    - Optimized for engagement

    Output ONLY a valid JSON list of strings. Example:
    [
      "Title Idea 1",
      "Title Idea 2",
      "Title Idea 3",
      "Title Idea 4",
      "Title Idea 5"
    ]
    """

def hook_variations_prompt(chosen_title: str, business_context: Optional[Dict[str, Any]]) -> str:
    """Generates the prompt for the hook stage."""
    context_str = _format_context_for_prompt(business_context)
    return f"""
Given the video title: "{chosen_title}".
{context_str}

Generate 3 distinct and compelling 1-sentence hooks (≤10 words each) to grab the viewer's attention immediately, keeping the brand voice and target audience in mind.

Output ONLY a valid JSON list of strings. Example:
[
  "Hook Idea 1",
  "Hook Idea 2",
  "Hook Idea 3"
]
"""

def intro_cta_prompt(chosen_hook: str, business_context: Optional[Dict[str, Any]]) -> str:
    """Generates the prompt for the intro/CTA stage."""
    context_str = _format_context_for_prompt(business_context)
    return f"""
Based on the hook: "{chosen_hook}".
{context_str}

Write 3 distinct pairs of Intro and CTA (Call to Action) for a short video. Adapt the Brand Voice.
- Intro: Expand on the hook, build curiosity. ~1 sentence.
- CTA: Clear, concise action for the viewer. ~1 sentence.

Output ONLY a valid JSON list of objects, where each object has "intro" and "cta" keys. Example:
[
  {{
    "intro": "Intro variation 1 building on the hook...",
    "cta": "CTA variation 1 asking for action..."
  }},
  {{
    "intro": "Intro variation 2...",
    "cta": "CTA variation 2..."
  }},
  {{
    "intro": "Intro variation 3...",
    "cta": "CTA variation 3..."
  }}
]
"""

def outline_prompt(chosen_intro: str, chosen_cta: str, chosen_title: str, business_context: Optional[Dict[str, Any]]) -> str:
    """Generates the prompt for the outline stage."""
    context_str = _format_context_for_prompt(business_context)
    return f"""
Video Title: "{chosen_title}"
Chosen Intro: "{chosen_intro}"
Chosen CTA: "{chosen_cta}"
{context_str}

Create 3 distinct, structured outlines for the main body of this video. Each outline should logically connect the intro to the CTA.

Each outline must follow this JSON structure:
{{
  "sections": [
    {{
      "heading": "Section 1 Title (Concise)",
      "bullets": ["Key point 1.1 (Actionable/Intriguing)", "Key point 1.2"]
    }},
    {{
      "heading": "Section 2 Title",
      "bullets": ["Key point 2.1", "Key point 2.2", "Key point 2.3"]
    }}
    // Include 3-4 sections total per outline
  ]
}}

Consider the business context (Offer, Audience, Objectives) when structuring the flow and points.

Output ONLY a valid JSON list containing these 3 outline objects. Do not include any text outside the JSON list.
"""

def draft_prompt(outline: Dict[str, Any], chosen_title: str, business_context: Optional[Dict[str, Any]]) -> str:
    """Generates the prompt for the draft stage."""
    context_str = _format_context_for_prompt(business_context)
    # Convert outline dict to a string format the LLM can easily parse
    outline_sections = outline.get('sections', [])
    if not outline_sections:
        return "Error: Outline is missing sections."

    outline_str = "\n".join([
        f"Section: {s.get('heading', 'Untitled Section')}\n" +
        "\n".join([f"- {b}" for b in s.get('bullets', [])])
        for s in outline_sections
    ])

    return f"""
Video Title: "{chosen_title}"
{context_str}

Write the full video script based on the following outline:
\n{outline_str}\n
Instructions:
- Write in the specified Brand Voice for the Target Audience.
- Ensure smooth, natural transitions between sections.
- Keep the language concise and engaging (short video format).
- Incorporate Key Benefits and the Unique Value Proposition subtly where relevant.
- The script should logically lead from the (implied) intro to the (implied) CTA based on the outline flow.

Output ONLY the raw script text. Do not include section headings unless part of the spoken script.
"""

def edit_prompt(draft: str, business_context: Optional[Dict[str, Any]], style_preferences: str = "review critically, make it more concise and impactful") -> str:
    """Generates the prompt for the edit stage."""
    context_str = _format_context_for_prompt(business_context)
    return f"""
{context_str}

Edit the following video script draft according to these preferences: "{style_preferences}".

Specific Focus Areas:
- Clarity and conciseness: Remove filler words, simplify complex sentences.
- Impact: Strengthen verbs, enhance hooks/key messages.
- Brand Voice Alignment: Ensure the tone matches the defined Brand Voice.
- Audience Relevance: Check if the language resonates with the Target Audience.

Draft:\n```
{draft}
```

Output ONLY the edited script text.
"""

# --- Wrapper functions used by routes ---
# These now accept business_context and pass it down

def get_intro_prompt(chosen_hook: str, business_context: Optional[Dict[str, Any]]) -> str:
    return intro_cta_prompt(chosen_hook=chosen_hook, business_context=business_context)

def get_outline_prompt(chosen_intro: str, business_context: Optional[Dict[str, Any]], **kwargs) -> str:
    """
    Wrapper for outline_prompt that handles missing parameters.

    Args:
        chosen_intro: The selected intro text from the previous step
        business_context: Optional business context information
        **kwargs: Additional parameters that might be needed

    Returns:
        A prompt string for generating outlines
    """
    # Extract session data if available
    session = kwargs.get('session', {})

    # Try to get chosen_cta from session or kwargs, or use a default
    chosen_cta = kwargs.get('chosen_cta', None)
    if not chosen_cta and session:
        # Try to get from session if available
        intro_index = session.get('chosen_intro_index')
        intro_results = session.get('intro_cta_results', [])
        if isinstance(intro_index, int) and intro_results and 0 <= intro_index < len(intro_results):
            if isinstance(intro_results[intro_index], dict):
                chosen_cta = intro_results[intro_index].get('cta', '')

    # If still no CTA, use a placeholder
    if not chosen_cta:
        chosen_cta = "Call to action placeholder"

    # Try to get chosen_title from session or kwargs, or use a default
    chosen_title = kwargs.get('chosen_title', None)
    if not chosen_title and session:
        # Try to get from session if available
        title_index = session.get('chosen_title_index')
        brainstorm_results = session.get('brainstorm_results', [])
        if isinstance(title_index, int) and brainstorm_results and 0 <= title_index < len(brainstorm_results):
            if isinstance(brainstorm_results[title_index], dict):
                chosen_title = brainstorm_results[title_index].get('title', '')
            elif isinstance(brainstorm_results[title_index], str):
                chosen_title = brainstorm_results[title_index]

    # If still no title, use a placeholder
    if not chosen_title:
        chosen_title = "Title placeholder"

    # If we have all the parameters, use the full outline_prompt
    if chosen_cta and chosen_title:
        return outline_prompt(
            chosen_intro=chosen_intro,
            chosen_cta=chosen_cta,
            chosen_title=chosen_title,
            business_context=business_context
        )

    # Otherwise, use a simplified prompt that only requires the intro
    context_str = _format_context_for_prompt(business_context)
    return f"""
Chosen Intro: "{chosen_intro}"
{context_str}

Create 3 distinct, structured outlines for the main body of a video that starts with this intro.

Each outline must follow this JSON structure:
{{
  "sections": [
    {{
      "heading": "Section 1 Title (Concise)",
      "bullets": ["Key point 1.1 (Actionable/Intriguing)", "Key point 1.2"]
    }},
    {{
      "heading": "Section 2 Title",
      "bullets": ["Key point 2.1", "Key point 2.2", "Key point 2.3"]
    }}
    // Include 3-4 sections total per outline
  ]
}}

Consider the business context (Offer, Audience, Objectives) when structuring the flow and points.

Output ONLY a valid JSON list containing these 3 outline objects. Do not include any text outside the JSON list.
"""

def get_brainstorm_prompt(content_idea: str, business_context: Dict[str, Any]) -> str:
    """Generate a prompt for the AI to brainstorm title ideas using business context."""
    # Extract relevant details from the business context, providing defaults
    audience = business_context.get("target_audience", "the target audience")
    brand_voice = business_context.get("brand_voice", "engaging and informative")
    objectives = business_context.get("primary_objectives", "provide value")
    # Format content focus array if present
    content_focus_list = business_context.get("content_focus", [])
    content_focus_str = f" focusing on topics like: {', '.join(content_focus_list)}" if content_focus_list else ""

    prompt = f"""
You are brainstorming video titles for a specific business context.

Business Context:
- Target Audience: {audience}
- Brand Voice: {brand_voice}
- Primary Objectives: {objectives}
- Content Focus:{content_focus_str}

Content Idea: "{content_idea}"

Based on the content idea and the business context provided, brainstorm 5 creative and engaging video title ideas.

Output ONLY a valid JSON list of strings, where each string is a title idea. Example:
[
  "Title Idea 1",
  "Title Idea 2",
  "Title Idea 3",
  "Title Idea 4",
  "Title Idea 5"
]
"""
    return prompt

def get_hook_prompt(chosen_title: str, business_context: Optional[Dict[str, Any]]) -> str:
    return hook_variations_prompt(chosen_title=chosen_title, business_context=business_context)