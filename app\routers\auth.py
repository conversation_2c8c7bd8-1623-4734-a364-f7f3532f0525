"""
Authentication routes for FastAPI.
Migrated from Flask app/auth/routes.py to use FastAPI dependency injection.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, EmailStr, Field

from app.auth.dependencies import CurrentUserType, CurrentUserIdType, OptionalCurrentUserType
from app.dependencies import UserRepositoryType
from app.repositories.supabase_user_repository import UserRepository, SupabaseAuthError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/auth", tags=["authentication"])


# Pydantic models for request/response
class RegisterRequest(BaseModel):
    email: EmailStr
    password: str = Field(..., min_length=6)
    username: Optional[str] = None
    first_name: Optional[str] = ""
    last_name: Optional[str] = ""


class LoginRequest(BaseModel):
    email: EmailStr
    password: str


class PasswordChangeRequest(BaseModel):
    current_password: Optional[str] = ""
    new_password: str = Field(..., min_length=6)


class PasswordResetRequest(BaseModel):
    email: EmailStr


class TokenVerifyRequest(BaseModel):
    token: str


class RefreshTokenRequest(BaseModel):
    refresh_token: str


class ProfileUpdateRequest(BaseModel):
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None


class AuthResponse(BaseModel):
    message: str
    user: Dict[str, Any]
    session: Optional[Dict[str, Any]] = None
    access_token: Optional[str] = None


class UserProfileResponse(BaseModel):
    id: str
    email: str
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    last_sign_in_at: Optional[str] = None


@router.post("/register", response_model=AuthResponse, status_code=201)
async def register(
    register_data: RegisterRequest,
    user_repo: UserRepositoryType
) -> AuthResponse:
    """Register a new user with Supabase"""
    try:
        # Check if email already exists
        existing_user = await user_repo.get_user_by_email(register_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Email already exists"
            )

        # Set username to email if not provided
        username = register_data.username or register_data.email

        # Register user with Supabase
        result = await user_repo.register(
            email=register_data.email,
            password=register_data.password,
            username=username,
            first_name=register_data.first_name,
            last_name=register_data.last_name,
        )

        # Return user info and session
        return AuthResponse(
            message="User registered successfully",
            user=result["user"],
            session={
                "access_token": result["session"].access_token,
                "refresh_token": result["session"].refresh_token,
                "expires_at": result["session"].expires_at,
                "user": {
                    "id": result["user"]["id"],
                    "email": result["user"]["email"],
                },
            },
            access_token=result["session"].access_token
        )

    except SupabaseAuthError as e:
        logger.error("Supabase auth error during registration: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error during registration: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=AuthResponse)
async def login(
    login_data: LoginRequest,
    user_repo: UserRepositoryType
) -> AuthResponse:
    """Log in a user with Supabase Auth"""
    try:
        logger.debug(f"Starting login for email: {login_data.email}")

        # Use direct Supabase authentication
        from app.models.supabase_client import get_auth

        # Get auth client
        auth_client = await get_auth()
        logger.debug("Auth client obtained successfully")

        # Perform authentication
        auth_response = await auth_client.sign_in_with_password({
            "email": login_data.email,
            "password": login_data.password
        })

        logger.debug(f"Auth response received: {bool(auth_response)}")

        if not auth_response or not auth_response.user:
            logger.error("Authentication failed - no user in response")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Extract user data
        user_data = {
            "id": auth_response.user.id,
            "email": auth_response.user.email,
            "created_at": auth_response.user.created_at.isoformat() if auth_response.user.created_at else None,
            "updated_at": auth_response.user.updated_at.isoformat() if auth_response.user.updated_at else None,
            "last_sign_in_at": auth_response.user.last_sign_in_at.isoformat() if auth_response.user.last_sign_in_at else None,
        }

        # Try to get additional user data from database
        try:
            db_user = await user_repo.get_by_id(auth_response.user.id)
            if db_user:
                user_data.update(db_user)
                logger.debug("Enhanced user data with database info")
        except Exception as e:
            logger.warning(f"Could not enhance user data from database: {e}")

        # Extract session data
        session_data = None
        access_token = None

        if auth_response.session:
            access_token = auth_response.session.access_token
            session_data = {
                "access_token": access_token,
                "refresh_token": auth_response.session.refresh_token,
                "expires_at": auth_response.session.expires_at,
                "expires_in": auth_response.session.expires_in,
                "token_type": auth_response.session.token_type,
                "user": user_data,
            }
            logger.debug("Session data extracted successfully")
        else:
            logger.error("No session in auth response")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Login failed - no session"
            )

        logger.info(f"Login successful for user: {user_data['id']}")

        return AuthResponse(
            message="Login successful",
            user=user_data,
            session=session_data,
            access_token=access_token
        )

    except SupabaseAuthError as e:
        logger.error("Supabase auth error during login: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error during login: %s", str(e))
        logger.exception("Full login exception traceback:")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/logout")
async def logout(
    current_user: CurrentUserType,
    user_repo: UserRepositoryType
) -> Dict[str, str]:
    """Log out a user from Supabase Auth"""
    try:
        await user_repo.logout()
        return {"message": "Logout successful"}

    except SupabaseAuthError as e:
        logger.error(f"Supabase auth error during logout: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except Exception as e:
        logger.error("Error during logout: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/profile", response_model=UserProfileResponse)
async def get_profile(current_user: CurrentUserType) -> UserProfileResponse:
    """Get the current user's profile"""
    if not current_user.user_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return UserProfileResponse(**current_user.user_data)


@router.put("/profile", response_model=Dict[str, Any])
async def update_profile(
    profile_data: ProfileUpdateRequest,
    current_user: CurrentUserType,
    user_repo: UserRepositoryType
) -> Dict[str, Any]:
    """Update the current user's profile"""
    try:
        # Remove None values from update data
        update_data = {k: v for k, v in profile_data.model_dump().items() if v is not None}

        # Update profile in Supabase
        updated_user = await user_repo.update_profile(current_user.user_id, update_data)

        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found or update failed"
            )

        return {"message": "Profile updated successfully", "user": updated_user}

    except SupabaseAuthError as e:
        if "already taken" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Email already taken"
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error updating profile: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )


@router.put("/password")
async def change_password(
    password_data: PasswordChangeRequest,
    current_user: CurrentUserType,
    user_repo: UserRepositoryType
) -> Dict[str, str]:
    """Change the current user's password"""
    try:
        # Change password in Supabase
        success = await user_repo.change_password(
            current_user.user_id,
            password_data.current_password,
            password_data.new_password,
        )

        if success:
            return {"message": "Password changed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to change password"
            )

    except SupabaseAuthError as e:
        logger.error(f"Supabase auth error during password change: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error changing password: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed"
        )


@router.post("/reset-password")
async def request_password_reset(
    reset_data: PasswordResetRequest,
    user_repo: UserRepositoryType
) -> Dict[str, str]:
    """Request a password reset for a user"""
    try:
        # Request password reset through Supabase
        success = await user_repo.request_password_reset(reset_data.email)

        if success:
            return {"message": "Password reset email sent"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send password reset email"
            )

    except SupabaseAuthError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error requesting password reset: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password reset request failed"
        )


@router.post("/verify-token")
async def verify_token(
    token_data: TokenVerifyRequest,
    user_repo: UserRepositoryType
) -> Dict[str, Any]:
    """Verify a token and return the user profile"""
    try:
        user = await user_repo.verify_session(token_data.token)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )

        return user

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error verifying token: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token verification failed"
        )


@router.post("/refresh")
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    user_repo: UserRepositoryType
) -> Dict[str, Any]:
    """Refresh an access token using a refresh token"""
    try:
        # Refresh the token
        result = await user_repo.refresh_session(refresh_data.refresh_token)

        # Handle different result formats for compatibility
        if isinstance(result, dict):
            return result
        else:
            # Handle object serialization
            return {
                "user": (
                    result.user.to_dict()
                    if hasattr(result.user, "to_dict")
                    else result.user
                ),
                "session": {
                    "access_token": result.session.access_token,
                    "refresh_token": result.session.refresh_token,
                    "expires_at": result.session.expires_at,
                },
            }
    except SupabaseAuthError as e:
        logger.error(f"Supabase auth error during token refresh: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error refreshing token: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed"
        )


@router.get("/session")
async def get_session(user_repo: UserRepositoryType) -> Dict[str, Any]:
    """Get the current session"""
    try:
        session = await user_repo.get_session()

        if not session:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="No active session found"
            )

        # Handle object serialization
        return {
            "user": (
                session.user.to_dict()
                if hasattr(session.user, "to_dict")
                else session.user
            ),
            "session": {
                "access_token": session.session.access_token,
                "refresh_token": session.session.refresh_token,
                "expires_at": session.session.expires_at,
            },
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error getting session: %s", str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Session retrieval failed"
        )


@router.get("/test-token")
async def test_token(current_user: CurrentUserType) -> Dict[str, Any]:
    """Test endpoint for token validation"""
    return {"message": "Token is valid", "user": current_user.user_data}


@router.get("/ping")
async def ping() -> Dict[str, str]:
    """Simple endpoint to test API connectivity without JWT"""
    return {
        "message": "API is accessible",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "auth_provider": "Supabase",
    }
