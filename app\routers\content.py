"""
Content management routes for FastAPI.
Migrated from Flask app/content/routes.py to use FastAPI dependency injection.
"""

import logging
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from app.auth.dependencies import CurrentUserType, CurrentUserIdType
from app.dependencies import get_content_item_repository
from app.repositories.supabase_content_item_repository import ContentItemRepository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/content", tags=["content"])


# Pydantic models for request/response
class ContentItemCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    content: str = Field(..., min_length=1)
    content_type: Optional[str] = None
    tags: Optional[List[str]] = []
    status: Optional[str] = "draft"


class ContentItemUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    content: Optional[str] = Field(None, min_length=1)
    content_type: Optional[str] = None
    tags: Optional[List[str]] = None
    is_business_context: Optional[bool] = None


class ContentItemResponse(BaseModel):
    id: str
    user_id: str
    title: str
    content: str
    content_type: Optional[str] = None
    tags: List[str] = []
    status: str = "draft"
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


def standardize_tags(tags: Any) -> List[str]:
    """
    Standardize tags to always be a list format regardless of input type.
    
    Args:
        tags: Input tags in any format (list, string, None, etc.)
        
    Returns:
        list: Standardized list of tags
    """
    if tags is None:
        return []
    elif isinstance(tags, str):
        # Handle comma-separated string or JSON string
        if tags.strip().startswith("[") and tags.strip().endswith("]"):
            # Try parsing JSON
            try:
                import json
                return json.loads(tags.replace("'", '"'))
            except:
                # Fall back to simple splitting if JSON parse fails
                return [t.strip() for t in tags.strip("[]").split(",") if t.strip()]
        else:
            # Simple comma-separated string
            return [t.strip() for t in tags.split(",") if t.strip()]
    elif isinstance(tags, list):
        # Already a list, ensure all items are strings
        return [str(t).strip() for t in tags if str(t).strip()]
    else:
        # Convert any other type to a string and make it a single-item list
        return [str(tags)]


async def get_content_item_repository() -> ContentItemRepository:
    """Get content item repository dependency"""
    from app.models.supabase_client import get_supabase_client
    from app.repositories.supabase_content_item_repository import ContentItemRepository
    
    supabase = await get_supabase_client()
    if not supabase:
        raise HTTPException(
            status_code=503,
            detail="Content service temporarily unavailable - Failed to get Supabase client"
        )
    
    return ContentItemRepository(supabase_client=supabase)


@router.post("/", response_model=Dict[str, Any], status_code=201)
async def create_content(
    content_data: ContentItemCreate,
    current_user_id: CurrentUserIdType,
    content_repo: ContentItemRepository = Depends(get_content_item_repository)
) -> Dict[str, Any]:
    """Create a new content item"""
    try:
        # Process tags to ensure they're always stored as arrays
        standardized_tags = standardize_tags(content_data.tags)

        content_item_data = {
            "user_id": current_user_id,
            "title": content_data.title,
            "content": content_data.content,
            "content_type": content_data.content_type,
            "tags": standardized_tags,
            "status": content_data.status,
        }
        
        content_item = await content_repo.create_content_item(content_item_data)
        
        return {
            "message": "Content item created successfully",
            "content_item": content_item,
        }
    except Exception as e:
        logger.error("Error creating content item in Supabase: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Content creation failed: {str(e)}"
        )


@router.get("/", response_model=Dict[str, List[Dict[str, Any]]])
async def get_content_items(
    current_user_id: CurrentUserIdType,
    content_repo: ContentItemRepository = Depends(get_content_item_repository),
    content_type: Optional[str] = Query(None, description="Filter by content type"),
    tags: Optional[str] = Query(None, description="Comma-separated tags to filter by"),
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)"),
    limit: int = Query(10, ge=1, le=100, description="Number of items to return")
) -> Dict[str, List[Dict[str, Any]]]:
    """Get all content items for the current user"""
    try:
        # Process tags if provided
        tags_list = tags.split(",") if tags else None

        # Prepare filter dictionary
        filters = {
            "user_id": current_user_id,
            "search_term": search,
            "content_type": content_type,
            "tags": tags_list,
            "sort_by": sort_by,
            "sort_order": sort_order,
            "limit": limit,
        }

        # Remove None values from filters to avoid passing empty params
        filters = {k: v for k, v in filters.items() if v is not None}

        # Call the unified filtering method in the repository
        content_items = await content_repo.get_filtered_items(**filters)

        return {"content_items": content_items}
    except Exception as e:
        logger.error("Error fetching content items from Supabase: %s", str(e))
        error_type = type(e).__name__
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch content items: {error_type}"
        )


@router.get("/{content_id}", response_model=Dict[str, Any])
async def get_content_item(
    content_id: str,
    current_user_id: CurrentUserIdType,
    content_repo: ContentItemRepository = Depends(get_content_item_repository)
) -> Dict[str, Any]:
    """Get a specific content item"""
    try:
        content_item = await content_repo.get_by_id(content_id, current_user_id)

        # Check if content item exists and belongs to user
        if not content_item:
            raise HTTPException(
                status_code=404,
                detail="Content item not found"
            )

        if content_item.get("user_id") != current_user_id:
            raise HTTPException(
                status_code=403,
                detail="Unauthorized access to content item"
            )

        return content_item
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error fetching content item from Supabase: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch content item: {str(e)}"
        )


@router.put("/{content_id}", response_model=Dict[str, Any])
async def update_content_item(
    content_id: str,
    content_data: ContentItemUpdate,
    current_user_id: CurrentUserIdType,
    content_repo: ContentItemRepository = Depends(get_content_item_repository)
) -> Dict[str, Any]:
    """Update a specific content item"""
    try:
        # Update content item fields
        update_data = {}
        if content_data.title is not None:
            update_data["title"] = content_data.title
        if content_data.content is not None:
            update_data["content"] = content_data.content
        if content_data.content_type is not None:
            update_data["content_type"] = content_data.content_type
        if content_data.tags is not None:
            # Process tags to ensure they're always stored as arrays
            standardized_tags = standardize_tags(content_data.tags)
            update_data["tags"] = standardized_tags
        if content_data.is_business_context is not None:
            update_data["is_business_context"] = content_data.is_business_context

        # Update the content item
        content_item = await content_repo.update_content_item(
            content_id, current_user_id, update_data
        )

        return {
            "message": "Content item updated successfully",
            "content_item": content_item,
        }
    except Exception as e:
        logger.error("Error updating content item in Supabase: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Content update failed: {str(e)}"
        )


@router.delete("/{content_id}", response_model=Dict[str, str])
async def delete_content_item(
    content_id: str,
    current_user_id: CurrentUserIdType,
    content_repo: ContentItemRepository = Depends(get_content_item_repository)
) -> Dict[str, str]:
    """Delete a specific content item"""
    try:
        # Delete the content item
        success = await content_repo.delete_content_item(
            id=content_id, user_id=current_user_id
        )

        if success:
            return {"message": "Content item deleted successfully"}
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to delete content item"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error deleting content item in Supabase: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Content deletion failed: {str(e)}"
        )


@router.get("/debug-repo", response_model=Dict[str, Any])
async def debug_repository(
    content_repo: ContentItemRepository = Depends(get_content_item_repository)
) -> Dict[str, Any]:
    """Debug endpoint to check repository initialization status"""
    try:
        repo_status = {
            "content_item_repository_type": str(type(content_repo)),
            "table_name": getattr(content_repo, "table_name", "N/A"),
        }

        # Check if supabase client is available
        try:
            from app.models.supabase_client import get_supabase_client
            client = await get_supabase_client()
            repo_status["supabase_client_available"] = client is not None
            repo_status["supabase_client_type"] = str(type(client)) if client is not None else "None"
        except Exception as e:
            repo_status["supabase_client_error"] = str(e)

        return repo_status
    except Exception as e:
        logger.error("Error in debug repository endpoint: %s", str(e))
        raise HTTPException(
            status_code=500,
            detail=f"Debug endpoint failed: {str(e)}"
        )
