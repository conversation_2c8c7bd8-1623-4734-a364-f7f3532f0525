import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to frontend directory
const frontendDir = path.join(__dirname, 'frontend');

// Function to install source-map-explorer if not present
function ensureSourceMapExplorerInstalled() {
  try {
    // Check if source-map-explorer is installed by running a simple command
    execSync('npx source-map-explorer --version', { stdio: 'ignore' });
    console.log('source-map-explorer is already installed');
  } catch (error) {
    console.log('Installing source-map-explorer...');
    execSync('npm install source-map-explorer --save-dev', { cwd: frontendDir });
  }
}

// Function to build the frontend with source maps
function buildFrontendWithSourceMaps() {
  console.log('Building frontend with source maps...');
  
  // Ensure we generate source maps by setting the environment variable
  const buildCommand = 'SET GENERATE_SOURCEMAP=true && npm run build';
  
  try {
    execSync(buildCommand, { cwd: frontendDir, stdio: 'inherit' });
    console.log('Frontend build completed successfully');
    return true;
  } catch (error) {
    console.error('Frontend build failed:', error.message);
    return false;
  }
}

// Function to analyze the bundle using source-map-explorer
function analyzeBundleSize() {
  const buildDir = path.join(frontendDir, 'build', 'static', 'js');
  
  // Check if build directory exists
  if (!fs.existsSync(buildDir)) {
    console.error(`Build directory not found: ${buildDir}`);
    return false;
  }
  
  // Find the main JavaScript file(s)
  const jsFiles = fs.readdirSync(buildDir)
    .filter(file => file.endsWith('.js') && file.startsWith('main'));
  
  if (jsFiles.length === 0) {
    console.error('No main JavaScript bundles found in the build directory');
    return false;
  }
  
  console.log(`Found ${jsFiles.length} JavaScript bundles to analyze`);
  
  // Create report directory
  const reportDir = path.join(__dirname, 'bundle-analysis');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir);
  }
  
  // Analyze each bundle and generate an HTML report
  try {
    jsFiles.forEach(jsFile => {
      const jsFilePath = path.join(buildDir, jsFile);
      const reportPath = path.join(reportDir, `${jsFile.replace('.js', '')}-analysis.html`);
      
      console.log(`Analyzing bundle: ${jsFile}`);
      execSync(`npx source-map-explorer ${jsFilePath} --html ${reportPath}`);
      console.log(`Report generated: ${reportPath}`);
    });
    
    // Generate a summary file with key insights
    generateBundleSummary(jsFiles, buildDir, reportDir);
    
    return true;
  } catch (error) {
    console.error('Bundle analysis failed:', error.message);
    return false;
  }
}

// Function to generate a summary file with key insights
function generateBundleSummary(jsFiles, buildDir, reportDir) {
  console.log('Generating bundle summary...');
  
  // Get file sizes
  const bundleSizes = jsFiles.map(file => {
    const filePath = path.join(buildDir, file);
    const stats = fs.statSync(filePath);
    return {
      file,
      size: stats.size,
      formattedSize: formatSize(stats.size)
    };
  });
  
  // Calculate total size
  const totalSize = bundleSizes.reduce((total, bundle) => total + bundle.size, 0);
  
  // Generate markdown report
  const summaryPath = path.join(__dirname, 'bundle-size-report.md');
  let markdown = `# Frontend Bundle Size Analysis\n\n`;
  markdown += `Analysis performed: ${new Date().toLocaleString()}\n\n`;
  
  markdown += `## Bundle Summary\n\n`;
  markdown += `| Bundle File | Size | Percentage |\n`;
  markdown += `|------------|------|------------|\n`;
  
  bundleSizes.forEach(bundle => {
    const percentage = ((bundle.size / totalSize) * 100).toFixed(2);
    markdown += `| ${bundle.file} | ${bundle.formattedSize} | ${percentage}% |\n`;
  });
  
  markdown += `\n**Total Bundle Size:** ${formatSize(totalSize)}\n\n`;
  
  markdown += `## Size Recommendations\n\n`;
  markdown += `- Recommended initial JavaScript bundle size: < 170KB minified and gzipped\n`;
  markdown += `- Current main bundle size: ${formatSize(bundleSizes[0].size)} (raw, not gzipped)\n\n`;
  
  markdown += `## Next Steps for Bundle Optimization\n\n`;
  markdown += `1. **Implement Code Splitting**:\n`;
  markdown += `   - Add React.lazy() and Suspense for route-based code splitting\n`;
  markdown += `   - Split large components into smaller chunks\n\n`;
  
  markdown += `2. **Optimize Dependencies**:\n`;
  markdown += `   - Review large dependencies and consider alternatives\n`;
  markdown += `   - Import only the needed components from UI libraries (e.g., Material-UI)\n`;
  markdown += `   - Check for duplicate dependencies\n\n`;
  
  markdown += `3. **Implement Tree Shaking**:\n`;
  markdown += `   - Ensure webpack configuration properly enables tree shaking\n`;
  markdown += `   - Use ES modules syntax (import/export) consistently\n`;
  markdown += `   - Set "sideEffects" properly in package.json\n\n`;
  
  markdown += `4. **Asset Optimization**:\n`;
  markdown += `   - Optimize images and other assets\n`;
  markdown += `   - Implement lazy loading for images\n`;
  markdown += `   - Consider using WebP format for images\n\n`;
  
  markdown += `5. **Vendor Bundle Optimization**:\n`;
  markdown += `   - Configure webpack to create separate vendor chunks\n`;
  markdown += `   - Implement long-term caching for vendor bundles\n`;
  
  fs.writeFileSync(summaryPath, markdown);
  console.log(`Bundle summary saved to: ${summaryPath}`);
}

// Helper function to format bytes to a human-readable size
function formatSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Main function
async function main() {
  console.log('Starting frontend bundle analysis...');
  
  // Install source-map-explorer if needed
  ensureSourceMapExplorerInstalled();
  
  // Build frontend with source maps
  const buildSuccess = buildFrontendWithSourceMaps();
  if (!buildSuccess) {
    console.error('Skipping bundle analysis due to build failure');
    process.exit(1);
  }
  
  // Analyze bundle size
  const analysisSuccess = analyzeBundleSize();
  if (!analysisSuccess) {
    console.error('Bundle analysis failed');
    process.exit(1);
  }
  
  console.log('Frontend bundle analysis completed successfully');
}

// Run the main function
main().catch(error => {
  console.error('An error occurred:', error);
  process.exit(1);
}); 