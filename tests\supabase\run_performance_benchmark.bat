@echo off
echo ===================================================
echo Performance Benchmark: PostgreSQL vs Supabase
echo ===================================================
echo.

REM Check for Python
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python and try again.
    exit /b 1
)

REM Check for required packages
echo Checking for required Python packages...
python -c "import psycopg2, requests, dotenv, matplotlib, numpy, pandas" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Installing required packages...
    pip install psycopg2-binary requests python-dotenv matplotlib numpy pandas
)

REM Parse command-line arguments
set MODE=basic
set ITERATIONS=10
set OPERATIONS=50
set PG_HOST=localhost
set PG_PORT=5432
set PG_DATABASE=
set PG_USER=postgres
set PG_PASSWORD=
set SUPABASE_URL=
set SUPABASE_KEY=
set SAVE_CHARTS=true
set VERBOSE=false

:parse_args
if "%~1"=="" goto :run_benchmark
if /i "%~1"=="--mode" (
    set MODE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--iterations" (
    set ITERATIONS=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--operations" (
    set OPERATIONS=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-host" (
    set PG_HOST=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-port" (
    set PG_PORT=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-database" (
    set PG_DATABASE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-user" (
    set PG_USER=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-password" (
    set PG_PASSWORD=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--supabase-url" (
    set SUPABASE_URL=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--supabase-key" (
    set SUPABASE_KEY=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--no-charts" (
    set SAVE_CHARTS=false
    shift
    goto :parse_args
)
if /i "%~1"=="--verbose" (
    set VERBOSE=true
    shift
    goto :parse_args
)
if /i "%~1"=="--help" (
    goto :show_help
)
shift
goto :parse_args

:show_help
echo.
echo Usage: run_performance_benchmark.bat [options]
echo.
echo Modes:
echo   basic         Basic CRUD operations on a single table (default)
echo   read-heavy    80%% read, 20%% write operations
echo   write-heavy   80%% write, 20%% read operations
echo   mixed         Equal mix of different operations
echo   complex       Complex queries with joins and filters
echo   comprehensive Run all modes sequentially
echo.
echo Options:
echo   --mode MODE           Benchmark mode (default: basic)
echo   --iterations N        Number of test iterations (default: 10)
echo   --operations N        Operations per iteration (default: 50)
echo   --pg-host HOST        PostgreSQL host (default: localhost)
echo   --pg-port PORT        PostgreSQL port (default: 5432)
echo   --pg-database DB      PostgreSQL database name (default: from .env)
echo   --pg-user USER        PostgreSQL username (default: postgres)
echo   --pg-password PASS    PostgreSQL password (default: from .env)
echo   --supabase-url URL    Supabase URL (default: from .env)
echo   --supabase-key KEY    Supabase API key (default: from .env)
echo   --no-charts           Don't generate performance charts
echo   --verbose             Display detailed logs during benchmark
echo   --help                Show this help message
echo.
exit /b 0

:run_benchmark
echo.
echo Running performance benchmark with the following settings:
echo Mode: %MODE%
echo Iterations: %ITERATIONS%
echo Operations per iteration: %OPERATIONS%
echo PostgreSQL host: %PG_HOST%
echo PostgreSQL port: %PG_PORT%
if not "%PG_DATABASE%"=="" echo PostgreSQL database: %PG_DATABASE%
echo PostgreSQL user: %PG_USER%
if not "%PG_PASSWORD%"=="" echo PostgreSQL password: [REDACTED]
if not "%SUPABASE_URL%"=="" echo Supabase URL: %SUPABASE_URL%
if not "%SUPABASE_KEY%"=="" echo Supabase key: [REDACTED]
echo Save charts: %SAVE_CHARTS%
echo Verbose: %VERBOSE%
echo.

REM Create timestamp for results
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~0,4%"
set "MM=%dt:~4,2%"
set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%"
set "Min=%dt:~10,2%"
set "Sec=%dt:~12,2%"
set "timestamp=%YY%%MM%%DD%_%HH%%Min%%Sec%"

REM Create results directory if it doesn't exist
if not exist "benchmark_results" mkdir benchmark_results
set "RESULTS_DIR=benchmark_results\benchmark_%timestamp%"
mkdir "%RESULTS_DIR%"

REM If mode is comprehensive, run all benchmark types
if /i "%MODE%"=="comprehensive" (
    echo Running comprehensive benchmark suite...
    echo This will run all benchmark modes sequentially.
    echo.
    
    set MODES=basic read-heavy write-heavy mixed complex
    for %%m in (%MODES%) do (
        echo.
        echo ===================================================
        echo Running %%m benchmark mode
        echo ===================================================
        echo.
        
        set CMD=python benchmark_performance.py --mode %%m --iterations %ITERATIONS% --operations %OPERATIONS%
        set CMD=%CMD% --pg-host %PG_HOST% --pg-port %PG_PORT%
        if not "%PG_DATABASE%"=="" set CMD=%CMD% --pg-database %PG_DATABASE%
        set CMD=%CMD% --pg-user %PG_USER%
        if not "%PG_PASSWORD%"=="" set CMD=%CMD% --pg-password %PG_PASSWORD%
        if not "%SUPABASE_URL%"=="" set CMD=%CMD% --supabase-url %SUPABASE_URL%
        if not "%SUPABASE_KEY%"=="" set CMD=%CMD% --supabase-key %SUPABASE_KEY%
        if "%SAVE_CHARTS%"=="true" set CMD=%CMD% --save-charts
        if "%VERBOSE%"=="true" set CMD=%CMD% --verbose
        set CMD=%CMD% --results-dir "%RESULTS_DIR%\%%m"
        
        echo Running: %CMD%
        echo.
        echo Results will be saved to "%RESULTS_DIR%\%%m"
        echo.
        
        %CMD%
    )
    
    REM Generate combined report for all modes
    echo.
    echo ===================================================
    echo Generating combined performance report
    echo ===================================================
    echo.
    
    set CMD=python combine_benchmark_results.py --results-dir "%RESULTS_DIR%"
    echo Running: %CMD%
    echo.
    
    %CMD%
    
    goto :benchmark_complete
)

REM Build command with available parameters for single mode benchmark
set CMD=python benchmark_performance.py --mode %MODE% --iterations %ITERATIONS% --operations %OPERATIONS%
set CMD=%CMD% --pg-host %PG_HOST% --pg-port %PG_PORT%
if not "%PG_DATABASE%"=="" set CMD=%CMD% --pg-database %PG_DATABASE%
set CMD=%CMD% --pg-user %PG_USER%
if not "%PG_PASSWORD%"=="" set CMD=%CMD% --pg-password %PG_PASSWORD%
if not "%SUPABASE_URL%"=="" set CMD=%CMD% --supabase-url %SUPABASE_URL%
if not "%SUPABASE_KEY%"=="" set CMD=%CMD% --supabase-key %SUPABASE_KEY%
if "%SAVE_CHARTS%"=="true" set CMD=%CMD% --save-charts
if "%VERBOSE%"=="true" set CMD=%CMD% --verbose
set CMD=%CMD% --results-dir "%RESULTS_DIR%"

echo Running: %CMD%
echo.
echo Results will be saved to "%RESULTS_DIR%"
echo.

%CMD%

:benchmark_complete
echo.
echo ===================================================
echo Performance Benchmark Complete
echo ===================================================
echo.
echo Benchmark results have been saved to:
echo %RESULTS_DIR%
echo.
echo Summary report: %RESULTS_DIR%\summary.txt
echo.

REM Check if charts were generated and display their locations
if "%SAVE_CHARTS%"=="true" (
    echo Performance charts have been saved to:
    if /i "%MODE%"=="comprehensive" (
        for %%m in (%MODES%) do (
            echo %RESULTS_DIR%\%%m\charts
        )
        echo %RESULTS_DIR%\combined_charts
    ) else (
        echo %RESULTS_DIR%\charts
    )
)

REM Display notes about interpreting the results
echo.
echo IMPORTANT NOTES:
echo - Lower response times indicate better performance
echo - Higher operations per second indicate better performance
echo - Check detailed analysis in the summary report
echo - Review the charts to visualize performance differences
echo.

exit /b 0 