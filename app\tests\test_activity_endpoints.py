import pytest

import json

# Removed unused playwright import
import logging


# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@pytest.mark.skip(reason="Activity feature is currently disabled")
def test_activity_endpoints(page, base_url):
    """Test the activity endpoints"""
    logger.info("Testing activity endpoints with base URL: %s", base_url)

    # First, login to get a token
    logger.info("Attempting login...")
    login_response = page.request.post(
        "http://localhost:5000/api/auth/login",
        data=json.dumps(
            {"email": "<EMAIL>", "password": "MK*9v9sseuu3#Z3qgypf"}
        ),
        headers={"Content-Type": "application/json"},
    )

    logger.info("Login response status: %s", login_response.status)
    assert login_response.status == 200

    token_data = login_response.json()
    token = token_data.get("access_token")
    logger.info("Got login token: %s...", token[:10])

    # Set up headers for subsequent requests
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}

    # Test 1: Get activities
    logger.info("Testing GET /api/activity/")
    get_response = page.request.get(
        "http://localhost:5000/api/activity/", headers=headers
    )
    logger.info("GET /api/activity/ response status: %s", get_response.status)
    assert get_response.status == 200

    activities = get_response.json()
    logger.info("Retrieved %s activities", len(activities))

    # Test 2: Log a new activity
    logger.info("Testing POST /api/activity/")
    test_activity = {
        "action_type": "test",  # Changed key from activityType to action_type
        "title": "Test activity",
        "description": "Testing the activity endpoint",
        "resourceType": "test",  # Keep camelCase for others as route handles both
        "resourceId": "12345",
    }

    post_response = page.request.post(
        "http://localhost:5000/api/activity/",
        headers=headers,
        data=json.dumps(test_activity),
    )
    logger.info("POST /api/activity/ response status: %s", post_response.status)
    assert post_response.status == 200

    created_activity = post_response.json()
    logger.info("Created activity: %s", json.dumps(created_activity, indent=2))

    # Test 3: Get activity summary
    logger.info("Testing GET /api/activity/summary")
    summary_response = page.request.get(
        "http://localhost:5000/api/activity/summary", headers=headers
    )
    logger.info(
        "GET /api/activity/summary response status: %s", summary_response.status
    )
    assert summary_response.status == 200

    summary = summary_response.json()
    logger.info("Activity summary: %s", json.dumps(summary, indent=2))

    # Verify the test activity was logged
    logger.info("Verifying test activity was logged...")
    get_response = page.request.get(
        "http://localhost:5000/api/activity/", headers=headers
    )
    activities = get_response.json()
    # Check using the key the route uses internally after handling camel/snake case
    test_activities = [a for a in activities if a.get("action_type") == "test"]
    logger.info("Found %s test activities", len(test_activities))
    assert (
        len(test_activities) > 0
    ), "Test activity was not found in the activities list"
