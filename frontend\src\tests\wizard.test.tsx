import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { WizardProvider } from '../contexts/WizardContext';
import { WizardLayout } from '../components/WizardLayout';
import WizardPage from '../pages/WizardPage';

// Mock the API calls
jest.mock('../services/api', () => ({
  getBusinessContextsList: jest.fn().mockResolvedValue({ data: [] }),
  wizardBrainstorm: jest.fn().mockResolvedValue({ data: { session_id: 'test-session', titles: ['Test Title 1', 'Test Title 2'] } }),
  wizardSelectHook: jest.fn().mockResolvedValue({ data: { hooks: ['Test Hook 1', 'Test Hook 2'] } }),
  wizardSelectIntro: jest.fn().mockResolvedValue({ data: { intro_ctas: ['Test Intro 1', 'Test Intro 2'] } }),
  wizardGenerateOutline: jest.fn().mockResolvedValue({ data: { outlines: ['Test Outline'] } }),
  wizardDraftScript: jest.fn().mockResolvedValue({ data: { draft: 'Test Draft Script' } }),
  wizardEditScript: jest.fn().mockResolvedValue({ data: { edited_script: 'Test Edited Script' } }),
  saveContentItem: jest.fn().mockResolvedValue({ data: { id: 'test-content-id' } }),
}));

describe('Wizard Flow', () => {
  it('maintains state through context changes', async () => {
    const { getByRole, queryByText } = render(
      <BrowserRouter>
        <WizardProvider>
          <WizardLayout businessContextId="test-context" onBusinessContextChange={() => {}}>
            <WizardPage />
          </WizardLayout>
        </WizardProvider>
      </BrowserRouter>
    );

    // Test complete wizard flow
    await userEvent.click(getByRole('button', { name: /start/i }));
    expect(queryByText(/confirm context change/i)).not.toBeInTheDocument();
  });
});