from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class BaseSuccessResponse(BaseModel):
    success: bool = True
    message: Optional[str] = None

class GeneratedTextResponse(BaseModel):
    generated_text: str = Field(..., description="The text content generated by the AI model.")
    model_used: Optional[str] = Field(None, description="The specific model version used for generation.")
    tokens_used: Optional[int] = Field(None, description="Number of tokens used for the generation.")
    # You can add other common fields here, e.g., warnings, raw_response snippets for debugging etc.

class ErrorDetail(BaseModel):
    code: Optional[str] = None
    message: str
    field: Optional[str] = None

class BaseErrorResponse(BaseModel):
    success: bool = False
    error: ErrorDetail
    details: Optional[Dict[str, Any]] = None # For additional structured error info

# Example of a more structured output, if the AI is asked to generate specific fields
class StructuredContentIdea(BaseModel):
    title_suggestion: str = Field(..., description="A catchy title for the content.")
    summary: str = Field(..., description="A brief summary of the content idea.")
    keywords: List[str] = Field(default_factory=list, description="Relevant keywords for SEO.")
    target_audience: Optional[str] = Field(None, description="The intended audience for this content.")

class ContentIdeationResponse(BaseModel):
    ideas: List[StructuredContentIdea]
    model_used: Optional[str] = None

# Add other Pydantic models as needed for different types of structured AI outputs. 