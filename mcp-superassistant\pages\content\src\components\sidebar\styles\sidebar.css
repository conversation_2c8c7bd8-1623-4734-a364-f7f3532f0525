/* 
 * Sidebar Core Styles
 * These styles provide the basic structure and positioning for the sidebar.
 * Component-specific styling is now handled by Tailwind CSS classes.
 */

/* Enhanced sidebar appearance/disappearance animations */
.sidebar.initial-render {
  transition: none !important;
  animation: none !important;
}

/* Prevent any flash during initial loading */
.sidebar.initial-render.collapsed {
  width: 56px !important;
  transition: none !important;
}

.sidebar.initial-render.collapsed .sidebar-inner-content {
  opacity: 0 !important;
  transform: translateX(100%) !important;
  transition: none !important;
}

/* Enhanced shadow host transitions with smooth appearing/disappearing */
#mcp-sidebar-shadow-host {
  transition: opacity 0.5s cubic-bezier(0.25, 0.8, 0.25, 1), 
              transform 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
  transform-origin: right center;
  will-change: opacity, transform;
}

#mcp-sidebar-shadow-host.showing {
  opacity: 0;
  transform: translateX(40px) scale(0.9) rotateY(5deg);
  animation: sidebarAppear 0.6s cubic-bezier(0.34, 1.26, 0.64, 1) forwards;
}

#mcp-sidebar-shadow-host.hiding {
  opacity: 1;
  transform: translateX(0) scale(1);
  animation: sidebarDisappear 0.4s cubic-bezier(0.55, 0.085, 0.68, 0.53) forwards;
}

#mcp-sidebar-shadow-host.initialized:not(.showing):not(.hiding) {
  opacity: 1;
  transform: translateX(0) scale(1);
}

/* Smooth appearing animation with subtle 3D effect */
@keyframes sidebarAppear {
  0% {
    opacity: 0;
    transform: translateX(60px) scale(0.85) rotateY(8deg);
    filter: blur(1px);
  }
  30% {
    opacity: 0.4;
    transform: translateX(20px) scale(0.95) rotateY(3deg);
    filter: blur(0.5px);
  }
  60% {
    opacity: 0.8;
    transform: translateX(-8px) scale(1.02) rotateY(-1deg);
    filter: blur(0px);
  }
  85% {
    opacity: 0.95;
    transform: translateX(2px) scale(1.01) rotateY(0deg);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1) rotateY(0deg);
    filter: blur(0px);
  }
}

/* Smooth disappearing animation with elegant fade */
@keyframes sidebarDisappear {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1) rotateY(0deg);
    filter: blur(0px);
  }
  20% {
    opacity: 0.9;
    transform: translateX(8px) scale(0.98) rotateY(2deg);
  }
  50% {
    opacity: 0.5;
    transform: translateX(20px) scale(0.92) rotateY(4deg);
    filter: blur(0.5px);
  }
  100% {
    opacity: 0;
    transform: translateX(40px) scale(0.85) rotateY(8deg);
    filter: blur(1px);
  }
}

/* Base sidebar container */
.sidebar {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  /* Smooth, natural width transitions with subtle elastic feel */
  transition: width 0.4s cubic-bezier(0.4, 0.0, 0.2, 1), 
              box-shadow 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow-y: auto;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08), 
              0 1px 6px rgba(0, 0, 0, 0.04);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  will-change: width, transform; 
  transform: translateZ(0); /* Force GPU acceleration */
  contain: layout style;
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

/* Sidebar in transitioning state */
.sidebar.sidebar-transitioning {
  contain: strict;
  overflow: hidden;
  transition: transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.sidebar.sidebar-transitioning:hover {
  transform: scale(1.01);
}

/* Collapsed state */
.sidebar.collapsed {
  width: 56px;
  overflow: hidden;
  transition: width 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* Push mode */
.sidebar.push-mode {
  box-shadow: -5px 0 20px rgba(0, 0, 0, 0.08), 
              0 0 40px rgba(0, 0, 0, 0.04);
}

/* Resizing state - disable transitions during active resize */
.sidebar.resizing {
  transition: none !important;
  will-change: width;
}

/* Enhanced inner content transitions */
.sidebar-inner-content {
  width: 100%;
  height: 100%;
  contain: layout;
  transition: opacity 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), 
              transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  transform: translateX(0);
  opacity: 1;
}

/* Smooth content hiding animation */
.sidebar.collapsed .sidebar-inner-content {
  opacity: 0;
  transform: translateX(20px);
  transition-delay: 0s;
}

/* Content revealing animation */
.sidebar:not(.collapsed) .sidebar-inner-content {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 0.15s;
}

/* Prevent flash on initial load when sidebar should start minimized */
[data-initial-minimized="true"] {
  /* Apply to the shadow host itself */
  width: 56px !important;
  max-width: 56px !important;
}

[data-initial-minimized="true"] .sidebar {
  width: 56px !important;
  max-width: 56px !important;
  transition: none !important; /* Disable transitions during initial load */
}

[data-initial-minimized="true"] .sidebar .sidebar-inner-content {
  opacity: 0 !important;
  transform: translateX(100%) !important;
  transition: none !important; /* Disable transitions during initial load */
}

/* Header should not resize or shift */
.sidebar-header {
  flex-shrink: 0;
  contain: layout size;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: auto;
  min-height: 60px;
}

/* Make sure the header title is properly spaced */
.sidebar-header h4 {
  line-height: 1.2;
  letter-spacing: -0.01em;
}

/* Ensure header buttons have proper spacing */
.sidebar-header button {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Scrollbar styling for webkit browsers */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: rgba(203, 213, 225, 0.4);
}

.dark .sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(71, 85, 105, 0.4);
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(148, 163, 184, 0.6);
}

.dark .sidebar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.6);
}

/* Spring animation for expanding content */
@keyframes expandIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.sidebar-expand-animation {
  animation: expandIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Push mode transition */
.content-wrapper {
  transition: padding-right 0.5s cubic-bezier(0.25, 1.0, 0.5, 1);
}

.content-wrapper.pushed {
  transition: padding-right 0.5s cubic-bezier(0.25, 1.0, 0.5, 1);
}

/* Focus styles */
.sidebar button:focus-visible,
.sidebar input:focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
  transition: outline-color 0.3s cubic-bezier(0.25, 1.0, 0.5, 1);
}

.dark .sidebar button:focus-visible,
.dark .sidebar input:focus-visible {
  outline-color: #6366f1;
}

/* Utility for preventing layout shifts during transitions */
.content-stable {
  contain: layout size;
  max-width: 100%;
}

/* Content transformation for width transitions - prevents squeezing */
.sidebar .content-fixed-width {
  width: 100% !important;
  transform: none !important;
  opacity: 1 !important;
  transition: opacity 0.3s ease !important;
}

.sidebar .content-hidden {
  opacity: 0 !important;
}

.sidebar .content-stabilizing {
  contain: layout size;
  transform: translateZ(0);
}

/* Content container */
.sidebar-content-container {
  transition-property: opacity, transform !important;
  transition-duration: 0.5s !important;
  transition-timing-function: cubic-bezier(0.25, 1.0, 0.5, 1) !important;
  will-change: opacity, transform !important;
  transform-origin: center right !important;
  backface-visibility: hidden !important;
  contain: style layout !important;
}

/* Enhanced spring-like animation utilities - reduced springiness */
.sidebar .fade-in {
  animation: fadeIn 0.4s cubic-bezier(0.25, 1.0, 0.5, 1);
}

.sidebar .slide-in-right {
  animation: slideInRight 0.5s cubic-bezier(0.25, 1.0, 0.5, 1);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(30px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Utility classes for transitions - reduced springiness */
.sidebar .transition-all {
  transition: all 0.4s cubic-bezier(0.25, 1.0, 0.5, 1);
}

.sidebar .transition-opacity {
  transition: opacity 0.4s cubic-bezier(0.25, 1.0, 0.5, 1);
}

.sidebar .transition-transform {
  transition: transform 0.4s cubic-bezier(0.25, 1.0, 0.5, 1);
}

/* Ghost element animation - reduced springiness */
@keyframes ghostAppear {
  from { opacity: 0; transform: translateX(10px); }
  to { opacity: 1; transform: translateX(0); }
}

.sidebar .ghost-content {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  pointer-events: none;
  animation: ghostAppear 0.4s cubic-bezier(0.25, 1.0, 0.5, 1) forwards;
  animation-delay: 0.15s;
}

/* Input styling with enhanced micro-interactions */
.sidebar .input-submit {
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s cubic-bezier(0.25, 0.8, 0.25, 1), 
              transform 0.15s cubic-bezier(0.34, 1.56, 0.64, 1),
              box-shadow 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.sidebar .input-submit:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar .input-submit:active {
  transform: translateY(0) scale(0.98);
  transition-duration: 0.1s;
}

.sidebar .input-submit:disabled {
  cursor: not-allowed;
  transform: none;
  opacity: 0.6;
}

/* Enhanced button animations - simplified */
.sidebar button {
  transition: all 0.2s ease-out;
}

.sidebar button:hover {
  transform: translateY(-1px);
}

.sidebar button:active {
  transform: translateY(0) scale(0.98);
}

/* ServerStatus specific enhancements - simplified and stable */
.server-status-container {
  transition: all 0.3s ease-out;
  will-change: transform;
}

.server-status-icon {
  transition: all 0.2s ease-out;
}

/* Prevent layout shifts during status changes */
.server-status-stable {
  min-height: 60px; /* Ensure consistent height */
  contain: layout;
}

/* Smooth status change animation without flickers */
@keyframes statusChangeSmooth {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.9;
  }
  100% {
    opacity: 1;
  }
}

.status-changing-smooth {
  animation: statusChangeSmooth 0.6s ease-out;
}

/* Prevent rapid status message changes */
.status-message-stable {
  min-height: 1.2em; /* Prevent height changes */
  transition: opacity 0.3s ease-out;
}

/* Loading state styling to prevent flickers */
.loading-state {
  opacity: 0.9;
  pointer-events: none;
}

/* Smooth transition for save process */
.save-process-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(1px);
  z-index: 10;
  transition: opacity 0.3s ease-out;
}

.dark .save-process-overlay {
  background: rgba(0, 0, 0, 0.5);
}

/* Prevent button text from jumping during state changes */
.save-button-stable {
  min-width: 120px; /* Ensure consistent button width */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Simple status change animation */
@keyframes statusChange {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.status-changing {
  animation: statusChange 0.4s ease-out;
}

/* Subtle success pulse animation */
@keyframes successPulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

.success-animation {
  animation: successPulse 0.8s ease-out 2;
}

/* Simple panel slide animations */
@keyframes slideInDown {
  from {
    transform: translateY(-8px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.panel-enter {
  animation: slideInDown 0.2s ease-out;
}

/* Micro-interaction for status message */
.status-message {
  transition: all 0.2s ease-out;
}

/* Simple card animations */
.sidebar-card {
  transition: all 0.2s ease-out;
}

.sidebar-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.dark .sidebar-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Font styles */
.sidebar h1, .sidebar h2, .sidebar h3, .sidebar h4, .sidebar h5, .sidebar h6 {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.sidebar p, .sidebar span, .sidebar div, .sidebar button, .sidebar input {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Resize handle styles */
.sidebar-resize-handle {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  cursor: ew-resize;
  transition: background-color 0.3s ease;
  touch-action: none; /* Prevent scrolling on touch devices */
}

.sidebar-resize-handle:hover,
.sidebar-resize-handle.dragging {
  background-color: rgba(99, 102, 241, 0.5);
}

/* Card styles */
.sidebar-card {
  overflow: hidden;
  transition: box-shadow 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), 
              transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  border-radius: 0.5rem;
  margin-bottom: 0.75rem;
}
/* 
.sidebar-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
} */

.dark .sidebar-card {
  /* background-color: #1f2937; */
  /* border-color: rgba(255, 255, 255, 0.1); */
}

.dark .sidebar-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* Spring animation for expanding content - reduced springiness */
@keyframes expandIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.sidebar .expand-in {
  animation: expandIn 0.5s cubic-bezier(0.25, 1.0, 0.5, 1);
}

/* Spring animation for expanding content - reduced springiness */
@keyframes expandIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.sidebar-expand-animation {
  animation: expandIn 0.5s cubic-bezier(0.25, 1.0, 0.5, 1);
}

/* Content transition animations - reduced springiness */
.sidebar-content-enter {
  opacity: 0;
  transform: translateX(10px) scale(0.98);
}

.sidebar-content-enter-active {
  opacity: 1;
  transform: translateX(0) scale(1);
  transition: opacity 0.4s cubic-bezier(0.25, 1.0, 0.5, 1),
              transform 0.4s cubic-bezier(0.25, 1.0, 0.5, 1);
}

.sidebar-content-exit {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.sidebar-content-exit-active {
  opacity: 0;
  transform: translateX(-10px) scale(0.98);
  transition: opacity 0.3s cubic-bezier(0.25, 1.0, 0.5, 1),
              transform 0.3s cubic-bezier(0.25, 1.0, 0.5, 1);
}

/* New styles for placeholder content during transitions */
.content-placeholder {
  opacity: 0;
  animation: fadeIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  animation-delay: 0.15s;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
}

.placeholder-card {
  height: 80px;
  border-radius: 8px;
  background: linear-gradient(90deg, 
    rgba(229, 231, 235, 0.3) 0%, 
    rgba(243, 244, 246, 0.4) 50%, 
    rgba(229, 231, 235, 0.3) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  will-change: background-position;
}

.dark .placeholder-card {
  background: linear-gradient(90deg, 
    rgba(55, 65, 81, 0.3) 0%, 
    rgba(75, 85, 99, 0.4) 50%, 
    rgba(55, 65, 81, 0.3) 100%);
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Simple loading animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Fade in animation for panels */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.2s ease-out;
}
