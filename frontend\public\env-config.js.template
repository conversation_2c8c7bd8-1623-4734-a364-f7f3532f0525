// This file injects environment variables into the window object
// It will be loaded before the React app
console.log('Loading environment variables from env-config.js');

window.ENV = {
  // API URL from environment variable or default
  REACT_APP_API_URL: '${REACT_APP_API_URL:-http://localhost:5000}',
  REACT_APP_ENV: '${REACT_APP_ENV:-development}',
  // Supabase configuration from environment variables
  SUPABASE_URL: '${SUPABASE_URL:-https://your-project-ref.supabase.co}',
  SUPABASE_KEY: '${SUPABASE_KEY:-your-anon-public-key-here}'
};

console.log('Environment variables loaded:');
console.log('API URL:', window.ENV.REACT_APP_API_URL);
console.log('Supabase URL:', window.ENV.SUPABASE_URL);
console.log('Supabase Key:', window.ENV.SUPABASE_KEY ? 'Set' : 'Not set'); 