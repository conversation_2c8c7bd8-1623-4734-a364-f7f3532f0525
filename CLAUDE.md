# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Writer v2 is an AI-powered content creation and analysis platform with a React/TypeScript frontend and FastAPI backend. The application has been **fully migrated from Flask to FastAPI** (migration completed June 2025).

### Technology Stack

**Backend (FastAPI)**
- FastAPI with async/await patterns
- Pydantic for data validation and settings
- Supabase for database and authentication
- OpenAI/OpenRouter for AI integration
- YouTube API integration
- uvicorn as ASGI server

**Frontend (React/TypeScript)**
- React 18 with TypeScript
- Material-UI (MUI) for components
- Redux Toolkit for state management
- Supabase client for authentication
- Framer Motion for animations

## Development Commands

### Backend Development
```bash
# Run backend with hot reload (development)
uvicorn main:app --host=0.0.0.0 --port=5000 --reload

# Run tests
pytest

# Code formatting and linting
black .
flake8 .

# Health check
python supabase_health_check.py
```

### Frontend Development
```bash
cd frontend

# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Linting and formatting
npm run lint
npm run format
```

### Docker Development
```bash
# Development with hot reload
docker-compose --profile dev up

# Production-like environment
docker-compose --profile prod up

# Rebuild containers
docker-compose build
```

## Architecture Overview

### Backend Structure
- `main.py` - FastAPI application factory and routing setup
- `app/config.py` - Pydantic settings management
- `app/routers/` - FastAPI route modules (auth, content, ai_assistant, etc.)
- `app/repositories/` - Data access layer with Supabase integration
- `app/utils/` - Shared utilities and helpers
- `app/ai_assistant/` - AI integration modules (OpenAI/OpenRouter adapters)

### Frontend Structure
- `src/pages/` - Main application pages
- `src/components/` - Reusable React components
- `src/contexts/` - React context providers (Auth, Wizard)
- `src/services/` - API integration and external services
- `src/store/` - Redux store configuration
- `src/utils/` - Frontend utilities

### Key Patterns

**FastAPI Dependency Injection**
- Use FastAPI's dependency injection for authentication, database connections
- Settings accessed via `get_settings()` function
- No Flask `g` object - use proper DI patterns

**Async/Await**
- All backend code uses async patterns
- Database operations are async with asyncpg
- HTTP clients use httpx for async requests

**Authentication Flow**
- Supabase handles user authentication
- JWT tokens validated through FastAPI dependencies
- Frontend uses Supabase client for auth state

**Repository Pattern**
- Database operations abstracted through repository classes
- All repositories extend base Supabase repository
- Async methods for all database operations

## Environment Configuration

The application uses environment variables loaded from `.env.local`:

**Required Backend Variables:**
- `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`
- `OPENAI_API_KEY` or `OPENROUTER_API_KEY` (based on `AI_PROVIDER`)
- `YOUTUBE_API_KEY`
- `JWT_SECRET_KEY`

**Frontend Variables:**
- `REACT_APP_SUPABASE_URL`, `REACT_APP_SUPABASE_KEY`
- `REACT_APP_API_URL` (defaults to `/api`)

## Testing

**Backend Tests:**
- Located in `tests/` directory
- Uses pytest with async support (`pytest-asyncio`)
- Test AI adapters, endpoints, and repositories

**Frontend Tests:**
- Uses React Testing Library and Jest
- Located in `frontend/src/tests/`
- Test components, contexts, and Redux slices

## Migration Notes

The codebase has been fully migrated from Flask to FastAPI:
- No Flask imports remain in main application code
- All routes converted to FastAPI routers
- Pydantic settings replace Flask config
- FastAPI dependency injection replaces Flask blueprints
- Async patterns throughout

When working with this codebase, always use FastAPI patterns and avoid any Flask-style code.