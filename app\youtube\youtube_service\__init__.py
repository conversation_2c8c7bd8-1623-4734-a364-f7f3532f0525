import os
import re
import requests
from googleapiclient.discovery import build
from youtube_transcript_api import (
    YouTubeTranscriptApi,
    TranscriptsDisabled,
    NoTranscriptFound,
)
import logging
from typing import Dict, Any, List

from main import get_settings


class YouTubeService:
    """Service for interacting with the YouTube API and processing YouTube data"""

    def __init__(self):
        """Initialize the YouTube service with API key from FastAPI settings"""
        settings = get_settings()
        self.api_key = settings.youtube_api_key
        if not self.api_key:
            logging.warning("YouTube API key not found in settings")
            raise ValueError("YouTube API key is required but not configured")

    def extract_video_id(self, url: str) -> str:
        """
        Extract the video ID from a YouTube URL

        Args:
            url (str): The YouTube URL

        Returns:
            str: The video ID or empty string if not found
        """
        try:
            # Handle different URL formats
            patterns = [
                r"(?:youtube\.com\/watch\?v=)([A-Za-z0-9_-]{11})",
                r"(?:youtu\.be\/)([A-Za-z0-9_-]{11})",
                r"(?:youtube\.com\/embed\/)([A-Za-z0-9_-]{11})",
                r"(?:youtube\.com\/v\/)([A-Za-z0-9_-]{11})",
            ]

            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    return match.group(1)

            return ""

        except Exception as e:
            logging.error(f"Error extracting video ID: {str(e)}")
            return ""

    def get_video_details(self, video_id: str) -> Dict[str, Any]:
        """
        Get video details from the YouTube API

        Args:
            video_id (str): The YouTube video ID

        Returns:
            Dict[str, Any]: Video details including title, channel, views, likes, etc.
        """
        try:
            if not self.api_key:
                logging.error("YouTube API key not available")
                return {}

            # API endpoint for video details
            url = f"https://www.googleapis.com/youtube/v3/videos?id={video_id}&key={self.api_key}&part=snippet,statistics,contentDetails"

            # Make the API request
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            # Parse the response
            data = response.json()

            # Check if video exists
            if not data.get("items"):
                logging.error(f"No video found with ID: {video_id}")
                return {}

            # Extract video details
            video_data = data["items"][0]
            snippet = video_data.get("snippet", {})
            statistics = video_data.get("statistics", {})
            content_details = video_data.get("contentDetails", {})

            # Get channel details
            channel_id = snippet.get("channelId", "")
            channel_details = self.get_channel_details(channel_id) if channel_id else {}

            # Combine data
            video_details = {
                "title": snippet.get("title", ""),
                "description": snippet.get("description", ""),
                "channel_title": snippet.get("channelTitle", ""),
                "channel_id": channel_id,
                "publish_date": snippet.get("publishedAt", ""),
                "view_count": int(statistics.get("viewCount", 0)),
                "like_count": int(statistics.get("likeCount", 0)),
                "comment_count": int(statistics.get("commentCount", 0)),
                "duration": content_details.get("duration", ""),
                "channel_subscribers": channel_details.get("subscriber_count", 0),
            }

            return video_details

        except Exception as e:
            logging.error(f"Error getting video details: {str(e)}")
            return {}

    def get_channel_details(self, channel_id: str) -> Dict[str, Any]:
        """
        Get channel details from the YouTube API

        Args:
            channel_id (str): The YouTube channel ID

        Returns:
            Dict[str, Any]: Channel details including subscriber count
        """
        try:
            if not self.api_key:
                logging.error("YouTube API key not available")
                return {}

            # API endpoint for channel details
            url = f"https://www.googleapis.com/youtube/v3/channels?id={channel_id}&key={self.api_key}&part=statistics"

            # Make the API request
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            # Parse the response
            data = response.json()

            # Check if channel exists
            if not data.get("items"):
                logging.error(f"No channel found with ID: {channel_id}")
                return {}

            # Extract channel details
            channel_data = data["items"][0]
            statistics = channel_data.get("statistics", {})

            # Combine data
            channel_details = {
                "subscriber_count": int(statistics.get("subscriberCount", 0))
            }

            return channel_details

        except Exception as e:
            logging.error(f"Error getting channel details: {str(e)}")
            return {}

    def get_transcript(self, video_id: str) -> str:
        """
        Get the transcript of a YouTube video

        Args:
            video_id (str): The YouTube video ID

        Returns:
            str: The transcript text or empty string if not available
        """
        try:
            # Use youtube_transcript_api to get transcript
            from youtube_transcript_api import YouTubeTranscriptApi

            # Get transcript
            transcript_list = YouTubeTranscriptApi.get_transcript(video_id)

            # Process and format the transcript
            return self.process_transcript(transcript_list)

        except Exception as e:
            logging.error(f"Error getting transcript: {str(e)}")
            return ""

    def process_transcript(self, transcript_list: List[Dict[str, Any]]) -> str:
        """
        Process and format a YouTube transcript

        Args:
            transcript_list (List[Dict[str, Any]]): The raw transcript data

        Returns:
            str: The formatted transcript text
        """
        if not transcript_list:
            return ""

        # Extract text and timestamps
        processed_segments = []
        current_paragraph = []
        sentence_buffer = ""

        for i, segment in enumerate(transcript_list):
            text = segment.get("text", "").strip()
            start_time = segment.get("start", 0)

            # Skip empty segments
            if not text:
                continue

            # Format timestamp as MM:SS
            minutes = int(start_time // 60)
            seconds = int(start_time % 60)
            timestamp = f"[{minutes:02d}:{seconds:02d}]"

            # Add timestamp to first segment and at regular intervals
            if i == 0 or i % 15 == 0:
                text_with_timestamp = f"{timestamp} {text}"
            else:
                text_with_timestamp = text

            # Add to sentence buffer
            sentence_buffer += " " + text_with_timestamp

            # Check if segment ends with sentence-ending punctuation
            if text.endswith((".", "!", "?")) or len(current_paragraph) >= 5:
                current_paragraph.append(sentence_buffer.strip())
                sentence_buffer = ""

            # Create a new paragraph after several sentences or at natural breaks
            if len(current_paragraph) >= 5 or (
                text.endswith((".", "!", "?")) and (i % 10 == 0)
            ):
                processed_segments.append(" ".join(current_paragraph))
                current_paragraph = []

        # Add any remaining content
        if sentence_buffer:
            current_paragraph.append(sentence_buffer.strip())
        if current_paragraph:
            processed_segments.append(" ".join(current_paragraph))

        # Join paragraphs with double newlines
        formatted_transcript = "\n\n".join(processed_segments)

        # Clean up any artifacts or formatting issues
        formatted_transcript = re.sub(r"\s+", " ", formatted_transcript)
        formatted_transcript = re.sub(r" \.", ".", formatted_transcript)
        formatted_transcript = re.sub(r" ,", ",", formatted_transcript)
        formatted_transcript = re.sub(r" !", "!", formatted_transcript)
        formatted_transcript = re.sub(r" \?", "?", formatted_transcript)
        formatted_transcript = re.sub(r" :", ":", formatted_transcript)
        formatted_transcript = re.sub(r" ;", ";", formatted_transcript)

        return formatted_transcript

    def analyze_transcript(self, transcript: str) -> Dict[str, Any]:
        """
        Analyze a transcript to extract key information

        Args:
            transcript (str): The transcript text

        Returns:
            Dict[str, Any]: Analysis results including key topics, structure, etc.
        """
        if not transcript:
            return {}

        try:
            # Basic transcript statistics
            word_count = len(transcript.split())
            sentence_count = len(re.findall(r"[.!?]+", transcript))
            paragraph_count = len(transcript.split("\n\n"))

            # Extract potential key phrases (simple implementation)
            # In a real implementation, this would use NLP techniques
            words = re.findall(r"\b[a-zA-Z]{3,}\b", transcript.lower())
            word_freq = {}
            for word in words:
                if word not in STOPWORDS:
                    word_freq[word] = word_freq.get(word, 0) + 1

            # Get top keywords
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            top_keywords = [word for word, count in sorted_words[:10]]

            # Identify potential key sentences (simple implementation)
            sentences = re.split(r"(?<=[.!?])\s+", transcript)
            key_sentences = []
            for sentence in sentences:
                # Score sentences based on keyword presence
                score = sum(1 for word in top_keywords if word in sentence.lower())
                if score >= 2 and len(sentence.split()) >= 10:
                    key_sentences.append(sentence)

            # Limit to top 5 key sentences
            key_sentences = key_sentences[:5]

            # Estimate transcript structure
            structure = {}
            if paragraph_count >= 3:
                # Estimate intro, body, conclusion
                paragraphs = transcript.split("\n\n")
                intro_length = max(1, paragraph_count // 5)
                conclusion_length = max(1, paragraph_count // 5)
                body_length = paragraph_count - intro_length - conclusion_length

                structure = {
                    "introduction": "\n\n".join(paragraphs[:intro_length]),
                    "body": "\n\n".join(
                        paragraphs[intro_length : intro_length + body_length]
                    ),
                    "conclusion": "\n\n".join(paragraphs[-conclusion_length:]),
                }

            # Return analysis results
            return {
                "word_count": word_count,
                "sentence_count": sentence_count,
                "paragraph_count": paragraph_count,
                "top_keywords": top_keywords,
                "key_sentences": key_sentences,
                "structure": structure,
            }

        except Exception as e:
            logging.error(f"Error analyzing transcript: {str(e)}")
            return {}

    def analyze_video(self, video_id: str) -> Dict[str, Any]:
        """
        Analyze a YouTube video's performance

        Args:
            video_id (str): The YouTube video ID

        Returns:
            Dict[str, Any]: Analysis results including glowAI score, performance category, etc.
        """
        try:
            # Get video details
            video_details = self.get_video_details(video_id)
            if not video_details:
                return {}

            # Extract metrics
            views = video_details.get("view_count", 0)
            likes = video_details.get("like_count", 0)
            comments = video_details.get("comment_count", 0)
            subscribers = video_details.get("channel_subscribers", 0)

            # Calculate engagement rate
            engagement = 0
            if views > 0:
                engagement = (likes + comments) / views * 100

            # Calculate view-to-subscriber ratio
            view_sub_ratio = 0
            if subscribers > 0:
                view_sub_ratio = views / subscribers * 100

            # Calculate glowAI score (simplified algorithm)
            # In a real implementation, this would be more sophisticated
            base_score = min(50, (views / 1000) * 0.5) if views > 0 else 0
            engagement_score = min(30, engagement * 3)
            ratio_score = min(20, view_sub_ratio / 5)

            glow_ai_score = int(base_score + engagement_score + ratio_score)

            # Determine performance category
            performance_category = "Unknown"
            if glow_ai_score >= 80:
                performance_category = "Exceptional"
            elif glow_ai_score >= 60:
                performance_category = "Strong"
            elif glow_ai_score >= 40:
                performance_category = "Good"
            elif glow_ai_score >= 20:
                performance_category = "Average"
            else:
                performance_category = "Below Average"

            # Calculate view multiple (compared to channel average)
            # In a real implementation, this would use actual channel averages
            view_multiple = 1.0
            if subscribers > 0:
                expected_views = subscribers * 0.1  # Simplified assumption
                if expected_views > 0:
                    view_multiple = views / expected_views

            # Return analysis results
            return {
                "glow_ai_score": glow_ai_score,
                "performance_category": performance_category,
                "engagement_rate": round(engagement, 2),
                "view_multiple": round(view_multiple, 2),
                "view_sub_ratio": round(view_sub_ratio, 2),
            }

        except Exception as e:
            logging.error(f"Error analyzing video: {str(e)}")
            return {}

    def search_videos(self, query: str, max_results: int = 5) -> List[Dict[str, Any]]:
        """
        Search for YouTube videos

        Args:
            query (str): The search query
            max_results (int, optional): Maximum number of results. Defaults to 5.

        Returns:
            List[Dict[str, Any]]: List of video data
        """
        try:
            if not self.api_key:
                logging.error("YouTube API key not available")
                return []

            # API endpoint for search
            url = f"https://www.googleapis.com/youtube/v3/search?part=snippet&q={query}&key={self.api_key}&maxResults={max_results}&type=video"

            # Make the API request
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            # Parse the response
            data = response.json()

            # Extract video data
            videos = []
            for item in data.get("items", []):
                video_id = item.get("id", {}).get("videoId", "")
                snippet = item.get("snippet", {})

                video_data = {
                    "video_id": video_id,
                    "url": f"https://youtube.com/watch?v={video_id}",
                    "title": snippet.get("title", ""),
                    "description": snippet.get("description", ""),
                    "channel_title": snippet.get("channelTitle", ""),
                    "publish_date": snippet.get("publishedAt", ""),
                    "thumbnail": snippet.get("thumbnails", {})
                    .get("high", {})
                    .get("url", ""),
                }

                videos.append(video_data)

            return videos

        except Exception as e:
            logging.error(f"Error searching videos: {str(e)}")
            return []

    def get_channel_videos(
        self, channel_id: str, max_results: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get recent videos for a specific channel.

        Args:
            channel_id (str): The YouTube channel ID.
            max_results (int, optional): Max number of videos to retrieve (up to 50 per API call). Defaults to 50.

        Returns:
            List[Dict[str, Any]]: List of video data including id, publish date, and current view count.
        """
        if not self.api_key:
            logging.error("YouTube API key not available")
            return []

        if not channel_id:
            logging.error("Channel ID is required to get channel videos")
            return []

        videos_data = []
        try:
            # Use googleapiclient for easier pagination if needed, but requests is fine for single page
            # 1. Search for recent video IDs by channel
            search_url = (
                f"https://www.googleapis.com/youtube/v3/search?part=snippet"
                f"&channelId={channel_id}"
                f"&maxResults={min(max_results, 50)}&order=date&type=video"
                f"&key={self.api_key}"
            )
            search_response = requests.get(search_url, timeout=15)
            search_response.raise_for_status()
            search_data = search_response.json()

            video_ids = [
                item.get("id", {}).get("videoId", "")
                for item in search_data.get("items", [])
                if item.get("id", {}).get("videoId")
            ]

            if not video_ids:
                logging.warning(f"No recent videos found for channel ID: {channel_id}")
                return []

            # 2. Get details for these video IDs (in batches if necessary, though 50 is the limit for videos.list too)
            ids_string = ",".join(video_ids)
            details_url = (
                f"https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics"
                f"&id={ids_string}"
                f"&key={self.api_key}"
            )
            details_response = requests.get(details_url, timeout=15)
            details_response.raise_for_status()
            details_data = details_response.json()

            # 3. Combine data
            for item in details_data.get("items", []):
                video_id = item.get("id")
                snippet = item.get("snippet", {})
                statistics = item.get("statistics", {})
                videos_data.append(
                    {
                        "video_id": video_id,
                        "publishedAt": snippet.get("publishedAt"),
                        "title": snippet.get("title"),
                        "viewCount": int(statistics.get("viewCount", 0)),
                        # Add other fields if needed later, like duration
                    }
                )

            return videos_data

        except requests.exceptions.RequestException as e:
            logging.error(
                f"HTTP Error fetching channel videos for {channel_id}: {str(e)}"
            )
            return []
        except Exception as e:
            logging.error(f"Error getting channel videos for {channel_id}: {str(e)}")
            return []

    def get_video_data_from_url(self, url: str) -> Dict[str, Any]:
        """
        Extract data from a YouTube video URL

        Args:
            url (str): The YouTube video URL

        Returns:
            Dict[str, Any]: Video data including title, channel, views, likes, transcript, etc.
        """
        try:
            # Extract video ID from URL
            video_id = self.extract_video_id(url)
            if not video_id:
                logging.error(f"Could not extract video ID from URL: {url}")
                return {}

            # Get video details from API
            video_details = self.get_video_details(video_id)
            if not video_details:
                logging.error(f"Could not get video details for ID: {video_id}")
                return {}

            # Get transcript
            transcript = self.get_transcript(video_id)

            # Get transcript analysis if transcript is available
            transcript_analysis = {}
            if transcript:
                transcript_analysis = self.analyze_transcript(transcript)

            # Combine data
            video_data = {
                "youtube_id": video_id,
                "url": f"https://youtube.com/watch?v={video_id}",
                "title": video_details.get("title", ""),
                "description": video_details.get("description", ""),
                "channel_title": video_details.get("channel_title", ""),
                "channel_id": video_details.get("channel_id", ""),
                "views": video_details.get("view_count", 0),
                "likes": video_details.get("like_count", 0),
                "comments": video_details.get("comment_count", 0),
                "publish_date": video_details.get("publish_date", ""),
                "duration": video_details.get("duration", ""),
                "channel_subscribers": video_details.get("channel_subscribers", 0),
                "transcript": transcript,
            }

            # Add transcript analysis if available
            if transcript_analysis:
                video_data.update(
                    {
                        "transcript_word_count": transcript_analysis.get(
                            "word_count", 0
                        ),
                        "transcript_top_keywords": transcript_analysis.get(
                            "top_keywords", []
                        ),
                        "transcript_key_sentences": transcript_analysis.get(
                            "key_sentences", []
                        ),
                    }
                )

            return video_data

        except Exception as e:
            logging.error(f"Error getting video data: {str(e)}")
            return {}


# Define common stopwords for transcript analysis
STOPWORDS = {
    "a",
    "an",
    "the",
    "and",
    "or",
    "but",
    "if",
    "because",
    "as",
    "what",
    "which",
    "this",
    "that",
    "these",
    "those",
    "then",
    "just",
    "so",
    "than",
    "such",
    "both",
    "through",
    "about",
    "for",
    "is",
    "of",
    "while",
    "during",
    "to",
    "from",
    "in",
    "out",
    "on",
    "off",
    "over",
    "under",
    "again",
    "further",
    "then",
    "once",
    "here",
    "there",
    "when",
    "where",
    "why",
    "how",
    "all",
    "any",
    "both",
    "each",
    "few",
    "more",
    "most",
    "other",
    "some",
    "such",
    "no",
    "nor",
    "not",
    "only",
    "own",
    "same",
    "so",
    "than",
    "too",
    "very",
    "s",
    "t",
    "can",
    "will",
    "just",
    "don",
    "should",
    "now",
    "i",
    "me",
    "my",
    "myself",
    "we",
    "our",
    "ours",
    "ourselves",
    "you",
    "your",
    "yours",
    "yourself",
    "yourselves",
    "he",
    "him",
    "his",
    "himself",
    "she",
    "her",
    "hers",
    "herself",
    "it",
    "its",
    "itself",
    "they",
    "them",
    "their",
    "theirs",
    "themselves",
    "am",
    "is",
    "are",
    "was",
    "were",
    "be",
    "been",
    "being",
    "have",
    "has",
    "had",
    "having",
    "do",
    "does",
    "did",
    "doing",
    "would",
    "should",
    "could",
    "ought",
    "i'm",
    "you're",
    "he's",
    "she's",
    "it's",
    "we're",
    "they're",
    "i've",
    "you've",
    "we've",
    "they've",
    "i'd",
    "you'd",
    "he'd",
    "she'd",
    "we'd",
    "they'd",
    "i'll",
    "you'll",
    "he'll",
    "she'll",
    "we'll",
    "they'll",
    "isn't",
    "aren't",
    "wasn't",
    "weren't",
    "hasn't",
    "haven't",
    "hadn't",
    "doesn't",
    "don't",
    "didn't",
    "won't",
    "wouldn't",
    "shan't",
    "shouldn't",
    "can't",
    "cannot",
    "couldn't",
    "mustn't",
    "let's",
    "that's",
    "who's",
    "what's",
    "here's",
    "there's",
    "when's",
    "where's",
    "why's",
    "how's",
    "um",
    "uh",
    "like",
    "yeah",
}
