import React, { useState } from "react";
import { Link as RouterLink } from "react-router-dom";
import {
  <PERSON>,
  <PERSON>ton,
  <PERSON>Field,
  Typography,
  Container,
  Al<PERSON>,
  <PERSON>,
  Paper,
} from "@mui/material";
import { useAuth } from "../../contexts/AuthContext";
import { motion } from "framer-motion";

const RequestPasswordReset: React.FC = () => {
  const [email, setEmail] = useState("");
  const [success, setSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const { resetPassword, loading } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage("");
    setSuccess(false);

    try {
      const result = await resetPassword(email);
      if (result.success) {
        setSuccess(true);
      } else {
        setErrorMessage(result.error || "Failed to send password reset email");
      }
    } catch (error: any) {
      setErrorMessage(error.message || "An unexpected error occurred");
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Paper
        elevation={3}
        sx={{
          padding: 4,
          marginTop: 8,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          borderRadius: 2,
        }}
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          style={{ width: "100%" }}
        >
          <Typography
            component="h1"
            variant="h5"
            sx={{ mb: 3, textAlign: "center" }}
          >
            Reset Password
          </Typography>

          {success ? (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                Password reset email sent! Please check your inbox.
              </Alert>
              <Typography variant="body2" sx={{ mb: 2 }}>
                If you don't see the email, please check your spam folder.
              </Typography>
              <Button
                component={RouterLink}
                to="/login"
                fullWidth
                variant="contained"
                color="primary"
                sx={{ mt: 1 }}
              >
                Return to Login
              </Button>
            </Box>
          ) : (
            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
              {errorMessage && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {errorMessage}
                </Alert>
              )}

              <Typography variant="body2" sx={{ mb: 2 }}>
                Enter your email address and we'll send you a link to reset your
                password.
              </Typography>

              <TextField
                margin="normal"
                required
                fullWidth
                id="email"
                label="Email Address"
                name="email"
                autoComplete="email"
                autoFocus
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={loading}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                color="primary"
                sx={{ mt: 3, mb: 2 }}
                disabled={loading || !email}
              >
                {loading ? "Sending..." : "Send Reset Link"}
              </Button>

              <Box sx={{ mt: 2, textAlign: "center" }}>
                <Link component={RouterLink} to="/login" variant="body2">
                  Back to login
                </Link>
              </Box>
            </Box>
          )}
        </motion.div>
      </Paper>
    </Container>
  );
};

export default RequestPasswordReset;
