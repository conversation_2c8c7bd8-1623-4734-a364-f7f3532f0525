from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Optional, Any
from datetime import datetime
import uuid


class Feedback(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    user_id: uuid.UUID
    content_item_id: Optional[uuid.UUID] = None
    chat_session_id: Optional[uuid.UUID] = None
    rating: int
    feedback_text: Optional[str] = None
    feedback_type: str = "general"
    is_resolved: bool = False
    admin_notes: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    @field_validator("rating")
    @classmethod
    def rating_must_be_between_1_and_5(cls, v: int):
        if not 1 <= v <= 5:
            raise ValueError("Rating must be between 1 and 5")
        return v

    @field_validator("content_item_id", "chat_session_id")
    @classmethod
    def check_target_ids_exclusive(cls, v: Optional[uuid.UUID], info: Any):
        pass

    model_config = ConfigDict(from_attributes=True)

    # Remove old V1 class Config
    # class Config:
    #     orm_mode = True
