@echo off
echo ===================================================
echo Supabase Sample Data Migration Test
echo ===================================================
echo.

REM Check for Python
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python and try again.
    exit /b 1
)

REM Check for required packages
echo Checking for required Python packages...
python -c "import psycopg2, requests, dotenv" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Installing required packages...
    pip install psycopg2-binary requests python-dotenv
)

REM Parse command-line arguments
set SAMPLE_SIZE=10
set CLEANUP=true
set PG_HOST=localhost
set PG_PORT=5432
set PG_DATABASE=
set PG_USER=postgres
set PG_PASSWORD=
set SUPABASE_URL=
set SUPABASE_KEY=

:parse_args
if "%~1"=="" goto :run_test
if /i "%~1"=="--sample-size" (
    set SAMPLE_SIZE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--no-cleanup" (
    set CLEANUP=false
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-host" (
    set PG_HOST=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-port" (
    set PG_PORT=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-database" (
    set PG_DATABASE=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-user" (
    set PG_USER=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--pg-password" (
    set PG_PASSWORD=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--supabase-url" (
    set SUPABASE_URL=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--supabase-key" (
    set SUPABASE_KEY=%~2
    shift
    shift
    goto :parse_args
)
if /i "%~1"=="--help" (
    goto :show_help
)
shift
goto :parse_args

:show_help
echo.
echo Usage: run_sample_data_migration_test.bat [options]
echo.
echo Options:
echo   --sample-size N       Number of sample records to generate (default: 10)
echo   --no-cleanup          Don't clean up sample data after test
echo   --pg-host HOST        PostgreSQL host (default: localhost)
echo   --pg-port PORT        PostgreSQL port (default: 5432)
echo   --pg-database DB      PostgreSQL database name (default: from .env)
echo   --pg-user USER        PostgreSQL username (default: postgres)
echo   --pg-password PASS    PostgreSQL password (default: from .env)
echo   --supabase-url URL    Supabase URL (default: from .env)
echo   --supabase-key KEY    Supabase API key (default: from .env)
echo   --help                Show this help message
echo.
exit /b 0

:run_test
echo.
echo Running sample data migration test with the following settings:
echo Sample size: %SAMPLE_SIZE%
echo Cleanup: %CLEANUP%
echo PostgreSQL host: %PG_HOST%
echo PostgreSQL port: %PG_PORT%
if not "%PG_DATABASE%"=="" echo PostgreSQL database: %PG_DATABASE%
echo PostgreSQL user: %PG_USER%
if not "%PG_PASSWORD%"=="" echo PostgreSQL password: [REDACTED]
if not "%SUPABASE_URL%"=="" echo Supabase URL: %SUPABASE_URL%
if not "%SUPABASE_KEY%"=="" echo Supabase key: [REDACTED]
echo.

REM Create timestamp for results
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~0,4%"
set "MM=%dt:~4,2%"
set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%"
set "Min=%dt:~10,2%"
set "Sec=%dt:~12,2%"
set "timestamp=%YY%%MM%%DD%_%HH%%Min%%Sec%"

REM Create results directory if it doesn't exist
if not exist "sample_test_results" mkdir sample_test_results
set "RESULTS_DIR=sample_test_results\test_%timestamp%"
mkdir "%RESULTS_DIR%"

REM Build command with available parameters
set CMD=python test_sample_data_migration.py --sample-size %SAMPLE_SIZE%
if "%CLEANUP%"=="false" set CMD=%CMD% --no-cleanup
set CMD=%CMD% --pg-host %PG_HOST% --pg-port %PG_PORT%
if not "%PG_DATABASE%"=="" set CMD=%CMD% --pg-database %PG_DATABASE%
set CMD=%CMD% --pg-user %PG_USER%
if not "%PG_PASSWORD%"=="" set CMD=%CMD% --pg-password %PG_PASSWORD%
if not "%SUPABASE_URL%"=="" set CMD=%CMD% --supabase-url %SUPABASE_URL%
if not "%SUPABASE_KEY%"=="" set CMD=%CMD% --supabase-key %SUPABASE_KEY%

echo Running: %CMD%
echo.
echo Test output will be saved to "%RESULTS_DIR%\test_output.log"
echo.

REM Run the test and capture output
%CMD% > "%RESULTS_DIR%\test_output.log" 2>&1

if %ERRORLEVEL% equ 0 (
    echo.
    echo ===================================================
    echo SAMPLE DATA MIGRATION TEST PASSED
    echo ===================================================
    echo.
    echo The sample data migration test completed successfully.
    echo All data was correctly migrated from PostgreSQL to Supabase.
    echo.
    echo This indicates that the migration process is working correctly
    echo and is ready for testing with production data.
    echo.
    echo See detailed log at "%RESULTS_DIR%\test_output.log"
    echo.
    
    REM Add timestamp to summary file
    echo ===================================================>> "%RESULTS_DIR%\summary.txt"
    echo SAMPLE DATA MIGRATION TEST - PASSED>> "%RESULTS_DIR%\summary.txt"
    echo ===================================================>> "%RESULTS_DIR%\summary.txt"
    echo.>> "%RESULTS_DIR%\summary.txt"
    echo Date: %YY%-%MM%-%DD% %HH%:%Min%:%Sec%>> "%RESULTS_DIR%\summary.txt"
    echo Sample size: %SAMPLE_SIZE%>> "%RESULTS_DIR%\summary.txt"
    echo.>> "%RESULTS_DIR%\summary.txt"
    echo All test data was successfully migrated from PostgreSQL to Supabase.>> "%RESULTS_DIR%\summary.txt"
    echo This test validates that the migration process is working correctly.>> "%RESULTS_DIR%\summary.txt"
    echo.>> "%RESULTS_DIR%\summary.txt"
    
    exit /b 0
) else (
    echo.
    echo ===================================================
    echo SAMPLE DATA MIGRATION TEST FAILED
    echo ===================================================
    echo.
    echo The sample data migration test encountered errors.
    echo Please review the detailed log to identify and address the issues.
    echo.
    echo See detailed log at "%RESULTS_DIR%\test_output.log"
    echo.
    
    REM Add timestamp to summary file
    echo ===================================================>> "%RESULTS_DIR%\summary.txt"
    echo SAMPLE DATA MIGRATION TEST - FAILED>> "%RESULTS_DIR%\summary.txt"
    echo ===================================================>> "%RESULTS_DIR%\summary.txt"
    echo.>> "%RESULTS_DIR%\summary.txt"
    echo Date: %YY%-%MM%-%DD% %HH%:%Min%:%Sec%>> "%RESULTS_DIR%\summary.txt"
    echo Sample size: %SAMPLE_SIZE%>> "%RESULTS_DIR%\summary.txt"
    echo.>> "%RESULTS_DIR%\summary.txt"
    echo The migration test encountered errors. Please review the detailed log.>> "%RESULTS_DIR%\summary.txt"
    echo.>> "%RESULTS_DIR%\summary.txt"
    
    exit /b 1
) 