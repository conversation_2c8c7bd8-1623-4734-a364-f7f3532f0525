import requests

print("🔍 Testing API endpoints after frontend fix...")
print("=" * 50)

# Test the endpoints that should work
endpoints = [
    ("http://localhost:5000/health", "Root health check"),
    ("http://localhost:5000/api/health", "API health check"),
    ("http://localhost:5000/api/", "API root"),
    ("http://localhost:5000/api/auth/ping", "Auth ping (no auth required)"),
]

for url, description in endpoints:
    try:
        print(f"\n✅ Testing: {description}")
        print(f"   URL: {url}")
        response = requests.get(url, timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code < 400:
            try:
                json_data = response.json()
                print(f"   Response: {json_data}")
            except:
                print(f"   Response: {response.text}")
        else:
            print(f"   Error Response: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

print("\n" + "=" * 50)
print("🎯 FRONTEND SIMULATION TEST")
print("=" * 50)
print("Simulating what frontend will now do with the fix:")

# Simulate frontend API calls with new baseURL
base_url = "http://localhost:5000"  # This is what frontend will use now
frontend_endpoints = [
    (f"{base_url}/api/health", "Frontend -> /api/health"),
    (f"{base_url}/api/auth/ping", "Frontend -> /api/auth/ping"),
]

for url, description in frontend_endpoints:
    try:
        print(f"\n🚀 {description}")
        response = requests.get(url, timeout=5)
        print(f"   Status: {response.status_code}")
        if response.status_code < 400:
            print(f"   ✅ SUCCESS: {response.json()}")
        else:
            print(f"   ❌ FAILED: {response.text}")
    except Exception as e:
        print(f"   ❌ Error: {e}")

print("\n" + "=" * 50)
print("📊 CONCLUSION")
print("=" * 50)
print("If all frontend simulation tests show SUCCESS, the fix is working!")
