#!/usr/bin/env python3
"""
Obsidian Vault Refactoring Script
Based on: Vault Refactoring Plan.md
Purpose: Systematically reorganize vault according to PARA methodology

CRITICAL: Run backup before executing!
"""

import os
import shutil
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple

class VaultRefactorer:
    def __init__(self, vault_path: str):
        self.vault_path = Path(vault_path)
        self.backup_path = self.vault_path.parent / f"ModernMeditations_Backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Create system directory if it doesn't exist
        system_dir = self.vault_path / "0. System"
        system_dir.mkdir(exist_ok=True)
        
        self.log_file = system_dir / f"refactor_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
        # Setup logging
        self.logger = logging.getLogger(f"VaultRefactorer_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.logger.setLevel(logging.INFO)
        
        # Clear any existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Create formatters
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        
        # Add file handler
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # Add console handler with safe encoding
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # Prevent propagation to root logger
        self.logger.propagate = False

    def create_backup(self) -> bool:
        """Create full vault backup before any changes"""
        try:
            self.logger.info(f"Creating backup at: {self.backup_path}")
            shutil.copytree(self.vault_path, self.backup_path)
            self.logger.info("[SUCCESS] Backup created successfully")
            return True
        except Exception as e:
            self.logger.error(f"[ERROR] Backup failed: {e}")
            return False

    def create_new_structure(self) -> None:
        """Create the new folder structure"""
        new_folders = [
            # Projects (renamed)
            "1. Projects/Creator Business Launch",
            "1. Projects/App Development", 
            "1. Projects/Content System Build",
            "1. Projects/Client Work",
            
            # Areas (renamed)
            "2. Areas/Personal Development",
            "2. Areas/Content & Creative",
            "2. Areas/Business Operations",
            "2. Areas/Health & Training",
            "2. Areas/Relationships",
            
            # Resources (new structure)
            "3. Resources/Business Strategy",
            "3. Resources/Content Creation",
            "3. Resources/People & Network", 
            "3. Resources/AI & Tech Tools",
            "3. Resources/Learning Materials",
            "3. Resources/Personal Interests",
            
            # System Support
            "_System Support/Readwise",
            "_System Support/Excalidraw",
            "_System Support/Backups",
            "_System Support/Templates"
        ]
        
        for folder in new_folders:
            folder_path = self.vault_path / folder
            folder_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"📁 Created: {folder}")

    def move_file_safely(self, source: Path, destination: Path) -> bool:
        """Safely move a file with error handling"""
        try:
            if not source.exists():
                self.logger.warning(f"⚠️  Source file not found: {source}")
                return False
                
            # Create destination directory if it doesn't exist
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # Handle file conflicts
            if destination.exists():
                backup_dest = destination.with_suffix(f".backup_{datetime.now().strftime('%H%M%S')}{destination.suffix}")
                shutil.move(str(destination), str(backup_dest))
                self.logger.info(f"🔄 Backed up existing file: {destination} → {backup_dest}")
            
            shutil.move(str(source), str(destination))
            self.logger.info(f"✅ Moved: {source.name} → {destination.parent}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to move {source} to {destination}: {e}")
            return False

    def move_folder_safely(self, source: Path, destination: Path) -> bool:
        """Safely move a folder with error handling"""
        try:
            if not source.exists():
                self.logger.warning(f"⚠️  Source folder not found: {source}")
                return False
                
            # Create parent destination directory
            destination.parent.mkdir(parents=True, exist_ok=True)
            
            # Handle folder conflicts
            if destination.exists():
                backup_dest = destination.with_suffix(f"_backup_{datetime.now().strftime('%H%M%S')}")
                shutil.move(str(destination), str(backup_dest))
                self.logger.info(f"🔄 Backed up existing folder: {destination} → {backup_dest}")
            
            shutil.move(str(source), str(destination))
            self.logger.info(f"📁 Moved folder: {source.name} → {destination.parent}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to move folder {source} to {destination}: {e}")
            return False

    def phase1_root_cleanup(self) -> None:
        """Phase 1: Move all loose files from root directory"""
        self.logger.info("🚀 Starting Phase 1: Root Directory Cleanup")
        
        # Files to move to Personal Development
        personal_files = [
            "Profile.md",
            "Why I'm uniquely Qualified.md", 
            "Profile and expertise for gemini.md",
            "Things that will help.md",
            "therapy.md",
            "Couples Therapy Notes.md"
        ]
        
        for file in personal_files:
            source = self.vault_path / file
            dest = self.vault_path / "2. Areas/Personal Development" / file
            self.move_file_safely(source, dest)

        # Files to move to Business Operations
        business_ops_files = [
            "Cursor Rules.md",
            "mcp servers.md", 
            "Roocode Configuration.md",
            "roomodes.md"
        ]
        
        for file in business_ops_files:
            source = self.vault_path / file
            dest = self.vault_path / "2. Areas/Business Operations" / file
            self.move_file_safely(source, dest)

        # Files to move to Relationships
        relationship_files = [
            "date ideas.md"
        ]
        
        for file in relationship_files:
            source = self.vault_path / file
            dest = self.vault_path / "2. Areas/Relationships" / file
            self.move_file_safely(source, dest)

        # Files to move to Business Strategy
        business_strategy_files = [
            "Business Viral System.md",
            "SMMA Market Analysis.md",
            "Social Media x AI.md", 
            "Market Research Template.md",
            "AI Systems cold calling.md",
            "Audience Page Rip (subscribr).md",
            "Mugsy Offer.md",
            "Offer Publisher's Path.md",
            "Raffiti Funnel.md",
            "Single sentence openers.md",
            "FLow Rules.md",
            "Youtube Video - Build a business with AI.md",
            "selfsortingoffer.png"
        ]
        
        for file in business_strategy_files:
            source = self.vault_path / file
            dest = self.vault_path / "3. Resources/Business Strategy" / file
            self.move_file_safely(source, dest)

        # Files to move to People & Network
        people_files = [
            "Alen Sultanic.md",
            "Alex Soltan.md",
            "Colin Fitzpatrick.md", 
            "Curtis Evans.md",
            "Helen Maroulis.md",
            "Jeff Miller.md",
            "Nick Saraev - How AI helps me get 100k views.md"
        ]
        
        for file in people_files:
            source = self.vault_path / file
            dest = self.vault_path / "3. Resources/People & Network" / file
            self.move_file_safely(source, dest)

        # Files to move to Personal Interests
        gaming_files = [
            "Nightreign.md",
            "Once Human.md",
            "Optimal Run Nightreign.md"
        ]
        
        for file in gaming_files:
            source = self.vault_path / file
            dest = self.vault_path / "3. Resources/Personal Interests" / file
            self.move_file_safely(source, dest)

        # Files to move to Archive
        archive_files = [
            "Untitled.md",
            "Untitled 1.md",
            "Untitled 2.md", 
            "Untitled 3.md",
            "Untitled 4.md",
            "Untitled 5.md",
            "Untitled 6.md",
            "Untitled.canvas"
        ]
        
        for file in archive_files:
            source = self.vault_path / file
            dest = self.vault_path / "4. Archive" / file
            self.move_file_safely(source, dest)

        # Move pasted images to Archive
        for file in self.vault_path.glob("Pasted image *.png"):
            dest = self.vault_path / "4. Archive" / file.name
            self.move_file_safely(file, dest)

        self.logger.info("✅ Phase 1 Complete: Root Directory Cleanup")

    def phase2_resource_consolidation(self) -> None:
        """Phase 2: Consolidate Resources from 18 to 6 categories"""
        self.logger.info("🚀 Starting Phase 2: Resource Consolidation")
        
        # Move AI folder contents to AI & Tech Tools
        ai_source = self.vault_path / "3. Resources/AI"
        ai_dest = self.vault_path / "3. Resources/AI & Tech Tools"
        if ai_source.exists():
            for item in ai_source.iterdir():
                if item.is_file():
                    self.move_file_safely(item, ai_dest / item.name)
                else:
                    self.move_folder_safely(item, ai_dest / item.name)

        # Move Content folder contents to Content Creation
        content_source = self.vault_path / "3. Resources/Content"
        content_dest = self.vault_path / "3. Resources/Content Creation"
        if content_source.exists():
            for item in content_source.iterdir():
                if item.is_file():
                    self.move_file_safely(item, content_dest / item.name)
                else:
                    self.move_folder_safely(item, content_dest / item.name)

        # Move People folder contents to People & Network
        people_source = self.vault_path / "3. Resources/People"
        people_dest = self.vault_path / "3. Resources/People & Network"
        if people_source.exists():
            for item in people_source.iterdir():
                if item.is_file():
                    self.move_file_safely(item, people_dest / item.name)
                else:
                    self.move_folder_safely(item, people_dest / item.name)

        # Move Business folder contents to Business Strategy
        business_source = self.vault_path / "3. Resources/Business"
        business_dest = self.vault_path / "3. Resources/Business Strategy"
        if business_source.exists():
            for item in business_source.iterdir():
                if item.is_file():
                    self.move_file_safely(item, business_dest / item.name)
                else:
                    self.move_folder_safely(item, business_dest / item.name)

        # Move Templates to Content Creation
        templates_source = self.vault_path / "3. Resources/Templates"
        templates_dest = self.vault_path / "3. Resources/Content Creation/Templates"
        if templates_source.exists():
            templates_dest.mkdir(exist_ok=True)
            for item in templates_source.iterdir():
                if item.is_file():
                    self.move_file_safely(item, templates_dest / item.name)
                else:
                    self.move_folder_safely(item, templates_dest / item.name)

        # Consolidate personal interests
        interest_folders = [
            "Gaming", "Music", "MMA Strategies", "Web3", "Training Techniques"
        ]
        
        for folder in interest_folders:
            source = self.vault_path / "3. Resources" / folder
            dest = self.vault_path / "3. Resources/Personal Interests" / folder
            if source.exists():
                self.move_folder_safely(source, dest)

        # Move learning materials
        learning_folders = [
            "Personal Development", "Reading", "Self"
        ]
        
        for folder in learning_folders:
            source = self.vault_path / "3. Resources" / folder
            dest = self.vault_path / "3. Resources/Learning Materials" / folder
            if source.exists():
                self.move_folder_safely(source, dest)

        # Move remaining technical folders
        tech_folders = [
            "Processes", "Troubleshooting"
        ]
        
        for folder in tech_folders:
            source = self.vault_path / "3. Resources" / folder
            dest = self.vault_path / "3. Resources/AI & Tech Tools" / folder
            if source.exists():
                self.move_folder_safely(source, dest)

        self.logger.info("✅ Phase 2 Complete: Resource Consolidation")

    def phase3_areas_projects_restructure(self) -> None:
        """Phase 3: Restructure Areas and Projects"""
        self.logger.info("🚀 Starting Phase 3: Areas & Projects Restructuring")
        
        # Rename Projects folders
        project_renames = {
            "1. Projects/1.01 Active Creator Business": "1. Projects/Creator Business Launch",
            "1. Projects/1.02 In Progress": "1. Projects/App Development",
            "1. Projects/1.03 Content Creation System": "1. Projects/Content System Build",
            "1. Projects/1.04 Client Projects": "1. Projects/Client Work"
        }
        
        for old_path, new_path in project_renames.items():
            source = self.vault_path / old_path
            dest = self.vault_path / new_path
            if source.exists():
                self.move_folder_safely(source, dest)

        # Rename Areas folders
        area_renames = {
            "2. Areas/2.01 Self": "2. Areas/Personal Development",
            "2. Areas/2.02 Creative": "2. Areas/Content & Creative", 
            "2. Areas/2.03 Business": "2. Areas/Business Operations",
            "2. Areas/2.04 Health": "2. Areas/Health & Training",
            "2. Areas/2.05 Relationship": "2. Areas/Relationships"
        }
        
        for old_path, new_path in area_renames.items():
            source = self.vault_path / old_path
            dest = self.vault_path / new_path
            if source.exists():
                self.move_folder_safely(source, dest)

        # Move Curtis Evans 6 folder
        curtis_source = self.vault_path / "Curtis Evans 6"
        curtis_dest = self.vault_path / "1. Projects/Client Work/Curtis Evans"
        if curtis_source.exists():
            self.move_folder_safely(curtis_source, curtis_dest)

        self.logger.info("✅ Phase 3 Complete: Areas & Projects Restructuring")

    def phase4_system_cleanup(self) -> None:
        """Phase 4: System folder consolidation"""
        self.logger.info("🚀 Starting Phase 4: System Cleanup")
        
        # Move system folders to _System Support
        system_moves = {
            "Readwise": "_System Support/Readwise",
            "Excalidraw": "_System Support/Excalidraw",
            "_NoteCompanion/Backups": "_System Support/Backups",
            "_NoteCompanion/Templates": "_System Support/Templates"
        }
        
        for source_path, dest_path in system_moves.items():
            source = self.vault_path / source_path
            dest = self.vault_path / dest_path
            if source.exists():
                self.move_folder_safely(source, dest)

        # Move Creator Inspiration to Content Creation
        creator_source = self.vault_path / "Creator Inspiration"
        creator_dest = self.vault_path / "3. Resources/Content Creation/Creator Inspiration"
        if creator_source.exists():
            self.move_folder_safely(creator_source, creator_dest)

        # Clean up empty _NoteCompanion folder
        note_companion = self.vault_path / "_NoteCompanion"
        if note_companion.exists() and not any(note_companion.iterdir()):
            note_companion.rmdir()
            self.logger.info("🗑️  Removed empty _NoteCompanion folder")

        self.logger.info("✅ Phase 4 Complete: System Cleanup")

    def verify_refactoring(self) -> Dict[str, int]:
        """Verify the refactoring results"""
        self.logger.info("🔍 Starting Verification")
        
        stats = {
            "root_files": 0,
            "resource_categories": 0,
            "empty_folders": 0,
            "total_files": 0
        }
        
        # Count root files (excluding allowed system files)
        allowed_root_files = ["00 - Master Index.md", "0. System"]
        for item in self.vault_path.iterdir():
            if item.is_file() and item.name not in allowed_root_files:
                stats["root_files"] += 1
                self.logger.warning(f"⚠️  Root file still present: {item.name}")

        # Count resource categories
        resources_path = self.vault_path / "3. Resources"
        if resources_path.exists():
            stats["resource_categories"] = len([d for d in resources_path.iterdir() if d.is_dir()])

        # Count empty folders
        for root, dirs, files in os.walk(self.vault_path):
            for dir in dirs:
                dir_path = Path(root) / dir
                if not any(dir_path.iterdir()):
                    stats["empty_folders"] += 1
                    self.logger.warning(f"📂 Empty folder found: {dir_path}")

        # Count total files
        for root, dirs, files in os.walk(self.vault_path):
            stats["total_files"] += len(files)

        self.logger.info(f"📊 Verification Results:")
        self.logger.info(f"   Root files: {stats['root_files']} (target: ≤2)")
        self.logger.info(f"   Resource categories: {stats['resource_categories']} (target: 6)")
        self.logger.info(f"   Empty folders: {stats['empty_folders']} (target: 0)")
        self.logger.info(f"   Total files: {stats['total_files']}")

        return stats

    def update_master_index(self) -> None:
        """Update the Master Index with new structure"""
        self.logger.info("📝 Updating Master Index")
        
        master_index_path = self.vault_path / "00 - Master Index.md"
        if master_index_path.exists():
            # Create backup of original
            backup_path = master_index_path.with_suffix(f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md")
            shutil.copy2(master_index_path, backup_path)
            
            # Update with new structure
            new_content = '''# 🎯 Master Index - Modern Meditations

## Quick Navigation Dashboard

### 🎯 Current Focus
- [ ] Check refactored structure
- [ ] Update internal links
- [ ] Verify file locations
- [ ] Test new filing system

### 🏗️ PARA System Overview (REFACTORED)

#### 1️⃣ [[1. Projects]] - Active Work
- **[[Creator Business Launch]]** - Main business initiatives  
- **[[App Development]]** - Current app projects
- **[[Content System Build]]** - Content workflows
- **[[Client Work]]** - All client projects

#### 2️⃣ [[2. Areas]] - Ongoing Responsibilities  
- **[[Personal Development]]** - Self-improvement & growth
- **[[Content & Creative]]** - Content creation & creative work
- **[[Business Operations]]** - Daily business management
- **[[Health & Training]]** - MMA, fitness & wellness
- **[[Relationships]]** - Personal connections

#### 3️⃣ [[3. Resources]] - Reference Material (SIMPLIFIED)
- **[[Business Strategy]]** - Frameworks & market research
- **[[Content Creation]]** - Templates, guides & examples
- **[[People & Network]]** - Contact profiles & notes
- **[[AI & Tech Tools]]** - AI prompts & tool configs
- **[[Learning Materials]]** - Courses, books & education
- **[[Personal Interests]]** - Gaming, music & hobbies

#### 4️⃣ [[4. Archive]] - Completed/Inactive

---

## 🛠️ System Support
- **[[0. System]]** - Vault organization & maintenance
- **[[_System Support]]** - Readwise, backups, templates

---

## 📊 Refactoring Complete
- Structure simplified: 18 → 6 resource categories
- Root files organized: 50+ files properly filed
- Areas renamed for clarity
- Projects outcome-focused
- Master Index updated

*Last refactored: {datetime.now().strftime('%Y-%m-%d %H:%M')}*
*System version: PARA Enhanced v2.0*
'''
            
            with open(master_index_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            self.logger.info("✅ Master Index updated")

    def run_refactoring(self) -> bool:
        """Execute the complete refactoring process"""
        self.logger.info("🚀 Starting Vault Refactoring Process")
        
        # Step 1: Create backup
        if not self.create_backup():
            self.logger.error("❌ Backup failed - aborting refactoring")
            return False

        try:
            # Step 2: Create new structure
            self.create_new_structure()
            
            # Step 3: Execute phases
            self.phase1_root_cleanup()
            self.phase2_resource_consolidation() 
            self.phase3_areas_projects_restructure()
            self.phase4_system_cleanup()
            
            # Step 4: Update Master Index
            self.update_master_index()
            
            # Step 5: Verify results
            stats = self.verify_refactoring()
            
            # Step 6: Final report
            self.logger.info("🎉 REFACTORING COMPLETE!")
            self.logger.info(f"✅ Backup location: {self.backup_path}")
            self.logger.info(f"📋 Log file: {self.log_file}")
            
            if stats["root_files"] <= 2 and stats["resource_categories"] == 6:
                self.logger.info("✅ SUCCESS: All targets met!")
                return True
            else:
                self.logger.warning("⚠️  Some targets not met - review log for details")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Refactoring failed: {e}")
            self.logger.info(f"🔄 Restore from backup: {self.backup_path}")
            return False

# Usage
if __name__ == "__main__":
    import sys
    
    # Vault path should be provided as argument or default
    vault_path = sys.argv[1] if len(sys.argv) > 1 else r"F:\ValentinMarketing\ModernMeditations"
    
    print("🏗️  Obsidian Vault Refactoring Script")
    print("=" * 50)
    print(f"Vault: {vault_path}")
    print(f"Plan: Based on PARA methodology")
    print("=" * 50)
    
    # Validate vault path exists
    if not Path(vault_path).exists():
        print(f"❌ Error: Vault path does not exist: {vault_path}")
        sys.exit(1)
    
    response = input("⚠️  This will reorganize your entire vault. Continue? (yes/no): ")
    if response.lower() != 'yes':
        print("❌ Aborted by user")
        sys.exit(1)
    
    try:
        refactorer = VaultRefactorer(vault_path)
        success = refactorer.run_refactoring()
    except Exception as e:
        print(f"❌ Error initializing refactorer: {e}")
        sys.exit(1)
    
    if success:
        print("\n🎉 Refactoring completed successfully!")
        print(f"📋 Check log file: {refactorer.log_file}")
        print(f"💾 Backup available: {refactorer.backup_path}")
    else:
        print("\n❌ Refactoring failed - check logs")
        print(f"🔄 Restore from: {refactorer.backup_path}") 