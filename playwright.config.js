// @ts-check
import { defineConfig } from '@playwright/test';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  // Specify test files
  testDir: './playwright',
  testMatch: '**/*.test.js',
  
  // Configure timeout
  timeout: 30000,
  
  // Configure reporters
  reporter: 'list',
  
  // Configure browsers to use
  use: {
    headless: false,
    viewport: { width: 1280, height: 720 },
    trace: 'on-first-retry',
    // Slow down for debugging
    launchOptions: {
      slowMo: 50,
    },
  },
  
  // Configure projects for different browsers
  projects: [
    {
      name: 'chromium',
      use: {
        browserName: 'chromium',
      },
    },
  ],
}); 