### **<PERSON>STAPI MIGRATION GUIDE**

**Objective:** Complete the migration from Flask to FastAPI, removing all legacy Flask components and dependencies. The final application will be a pure FastAPI service.

**Guiding Principles:**

*   **Dependency Injection:** Replace all uses of Flask's global context objects (`g`, `current_app`) with FastAPI's dependency injection system.
*   **Pydantic:** Use Pydantic for configuration management and data validation.
*   **APIRouters:** Convert all Flask Blueprints to FastAPI `APIRouter`s.
*   **Cleanup:** Thoroughly remove all obsolete code and dependencies.

---

### **Phase 1: Establish Core FastAPI Services**

This phase replaces Flask's application factory and global objects with a robust, dependency-injection-based system for configuration and services like the Supabase client.

**Step 1.1: Create Pydantic Configuration**

1.  **Create `app/config.py`:** This file will house all application settings, loaded from environment variables.

    ```python
    # app/config.py
    from pydantic_settings import BaseSettings

    class Settings(BaseSettings):
        """
        Application settings managed by Pydantic.
        Loads from .env file by default.
        """
        # Supabase
        SUPABASE_URL: str
        SUPABASE_KEY: str
        SUPABASE_JWT_SECRET: str

        # OpenAI
        OPENAI_API_KEY: str

        # Caching
        CACHE_DEFAULT_TTL: int = 300
        CACHE_DEFAULT_MAX_SIZE: int = 128

        class Config:
            env_file = ".env"
            env_file_encoding = "utf-8"

    settings = Settings()
    ```

**Step 1.2: Create Core Dependencies**

1.  **Create `app/dependencies.py`:** This file will define the dependency provider functions.

    ```python
    # app/dependencies.py
    from functools import lru_cache
    from supabase import create_client, Client
    from app.config import Settings, settings

    @lru_cache()
    def get_settings() -> Settings:
        """
        Returns the application settings instance.
        Uses lru_cache to ensure settings are loaded only once.
        """
        return settings

    def get_supabase(settings: Settings = Depends(get_settings)) -> Client:
        """
        Dependency to get a Supabase client instance.
        """
        return create_client(settings.SUPABASE_URL, settings.SUPABASE_KEY)
    ```

**Step 1.3: Refactor AI Assistant Configuration**

1.  **Update `app/ai_assistant/config.py`:** Modify functions to accept a `Settings` object instead of calling `current_app`.

    *   **Before:**
        ```python
        from flask import current_app
        # ...
        def get_api_key():
            return current_app.config.get("OPENAI_API_KEY")
        ```

    *   **After (Example):**
        ```python
        from app.config import Settings
        # ...
        def get_api_key(settings: Settings):
            return settings.OPENAI_API_KEY
        ```

    *   **Action:** Apply this pattern to all functions in `app/ai_assistant/config.py` that rely on `current_app`.

---

### **Phase 2: Migrate Routers and Authentication**

This phase involves converting all Flask Blueprints to FastAPI `APIRouter`s and replacing Flask-specific logic within the routes.

**Step 2.1: Convert a Blueprint to an APIRouter (Example: `activity`)**

1.  **Analyze `app/activity/routes.py`:** Note the use of `g`, `request`, and `jsonify`.
2.  **Create `app/routers/activity.py` (if not present) or refactor it:**

    ```python
    # app/routers/activity.py
    from fastapi import APIRouter, Depends, HTTPException
    from supabase import Client
    from app.dependencies import get_supabase
    # Import your repository and schemas

    router = APIRouter()

    @router.post("/log")
    async def log_activity(
        activity_data: dict, # Replace with a Pydantic model
        supabase: Client = Depends(get_supabase)
    ):
        # Your repository logic here
        # Example: activity_log_repo = ActivityLogRepository(supabase)
        #          activity_log_repo.log_activity(...)
        return {"status": "success"}
    ```

**Step 2.2: Migrate Authentication**

1.  **Choose a JWT library:** Since `flask_jwt_extended` will be removed, use `python-jose` (already in `requirements.txt`).
2.  **Create `app/utils/auth_utils.py`:** This will contain JWT creation and validation logic.

    ```python
    # app/utils/auth_utils.py
    from fastapi import Depends, HTTPException, status
    from fastapi.security import OAuth2PasswordBearer
    from jose import JWTError, jwt
    from pydantic import BaseModel
    from app.config import Settings
    from app.dependencies import get_settings

    oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/token")

    class TokenData(BaseModel):
        username: str | None = None

    def create_access_token(data: dict, settings: Settings):
        to_encode = data.copy()
        # Add expiration, etc.
        encoded_jwt = jwt.encode(to_encode, settings.SUPABASE_JWT_SECRET, algorithm="HS256")
        return encoded_jwt

    async def get_current_user(token: str = Depends(oauth2_scheme), settings: Settings = Depends(get_settings)):
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
        try:
            payload = jwt.decode(token, settings.SUPABASE_JWT_SECRET, algorithms=["HS256"])
            username: str = payload.get("sub")
            if username is None:
                raise credentials_exception
            token_data = TokenData(username=username)
        except JWTError:
            raise credentials_exception
        # You can fetch the user from the database here if needed
        return token_data
    ```

3.  **Apply to routes:** Secure your endpoints using `Depends(get_current_user)`.

**Step 2.3: Systematically Convert All Remaining Blueprints**

*   **Action:** Repeat the process from Step 2.1 for all modules in `app/` that contain a `routes.py` file (`business`, `content`, `feedback`, `auth`, `ai_assistant`, etc.).
    *   Create a corresponding file in `app/routers/`.
    *   Convert the blueprint to an `APIRouter`.
    *   Refactor all routes to use FastAPI syntax and dependency injection.
    *   Use Pydantic models for request bodies and responses for automatic validation and documentation.

---

### **Phase 3: Finalize Application and Clean Up**

This phase brings everything together in the main application file and removes all obsolete Flask dependencies.

**Step 3.1: Rewrite the Main Application Entrypoint**

1.  **Update `main.py`:** Replace the Flask app factory with a FastAPI instance and include the new routers.

    ```python
    # main.py
    from fastapi import FastAPI
    from app.routers import health, activity, business, chat # ... and all other routers
    from app.config import settings
    from fastapi.middleware.cors import CORSMiddleware

    app = FastAPI(title="Writer v2 API")

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"], # Or specify your frontend URL
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include all routers
    app.include_router(health.router, prefix="/api/health", tags=["Health"])
    app.include_router(activity.router, prefix="/api/activity", tags=["Activity"])
    app.include_router(business.router, prefix="/api/business", tags=["Business"])
    app.include_router(chat.router, prefix="/api/chat", tags=["Chat"])
    # ... include all other routers here

    @app.get("/")
    def read_root():
        return {"message": "Welcome to the Writer v2 API"}
    ```

**Step 3.2: Clean Up Dependencies**

1.  **Edit `requirements.txt`:** Remove all Flask-related packages.
    *   **Remove:** `Flask`, `Flask-Cors`, `flask_jwt_extended`.
2.  **Run in terminal:** Rebuild the backend container to install the updated dependencies.

    ```bash
    docker-compose build backend
    docker-compose up -d backend
    ```

**Step 3.3: Delete Obsolete Files**

1.  **Delete `app/__init__.py`:** This file is part of the Flask application factory pattern and is no longer needed.
2.  **Delete old Flask route files:** Once a module's routes have been migrated to `app/routers/`, delete the original `app/<module_name>/routes.py` file.
3.  **Delete the `backup` directory:** This contains old Flask code and should be removed.

---

By following this guide, the AI will systematically refactor the codebase, resulting in a clean, modern, and performant FastAPI application. 