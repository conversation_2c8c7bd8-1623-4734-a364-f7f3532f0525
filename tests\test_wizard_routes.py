# tests/test_wizard_routes.py
import pytest
import json
from uuid import uuid4, UUID
from unittest.mock import patch, MagicMock
from flask import g # Import g
from datetime import datetime

# Assuming your Flask app instance is created via a factory `create_app`
# and you have fixtures like `client` and `auth_headers` set up in conftest.py
# If not, these tests will need adjustment based on your testing setup.

# Mock data
MOCK_USER_ID = UUID('efea4187-dc5c-4672-b72f-54d7d44c5a43') # Example UUID
MOCK_SESSION_ID = UUID('8194db0a-1190-42f0-86ad-62bf5ebe29b1') # Example UUID

MOCK_AI_RESPONSE_BRAINSTORM = {
    "generated_text": json.dumps([
        {"title": "AI Title 1", "description": "Desc 1"},
        {"title": "AI Title 2", "description": "Desc 2"},
        {"title": "AI Title 3", "description": "Desc 3"},
        {"title": "AI Title 4", "description": "Desc 4"},
        {"title": "AI Title 5", "description": "Desc 5"}
    ]),
    "model": "mock-model"
}
MOCK_SESSION_WITH_BRAINSTORM = {
    "id": MOCK_SESSION_ID,
    "user_id": MOCK_USER_ID,
    "topic": "Test Topic",
    "audience": "Test Audience",
    "goal": "Test Goal",
    "brainstorm_results": json.loads(MOCK_AI_RESPONSE_BRAINSTORM["generated_text"]),
    "created_at": datetime.now(),
    "updated_at": datetime.now()
}

MOCK_AI_RESPONSE_HOOK = {
    "generated_text": json.dumps([
        "Hook 1: Did you know...?",
        "Hook 2: The secret to...",
        "Hook 3: Stop scrolling if..."
    ]),
    "model": "mock-model"
}
MOCK_SESSION_WITH_HOOK = {
    **MOCK_SESSION_WITH_BRAINSTORM,
    "chosen_title_index": 1,
    "hook_results": json.loads(MOCK_AI_RESPONSE_HOOK["generated_text"])
}

MOCK_AI_RESPONSE_INTRO = {
    "generated_text": json.dumps([
        "Intro 1: Hook + Setup + CTA",
        "Intro 2: Different Hook + Setup + CTA",
        "Intro 3: Yet another Hook + Setup + CTA"
    ]),
    "model": "mock-model"
}
# Represents session state after successful intro stage
MOCK_SESSION_WITH_INTRO = {
    **MOCK_SESSION_WITH_HOOK,
    "chosen_hook_index": 0,
    "intro_cta_results": json.loads(MOCK_AI_RESPONSE_INTRO["generated_text"])
}

MOCK_AI_RESPONSE_OUTLINE = {
    "generated_text": json.dumps([
        {
            "sections": [
                {"heading": "Section 1A", "bullets": ["Point A1", "Point A2"]},
                {"heading": "Section 2A", "bullets": ["Point B1", "Point B2"]}
            ]
        },
        {
            "sections": [
                {"heading": "Section 1B", "bullets": ["Step 1", "Step 2", "Step 3"]},
                {"heading": "Section 2B", "bullets": ["Conclusion B"]}
            ]
        },
        {
            "sections": [
                {"heading": "Intro C", "bullets": ["Hook C1"]},
                {"heading": "Body C", "bullets": ["Detail C1", "Detail C2"]},
                {"heading": "Outro C", "bullets": ["CTA C1"]}
            ]
        }
    ]),
    "model": "mock-model"
}
# Represents session state after successful outline stage
MOCK_SESSION_WITH_OUTLINE = {
    **MOCK_SESSION_WITH_INTRO,
    "chosen_intro_index": 0,
    "outline_results": json.loads(MOCK_AI_RESPONSE_OUTLINE["generated_text"])
    # Note: outline_results are now expected to be a list of structured dicts
}

MOCK_AI_RESPONSE_DRAFT = {
    "generated_text": "This is the full generated draft text. It includes sections based on the outline.",
    "model": "mock-model"
}
# Represents session state after successful draft stage
MOCK_SESSION_WITH_DRAFT = {
    **MOCK_SESSION_WITH_OUTLINE,
    "chosen_outline_index": 0,
    "draft_text": MOCK_AI_RESPONSE_DRAFT["generated_text"]
}

MOCK_AI_RESPONSE_EDIT = {
    "generated_text": "This is the FINAL edited script text, perhaps funnier or shorter.",
    "model": "mock-model"
}
# Represents session state after successful edit stage (final state for this flow)
MOCK_SESSION_WITH_EDIT = {
    **MOCK_SESSION_WITH_DRAFT,
    "edited_text": MOCK_AI_RESPONSE_EDIT["generated_text"]
}

@pytest.fixture(autouse=True)
def mock_auth():
    """Mock authentication for all tests in this module."""
    # This mock ensures the @supabase_auth_required decorator allows access
    # and attempts to set g.user_id based on the 'sub' field.
    with patch('app.utils.supabase_auth.verify_supabase_token', return_value={'sub': str(MOCK_USER_ID), 'user_metadata': {'full_name': 'Test User'}}):
        yield

@pytest.fixture
def mock_ai_generator():
    """Fixture for a mocked AI generator instance."""
    mock_gen = MagicMock()
    mock_gen.generate_content.return_value = MOCK_AI_RESPONSE_BRAINSTORM
    return mock_gen

@pytest.fixture
def mock_wizard_repo():
    """Fixture for a mocked WizardSessionRepository instance."""
    mock_repo = MagicMock()
    mock_repo.create_session.return_value = MOCK_SESSION_WITH_BRAINSTORM

    # Store the intended return value for update_stage
    update_return_value = {**MOCK_SESSION_WITH_BRAINSTORM, "brainstorm_results": json.loads(MOCK_AI_RESPONSE_BRAINSTORM["generated_text"])}

    # Define a side effect function to print args and return value (for debugging)
    def update_stage_side_effect(*args, **kwargs):
        print(f"DEBUG: mock_wizard_repo.update_stage called with:")
        print(f"  args: {args}")
        print(f"  kwargs: {kwargs}")
        # Check if user_id is None and raise error if so, mimicking DB constraint
        if kwargs.get('user_id') is None:
             print("ERROR SIM: user_id is None in update_stage call!")
             # Simulate a potential DB error or simply don't return expected data
             # raise ValueError("user_id cannot be null") # Option 1: Raise
             return None # Option 2: Return None
        return update_return_value

    # Assign the side effect (keep only if debugging needed, otherwise just set return_value)
    # mock_repo.update_stage.side_effect = update_stage_side_effect
    mock_repo.update_stage.return_value = update_return_value # Use simple return_value normally

    # Configure get_session and other methods used by tests
    mock_repo.get_session.return_value = None # Default to not found
    return mock_repo

# --- Common configure_g function for patched tests ---
def configure_g_for_mock_repo(mock_repo):
    """Sets mock wizard repo and ensures g.user_id is set."""
    print(f"DEBUG: configure_g_for_mock_repo setting g.wizard_session_repository = {mock_repo}")
    g.wizard_session_repository = mock_repo
    # Explicitly set g.user_id, simulating what the auth decorator does.
    # This ensures the route handler gets the correct user ID.
    if not hasattr(g, 'user_id') or g.user_id is None:
        print(f"DEBUG: configure_g_for_mock_repo setting g.user_id = {MOCK_USER_ID}")
        g.user_id = MOCK_USER_ID
    else:
        print(f"DEBUG: configure_g_for_mock_repo found existing g.user_id = {g.user_id}")
    # Set other repositories to None or mock them if needed by the specific test
    g.user_repository = getattr(g, 'user_repository', None)
    g.activity_log_repository = getattr(g, 'activity_log_repository', None)
    g.content_item_repository = getattr(g, 'content_item_repository', None)
    g.business_context_repository = getattr(g, 'business_context_repository', None)
    g.chat_session_repository = getattr(g, 'chat_session_repository', None)
    g.feedback_repository = getattr(g, 'feedback_repository', None)
    g.video_data_repository = getattr(g, 'video_data_repository', None)

# --- Tests for /ai/wizard/brainstorm ---
# Keep using direct g patch for brainstorm tests for now, assuming they work.

def test_brainstorm_missing_fields(client, auth_headers):
    """Test brainstorm endpoint returns 400 if fields are missing."""
    response = client.post('/ai/wizard/brainstorm', headers=auth_headers, json={})
    assert response.status_code == 400
    assert b"Missing required field: topic" in response.data or b"Missing required field: target_audience" in response.data

    response = client.post('/ai/wizard/brainstorm', headers=auth_headers, json={"topic": "Test"})
    assert response.status_code == 400
    assert b"Missing required field: audience" in response.data

def test_brainstorm_unauthenticated(client):
    """Test brainstorm endpoint returns 401 if not authenticated."""
    response = client.post('/ai/wizard/brainstorm', json={"topic": "Test", "target_audience": "Test"})
    assert response.status_code == 401

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_brainstorm_success(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test successful brainstorm request."""
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_BRAINSTORM
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.create_session.return_value = {'id': MOCK_SESSION_ID}
    updated_session_data = {
        "id": MOCK_SESSION_ID,
        "user_id": MOCK_USER_ID,
        "topic": "Test Topic",
        "audience": "Test Audience",
        "goal": "Test Goal",
        "brainstorm_results": json.loads(MOCK_AI_RESPONSE_BRAINSTORM["generated_text"])
    }
    mock_wizard_repo.update_stage.return_value = updated_session_data

    payload = {
        "topic": "Test Topic",
        "audience": "Test Audience",
        "goal": "Test Goal",
    }

    with client.application.app_context():
        response = client.post('/ai/wizard/brainstorm', headers=auth_headers, json=payload)

        assert response.status_code == 200
        data = response.get_json()
        assert "session_id" in data
        assert data["session_id"] == str(MOCK_SESSION_ID)
        assert "titles" in data
        assert isinstance(data["titles"], list)
        assert len(data["titles"]) == 5
        assert data["titles"][0] == {"title": "AI Title 1", "description": "Desc 1"}

        mock_wizard_repo.create_session.assert_called_once_with(
            user_id=MOCK_USER_ID,
            topic="Test Topic",
            audience="Test Audience",
            goal="Test Goal"
        )
        mock_get_ai_generator.assert_called_once()
        mock_ai_generator.generate_content.assert_called_once()
        mock_wizard_repo.update_stage.assert_called_once_with(
            session_id=MOCK_SESSION_ID,
            user_id=MOCK_USER_ID,
            stage_data={
                "brainstorm_results": json.loads(MOCK_AI_RESPONSE_BRAINSTORM["generated_text"])
            }
        )

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_brainstorm_ai_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_wizard_repo):
    """Test handling of AI generation error during brainstorm."""
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)
    mock_ai_gen_error = MagicMock()
    mock_ai_gen_error.generate_content.return_value = {"error": True, "error_message": "AI failed"}
    mock_get_ai_generator.return_value = mock_ai_gen_error
    mock_wizard_repo.create_session.return_value = {'id': MOCK_SESSION_ID}

    payload = {
        "topic": "Test Topic",
        "audience": "Test Audience",
        "goal": "Test Goal",
    }

    with client.application.app_context():
        response = client.post('/ai/wizard/brainstorm', headers=auth_headers, json=payload)

        assert response.status_code == 500
        assert b"AI generation failed" in response.data
        mock_wizard_repo.create_session.assert_called_once()
        mock_wizard_repo.update_stage.assert_not_called()

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_brainstorm_db_update_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test handling of database update error during brainstorm."""
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_BRAINSTORM
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.create_session.return_value = {'id': MOCK_SESSION_ID}
    mock_wizard_repo.update_stage.side_effect = Exception("DB update failed")

    payload = {
        "topic": "Test Topic",
        "audience": "Test Audience",
        "goal": "Test Goal",
    }

    with client.application.app_context():
        response = client.post('/ai/wizard/brainstorm', headers=auth_headers, json=payload)

        assert response.status_code == 500
        assert b"Failed to update wizard session" in response.data
        mock_wizard_repo.create_session.assert_called_once()
        mock_ai_generator.generate_content.assert_called_once()
        mock_wizard_repo.update_stage.assert_called_once()

# --- Tests for /ai/wizard/hook --- # Apply new patching strategy

def test_hook_missing_fields(client, auth_headers):
    """Test hook endpoint returns 400 if fields are missing."""
    response = client.post('/ai/wizard/hook', headers=auth_headers, json={})
    assert response.status_code == 400
    assert b"Missing required field: session_id" in response.data or b"Missing required field: chosen_title_index" in response.data

    response = client.post('/ai/wizard/hook', headers=auth_headers, json={"session_id": str(MOCK_SESSION_ID)})
    assert response.status_code == 400
    assert b"Missing required field: chosen_title_index" in response.data

def test_hook_unauthenticated(client):
    """Test hook endpoint returns 401 if not authenticated."""
    response = client.post('/ai/wizard/hook', json={"session_id": "some-id", "chosen_title_index": 0})
    assert response.status_code == 401

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_hook_invalid_session(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test hook endpoint returns 404 if session_id is invalid or doesn't belong to user."""
    mock_wizard_repo.get_session.return_value = None
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/hook', headers=auth_headers, json={
        "session_id": str(uuid4()),
        "chosen_title_index": 0
    })
    assert response.status_code == 404
    assert response.get_json() == {"error": "Wizard session not found"}
    mock_wizard_repo.get_session.assert_called_once()

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_hook_invalid_index(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test hook endpoint returns 400 if chosen_title_index is out of bounds."""
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_BRAINSTORM
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    # Index too high
    response = client.post('/ai/wizard/hook', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_title_index": 10
    })
    assert response.status_code == 400
    assert b"Invalid chosen_title_index" in response.data

    # Reset mock for next call within the same test
    mock_wizard_repo.reset_mock()
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_BRAINSTORM

    # Index negative
    response = client.post('/ai/wizard/hook', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_title_index": -1
    })
    assert response.status_code == 400
    assert b"Invalid chosen_title_index" in response.data

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_hook_success(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test successful hook request."""
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_HOOK
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_BRAINSTORM
    updated_session_data = {
        **MOCK_SESSION_WITH_BRAINSTORM,
        "chosen_title_index": 1,
        "hook_results": json.loads(MOCK_AI_RESPONSE_HOOK["generated_text"])
    }
    mock_wizard_repo.update_stage.return_value = updated_session_data
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    chosen_index = 1
    response = client.post('/ai/wizard/hook', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_title_index": chosen_index
    })

    assert response.status_code == 200
    data = response.get_json()
    assert "hooks" in data
    assert isinstance(data["hooks"], list)
    assert len(data["hooks"]) == 3
    assert data["hooks"][0] == "Hook 1: Did you know...?"

    mock_wizard_repo.get_session.assert_called_once_with(session_id=MOCK_SESSION_ID, user_id=MOCK_USER_ID)
    mock_get_ai_generator.assert_called_once()
    mock_ai_generator.generate_content.assert_called_once()
    call_args, call_kwargs = mock_ai_generator.generate_content.call_args
    expected_title = MOCK_SESSION_WITH_BRAINSTORM["brainstorm_results"][chosen_index]["title"]
    assert expected_title in call_kwargs["prompt"]

    mock_wizard_repo.update_stage.assert_called_once_with(
        session_id=MOCK_SESSION_ID,
        user_id=MOCK_USER_ID,
        stage_data={
            "chosen_title_index": chosen_index,
            "hook_results": json.loads(MOCK_AI_RESPONSE_HOOK["generated_text"])
        }
    )

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_hook_ai_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_wizard_repo):
    """Test handling of AI generation error during hook stage."""
    mock_ai_gen_error = MagicMock()
    mock_ai_gen_error.generate_content.return_value = {"error": True, "error_message": "Hook AI failed"}
    mock_get_ai_generator.return_value = mock_ai_gen_error
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_BRAINSTORM
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/hook', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_title_index": 0
    })

    assert response.status_code == 500
    # The error message check needs refinement based on the actual route's error handling
    # assert b"Hook AI failed" in response.data # This might be too specific
    assert b"AI generation failed" in response.data

    mock_wizard_repo.get_session.assert_called_once()
    mock_wizard_repo.update_stage.assert_not_called()

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_hook_db_update_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test handling of database update error during hook stage."""
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_HOOK
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_BRAINSTORM
    mock_wizard_repo.update_stage.side_effect = Exception("DB update failed - hook")
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/hook', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_title_index": 0
    })

    assert response.status_code == 500
    assert b"Failed to update wizard session" in response.data
    mock_wizard_repo.get_session.assert_called_once()
    mock_ai_generator.generate_content.assert_called_once()
    mock_wizard_repo.update_stage.assert_called_once()

# --- Tests for /ai/wizard/intro --- # Apply new patching strategy

def test_intro_missing_fields(client, auth_headers):
    """Test intro endpoint returns 400 if fields are missing."""
    response = client.post('/ai/wizard/intro', headers=auth_headers, json={})
    assert response.status_code == 400
    assert b"Missing required field: session_id" in response.data or b"Missing required field: chosen_hook_index" in response.data

    response = client.post('/ai/wizard/intro', headers=auth_headers, json={"session_id": str(MOCK_SESSION_ID)})
    assert response.status_code == 400
    assert b"Missing required field: chosen_hook_index" in response.data

def test_intro_unauthenticated(client):
    """Test intro endpoint returns 401 if not authenticated."""
    response = client.post('/ai/wizard/intro', json={"session_id": "some-id", "chosen_hook_index": 0})
    assert response.status_code == 401

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_intro_invalid_session(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test intro endpoint returns 404 if session_id is invalid or doesn't belong to user."""
    mock_wizard_repo.get_session.return_value = None
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/intro', headers=auth_headers, json={
        "session_id": str(uuid4()),
        "chosen_hook_index": 0
    })
    assert response.status_code == 404
    assert response.get_json() == {"error": "Wizard session not found"}
    mock_wizard_repo.get_session.assert_called_once()

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_intro_missing_hooks(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test intro endpoint returns 400 if session is missing hook_options."""
    session_without_hooks = MOCK_SESSION_WITH_BRAINSTORM.copy()
    session_without_hooks.pop("hook_results", None)
    mock_wizard_repo.get_session.return_value = session_without_hooks
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/intro', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_hook_index": 0
    })
    assert response.status_code == 400
    assert response.get_json() == {"error": "Previous stage (hook) data is missing or invalid"}
    mock_wizard_repo.get_session.assert_called_once()

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_intro_invalid_index(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test intro endpoint returns 400 if chosen_hook_index is out of bounds."""
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_HOOK
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    # Use the correct key from the global MOCK_SESSION_WITH_HOOK
    invalid_index = len(MOCK_SESSION_WITH_HOOK["hook_results"])

    response = client.post('/ai/wizard/intro', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_hook_index": invalid_index
    })
    assert response.status_code == 400
    # Update assertion to match the actual JSON error response structure
    # The route now returns a specific error message with bounds
    expected_error_msg = f"Invalid chosen_hook_index (must be between 0 and {invalid_index - 1})"
    assert response.get_json() == {"error": expected_error_msg}
    mock_wizard_repo.get_session.assert_called_once()

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_intro_success(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test successful intro request."""
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_INTRO
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_HOOK
    updated_session_data = {
        **MOCK_SESSION_WITH_HOOK,
        "chosen_hook_index": 0,
        "intro_cta_results": json.loads(MOCK_AI_RESPONSE_INTRO["generated_text"])
    }
    mock_wizard_repo.update_stage.return_value = updated_session_data
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    chosen_index = 0
    response = client.post('/ai/wizard/intro', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_hook_index": chosen_index
    })

    assert response.status_code == 200
    data = response.get_json()
    assert "intro_ctas" in data
    assert isinstance(data["intro_ctas"], list)
    assert len(data["intro_ctas"]) == 3
    assert data["intro_ctas"][0] == "Intro 1: Hook + Setup + CTA"

    mock_wizard_repo.get_session.assert_called_once_with(session_id=MOCK_SESSION_ID, user_id=MOCK_USER_ID)
    mock_get_ai_generator.assert_called_once()
    mock_ai_generator.generate_content.assert_called_once()
    call_args, call_kwargs = mock_ai_generator.generate_content.call_args
    assert "prompt" in call_kwargs
    assert MOCK_SESSION_WITH_HOOK["hook_results"][chosen_index] in call_kwargs["prompt"]

    mock_wizard_repo.update_stage.assert_called_once_with(
        session_id=MOCK_SESSION_ID,
        user_id=MOCK_USER_ID,
        stage_data={
            "chosen_hook_index": chosen_index,
            "intro_cta_results": json.loads(MOCK_AI_RESPONSE_INTRO["generated_text"])
        }
    )

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_intro_ai_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_wizard_repo):
    """Test handling of AI generation error during intro."""
    mock_ai_gen_error = MagicMock()
    mock_ai_gen_error.generate_content.return_value = {"error": True, "error_message": "AI failed intro"}
    mock_get_ai_generator.return_value = mock_ai_gen_error
    # Set side_effect to configure g AND set mock return value just before route runs
    mock_init_repos.side_effect = lambda: (configure_g_for_mock_repo(mock_wizard_repo), setattr(mock_wizard_repo.get_session, 'return_value', MOCK_SESSION_WITH_HOOK))

    response = client.post('/ai/wizard/intro', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_hook_index": 0
    })

    assert response.status_code == 500
    assert b"AI generation failed" in response.data
    mock_wizard_repo.get_session.assert_called_once()
    mock_get_ai_generator.assert_called_once()
    mock_ai_gen_error.generate_content.assert_called_once()
    mock_wizard_repo.update_stage.assert_not_called()

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_intro_db_update_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test handling of database update error during intro."""
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_INTRO
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.update_stage.side_effect = Exception("DB intro update failed")
    # Set side_effect to configure g AND set mock return value just before route runs
    mock_init_repos.side_effect = lambda: (configure_g_for_mock_repo(mock_wizard_repo), setattr(mock_wizard_repo.get_session, 'return_value', MOCK_SESSION_WITH_HOOK))

    response = client.post('/ai/wizard/intro', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_hook_index": 0
    })

    assert response.status_code == 500
    # Update assertion to match the actual JSON error from the generic exception handler
    assert response.get_json() == {"error": "An unexpected error occurred during the intro stage."}
    mock_wizard_repo.get_session.assert_called_once()
    mock_get_ai_generator.assert_called_once()
    mock_ai_generator.generate_content.assert_called_once()
    mock_wizard_repo.update_stage.assert_called_once()

# --- Tests for /ai/wizard/outline --- # Apply new patching strategy

def test_outline_missing_fields(client, auth_headers):
    """Test outline endpoint returns 400 if fields are missing."""
    response = client.post('/ai/wizard/outline', headers=auth_headers, json={})
    assert response.status_code == 400
    assert b"Missing required field: session_id" in response.data or b"Missing required field: chosen_intro_index" in response.data

    response = client.post('/ai/wizard/outline', headers=auth_headers, json={"session_id": str(MOCK_SESSION_ID)})
    assert response.status_code == 400
    assert b"Missing required field: chosen_intro_index" in response.data

def test_outline_unauthenticated(client):
    """Test outline endpoint returns 401 if not authenticated."""
    response = client.post('/ai/wizard/outline', json={"session_id": "some-id", "chosen_intro_index": 0})
    assert response.status_code == 401

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_outline_invalid_session(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test outline endpoint returns 404 if session_id is invalid or doesn't belong to user."""
    mock_wizard_repo.get_session.return_value = None
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/outline', headers=auth_headers, json={
        "session_id": str(uuid4()),
        "chosen_intro_index": 0
    })
    assert response.status_code == 404
    # Update assertion to match the actual JSON error response structure
    assert response.get_json() == {"error": "Wizard session not found"}
    mock_wizard_repo.get_session.assert_called_once()

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_outline_missing_intros(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test outline endpoint returns 400 if session is missing intro_options."""
    session_without_intros = MOCK_SESSION_WITH_HOOK.copy()
    session_without_intros.pop("intro_options", None)
    mock_wizard_repo.get_session.return_value = session_without_intros
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/outline', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_intro_index": 0
    })
    assert response.status_code == 400
    assert response.get_json() == {"error": "Previous stage (intro) data is missing or invalid"}
    mock_wizard_repo.get_session.assert_called_once()

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_outline_invalid_index(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test outline endpoint returns 400 if chosen_intro_index is out of bounds."""
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_INTRO
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    invalid_index = len(MOCK_SESSION_WITH_INTRO["intro_cta_results"])

    response = client.post('/ai/wizard/outline', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_intro_index": invalid_index
    })
    assert response.status_code == 400
    # Update assertion to match the actual JSON error response structure
    expected_error_msg = f"Invalid chosen_intro_index (must be between 0 and {invalid_index - 1})"
    assert response.get_json() == {"error": expected_error_msg}
    mock_wizard_repo.get_session.assert_called_once()

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_outline_success(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test successful outline request."""
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_OUTLINE
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_INTRO
    updated_session_data = {
        **MOCK_SESSION_WITH_INTRO,
        "chosen_intro_index": 0,
        "outline_results": json.loads(MOCK_AI_RESPONSE_OUTLINE["generated_text"])
    }
    mock_wizard_repo.update_stage.return_value = updated_session_data
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    chosen_index = 0
    response = client.post('/ai/wizard/outline', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_intro_index": chosen_index
    })

    assert response.status_code == 200
    data = response.get_json()
    assert "outlines" in data
    assert isinstance(data["outlines"], list)
    assert len(data["outlines"]) == 3
    assert data["outlines"][0] == json.loads(MOCK_AI_RESPONSE_OUTLINE["generated_text"])[0]

    mock_wizard_repo.get_session.assert_called_once_with(session_id=MOCK_SESSION_ID, user_id=MOCK_USER_ID)
    mock_get_ai_generator.assert_called_once()
    mock_ai_generator.generate_content.assert_called_once()
    call_args, call_kwargs = mock_ai_generator.generate_content.call_args
    assert "prompt" in call_kwargs
    assert MOCK_SESSION_WITH_INTRO["intro_cta_results"][chosen_index] in call_kwargs["prompt"]

    mock_wizard_repo.update_stage.assert_called_once_with(
        session_id=MOCK_SESSION_ID,
        user_id=MOCK_USER_ID,
        stage_data={
            "chosen_intro_index": chosen_index,
            "outline_results": json.loads(MOCK_AI_RESPONSE_OUTLINE["generated_text"])
        }
    )

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_outline_ai_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_wizard_repo):
    """Test handling of AI generation error during outline."""
    mock_ai_gen_error = MagicMock()
    mock_ai_gen_error.generate_content.return_value = {"error": True, "error_message": "AI failed outline"}
    mock_get_ai_generator.return_value = mock_ai_gen_error
    # Set side_effect to configure g AND set mock return value just before route runs
    mock_init_repos.side_effect = lambda: (configure_g_for_mock_repo(mock_wizard_repo), setattr(mock_wizard_repo.get_session, 'return_value', MOCK_SESSION_WITH_INTRO))

    response = client.post('/ai/wizard/outline', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_intro_index": 0
    })

    assert response.status_code == 500
    assert b"AI generation failed" in response.data
    mock_wizard_repo.get_session.assert_called_once()
    mock_get_ai_generator.assert_called_once()
    mock_ai_gen_error.generate_content.assert_called_once()
    mock_wizard_repo.update_stage.assert_not_called()

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_outline_db_update_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test handling of database update error during outline."""
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_OUTLINE
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.update_stage.side_effect = Exception("DB outline update failed")
    # Set side_effect to configure g AND set mock return value just before route runs
    mock_init_repos.side_effect = lambda: (configure_g_for_mock_repo(mock_wizard_repo), setattr(mock_wizard_repo.get_session, 'return_value', MOCK_SESSION_WITH_INTRO))

    response = client.post('/ai/wizard/outline', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_intro_index": 0
    })

    assert response.status_code == 500
    # Update assertion to match the actual JSON error from the generic exception handler
    assert response.get_json() == {"error": "An unexpected error occurred during the outline stage."}
    mock_wizard_repo.get_session.assert_called_once()
    mock_get_ai_generator.assert_called_once()
    mock_ai_generator.generate_content.assert_called_once()
    mock_wizard_repo.update_stage.assert_called_once()


# --- Tests for /ai/wizard/draft ---

def test_draft_missing_fields(client, auth_headers):
    """Test draft endpoint returns 400 if fields are missing."""
    response = client.post('/ai/wizard/draft', headers=auth_headers, json={})
    assert response.status_code == 400
    assert b"Missing required field" in response.data

    response = client.post('/ai/wizard/draft', headers=auth_headers, json={"session_id": str(MOCK_SESSION_ID)})
    assert response.status_code == 400
    assert b"Missing required field: chosen_outline_index" in response.data

def test_draft_unauthenticated(client):
    """Test draft endpoint returns 401 if not authenticated."""
    response = client.post('/ai/wizard/draft', json={"session_id": "some-id", "chosen_outline_index": 0})
    assert response.status_code == 401

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_draft_invalid_session(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test draft endpoint returns 404 if session_id is invalid or doesn't belong to user."""
    mock_wizard_repo.get_session.return_value = None
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/draft', headers=auth_headers, json={
        "session_id": str(uuid4()),
        "chosen_outline_index": 0
    })
    assert response.status_code == 404
    assert response.get_json() == {"error": "Wizard session not found"}
    mock_wizard_repo.get_session.assert_called_once()

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_draft_missing_outline_results(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test draft endpoint returns 400 if outline_results are missing."""
    mock_session_no_outline = MOCK_SESSION_WITH_INTRO.copy() # Simulate session state before outline
    mock_session_no_outline.pop("outline_results", None) # Ensure it's missing
    mock_wizard_repo.get_session.return_value = mock_session_no_outline
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/draft', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_outline_index": 0
    })
    assert response.status_code == 400
    assert b"Previous stage (outline) data is missing or invalid" in response.data

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_draft_invalid_index(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test draft endpoint returns 400 if chosen_outline_index is out of bounds."""
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_OUTLINE # Assumes this mock exists
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/draft', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_outline_index": 99 # Index too high
    })
    assert response.status_code == 400
    assert b"Invalid chosen_outline_index" in response.data

@patch('app.ai_assistant.wizard_routes.draft_prompt') # Patch the specific prompt function
@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_draft_success(mock_init_repos, mock_get_ai_generator, mock_draft_prompt, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test successful draft request."""
    # Mock setup
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_DRAFT # Assumes this mock exists
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_OUTLINE # Assumes this mock exists
    mock_draft_prompt.return_value = "Mock draft prompt"
    updated_session_data = {
        **MOCK_SESSION_WITH_OUTLINE,
        "chosen_outline_index": 0,
        "draft_text": MOCK_AI_RESPONSE_DRAFT["generated_text"]
    }
    mock_wizard_repo.update_stage.return_value = updated_session_data
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    chosen_index = 0
    response = client.post('/ai/wizard/draft', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_outline_index": chosen_index
    })

    # Assertions
    assert response.status_code == 200
    data = response.get_json()
    assert "draft" in data
    assert data["draft"] == MOCK_AI_RESPONSE_DRAFT["generated_text"]

    mock_wizard_repo.get_session.assert_called_once_with(session_id=MOCK_SESSION_ID, user_id=MOCK_USER_ID)

    # Assert chosen_title was correctly retrieved and passed
    expected_title = MOCK_SESSION_WITH_OUTLINE["brainstorm_results"][MOCK_SESSION_WITH_OUTLINE["chosen_title_index"]]["title"]
    expected_outline = MOCK_SESSION_WITH_OUTLINE["outline_results"][chosen_index]
    mock_draft_prompt.assert_called_once_with(outline=expected_outline, chosen_title=expected_title)

    mock_get_ai_generator.assert_called_once()
    mock_ai_generator.generate_content.assert_called_once_with(prompt="Mock draft prompt")

    mock_wizard_repo.update_stage.assert_called_once_with(
        session_id=MOCK_SESSION_ID,
        user_id=MOCK_USER_ID,
        stage_data={
            "chosen_outline_index": chosen_index,
            "draft_text": MOCK_AI_RESPONSE_DRAFT["generated_text"]
        }
    )

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_draft_ai_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_wizard_repo):
    """Test handling of AI generation error during draft."""
    mock_ai_gen_error = MagicMock()
    mock_ai_gen_error.generate_content.return_value = {"error": True, "error_message": "AI failed draft"}
    mock_get_ai_generator.return_value = mock_ai_gen_error
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_OUTLINE # Assumes this mock exists
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/draft', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_outline_index": 0
    })

    assert response.status_code == 500
    assert b"AI generation failed" in response.data
    mock_wizard_repo.update_stage.assert_not_called()

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_draft_db_update_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test handling of database update error during draft."""
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_DRAFT # Assumes mock exists
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_OUTLINE # Assumes mock exists
    mock_wizard_repo.update_stage.side_effect = Exception("DB draft update failed")
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/draft', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "chosen_outline_index": 0
    })

    assert response.status_code == 500
    # Update assertion to match the actual JSON error response structure from the generic exception handler
    assert response.get_json() == {"error": "An unexpected error occurred during the draft stage."}
    mock_wizard_repo.update_stage.assert_called_once()


# --- Add tests for /ai/wizard/edit --- #

def test_edit_missing_fields(client, auth_headers):
    """Test edit endpoint returns 400 if session_id is missing."""
    response = client.post('/ai/wizard/edit', headers=auth_headers, json={})
    assert response.status_code == 400
    assert b"Missing required field: session_id" in response.data

def test_edit_unauthenticated(client):
    """Test edit endpoint returns 401 if not authenticated."""
    response = client.post('/ai/wizard/edit', json={"session_id": "some-id"})
    assert response.status_code == 401

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_edit_invalid_session(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test edit endpoint returns 404 if session_id is invalid or doesn't belong to user."""
    mock_wizard_repo.get_session.return_value = None
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/edit', headers=auth_headers, json={
        "session_id": str(uuid4())
    })
    assert response.status_code == 404
    assert response.get_json() == {"error": "Wizard session not found"}

@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_edit_missing_draft_text(mock_init_repos, client, auth_headers, mock_wizard_repo):
    """Test edit endpoint returns 400 if draft_text is missing."""
    mock_session_no_draft = MOCK_SESSION_WITH_OUTLINE.copy() # Simulate session before draft
    mock_session_no_draft.pop("draft_text", None)
    mock_wizard_repo.get_session.return_value = mock_session_no_draft
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/edit', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID)
    })
    assert response.status_code == 400
    assert b"Previous stage (draft) data is missing or invalid" in response.data

@patch('app.ai_assistant.wizard_routes.edit_prompt') # Patch the specific prompt function
@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_edit_success_default_prefs(mock_init_repos, mock_get_ai_generator, mock_edit_prompt, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test successful edit request using default style preferences."""
    # Mock setup
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_EDIT # Assumes mock exists
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_DRAFT # Assumes mock exists
    mock_edit_prompt.return_value = "Mock edit prompt"
    updated_session_data = {
        **MOCK_SESSION_WITH_DRAFT,
        "edited_text": MOCK_AI_RESPONSE_EDIT["generated_text"]
    }
    mock_wizard_repo.update_stage.return_value = updated_session_data
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/edit', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID)
        # No style_preferences provided
    })

    # Assertions
    assert response.status_code == 200
    data = response.get_json()
    assert "edited_script" in data
    assert data["edited_script"] == MOCK_AI_RESPONSE_EDIT["generated_text"]

    mock_wizard_repo.get_session.assert_called_once_with(session_id=MOCK_SESSION_ID, user_id=MOCK_USER_ID)

    # Assert edit_prompt called correctly (using default prefs)
    expected_draft = MOCK_SESSION_WITH_DRAFT["draft_text"]
    mock_edit_prompt.assert_called_once_with(draft=expected_draft) # Default pref not passed explicitly

    mock_get_ai_generator.assert_called_once()
    mock_ai_generator.generate_content.assert_called_once_with(prompt="Mock edit prompt")

    mock_wizard_repo.update_stage.assert_called_once_with(
        session_id=MOCK_SESSION_ID,
        user_id=MOCK_USER_ID,
        stage_data={
            "edited_text": MOCK_AI_RESPONSE_EDIT["generated_text"]
        }
    )

@patch('app.ai_assistant.wizard_routes.edit_prompt')
@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_edit_success_custom_prefs(mock_init_repos, mock_get_ai_generator, mock_edit_prompt, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test successful edit request using custom style preferences."""
    # Mock setup (similar to default, just checking prompt call)
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_EDIT
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_DRAFT
    mock_edit_prompt.return_value = "Mock edit prompt custom"
    updated_session_data = {
        **MOCK_SESSION_WITH_DRAFT,
        "edited_text": MOCK_AI_RESPONSE_EDIT["generated_text"]
    }
    mock_wizard_repo.update_stage.return_value = updated_session_data
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    custom_prefs = "make it funny"
    response = client.post('/ai/wizard/edit', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID),
        "style_preferences": custom_prefs
    })

    # Assertions
    assert response.status_code == 200
    data = response.get_json()
    assert "edited_script" in data

    mock_wizard_repo.get_session.assert_called_once()
    # Assert edit_prompt called correctly with custom prefs
    expected_draft = MOCK_SESSION_WITH_DRAFT["draft_text"]
    mock_edit_prompt.assert_called_once_with(draft=expected_draft, style_preferences=custom_prefs)
    mock_get_ai_generator.assert_called_once()
    mock_ai_generator.generate_content.assert_called_once_with(prompt="Mock edit prompt custom")
    mock_wizard_repo.update_stage.assert_called_once()


@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_edit_ai_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_wizard_repo):
    """Test handling of AI generation error during edit."""
    mock_ai_gen_error = MagicMock()
    mock_ai_gen_error.generate_content.return_value = {"error": True, "error_message": "AI failed edit"}
    mock_get_ai_generator.return_value = mock_ai_gen_error
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_DRAFT
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/edit', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID)
    })

    assert response.status_code == 500
    assert b"AI generation failed" in response.data
    mock_wizard_repo.update_stage.assert_not_called()

@patch('app.ai_assistant.wizard_routes.get_ai_generator')
@patch('app.utils.supabase_hooks.init_supabase_repositories')
def test_edit_db_update_error(mock_init_repos, mock_get_ai_generator, client, auth_headers, mock_ai_generator, mock_wizard_repo):
    """Test handling of database update error during edit."""
    mock_ai_generator.generate_content.return_value = MOCK_AI_RESPONSE_EDIT
    mock_get_ai_generator.return_value = mock_ai_generator
    mock_wizard_repo.get_session.return_value = MOCK_SESSION_WITH_DRAFT
    mock_wizard_repo.update_stage.side_effect = Exception("DB edit update failed")
    mock_init_repos.side_effect = lambda: configure_g_for_mock_repo(mock_wizard_repo)

    response = client.post('/ai/wizard/edit', headers=auth_headers, json={
        "session_id": str(MOCK_SESSION_ID)
    })

    assert response.status_code == 500
    # Update assertion to match the actual JSON error response structure from the generic exception handler
    assert response.get_json() == {"error": "An unexpected error occurred during the edit stage."}
    mock_wizard_repo.update_stage.assert_called_once()


# --- Add tests for /ai/wizard/edit --- # 