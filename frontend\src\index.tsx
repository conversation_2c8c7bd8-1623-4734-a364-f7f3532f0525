import React from "react";
import ReactDOM from "react-dom/client";
import { Provider } from "react-redux";
import { BrowserRouter } from "react-router-dom";
import App from "./App";
import { store } from "./store";
import ThemeProvider from "./components/ThemeProvider";
import "./styles/index.css";
import { suppressResizeObserverWarning } from "./utils/errorHandlers";

// Suppress ResizeObserver warnings in development
if (process.env.NODE_ENV === "development") {
  suppressResizeObserverWarning();
}

const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement
);

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider>
          <App />
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>
);
