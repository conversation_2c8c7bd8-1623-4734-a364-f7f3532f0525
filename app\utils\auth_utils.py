"""
Authentication utility functions for Writer v2.
This module provides utility functions for authentication.
"""

import logging
from app.repositories.supabase_user_repository import UserRepository

logger = logging.getLogger(__name__)


def create_user_repository() -> UserRepository:
    """
    Create a new user repository instance.
    
    Returns:
        UserRepository: A UserRepository instance.
    """
    return UserRepository()
