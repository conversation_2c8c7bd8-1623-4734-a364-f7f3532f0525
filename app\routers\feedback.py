"""
Feedback routes for FastAPI.
Migrated from Flask app/feedback/routes.py to use FastAPI dependency injection.
"""

import logging
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel, Field

from app.auth.dependencies import CurrentUserType, CurrentUserIdType, get_current_user
from app.dependencies import get_feedback_repository
from app.repositories.supabase_feedback_repository import FeedbackRepository

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/feedback", tags=["feedback"])


# Pydantic models for request/response
class FeedbackSubmission(BaseModel):
    session_id: str = Field(..., description="Chat session ID")
    message_id: str = Field(..., description="Message ID")
    feedback_type: str = Field(..., description="Type of feedback")
    rating: str = Field(..., pattern="^(up|down)$", description="Rating: 'up' or 'down'")
    comment: Optional[str] = Field(None, description="Optional feedback comment")


class FeedbackResponse(BaseModel):
    message: str
    feedback_id: Optional[str] = None


async def get_feedback_repository() -> FeedbackRepository:
    """Get feedback repository dependency"""
    from app.models.supabase_client import get_supabase_client
    from app.repositories.supabase_feedback_repository import FeedbackRepository
    
    supabase = await get_supabase_client()
    if not supabase:
        raise HTTPException(
            status_code=503,
            detail="Feedback service temporarily unavailable - Failed to get Supabase client"
        )
    
    return FeedbackRepository(supabase_client=supabase)


@router.post("/", response_model=FeedbackResponse, status_code=201)
async def submit_feedback(
    feedback_data: FeedbackSubmission,
    current_user_id: CurrentUserIdType,
    feedback_repo: FeedbackRepository = Depends(get_feedback_repository)
) -> FeedbackResponse:
    """Submit feedback for a chat message"""
    try:
        # Validate rating value
        if feedback_data.rating not in ["up", "down"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid rating value. Must be 'up' or 'down'."
            )

        # Prepare feedback data
        feedback_payload = {
            "user_id": current_user_id,
            "session_id": feedback_data.session_id,
            "message_id": feedback_data.message_id,
            "feedback_type": feedback_data.feedback_type,
            "rating": feedback_data.rating,
            "comment": feedback_data.comment,
        }

        # Insert feedback into database
        feedback_result = await feedback_repo.create_feedback(feedback_payload)

        if not feedback_result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save feedback"
            )

        logger.info(f"Feedback submitted successfully by user {current_user_id}")

        return FeedbackResponse(
            message="Feedback received successfully",
            feedback_id=feedback_result.get("id")
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"Error inserting feedback into database for user {current_user_id}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal error occurred while saving feedback"
        )


@router.get("/all", response_model=Dict[str, List[Dict[str, Any]]])
async def get_all_feedback(
    current_user: CurrentUserType,
    feedback_repo: FeedbackRepository = Depends(get_feedback_repository)
) -> Dict[str, List[Dict[str, Any]]]:
    """Get all feedback (admin only)"""
    # Check if user is admin
    if not current_user.user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Unauthorized - User not found"
        )

    # For now, we'll check if the user has admin privileges
    # This should be replaced with proper role-based access control
    user_email = current_user.user_data.get("email", "")
    is_admin = user_email in ["<EMAIL>", "<EMAIL>"]  # Configure admin emails

    if not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Unauthorized - Admin access required"
        )

    try:
        # Get all feedback from the database
        feedback_list = await feedback_repo.get_all_feedback()

        return {"feedback": feedback_list}

    except Exception as e:
        logger.error(f"Error retrieving all feedback: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal error occurred while retrieving feedback"
        )


@router.get("/user", response_model=Dict[str, List[Dict[str, Any]]])
async def get_user_feedback(
    current_user_id: CurrentUserIdType,
    feedback_repo: FeedbackRepository = Depends(get_feedback_repository)
) -> Dict[str, List[Dict[str, Any]]]:
    """Get feedback submitted by the current user"""
    try:
        # Get user's feedback from the database
        feedback_list = await feedback_repo.get_feedback_by_user(current_user_id)

        return {"feedback": feedback_list}

    except Exception as e:
        logger.error(f"Error retrieving user feedback for {current_user_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal error occurred while retrieving feedback"
        )


@router.get("/session/{session_id}", response_model=Dict[str, List[Dict[str, Any]]])
async def get_session_feedback(
    session_id: str,
    current_user_id: CurrentUserIdType,
    feedback_repo: FeedbackRepository = Depends(get_feedback_repository)
) -> Dict[str, List[Dict[str, Any]]]:
    """Get feedback for a specific chat session"""
    try:
        # Get feedback for the session
        feedback_list = await feedback_repo.get_feedback_by_session(session_id, current_user_id)

        return {"feedback": feedback_list}

    except Exception as e:
        logger.error(f"Error retrieving session feedback for {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal error occurred while retrieving session feedback"
        )


@router.delete("/{feedback_id}", response_model=Dict[str, str])
async def delete_feedback(
    feedback_id: str,
    current_user_id: CurrentUserIdType,
    feedback_repo: FeedbackRepository = Depends(get_feedback_repository)
) -> Dict[str, str]:
    """Delete a feedback entry (user can only delete their own feedback)"""
    try:
        # Check if feedback exists and belongs to the user
        feedback = await feedback_repo.get_feedback_by_id(feedback_id)
        
        if not feedback:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Feedback not found"
            )

        if feedback.get("user_id") != current_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Unauthorized - You can only delete your own feedback"
            )

        # Delete the feedback
        success = await feedback_repo.delete_feedback(feedback_id, current_user_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete feedback"
            )

        return {"message": "Feedback deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting feedback {feedback_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal error occurred while deleting feedback"
        )


@router.get("/stats", response_model=Dict[str, Any])
async def get_feedback_stats(
    current_user: CurrentUserType,
    feedback_repo: FeedbackRepository = Depends(get_feedback_repository)
) -> Dict[str, Any]:
    """Get feedback statistics (admin only)"""
    # Check if user is admin
    if not current_user.user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Unauthorized - User not found"
        )

    user_email = current_user.user_data.get("email", "")
    is_admin = user_email in ["<EMAIL>", "<EMAIL>"]

    if not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Unauthorized - Admin access required"
        )

    try:
        # Get feedback statistics
        stats = await feedback_repo.get_feedback_stats()

        return {
            "total_feedback": stats.get("total", 0),
            "positive_feedback": stats.get("positive", 0),
            "negative_feedback": stats.get("negative", 0),
            "feedback_by_type": stats.get("by_type", {}),
            "recent_feedback_count": stats.get("recent", 0),
        }

    except Exception as e:
        logger.error(f"Error retrieving feedback statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An internal error occurred while retrieving feedback statistics"
        )
