import os
import openai
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Environment variable name for the API key
ROUTER_API_KEY_ENV_VAR = "ROUTER_API_KEY"

# Load API key from environment variables
# This needs to be run once when the module is imported
load_dotenv()

# Initialize the client globally if the key exists, for potential reuse
# Handle the case where the key might not be present at import time gracefully
client: openai.OpenAI | None = None
_initial_api_key = os.getenv(ROUTER_API_KEY_ENV_VAR)

if _initial_api_key:
    try:
        client = openai.OpenAI(
            api_key=_initial_api_key,
            base_url="https://router.requesty.ai/v1",
            default_headers={"Authorization": f"Bearer {_initial_api_key}"},
        )
        logger.info("Requesty client initialized successfully during module import.")
    except Exception as e:
        logger.error(
            f"Failed to initialize OpenAI client for Requesty during import: {e}"
        )
        client = None  # Ensure client is None if initialization fails
elif not os.environ.get(
    "PYTEST_CURRENT_TEST"
):  # Avoid warning during test discovery/runs
    logger.warning(
        f"{ROUTER_API_KEY_ENV_VAR} not found in environment variables during import."
    )


def call_requesty_llm(model: str, messages: list[dict]) -> str:
    """
    Calls the Requesty.ai chat completion endpoint using the OpenAI client.

    Args:
        model: The model identifier string (e.g., "google/gemini-2.5-pro-preview-03-25").
        messages: A list of message dictionaries, following the OpenAI format
                  (e.g., [{"role": "user", "content": "Hello?"}]).

    Returns:
        The content string from the first choice in the response.

    Raises:
        ValueError: If the ROUTER_API_KEY environment variable is not set.
        openai.APIError: If there is an issue communicating with the Requesty API.
        Exception: For other unexpected errors during the API call or response processing.
    """
    global client  # Access the potentially initialized client

    current_client = client

    # If client wasn't initialized at import, try again now.
    # This ensures it picks up env vars set after import (e.g., in tests).
    if current_client is None:
        logger.info(
            "Requesty client was not initialized at import. Attempting dynamic initialization..."
        )
        api_key = os.getenv(ROUTER_API_KEY_ENV_VAR)
        if not api_key:
            raise ValueError(
                f"{ROUTER_API_KEY_ENV_VAR} not found. Please set it in your environment (e.g., .env file)."
            )

        try:
            current_client = openai.OpenAI(
                api_key=api_key,
                base_url="https://router.requesty.ai/v1",
                default_headers={"Authorization": f"Bearer {api_key}"},
            )
            # Optionally update the global client if successful for subsequent calls in the same process
            # client = current_client
            logger.info("Requesty client initialized dynamically.")
        except Exception as e:
            logger.error(f"Failed to initialize Requesty client dynamically: {e}")
            # Use f-string for the exception message
            raise Exception(f"Failed to initialize Requesty client: {e}") from e

    # Now we expect current_client to be non-None if we reached here
    if current_client is None:
        # This should theoretically not be reachable due to checks above, but added for safety.
        raise Exception("Requesty client could not be initialized.")

    try:
        logger.info(
            f"Calling Requesty model '{model}' using client {id(current_client)}..."
        )
        response = current_client.chat.completions.create(
            model=model, messages=messages
        )

        if not response.choices:
            logger.error("Requesty API call successful but returned no choices.")
            raise Exception("No response choices found.")

        content = response.choices[0].message.content
        logger.info("Successfully received response from Requesty.")
        return content

    except openai.APIError as e:
        logger.error(f"Requesty API error: {e}")
        raise  # Re-raise the specific API error for upstream handling
    except Exception as e:
        logger.error(f"An unexpected error occurred during Requesty call: {e}")
        # Wrap unexpected errors for clarity
        raise Exception(f"An unexpected error occurred: {e}") from e


# Example usage (for potential direct execution testing, though prefer unit tests)
if __name__ == "__main__":
    # Ensure you have a .env file with ROUTER_API_KEY set for this to work
    # Re-check key directly for example run
    local_api_key = os.getenv(ROUTER_API_KEY_ENV_VAR)
    if not local_api_key:
        print(f"Error: {ROUTER_API_KEY_ENV_VAR} not set. Cannot run example.")
    else:
        try:
            print("Running Requesty client example...")
            example_model = "google/gemini-2.5-pro-preview-03-25"
            example_messages = [
                {"role": "user", "content": "Explain the concept of recursion briefly."}
            ]
            result = call_requesty_llm(example_model, example_messages)
            print("\nResponse:")
            print(result)
        except ValueError as e:
            print(f"Configuration error: {e}")
        except openai.APIError as e:
            print(f"API error during example: {e}")
        except Exception as e:
            print(f"Unexpected error during example: {e}")
