{"name": "mcp-superassistant", "version": "0.5.1", "description": "MCP SuperAssistant", "license": "MIT", "private": true, "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/srbhptl39/MCP-SuperAssistant.git"}, "type": "module", "scripts": {"clean:bundle": "rimraf dist && turbo clean:bundle", "clean:node_modules": "pnpm dlx rimraf node_modules && pnpm dlx turbo clean:node_modules", "clean:turbo": "rimraf .turbo && turbo clean:turbo", "clean": "pnpm clean:bundle && pnpm clean:turbo && pnpm clean:node_modules", "clean:install": "pnpm clean:node_modules && pnpm install --frozen-lockfile", "type-check": "turbo type-check", "base-build": "pnpm clean:bundle && turbo build", "build": "pnpm set-global-env && pnpm base-build", "build:firefox": "pnpm set-global-env CLI_CEB_FIREFOX=true && pnpm base-build", "base-dev": "pnpm clean:bundle && turbo ready && turbo watch dev --concurrency 20", "dev": "pnpm set-global-env CLI_CEB_DEV=true && pnpm base-dev", "dev:firefox": "pnpm set-global-env CLI_CEB_DEV=true CLI_CEB_FIREFOX=true && pnpm base-dev", "build:eslint": "tsc -b", "zip": "pnpm build && pnpm -F zipper zip", "zip:firefox": "pnpm build:firefox && pnpm -F zipper zip", "e2e": "pnpm zip && turbo e2e", "e2e:firefox": "pnpm zip:firefox && turbo e2e", "lint": "turbo lint --continue -- --fix --cache --cache-location node_modules/.cache/.eslintcache", "lint:fix": "turbo lint:fix --continue -- --fix --cache --cache-location node_modules/.cache/.eslintcache", "prettier": "turbo prettier --continue -- --cache --cache-location node_modules/.cache/.prettiercache", "prepare": "husky", "update-version": "bash bash-scripts/update_version.sh", "copy_env": "bash bash-scripts/copy_env.sh", "set-global-env": "bash bash-scripts/set_global_env.sh", "postinstall": "pnpm build:eslint && pnpm copy_env", "module-manager": "pnpm -F module-manager start"}, "dependencies": {"react": "19.1.0", "react-dom": "19.0.0", "firebase": "^11.9.1"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.20.0", "@types/chrome": "0.0.304", "@types/eslint-plugin-jsx-a11y": "^6.10.0", "@types/eslint__eslintrc": "^2.1.2", "@types/eslint__js": "^9.14.0", "@types/node": "^22.5.5", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "deepmerge": "^4.3.1", "esbuild": "^0.25.0", "eslint": "^9.20.1", "eslint-config-airbnb-typescript": "18.0.0", "eslint-config-prettier": "^10.0.1", "eslint-import-resolver-typescript": "4.3.4", "eslint-plugin-import": "2.29.1", "eslint-plugin-import-x": "4.6.1", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-tailwindcss": "^3.17.5", "fast-glob": "^3.3.3", "globals": "^15.14.0", "husky": "^9.1.4", "lint-staged": "^15.2.7", "postcss": "^8.5.2", "postcss-load-config": "^6.0.1", "prettier": "^3.3.3", "rimraf": "^6.0.1", "run-script-os": "^1.1.6", "tailwindcss": "^3.4.17", "tslib": "^2.8.1", "turbo": "^2.4.2", "typescript": "5.8.1-rc", "typescript-eslint": "^8.20.0", "vite": "6.1.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,json}": ["prettier --write"]}, "packageManager": "pnpm@9.15.1", "engines": {"node": ">=22.12.0"}}