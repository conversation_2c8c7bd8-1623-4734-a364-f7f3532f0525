import os
from typing import Dict, List, Optional, Any, Type  # Added Type

import openai
from openai import Async<PERSON>penAI # Import AsyncOpenAI
import logging # Added import for logging
from pydantic import BaseModel, ValidationError # Added BaseModel, ValidationError

from app.ai_assistant.config import get_default_model, get_max_tokens, get_model_params
from . import prompts
from .schemas import GeneratedTextResponse # Import the Pydantic model

# Configure basic logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class OpenAIAdapter:
    """Adapter for OpenAI API integration"""

    def __init__(self, api_key: Optional[str] = None):
        """Initialize the OpenAI adapter with API key"""
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        # Initialize the AsyncOpenAI client
        self.client = AsyncOpenAI(api_key=self.api_key)

        # Get the default model from configuration
        self.model = get_default_model()

    async def generate_content(
        self,
        prompt: str,
        business_context: Optional[Dict] = None,
        content_examples: Optional[List[Dict]] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Generate content using OpenAI API with context and examples (now async)"""
        # Get max tokens from config if not provided
        if max_tokens is None:
            max_tokens = get_max_tokens("default")

        # Use provided system prompt or build one if not provided
        if system_prompt is None:
            system_message = prompts.build_system_prompt(
                content_format="general",
                business_context=business_context,
                content_examples=content_examples,
            )
        else:
            system_message = system_prompt

        # Construct the messages for the API call
        messages = [
            {"role": "system", "content": system_message},
        ]

        # Add the user prompt
        messages.append({"role": "user", "content": prompt})

        try:
            # Get model parameters from config
            params = get_model_params()

            # Make the API call to OpenAI (now async)
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=params["temperature"],
                top_p=params["top_p"],
                frequency_penalty=params["frequency_penalty"],
                presence_penalty=params["presence_penalty"],
            )

            # Extract the generated content
            generated_text = response.choices[0].message.content

            # Return the result
            return {
                "generated_text": generated_text,
                "context_used": business_context is not None,
                "examples_used": content_examples is not None
                and len(content_examples) > 0,
                "model": self.model,
                "tokens_used": response.usage.total_tokens,
            }

        except openai.APIConnectionError as e:
            logging.error(f"OpenAI API connection error: {e}")
            return {
                "error": True,
                "error_message": "The server could not be reached. Please try again later.",
                "generated_text": "Error: Could not connect to AI service.",
            }
        except openai.RateLimitError as e:
            logging.error(f"OpenAI API rate limit exceeded: {e}")
            return {
                "error": True,
                "error_message": "API rate limit exceeded. Please try again later.",
                "generated_text": "Error: API rate limit reached.",
            }
        except openai.APIStatusError as e:
            logging.error(f"OpenAI API status error ({e.status_code}): {e.response}")
            return {
                "error": True,
                "error_message": f"OpenAI API returned an error (status {e.status_code}). Please try again.",
                "generated_text": f"Error: AI service returned status {e.status_code}.",
            }
        except Exception as e:
            # Handle other unexpected errors
            logging.error(f"An unexpected error occurred in generate_content: {e}", exc_info=True)
            error_message = str(e)
            return {
                "error": True,
                "error_message": error_message,
                "generated_text": (
                    f"An unexpected error occurred: {error_message}"
                ),
            }

    async def generate_chat_response(
        self,
        messages: List[Dict[str, str]],
        content_format: str = "general",
        business_context: Optional[Dict] = None,
        content_examples: Optional[List[Dict]] = None,
        video_data: Optional[Dict] = None,
        max_tokens: Optional[int] = None,
        response_model: Optional[Type[BaseModel]] = None
    ) -> Any:
        """Generate a response in a chat conversation, optionally parsing to Pydantic model (now async)"""
        if max_tokens is None:
            max_tokens = get_max_tokens("chat")

        system_message = prompts.build_system_prompt(
            content_format=content_format,
            business_context=business_context,
            content_examples=content_examples,
        )

        api_messages = [{"role": "system", "content": system_message}]
        api_messages.extend(messages)

        try:
            params = get_model_params()

            if response_model:
                # Use the .parse() method if a response_model is provided
                # This method typically works best when the model is instructed to output JSON
                # matching the Pydantic schema.
                # The .parse() method itself handles sending the Pydantic model's schema as a tool.
                logging.info(f"Attempting to parse response into Pydantic model: {response_model.__name__}")
                completion = await self.client.beta.chat.completions.parse(
                    model=self.model,
                    messages=api_messages,
                    max_tokens=max_tokens,
                    temperature=params["temperature"],
                    top_p=params["top_p"],
                    frequency_penalty=params["frequency_penalty"],
                    presence_penalty=params["presence_penalty"],
                    response_format=response_model # Pass the Pydantic model here
                )
                message = completion.choices[0].message
                if message.parsed:
                    # TODO: How to get tokens_used from .parse() method?
                    # The `completion` object from .parse() might not have `usage` directly.
                    # This needs to be investigated with the OpenAI library.
                    # For now, returning the parsed object without token info if not readily available.
                    if isinstance(message.parsed, GeneratedTextResponse):
                        # If we want to add model/token info to our standard Pydantic model
                        message.parsed.model_used = self.model
                        # message.parsed.tokens_used = completion.usage.total_tokens # If usage is available
                    return message.parsed # This is the Pydantic model instance
                elif message.refusal:
                    logging.warning(f"OpenAI model refused to generate content for Pydantic model: {message.refusal}")
                    return {
                        "error": True,
                        "error_message": f"Model refusal: {message.refusal}",
                        "generated_text": f"Error: The AI model refused to generate the requested structured output. Refusal: {message.refusal}"
                    }
                else:
                    logging.error("Pydantic parsing failed, but no refusal reported.")
                    return {
                        "error": True,
                        "error_message": "Pydantic parsing failed after API call.",
                        "generated_text": "Error: Failed to parse AI response into the requested structure."
                    }

            else:
                # Original path for non-Pydantic responses
                response = await self.client.chat.completions.create(
                    model=self.model,
                    messages=api_messages,
                    max_tokens=max_tokens,
                    temperature=params["temperature"],
                    top_p=params["top_p"],
                    frequency_penalty=params["frequency_penalty"],
                    presence_penalty=params["presence_penalty"],
                )

                # Extract the generated content
                generated_text = response.choices[0].message.content

                # Return the result
                return {
                    "generated_text": generated_text,
                    "context_used": business_context is not None,
                    "examples_used": content_examples is not None
                    and len(content_examples) > 0,
                    "video_data_used": video_data is not None,
                    "model": self.model,
                    "tokens_used": response.usage.total_tokens,
                }

        except openai.APIConnectionError as e:
            logging.error(f"OpenAI API connection error in chat: {e}")
            return {
                "error": True,
                "error_message": "The server could not be reached. Please try again later.",
                "generated_text": "Error: Could not connect to AI service.",
            }
        except openai.RateLimitError as e:
            logging.error(f"OpenAI API rate limit exceeded in chat: {e}")
            return {
                "error": True,
                "error_message": "API rate limit exceeded. Please try again later.",
                "generated_text": "Error: API rate limit reached.",
            }
        except openai.APIStatusError as e:
            logging.error(f"OpenAI API status error in chat ({e.status_code}): {e.response}")
            return {
                "error": True,
                "error_message": f"OpenAI API returned an error (status {e.status_code}). Please try again.",
                "generated_text": f"Error: AI service returned status {e.status_code}.",
            }
        except Exception as e:
            # Handle other unexpected errors
            logging.error(f"An unexpected error occurred in generate_chat_response: {e}", exc_info=True)
            error_message = str(e)
            return {
                "error": True,
                "error_message": error_message,
                "generated_text": f"An unexpected error occurred: {error_message}",
            }

    async def generate_content_with_youtube_data(
        self,
        prompt: str,
        business_context: Optional[Dict] = None,
        video_data: Optional[Dict] = None,
        max_tokens: Optional[int] = None,
    ) -> Dict[str, Any]:
        """Generate content using OpenAI API with context and YT video data (now async)."""
        # Get max tokens from config if not provided
        if max_tokens is None:
            max_tokens = get_max_tokens("youtube_content")

        # Get the system prompt from the centralized prompts module
        # Using the new builder. Assuming YT content implies YT capabilities.
        system_message = prompts.build_system_prompt(
            content_format="youtube_content",
            business_context=business_context,
            video_data=video_data,
        )

        # Construct the messages for the API call
        messages = [
            {"role": "system", "content": system_message},
        ]

        # Add the user prompt
        messages.append({"role": "user", "content": prompt})

        try:
            # Get model parameters from config
            params = get_model_params()

            # Make the API call to OpenAI (now async)
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=params["temperature"],
                top_p=params["top_p"],
                frequency_penalty=params["frequency_penalty"],
                presence_penalty=params["presence_penalty"],
            )

            # Extract the generated content
            generated_text = response.choices[0].message.content

            # Return the result
            return {
                "generated_text": generated_text,
                "context_used": business_context is not None,
                "video_data_used": video_data is not None,
                "model": self.model,
                "tokens_used": response.usage.total_tokens,
            }

        except openai.APIConnectionError as e:
            logging.error(f"OpenAI API connection error with YouTube data: {e}")
            return {
                "error": True,
                "error_message": "The server could not be reached. Please try again later.",
                "generated_text": "Error: Could not connect to AI service.",
            }
        except openai.RateLimitError as e:
            logging.error(f"OpenAI API rate limit exceeded with YouTube data: {e}")
            return {
                "error": True,
                "error_message": "API rate limit exceeded. Please try again later.",
                "generated_text": "Error: API rate limit reached.",
            }
        except openai.APIStatusError as e:
            logging.error(f"OpenAI API status error with YouTube data ({e.status_code}): {e.response}")
            return {
                "error": True,
                "error_message": f"OpenAI API returned an error (status {e.status_code}). Please try again.",
                "generated_text": f"Error: AI service returned status {e.status_code}.",
            }
        except Exception as e:
            # Handle other unexpected errors
            logging.error(f"An unexpected error occurred in generate_content_with_youtube_data: {e}", exc_info=True)
            error_message = str(e)
            return {
                "error": True,
                "error_message": f"An unexpected error occurred with YouTube data: {error_message}",
                "generated_text": (
                    f"An unexpected error occurred with YouTube data: {error_message}"
                ),
            }

    async def generate_chat_response_stream(
        self,
        messages: List[Dict[str, str]],
        content_format: str = "general",
        business_context: Optional[Dict] = None,
        content_examples: Optional[List[Dict]] = None,
        # video_data is not directly used in system prompt for streaming text, consider if needed
        max_tokens: Optional[int] = None,
    ):
        """Generate a streamed response in a chat conversation (now async generator)."""
        if max_tokens is None:
            max_tokens = get_max_tokens("chat_stream")

        system_message = prompts.build_system_prompt(
            content_format=content_format,
            business_context=business_context,
            content_examples=content_examples,
        )

        api_messages = [{"role": "system", "content": system_message}]
        api_messages.extend(messages)

        try:
            params = get_model_params()
            stream = await self.client.chat.completions.create(
                model=self.model,
                messages=api_messages,
                max_tokens=max_tokens,
                temperature=params["temperature"],
                top_p=params["top_p"],
                frequency_penalty=params["frequency_penalty"],
                presence_penalty=params["presence_penalty"],
                stream=True,
            )
            async for chunk in stream: # Use async for
                if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
        
        except openai.APIConnectionError as e:
            logging.error(f"OpenAI API connection error during stream: {e}")
            yield f"Error: Could not connect to AI service. Details: {str(e)}"
        except openai.RateLimitError as e:
            logging.error(f"OpenAI API rate limit exceeded during stream: {e}")
            yield f"Error: API rate limit reached. Details: {str(e)}"
        except openai.APIStatusError as e:
            logging.error(f"OpenAI API status error during stream ({e.status_code}): {e.response}")
            yield f"Error: AI service returned status {e.status_code}. Details: {str(e)}"
        except Exception as e:
            logging.error(f"An unexpected error occurred during stream: {e}", exc_info=True)
            yield f"Error: An unexpected error occurred. Details: {str(e)}"
