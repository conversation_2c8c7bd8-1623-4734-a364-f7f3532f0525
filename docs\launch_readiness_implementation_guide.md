# Writer v2 Launch Readiness Implementation Guide

_This document maps out the work required to make the Writer v2 stack production-ready.  Tasks are grouped by criticality, list the impacted files, and include concrete implementation hints so another AI (or human) can tackle them one by one._

---

## 1  Overview
The project ships a FastAPI backend (Dockerised) and a CRA + MUI v5 front-end served via nginx or the CRA dev-server in development.  Most outstanding problems are wiring / config rather than deep architectural flaws.

**Goal:** achieve a minimal viable launch where all advertised features—authentication against Supabase, AI assistant endpoints, CRUD routes, and the React UI—work behind a single origin (`/api` proxy) without runtime 500s or leaking secrets.

---

## 2  Task Matrix by Launch Necessity

| Priority | ID | Description | Impacted Layer / Files | Done When |
|---|---|---|---|---|
| **BLOCKER** | B-1 | Provide and load required secrets (`OPENROUTER_API_KEY`/**or** `OPENAI_API_KEY`, `SUPABASE_URL`, `SUPABASE_KEY`, `SUPABASE_SERVICE_ROLE_KEY`, `SUPABASE_JWT_SECRET`) | `.env`, `docker-compose.yml`, `main.Settings` | Backend boots, `/api/ai_assistant/test-connection` returns 200, Supabase repo call succeeds |
| **BLOCKER** | B-2 | Ensure backend container stays up (detach mode or remove `--reload`) | `docker-compose.yml`, `Dockerfile.backend` | `docker ps` shows backend healthy ≥ 10 min |
| **BLOCKER** | B-3 | Fix health endpoint mismatch `/health` vs `/api/health` | `app/routers/health.py`, monitors, Playwright tests | Both URLs return 200 (or monitors updated) |
| **BLOCKER** | B-4 | Default front-end API base URL to **relative** `/api` instead of `http://localhost:5000` | `frontend/src/services/envConfig.ts`, env files | Browser network tab shows calls to `/api/*`, not port 5000 |
| **BLOCKER** | B-5 | Strip `Authorization` & body logging from `setupProxy.js` to avoid secret leakage | `frontend/src/setupProxy.js` | Proxy logs no sensitive headers/body |
| **BLOCKER** | B-6 | Health-check script must fail when Supabase vars missing | `scripts/supabase_health_check.py` | `docker inspect` health status == `unhealthy` when creds absent |
| **BLOCKER** | B-7 | Remove Supabase anon key from committed source | `frontend/src/services/envConfig.ts`, any `.env.example` | No live secret values in repo |
| **SHOULD** | S-1 | Eliminate hot-reload mismatch or add bind-mount for dev | `docker-compose.yml` | Code edits visible in container _or_ reload flag removed |
| **SHOULD** | S-2 | Enforce router prefix starts with `/api` (pytest) | `tests/test_router_prefix.py` (new) | CI fails on bad prefix |
| **SHOULD** | S-3 | Guard TODO/stubbed functions so routes return 503 not 500 | `app/ai_assistant/*`, `app/utils/ai_integration.py`, `ai_generation.py` | No unhandled `NotImplemented` / TODOs hit in prod paths |
| **SHOULD** | S-4 | Parameterise test base URLs | `tests/**`, `playwright.config.js` | Tests pass in Docker and host env |
| **NICE** | N-1 | Suppress google-api discovery cache warning | `main.py` (early import patch) | Startup logs clean |
| **NICE** | N-2 | Improve graceful shutdown (await long DB ops) | `main.lifespan` | No in-flight query errors during `docker stop` |
| **NICE** | N-3 | Add centralised log driver / rotation policy | `docker-compose.yml` | Logs forward to chosen sink |

---

## 3  Implementation Steps

### Stage 1  Secrets & Environment
1. Create `.env.local` (ignored by Git) holding all required keys.  Example snippet:
   ```env
   OPENROUTER_API_KEY=sk-...
   SUPABASE_URL=https://xxxx.supabase.co
   SUPABASE_KEY=service_role_key
   SUPABASE_SERVICE_ROLE_KEY=service_role_key
   SUPABASE_JWT_SECRET=super-secret
   ```
2. Add `env_file: ./.env.local` under **backend** and **frontend** services in `docker-compose.yml`.
3. Delete hard-coded Supabase anon key from `envConfig.ts`; instead read from env at build time (use `.env.frontend`).

### Stage 2  Backend Container Hardening
1. Edit `docker-compose.yml`:
   * Remove `--reload` from backend `command` for prod profile.
   * Optionally add a named _dev_ profile that **does** mount source and uses `--reload`.
2. `scripts/supabase_health_check.py` ⇒ exit 1 if either `SUPABASE_URL` or `SUPABASE_KEY` missing; keep query.
3. Add alias route `/health` in `app/routers/health.py` or update external probes to `/api/health`.

### Stage 3  Front-end URL Hygiene & Proxy
1. In `envConfig.ts`: change default to `"/api"`; remove absolute fallback.
2. Search & replace hard-coded `localhost:5000` / `backend:5000`; move constants into a helper or remove entirely.
3. In `setupProxy.js`: redact sensitive headers in `onProxyReq`; keep basic debug lines.
4. Ensure production nginx (`frontend/nginx.conf`) passes thru `/api` to backend container.

### Stage 4  Testing & CI Guards
1. New pytest `tests/test_router_prefix.py`:
   ```python
   from app import routers
   import inspect

   def test_router_prefixes():
       for name, module in inspect.getmembers(routers):
           if hasattr(module, 'router'):
               prefix = getattr(module.router, 'prefix', '')
               assert prefix.startswith('/api'), f"{name} prefix must start with /api"
   ```
2. Parametrise test base URL via env var `API_BASE_URL`; default to `http://localhost:5000` when absent.
3. Add GitHub Action (or local script) that runs: lint → tests → docker build → docker compose up (health ok) → e2e tests.

---

## 4  Validation Checklist (pre-launch)
- [ ] `docker compose --profile prod up -d` brings stack healthy.
- [ ] `/api/health` 200 & `/health` 200 (or documented choice).
- [ ] `/api/ai_assistant/test-connection` returns success.
- [ ] CRUD routes (`/api/business/`, `/api/content/`) return 200 with valid Supabase data.
- [ ] Front-end dev-server proxies via `/api` and stores no secrets in JS bundle.
- [ ] Playwright smoke-test passes in CI with containerised environment.
- [ ] No secrets (keys, JWTs) appear in logs.

---

## 5  Post-Launch Enhancements
See NICE items above plus performance tuning, CORS production domains, and monitoring dashboards.

---

_End of guide._ 