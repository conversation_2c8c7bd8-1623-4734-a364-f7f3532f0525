name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: writer_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python 3.9
      uses: actions/setup-python@v4
      with:
        python-version: 3.9
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    - name: Set up environment variables
      run: |
        cp .env.example .env
        echo "FLASK_APP=app.py" >> .env
        echo "FLASK_ENV=testing" >> .env
        echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/writer_test" >> .env
        echo "SECRET_KEY=test_secret_key" >> .env
        echo "JWT_SECRET_KEY=test_jwt_secret_key" >> .env
        echo "OPENAI_API_KEY=test_openai_api_key" >> .env
        echo "YOUTUBE_API_KEY=test_youtube_api_key" >> .env
    - name: Run tests
      run: |
        pytest

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 16
    - name: Install dependencies
      working-directory: ./frontend
      run: |
        npm ci
    - name: Run linting
      working-directory: ./frontend
      run: |
        npm run lint
    - name: Run tests
      working-directory: ./frontend
      run: |
        npm test 