"""
Base Supabase repository class for Writer v2 - FastAPI version.
This module provides a base repository class for interacting with Supabase tables.
Migrated from Flask to use dependency injection for the Supabase client.
"""

from typing import Dict, List, Any, Optional, TypeVar, Generic, Union
import logging

from supabase import Client

T = TypeVar("T")
logger = logging.getLogger(__name__)


class SupabaseRepositoryError(Exception):
    """Exception raised for Supabase repository errors."""

    def __init__(
        self,
        message: str,
        status_code: Optional[int] = None,
        original_exception: Optional[Exception] = None,
        response: Optional[Any] = None,
    ):
        super().__init__(message)
        self.status_code = status_code
        self.original_exception = original_exception
        self.response = response  # Store the raw response if available


class SupabaseRepository(Generic[T]):
    """Base class for Supabase repositories."""

    def __init__(self, table_name: Optional[str] = None, supabase_client: Optional[Client] = None):
        """
        Initialize the repository with a table name and Supabase client.
        
        Args:
            table_name: Name of the Supabase table
            supabase_client: Supabase client instance (injected via dependency)
        """
        self.table_name = table_name
        self.supabase_client = supabase_client
        
        # For backward compatibility during migration, allow lazy loading
        if supabase_client is None:
            logger.warning(f"Repository {self.__class__.__name__} initialized without Supabase client - using legacy mode")

    def handle_error(self, message: str, exception: Exception):
        """Handle repository errors consistently."""
        logger.error("%s: %s", message, str(exception))
        raise SupabaseRepositoryError(f"{message}: {str(exception)}")

    def _get_table(self):
        """Get the table reference."""
        if not self.table_name:
            raise SupabaseRepositoryError("Table name not set")
        
        try:
            # Use injected client or fall back to legacy method
            if self.supabase_client:
                client = self.supabase_client
            else:
                # Backward compatibility - use legacy client getter
                from app.models.supabase_client import get_sync_supabase_client
                client = get_sync_supabase_client()
                logger.debug(f"Using legacy client getter for {self.table_name}")
            
            return client.table(str(self.table_name))
        except Exception as e:
            logger.error("Failed to get table %s: %s", self.table_name, e)
            raise SupabaseRepositoryError(f"Failed to get table {self.table_name}: {e}")

    def create(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new record."""
        try:
            table = self._get_table()
            response = table.insert(data).execute()
            if response.data:
                return response.data[0]
            raise SupabaseRepositoryError(
                f"Failed to create record in {self.table_name}: No data returned"
            )
        except Exception as e:
            logger.error("Failed to create record in %s: %s", self.table_name, e)
            raise SupabaseRepositoryError(
                f"Failed to create record in {self.table_name}: {e}"
            )

    def get_by_id(self, id: Union[str, int]) -> Optional[Dict[str, Any]]:
        """Get a record by ID."""
        logger.debug(
            f"Attempting to get record by ID from table '{self.table_name}' with ID: {id} (type: {type(id)})"
        )
        try:
            table = self._get_table()
            query = table.select("*").eq("id", id)
            logger.debug(
                f"Executing Supabase query: table='{self.table_name}', select='*', eq=('id', {id})"
            )
            response = query.execute()
            logger.debug(
                f"Raw response from Supabase for get_by_id({id}) in '{self.table_name}': {response}"
            )

            if response.data:
                logger.debug(
                    f"Record found for ID {id} in table '{self.table_name}'. Data: {response.data[0]}"
                )
                return response.data[0]
            else:
                logger.warning(
                    f"No record found for ID {id} in table '{self.table_name}'. Response data was empty."
                )
                return None
        except Exception as e:
            logger.error(
                f"Exception occurred during get_by_id for ID {id} in table '{self.table_name}': {type(e).__name__} - {e}"
            )
            logger.error("Failed to get record by ID from %s: %s", self.table_name, e)
            raise SupabaseRepositoryError(
                f"Failed to get record by ID from {self.table_name}: {e}"
            )

    def get_all(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Get all records with optional filters."""
        try:
            table = self._get_table()
            query = table.select("*")

            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)

            response = query.execute()
            return response.data
        except Exception as e:
            logger.error("Failed to get records from %s: %s", self.table_name, e)
            raise SupabaseRepositoryError(
                f"Failed to get records from {self.table_name}: {e}"
            )

    def update(self, id: Union[str, int], data: Dict[str, Any]) -> Dict[str, Any]:
        """Update a record by ID."""
        try:
            table = self._get_table()
            response = table.update(data).eq("id", id).execute()
            if response.data:
                return response.data[0]
            raise SupabaseRepositoryError(
                f"Failed to update record in {self.table_name}: No data returned"
            )
        except Exception as e:
            logger.error("Failed to update record in %s: %s", self.table_name, e)
            raise SupabaseRepositoryError(
                f"Failed to update record in {self.table_name}: {e}"
            )

    def delete(self, id: Union[str, int]) -> bool:
        """Delete a record by ID."""
        try:
            table = self._get_table()
            response = table.delete().eq("id", id).execute()
            return len(response.data) > 0
        except Exception as e:
            logger.error("Failed to delete record from %s: %s", self.table_name, e)
            raise SupabaseRepositoryError(
                f"Failed to delete record from {self.table_name}: {e}"
            )

    def query(self, query_builder_func) -> List[Dict[str, Any]]:
        """Execute a custom query using the provided query builder."""
        try:
            table = self._get_table()
            response = query_builder_func(table).execute()
            return response.data
        except Exception as e:
            logger.error("Failed to execute custom query on %s: %s", self.table_name, e)
            raise SupabaseRepositoryError(
                f"Failed to execute custom query on {self.table_name}: {e}"
            )

    def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """Count records with optional filters."""
        try:
            table = self._get_table()
            query = table.select("count", count="exact")

            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)

            response = query.execute()
            return response.count if hasattr(response, "count") else 0
        except Exception as e:
            logger.error("Failed to count records in %s: %s", self.table_name, e)
            raise SupabaseRepositoryError(
                f"Failed to count records in {self.table_name}: {e}"
            )

    def paginate(
        self,
        page: int = 1,
        per_page: int = 20,
        filters: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Paginate records with optional filters."""
        try:
            offset = (page - 1) * per_page
            
            table = self._get_table()
            query = table.select("*")

            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)

            # Apply pagination
            query = query.range(offset, offset + per_page - 1)
            response = query.execute()

            # Get total count
            total = self.count(filters)
            total_pages = (total + per_page - 1) // per_page

            return {
                "data": response.data,
                "pagination": {
                    "page": page,
                    "per_page": per_page,
                    "total": total,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1,
                }
            }
        except Exception as e:
            logger.error("Failed to paginate records from %s: %s", self.table_name, e)
            raise SupabaseRepositoryError(
                f"Failed to paginate records from {self.table_name}: {e}"
            )
