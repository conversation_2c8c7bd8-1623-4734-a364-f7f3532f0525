services:
  # Backend FastAPI
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    image: writerv2-backend:latest
    ports:
      - "127.0.0.1:5000:5000"
    env_file:
      - .env.local  # Load environment from secure file
    environment:
      - FASTAPI_ENV=${FASTAPI_ENV:-development}
      - SQLALCHEMY_TRACK_MODIFICATIONS=False
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev-jwt-key}
      - JWT_ACCESS_TOKEN_EXPIRES=31536000
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-}
      - AI_PROVIDER=${AI_PROVIDER:-openrouter}
      - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000,http://127.0.0.1:3000,http://backend:5000
      - CORS_ALLOWED_HEADERS=Content-Type,Authorization,Accept,X-Client-Time,X-Client-Timezone,X-Time-Offset
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE=/app/logs/writer_v2.log
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_ANON_KEY:-${SUPABASE_KEY}}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-${SUPABASE_KEY}}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      - CACHE_DEFAULT_TTL=${CACHE_DEFAULT_TTL:-300}
      - CACHE_DEFAULT_MAX_SIZE=${CACHE_DEFAULT_MAX_SIZE:-100}
    command: uvicorn main:app --host=0.0.0.0 --port=5000 --log-level info
    restart: unless-stopped
    networks:
      - writer_network
    # Health check is now handled in Dockerfile
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Using named volumes instead of bind mounts to avoid path issues with spaces
    volumes:
      - backend_logs:/app/logs
      - backend_instance:/app/instance
    profiles:
      - ""  # Default profile (production-like)
      - prod

  # Frontend React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "127.0.0.1:3000:3000"
    env_file:
      - .env.local  # Load environment from secure file
    # Using named volumes for node_modules to improve performance
    volumes:
      - frontend_node_modules:/app/node_modules
      # Mount source code for hot reload in development
      - ./frontend/src:/app/src:ro
      - ./frontend/public:/app/public:ro
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://backend:5000/api  # Direct backend URL in Docker
      - REACT_APP_JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev-jwt-key}
      - ESLINT_NO_DEV_ERRORS=true
      - DISABLE_ESLINT_PLUGIN=true
      - REACT_APP_SUPABASE_URL=${SUPABASE_URL}
      - REACT_APP_SUPABASE_KEY=${SUPABASE_ANON_KEY:-${SUPABASE_KEY}}
      - REACT_APP_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-${SUPABASE_KEY}}
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - DOCKER_ENV=true
      - GENERATE_SOURCEMAP=false  # Reduce build time and size
      - FAST_REFRESH=true  # Enable React Fast Refresh
      - HOST=0.0.0.0  # Bind to all interfaces for Docker
      - PORT=3000
      - WDS_SOCKET_HOST=127.0.0.1  # For hot reload
      - WDS_SOCKET_PORT=3000
    depends_on:
      backend:
        condition: service_healthy  # Wait for backend to be healthy
    restart: unless-stopped
    networks:
      - writer_network
    profiles:
      - ""  # Default profile
      - dev
      - prod

  # Development backend with hot reload
  backend-dev:
    build:
      context: .
      dockerfile: Dockerfile.backend
    image: writerv2-backend:latest
    ports:
      - "5001:5000"  # Different port to avoid conflicts
    env_file:
      - .env.local
    environment:
      - FASTAPI_ENV=development
      - SQLALCHEMY_TRACK_MODIFICATIONS=False
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-dev-jwt-key}
      - JWT_ACCESS_TOKEN_EXPIRES=31536000
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-}
      - AI_PROVIDER=${AI_PROVIDER:-openrouter}
      - YOUTUBE_API_KEY=${YOUTUBE_API_KEY}
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://frontend:3000,http://127.0.0.1:3000
      - CORS_ALLOWED_HEADERS=Content-Type,Authorization,Accept,X-Client-Time,X-Client-Timezone,X-Time-Offset
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - LOG_FILE=/app/logs/writer_v2.log
      - USE_SUPABASE=true
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_KEY=${SUPABASE_ANON_KEY:-${SUPABASE_KEY}}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-${SUPABASE_KEY}}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      - CACHE_DEFAULT_TTL=${CACHE_DEFAULT_TTL:-300}
      - CACHE_DEFAULT_MAX_SIZE=${CACHE_DEFAULT_MAX_SIZE:-100}
    command: uvicorn main:app --host=0.0.0.0 --port=5000 --reload --log-level debug
    restart: unless-stopped
    networks:
      - writer_network
    # Health check is now handled in Dockerfile
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    volumes:
      - backend_logs:/app/logs
      - backend_instance:/app/instance
      # Mount source code for development hot reload
      - .:/app:ro  # Read-only mount to prevent permission issues
    profiles:
      - dev

volumes:
  frontend_node_modules:
  backend_logs:
  backend_instance:

networks:
  writer_network:
    driver: bridge