{"$schema": "https://json.schemastore.org/tsconfig", "display": "Base", "compilerOptions": {"allowJs": true, "noEmit": true, "downlevelIteration": true, "isolatedModules": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "noImplicitReturns": true, "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "bundler", "target": "ESNext", "lib": ["DOM", "ESNext"], "types": ["node", "chrome"]}}