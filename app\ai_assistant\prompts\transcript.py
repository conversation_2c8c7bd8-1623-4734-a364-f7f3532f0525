# app/ai_assistant/prompts/transcript.py
"""
Very simple transcript formatter:
  • Preserves every word verbatim
  • Inserts headings & paragraphs at logical breaks
  • Converts lists into bullets
  • Uses markdown: ## for headings, blank line for paragraph, - for bullets
  • NEVER summarizes, paraphrases, adds, or deletes content
"""

TEMPLATE = """You are an expert transcript‐formatter.  
Your job is to take the raw YouTube script below and produce an easy‑to‑read
markdown document that:

  1. PRESERVES EVERY WORD verbatim (no paraphrasing or summarizing)  
  2. BREAKS into paragraphs on topic shifts (blank line between)  
  3. ADDS headings (##) for major sections  
  4. TURNS enumerations into bullet lists (")-"
  5. EMPHASIZES key statements with **bold**  

DO NOT add, remove, or change any words. Only format.

---

{transcript}
"""

def format_transcript(transcript: str) -> str:
    """
    Injects the user's raw script into our universal TEMPLATE.

    Args:
        transcript: The raw transcript text.

    Returns:
        The formatted prompt string ready for the AI.
    """
    # Using f-string for cleaner placeholder replacement
    return f"""You are an expert transcript‐formatter.  
Your job is to take the raw YouTube script below and produce an easy‑to‑read
markdown document that:

  1. PRESERVES EVERY WORD verbatim (no paraphrasing or summarizing)  
  2. BREAKS into paragraphs on topic shifts (blank line between)  
  3. ADDS headings (##) for major sections  
  4. TURNS enumerations into bullet lists ("- ")
  5. EMPHASIZES key statements with **bold**  

DO NOT add, remove, or change any words. Only format.

---

{transcript}
""".strip()
