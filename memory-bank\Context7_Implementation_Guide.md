# Context7 MCP Implementation Guide for Codebase Optimization

## Introduction

This guide provides detailed instructions for implementing improvements to the AI-powered content creation platform using Context7 MCP documentation. The platform consists of a React/TypeScript frontend with Material UI v5 and a Flask/Supabase backend with OpenAI/OpenRouter integration for AI capabilities.

### Current Architecture Overview

- **Frontend**: React (Create React App), TypeScript, Material UI v5, Redux Toolkit, React Context API
- **Backend**: Flask, Supabase (PostgreSQL), OpenRouter for AI integration
- **Deployment**: Docker (docker-compose for local development)
- **Key Features**: Authentication, content generation, business context management, YouTube integration, AI script wizard

## Current Codebase Structure

### Key File Locations

- **Frontend**:
  - `frontend/src/store/` - Redux store and slices
  - `frontend/src/contexts/` - React Context providers
  - `frontend/src/services/` - API services
  - `frontend/src/pages/AIAssistant.tsx` - AI Assistant interface

- **Backend**:
  - `app/repositories/` - Supabase data access
  - `app/routes/` - Flask API routes
  - `app/ai_assistant/` - AI integration code
    - `app/ai_assistant/config.py` - AI configuration
    - `app/ai_assistant/openai_adapter.py` - OpenAI integration
    - `app/ai_assistant/openrouter_adapter.py` - OpenRouter integration
    - `app/ai_assistant/prompts/` - Prompt templates
  - `app/utils/ai_integration.py` - AI utility functions

## Implementation Recommendations

### 1. OpenAI Integration Improvements

The application currently uses OpenAI/OpenRouter for AI model access through adapters in `app/ai_assistant/openai_adapter.py` and `app/ai_assistant/openrouter_adapter.py`. The configuration is managed in `app/ai_assistant/config.py`.

#### 1.1 Implement Structured Error Handling

**Current Issue**: Basic error handling without specific error types.
**Status**: Completed

**Implementation Steps**:
1. Use Context7 MCP to search for "OpenAI error handling" to get the latest error handling patterns.
2. Replace generic exception handling with specific OpenAI error types (APIConnectionError, RateLimitError, APIStatusError).
3. Implement proper logging and user-friendly error messages for each error type.
4. Update the `openai_adapter.py` and `openrouter_adapter.py` files with the new error handling patterns.

**Notes**:
- For `openai_adapter.py`: Implemented `try-except` blocks for `openai.APIConnectionError`, `openai.RateLimitError`, and `openai.APIStatusError`. Added logging for these specific errors and a general logger for other exceptions.
- For `openrouter_adapter.py`: Since it uses the `requests` library, implemented `try-except` blocks for `requests.exceptions.HTTPError` (handling 429 for rate limits and 5xx for server errors specifically) and `requests.exceptions.RequestException` for connection issues. Enhanced logging for these and other exceptions.

#### 1.2 Implement Streaming Responses

**Current Issue**: No streaming responses for real-time feedback.
**Status**: Completed

**Implementation Steps**:
1. Use Context7 MCP to search for "OpenAI streaming responses" to get the latest streaming implementation patterns. (Completed)
2. Update the OpenAI adapter to support streaming with the `stream=True` parameter. (Completed - Added `generate_chat_response_stream` to `OpenAIAdapter`)
3. Create a new Flask route for streaming responses. (Completed - Added `/chat/stream` route in `chat_routes.py`)
4. Update the frontend to handle streaming responses using Server-Sent Events (SSE). (Completed - Updated `sendMessage` in `AIAssistant.tsx` to use `fetch` with streaming and handle SSE)
5. Implement proper error handling for streaming responses. (Completed - Implemented at adapter, route, and frontend levels)

**Notes**:
- The `OpenAIAdapter` now has a `generate_chat_response_stream` method that yields content chunks or error messages.
- A new Flask route `/api/chat/stream` (in `app/ai_assistant/chat_routes.py`) was created to handle streaming requests and save messages appropriately.
- The frontend (`AIAssistant.tsx`) was updated to call this new endpoint, process Server-Sent Events (SSE) for real-time updates, and handle potential streaming errors.
- Assumed `/api/chat/stream` as the frontend-facing URL (actual prefix might vary based on Flask setup).
- `OpenRouterAdapter` does not yet have a streaming implementation; the new route currently sends an error if OpenRouter is the active provider and streaming is attempted.
- Placeholder for `get_max_tokens("chat_stream")` config added; needs to be defined.
- Token counting for streamed messages is a placeholder and may need further implementation.

#### 1.3 Implement Async/Await Patterns

**Current Issue**: Synchronous API calls that could block the application.
**Status**: Completed

**Implementation Steps**:
1. Use Context7 MCP to search for "OpenAI async" to get the latest async implementation patterns. (Completed)
2. Update the OpenAI adapter to use `AsyncOpenAI` client. (Completed - `OpenAIAdapter` methods are now `async` and use `AsyncOpenAI`)
3. Implement async route handlers in Flask using asynchronous libraries. (Completed - `/chat` and `/chat/stream` routes are now `async` and conditionally `await` `OpenAIAdapter` calls. Requires ASGI server setup for Flask.)
4. Update the frontend to handle async responses properly. (Completed - Frontend `fetch` for streaming is already async. No specific changes needed for this step beyond what was done for streaming.)

**Notes**:
- `OpenAIAdapter` now uses `AsyncOpenAI` and all its API-calling methods are `async`.
- Flask routes `/chat` and `/chat/stream` in `app/ai_assistant/chat_routes.py` were converted to `async def`.
- These routes conditionally `await` calls if the AI provider is OpenAI.
- **Crucial**: The Flask application must be run with an ASGI-compatible server (e.g., Uvicorn, Hypercorn) for `async` routes to function correctly.
- `OpenRouterAdapter` remains synchronous. Calls to it from async routes will run synchronously within the async event loop, which is not ideal for I/O-bound operations and should be addressed later (e.g., by making `OpenRouterAdapter` async or running its calls in a thread pool executor).
- Frontend changes for streaming already handle asynchronous operations via `fetch`.

#### 1.4 Use Pydantic Model Parsing

**Current Issue**: No structured output parsing with Pydantic.
**Status**: Completed (Initial implementation)

**Implementation Steps**:
1. Use Context7 MCP to search for "OpenAI Pydantic" to get the latest Pydantic integration patterns. (Completed)
2. Define Pydantic models for structured outputs from OpenAI. (Completed - Added `schemas.py` with `GeneratedTextResponse` and examples)
3. Update the OpenAI adapter to use Pydantic models for parsing responses. (Completed - `OpenAIAdapter.generate_chat_response` now optionally uses `client.beta.chat.completions.parse()` with a Pydantic model)
4. Implement proper validation and error handling for Pydantic models. (Completed - Basic handling for `message.parsed` and `message.refusal` from `.parse()` method. Pydantic handles schema validation.)

**Notes**:
- Created `app/ai_assistant/schemas.py` with initial Pydantic models (`GeneratedTextResponse`, `StructuredContentIdea`, etc.).
- The `OpenAIAdapter.generate_chat_response` method was updated to accept an optional `response_model` argument.
- If a `response_model` is provided, the adapter uses `await self.client.beta.chat.completions.parse(..., response_format=response_model)`.
- This relies on the LLM being prompted correctly to output JSON conforming to the Pydantic schema.
- The method returns the parsed Pydantic object or an error dictionary if parsing/generation fails or is refused.
- Token usage information from the `.parse()` method needs further investigation for accurate reporting within the Pydantic model.
- More specific Pydantic models should be defined as use cases for structured AI output arise.
- Error handling for `pydantic.ValidationError` might be further refined if `.parse()` doesn't fully abstract it.

### 2. Material UI Optimizations

The frontend uses Material UI v5 for component styling and theming.

#### 2.1 Use sx Prop for Inline Styling

**Current Issue**: Inconsistent styling patterns.
**Status**: Partially Addressed

**Implementation Steps**:
1. Use Context7 MCP to search for "Material UI sx prop" to get the latest sx prop usage patterns. (Completed)
2. Replace styled components with sx prop for one-off customizations. (Reviewed - `sx` prop seems to be predominantly used already; no widespread `styled()` for one-off styles found in `AIAssistant.tsx`)
3. Implement consistent styling patterns across the application. (Partially Addressed - Made MVP updates to `getComponentOverrides` in `frontend/src/styles/theme.ts` to use theme tokens like `theme.spacing()`, `theme.shadows`, and `theme.palette` more consistently for `MuiButton`, `MuiCard`, and `MuiTextField`. Addressed related TypeScript type errors for `shadows` array and `theme` parameter in style overrides.)

**Notes**:
- The `sx` prop is heavily used in `AIAssistant.tsx` for inline and one-off styles, adhering to theme-aware principles (e.g., theme colors, spacing).
- No clear instances of `styled()` or older MUI styling utilities (makeStyles, withStyles) were found for one-off customizations that needed immediate replacement by `sx` in the reviewed file.
- MVP changes for theme token consistency were applied to `getComponentOverrides`.
- A broader review of the entire frontend codebase is still recommended to ensure consistent application of `sx` prop and theme token usage, and to identify any remaining areas where `styled()` might be used for one-off styles unnecessarily or where hardcoded values can be replaced by theme tokens.

#### 2.2 Implement Responsive Designs

**Current Issue**: Inconsistent responsive design patterns.
**Status**: Partially Addressed

**Implementation Steps**:
1. Use Context7 MCP to search for "Material UI responsive" to get the latest responsive design patterns. (Completed)
2. Use `theme.breakpoints` consistently for responsive designs. (Partially Addressed - Applied to main layout in `AIAssistant.tsx`)
3. Implement responsive spacing, typography, and layout across the application. (Partially Addressed - Main layout in `AIAssistant.tsx` made responsive. Further review needed for spacing/typography.)

**Notes**:
- The main layout of `AIAssistant.tsx` (session list and chat area) was updated using `<Grid>` item props (`xs`, `sm`, `md`) and the `sx` prop (`display: { xs: 'none', sm: 'block' }`) to make the session list collapsible on smaller screens and adjust the chat area width accordingly.
- This demonstrates the use of breakpoints for responsive layout.
- A full review of the application for responsive spacing (e.g., making paddings/margins responsive using object syntax in `sx`) and responsive typography (e.g., using `fontSize` with breakpoint objects or responsive `Typography` variants) is still recommended for comprehensive coverage.

#### 2.3 Improve Accessibility

**Current Issue**: Limited accessibility features.
**Status**: Partially Addressed

**Implementation Steps**:
1. Use Context7 MCP to search for "Material UI accessibility" to get the latest accessibility patterns. (Completed)
2. Add proper accessibility attributes to components (especially modals and form elements). (Partially Addressed - Added `aria-label` to several `IconButton`s and a `label` to the main `TextField` in `AIAssistant.tsx`.)
3. Implement keyboard navigation and screen reader support. (Not directly addressed by code changes - Requires manual testing and potential further adjustments.)

**Notes**:
- Added `aria-label` attributes to various `IconButton` components in `AIAssistant.tsx` (e.g., for delete session, toggle selection, copy message, save message, session options) to provide accessible names, especially since many are used with `Tooltip`s which might not always be the primary accessible name source.
- Added a `label` prop to the main message input `TextField` in `AIAssistant.tsx` for better accessibility.
- MUI components generally have good built-in keyboard navigation. However, thorough manual testing with keyboard and screen readers is crucial to identify and fix any gaps, especially for custom interactions or complex components like `SaveToLibraryDialog` and `Drawer`.
- For modals/dialogs like `SaveToLibraryDialog` and the `KnowledgeDrawer`, ensure they correctly trap focus, have proper `aria-labelledby` and `aria-describedby` attributes, and that focus is returned to the trigger element upon closing.

#### 2.4 Enable CSS Variables

**Current Issue**: Limited theme customization.
**Status**: Completed (Initial Implementation)

**Implementation Steps**:
1. Use Context7 MCP to search for "Material UI CSS variables" to get the latest CSS variables implementation patterns. (Completed)
2. Analyze the current theme implementation to identify the most suitable approach. (Completed)
3. Refactor `frontend/src/styles/theme.ts` to fully enable and utilize CSS variables with `experimental_extendTheme`: (Completed)
    a. Placed component overrides at the top level of `extendTheme` call, removing the `getComponentOverrides(mode)` function. (Completed)
    b. Updated component overrides to use `theme.vars.palette.*` for mode-dependent styling where appropriate, and set `cssVarPrefix: 'writer'`. (Completed)
    c. Removed the `getComponentOverrides(mode)` function and its usage. (Completed as part of 3a)
    d. Replaced `MuiThemeProvider` with `Experimental_CssVarsProvider` in `frontend/src/components/ThemeProvider.tsx` and configured it with the theme and `defaultMode`. (Completed)
    e. Resolved TypeScript errors and linter issues arising from these changes. (Completed)

**Notes**:
- Component overrides in `frontend/src/styles/theme.ts` now reside in the top-level `components` key of `extendTheme`.
- Mode-dependent styles within these overrides have been updated to use `theme.vars.palette.*` where feasible. Some complex conditional styles (e.g., for `MuiCard` box shadow, `MuiTextField` border colors) have been adapted to use `theme.vars` but may benefit from further refinement by defining more specific semantic CSS variables in the palette for light/dark modes.
- `Experimental_CssVarsProvider` is now in use in `frontend/src/components/ThemeProvider.tsx`, with `cssVarPrefix: 'writer'` set in `theme.ts`.
- Further testing across the application is recommended to ensure all themed components render correctly in both light and dark modes with CSS variables.
- The `mode` argument to `getTheme(mode)` is still used by `extendTheme` to understand the initial structure, and `Experimental_CssVarsProvider`'s `defaultMode` prop is also set.

### 3. Supabase Integration Enhancements

The application uses Supabase for authentication and database access.

#### 3.1 Explore Supabase MCP

**Current Issue**: Limited AI integration with Supabase.
**Status**: In Progress

**Implementation Steps**:
1. Use Context7 MCP to search for "Supabase MCP" to get information about Supabase MCP integration. (Completed - Found `/supabase-community/supabase-mcp` library and its documentation. Also found `modelcontextprotocol/python-sdk` for client-side integration.)
2. Explore the `supabase-community/supabase-mcp` library and `modelcontextprotocol/python-sdk`. (Completed - Reviewed documentation. The `supabase-community/supabase-mcp` server can be run via `npx`. The `modelcontextprotocol/python-sdk` allows a Python application, like our Flask backend, to act as a client to an MCP server.)
3. Implement Supabase MCP for improved AI integration by having the Flask backend's AI assistant act as a client to the Supabase MCP server:
    a. **Prerequisites**: Ensure Node.js is available for `npx` (to run Supabase MCP server). Add `"mcp[cli]"` to backend Python dependencies. (Pending)
    b. **Configuration**: Securely manage Supabase Access Token and Project Ref. These will be needed to run the Supabase MCP server and potentially by the Flask app to configure the server connection. (Pending)
    c. **Supabase MCP Server Management**: Define a strategy for running and managing the `npx @supabase/mcp-server-supabase@latest ...` process (e.g., as a separate service in development/production). (Pending)
    d. **Flask MCP Client Service**: Create a new service/module in `app/ai_assistant/` (e.g., `supabase_mcp_service.py`) using `modelcontextprotocol/python-sdk`. This service will handle connection to the Supabase MCP server process and expose methods to call its tools (e.g., `postgrestRequest`). (Pending)
    e. **Integrate with AI Assistant Logic**: Modify existing AI assistant components (e.g., within `app/ai_assistant/openai_adapter.py`, `app/ai_assistant/openrouter_adapter.py`, or services they consume) to utilize the new Flask MCP Client Service for relevant Supabase interactions. (Pending)
    f. **Error Handling & Logging**: Implement robust error handling and logging for the MCP client interactions. (Pending)
    g. **Testing**: Add unit/integration tests for the new MCP client service and its integration into the AI assistant. (Pending)

**Notes**:
- The Supabase MCP server (`@supabase/mcp-server-supabase@latest`) provides tools to interact with a Supabase project (e.g., making PostgREST requests).
- The Flask backend will use the `mcp` Python SDK to communicate with this server, allowing the AI assistant to leverage Supabase tools in a standardized way.
- This approach enhances the AI assistant's ability to interact with Supabase by abstracting direct database interactions through the MCP layer.

#### 3.2 Implement Supabase Cache Helpers

**Current Issue**: Limited caching for Supabase queries.
**Status**: In Progress

**Implementation Steps**:
1. Use Context7 MCP to search for "Supabase Cache Helpers" to get the latest caching patterns. (Completed - Learned about general server-side caching patterns like `QueryCache`, stores, SWR from Node.js library `/psteinroe/supabase-cache-helpers`. Found Python examples using Redis via `supafast` project.)
2. Implement Supabase Query Caching in the Flask Backend:
    a. **Choose Cache Store & Library**: Decided to use an in-memory cache for initial implementation with the `cachetools` Python library. Added `cachetools` to `requirements.txt`. (Completed)
    b. **Caching Service/Decorator (Python)**: Created `app/utils/caching.py` with a caching decorator (`@cache_supabase_query`) using `cachetools.TTLCache`. It supports custom key generation (including function name and hashed arguments) and a basic per-entry invalidation method. (Completed)
    c. **Cache Invalidation Strategy**: For repository methods performing writes (insert, update, delete), implement logic to invalidate relevant cache entries. This will involve calling the `wrapper.invalidate()` method provided by the decorator or potentially more broad invalidation strategies (e.g., clearing all entries for a table by iterating keys or using prefixed keys if feasible). (Pending)
    d. **Integrate into Repositories**: Apply the caching decorator to read-methods in `app/repositories/` (demonstrated with `UserRepository` for `get_user_by_email` and `get_by_id`) and call invalidation logic in write-methods (demonstrated in `UserRepository.update_profile`). Further integration across other repositories is pending. (Partially Completed)
    e. **Configuration**: Added Flask app configuration for `CACHE_DEFAULT_TTL` and `CACHE_DEFAULT_MAX_SIZE` in `app/__init__.py`, used by `app/utils/caching.py`. (Completed)
    f. **Testing**: Write unit tests for the caching utilities in `app/utils/caching.py` (key generation, decorator logic, invalidation) and integration tests for cached repository methods. (Pending)

**Notes**:
- The initial implementation uses `cachetools` for in-memory caching, configured via Flask app settings.
- Cache keys must be carefully designed to include all query parameters that affect the result to avoid stale data.
- Invalidation is crucial for maintaining data consistency.

#### 3.3 Optimize Query Patterns

**Current Issue**: Potentially inefficient query patterns.
**Status**: In Progress

**Implementation Steps**:
1. Use Context7 MCP to search for "Supabase query optimization" to get the latest query optimization patterns. (Completed - Found official Supabase/PostgreSQL documentation on indexing, query analysis (`EXPLAIN`), different index types (B-tree, GIN, BRIN, partial, composite), and tools like `index_advisor` and `ANALYZE`.)
2. Review and optimize query patterns in the repository classes (`app/repositories/`). This involves:
    a. Identifying frequently executed or slow queries. (Pending - Requires monitoring or profiling)
    b. Analyzing their execution plans using `EXPLAIN`. (Pending)
    c. Optimizing query structure: ensuring efficient joins, filtering early, avoiding `SELECT *` where possible, using appropriate functions. (Pending)
3. Implement proper indexing and query optimization based on the review:
    a. Add necessary indexes (B-tree, GIN, BRIN, partial, composite) to tables based on common query patterns (targeting `WHERE`, `JOIN`, `ORDER BY` clauses). Use `CREATE INDEX CONCURRENTLY` for large tables. (Pending)
    b. Regularly run `ANALYZE` on tables to keep statistics updated for the query planner. Consider automating this. (Pending)
    c. Monitor the impact of new indexes on read and write performance. Remove unused or detrimental indexes. (Pending)
    d. Utilize Supabase tools like the Index Advisor in the dashboard if applicable. (Pending)

**Notes**:
- Key optimization areas include indexing columns used in `WHERE` clauses, `JOIN` conditions, and `ORDER BY` clauses.
- Use `EXPLAIN` to understand query plans before and after changes.
- Choose appropriate index types (B-tree, GIN, BRIN, partial, composite) for the specific data and query patterns.
- Balance read performance gains from indexes against potential write performance degradation.
- Keep table statistics current using `ANALYZE`.
- Consider using `CREATE INDEX CONCURRENTLY` for large tables to avoid locking.

### 4. Flask Backend Improvements

The backend uses Flask for API routes and request handling.

#### 4.1 Implement Better Error Handling

**Current Issue**: Basic error handling without specific error types.
**Status**: Completed (Based on previous session summary)

**Implementation Steps**:
1. Use Context7 MCP to search for "Flask error handling" to get the latest error handling patterns.
2. Implement proper error handling middleware. (Completed - Global handlers in `app/__init__.py`)
3. Define custom error types and handlers. (Completed - `AppException` and specific exceptions in `app/utils/errors.py`, handled in `app/__init__.py`)
4. Implement consistent error responses across the API. (Completed - Routes refactored to raise custom exceptions)

**Notes**:
- Custom exceptions (`AppException`, `NotFoundException`, `ValidationException`, etc.) defined in `app/utils/errors.py`.
- Global error handlers registered in `app/__init__.py` for `AppException`, `HTTPException`, and generic `Exception`.
- Routes in `app/youtube/routes.py`, `app/feedback/routes.py`, `app/business/routes.py`, `app/auth/routes.py` and utility `app/utils/supabase_auth.py` were refactored to raise these custom exceptions.

#### 4.2 Optimize Route Handlers

**Current Issue**: Potentially inefficient route handlers, synchronous DB operations.
**Status**: In Progress

**Implementation Steps**:
1. Use Context7 MCP to search for "Flask route optimization" to get the latest route optimization patterns.
2. Review and optimize route handlers for better performance.
3. Implement proper caching and request validation. (Partially addressed with custom exceptions for validation)
4. Migrate repository methods and Supabase client interactions to be asynchronous. (In Progress)

**Notes on Async Refactoring and Repository Optimization**:
- **Goal**: Make all Supabase interactions non-blocking using `async/await` and `supabase-py`'s `AsyncClient`.
- **`requirements.txt`**: Updated `supabase>=2.0.0` to `supabase>=2.2.0`.
- **`app/models/supabase_client.py`**:
    - `get_supabase_client` changed to `async def` and returns `AsyncClient`.
    - Helper functions `get_table`, `get_auth`, `get_storage` made `async def`.
- **General Repository Refactoring Pattern**:
    - `__init__` methods no longer take synchronous `supabase_client`.
    - Data-access methods changed to `async def`.
    - Methods fetch async client internally (e.g., `client = await get_supabase_client()`).
    - Supabase operations are `await`ed.
    - Error handling updated (often re-raising `SupabaseRepositoryError`).
- **Specific Repositories Refactored (Async)**:
    - `app/repositories/supabase_repository.py` (Base class)
    - `app/repositories/supabase_user_repository.py`
    - `app/repositories/supabase_business_context_repository.py`
    - `app/repositories/supabase_content_item_repository.py` (FTS and tag search updated)
    - `app/repositories/supabase_feedback_repository.py`
    - `app/repositories/supabase_video_data_repository.py`
    - `app/repositories/wizard_session_repository.py` (Supabase parts async; in-memory sync)
    - `app/repositories/supabase_chat_session_repository.py` (Added based on file list from previous session)
    - `app/repositories/supabase_audience_analysis_repository.py` (Added based on file list from previous session)
    - **`app/repositories/activity_log_repository.py`**:
        - Refactored for async operations.
        - **Consolidated with `app/repositories/supabase_activity_log_repository.py` (which has been deleted).**
        - Implemented an **adapter pattern** to maintain its modern API (e.g., `activity_type`, `title`, `description`) while interacting with the existing older database schema (which uses `action_type` and a `details` JSONB field).
        - `log_activity` now accepts `ip_address` and maps fields appropriately (e.g., `activity_type` to `action_type`; `title`, `description`, `link_path` into `details` JSONB).
        - Retrieval methods (`get_user_activities`, etc.) map query parameters and transform results between API and DB representations.
        - Documented a 3-phase migration plan (Adapter -> DB Migration -> Remove Adapter) within the file comments.
- **Cache Decorator**: `@cache_supabase_query()` in `app/utils/caching.py` needs to be made async-compatible for use with async repository methods. (Status: Pending - this was noted as a general pending item).
- **Skipped/Pending Clarification**:
    - `app/repositories/supabase_content_repository.py`: Previously noted as skipped (potential duplicate of `supabase_content_item_repository.py`). Needs review and decision.

#### 4.3 Improve Testing Patterns

**Current Issue**: Limited test coverage.

**Implementation Steps**:
1. Use Context7 MCP to search for "Flask testing" to get the latest testing patterns.
2. Implement proper unit and integration tests.
3. Configure test coverage reporting.

### 5. General Architecture Improvements

#### 5.1 Implement TypeScript Types

**Current Issue**: Limited TypeScript types for API responses.

**Implementation Steps**:
1. Use Context7 MCP to search for "TypeScript API types" to get the latest TypeScript type patterns.
2. Define proper TypeScript interfaces for API requests and responses.
3. Implement consistent type checking across the application.

#### 5.2 Improve State Management

**Current Issue**: Inconsistent state management patterns.

**Implementation Steps**:
1. Use Context7 MCP to search for "React Context API" to get the latest state management patterns.
2. Use React Context API more effectively for feature-specific state.
3. Implement consistent state management patterns across the application.

#### 5.3 Add Error Boundaries

**Current Issue**: Limited error handling in the UI.

**Implementation Steps**:
1. Use Context7 MCP to search for "React error boundaries" to get the latest error boundary patterns.
2. Implement proper error boundaries for different parts of the application.
3. Add user-friendly error messages and recovery options.

## Implementation Priorities

1. **High Priority**:
   - OpenAI Integration Improvements (Error Handling, Streaming)
   - Material UI Optimizations (sx Prop, Accessibility)
   - Supabase Query Optimization

2. **Medium Priority**:
   - Async/Await Patterns
   - Pydantic Model Parsing
   - Responsive Designs
   - Error Boundaries

3. **Low Priority**:
   - CSS Variables
   - Supabase MCP
   - Supabase Cache Helpers
   - Testing Patterns

## Additional Resources

When implementing these recommendations, use Context7 MCP to search for the latest documentation and best practices for each library and framework. The search queries provided in each section should help you find the most relevant information.

For example:
- To get the latest OpenAI error handling patterns, search for "OpenAI error handling" in Context7 MCP.
- To get the latest Material UI sx prop usage patterns, search for "Material UI sx prop" in Context7 MCP.

## Conclusion

This implementation guide provides a comprehensive roadmap for improving the application using Context7 MCP documentation. By following these recommendations, you can enhance the performance, maintainability, and user experience of the application.

Remember to use Context7 MCP to search for the latest documentation and best practices for each library and framework as you implement these recommendations.