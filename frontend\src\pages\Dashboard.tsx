import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Typography,
  Button,
  useTheme,
  Avatar,
  CircularProgress,
} from "@mui/material";
import {
  LibraryBooks as LibraryBooksIcon,
  Business as BusinessIcon,
  Chat as ChatIcon,
  YouTube as YouTubeIcon,
  Add as AddIcon,
} from "@mui/icons-material";
import { motion } from "framer-motion";
import { selectCurrentUser } from "../store/auth/authSlice";
import {
  fetchUserActivities,
  selectActivities,
  selectActivityLoading,
} from "../store/activity/activitySlice";
import { AppDispatch } from "../store";

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 12,
    },
  },
};

const Dashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const user = useSelector(selectCurrentUser);
  const activities = useSelector(selectActivities);
  const isLoading = useSelector(selectActivityLoading);

  // Fetch user activities on component mount
  useEffect(() => {
    dispatch(fetchUserActivities(10));
  }, [dispatch]);

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return "Good morning";
    if (hour < 18) return "Good afternoon";
    return "Good evening";
  };

  // Feature cards data
  const features = [
    {
      title: "AI Assistant",
      description: "Generate content with AI assistance",
      icon: <ChatIcon fontSize="large" />,
      color: theme.palette.success.main,
      path: "/ai-assistant",
    },
    {
      title: "Search",
      description: "Analyze YouTube videos and find trends",
      icon: <YouTubeIcon fontSize="large" />,
      color: theme.palette.error.main,
      path: "/youtube-analytics",
    },
    {
      title: "Library",
      description: "Manage your content examples and templates",
      icon: <LibraryBooksIcon fontSize="large" />,
      color: theme.palette.primary.main,
      path: "/content-library",
    },
    {
      title: "Profile",
      description: "Define your brand voice and target audience",
      icon: <BusinessIcon fontSize="large" />,
      color: theme.palette.secondary.main,
      path: "/business-context",
    },
  ];

  // Get icon for activity type
  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case "chat":
        return <ChatIcon fontSize="small" />;
      case "search":
        return <YouTubeIcon fontSize="small" />;
      case "content":
        return <LibraryBooksIcon fontSize="small" />;
      default:
        return <AddIcon fontSize="small" />;
    }
  };

  // Get color for activity type
  const getActivityColor = (activityType: string) => {
    switch (activityType) {
      case "chat":
        return theme.palette.success.main;
      case "search":
        return theme.palette.error.main;
      case "content":
        return theme.palette.primary.main;
      default:
        return theme.palette.info.main;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            {getGreeting()}, {user?.firstName || "friend"}!
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Welcome to Writer v2. Let's create some amazing content today.
          </Typography>
        </Box>
      </motion.div>

      <Grid container spacing={3}>
        {/* Main features */}
        <Grid item xs={12} md={12}>
          <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
            Features
          </Typography>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Grid container spacing={2}>
              {features.map((feature, index) => (
                <Grid item xs={12} sm={6} key={index}>
                  <motion.div variants={itemVariants}>
                    <Card
                      sx={{
                        height: "100%",
                        position: "relative",
                        overflow: "hidden",
                        "&::before": {
                          content: '""',
                          position: "absolute",
                          top: 0,
                          left: 0,
                          width: "4px",
                          height: "100%",
                          backgroundColor: feature.color,
                        },
                      }}
                    >
                      <CardActionArea
                        onClick={() => navigate(feature.path)}
                        sx={{ height: "100%" }}
                      >
                        <CardContent sx={{ p: 3 }}>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              mb: 1.5,
                            }}
                          >
                            <Avatar
                              sx={{
                                bgcolor: `${feature.color}15`,
                                color: feature.color,
                                mr: 2,
                              }}
                            >
                              {feature.icon}
                            </Avatar>
                            <Typography variant="h6" fontWeight="bold">
                              {feature.title}
                            </Typography>
                          </Box>
                          <Typography variant="body2" color="text.secondary">
                            {feature.description}
                          </Typography>
                        </CardContent>
                      </CardActionArea>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
