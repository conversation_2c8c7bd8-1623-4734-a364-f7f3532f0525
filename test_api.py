#!/usr/bin/env python3
"""
Test script to verify API endpoints are working correctly.
This will help diagnose frontend-backend communication issues.
"""

import requests
import json
import sys

def test_endpoint(url, method="GET", data=None, headers=None):
    """Test a single API endpoint"""
    try:
        print(f"\n🔍 Testing {method} {url}")
        
        if method == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method == "POST":
            response = requests.post(url, json=data, headers=headers, timeout=10)
        else:
            print(f"❌ Unsupported method: {method}")
            return False
            
        print(f"   Status: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        
        try:
            json_data = response.json()
            print(f"   Response: {json.dumps(json_data, indent=2)}")
        except:
            print(f"   Response (text): {response.text}")
            
        return response.status_code < 400
        
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection failed - server not running or wrong URL")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ Request timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def main():
    """Test all critical API endpoints"""
    base_url = "http://localhost:5000"
    
    print("🚀 Testing AI Assistant API Endpoints")
    print("=" * 50)
    
    # Test basic health endpoints
    endpoints_to_test = [
        # Health checks
        (f"{base_url}/health", "GET"),
        (f"{base_url}/api/health", "GET"),
        (f"{base_url}/api/", "GET"),
        
        # Auth endpoints (no auth required)
        (f"{base_url}/api/auth/ping", "GET"),
        
        # Test what frontend would actually call
        (f"{base_url}/api/api/health", "GET"),  # This is what frontend might be calling
        (f"{base_url}/api/api/auth/ping", "GET"),  # This is what frontend might be calling
    ]
    
    results = []
    for url, method in endpoints_to_test:
        success = test_endpoint(url, method)
        results.append((url, method, success))
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    
    for url, method, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {method} {url}")
    
    # Check if we have the double /api issue
    api_health_works = any(success for url, method, success in results if "/api/health" in url and success)
    api_api_health_works = any(success for url, method, success in results if "/api/api/health" in url and success)
    
    if api_health_works and not api_api_health_works:
        print("\n🔍 DIAGNOSIS: Frontend-Backend URL Mismatch Detected!")
        print("   - Backend endpoints work at /api/health")
        print("   - Frontend likely making requests to /api/api/health")
        print("   - This suggests frontend baseURL is '/api' but backend routes have '/api' prefix")
        print("   - SOLUTION: Either remove '/api' prefix from backend routes OR change frontend baseURL")
    
    return all(success for _, _, success in results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
