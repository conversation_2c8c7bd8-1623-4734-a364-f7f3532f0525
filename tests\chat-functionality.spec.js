const { test, expect } = require('@playwright/test');

const TEST_USER = {
  email: '<EMAIL>',
  password: 'MK*9v9sseuu3#Z3qgypf'
};

test.describe('AI Assistant Chat Functionality', () => {
  test('should display both user message and AI response correctly', async ({ page }) => {
    // Enable console logging to capture any errors
    page.on('console', msg => {
      if (msg.type() === 'error' || msg.text().includes('[CHAT]')) {
        console.log(`[BROWSER ${msg.type().toUpperCase()}]:`, msg.text());
      }
    });

    // Step 1: Login
    console.log('🔐 Logging in...');
    await page.goto('http://localhost:3001/login');

    // Wait for page to load and dismiss any webpack error overlay
    await page.waitForTimeout(3000);

    // Try to dismiss webpack error overlay if present
    try {
      const dismissButton = page.locator('button:has-text("×"), button:has-text("Dismiss")').first();
      if (await dismissButton.isVisible({ timeout: 2000 })) {
        await dismissButton.click();
        console.log('✅ Dismissed webpack error overlay');
        await page.waitForTimeout(1000);
      }
    } catch (e) {
      console.log('ℹ️ No webpack error overlay to dismiss');
    }
    await page.waitForLoadState('networkidle');
    
    await page.fill('#email', TEST_USER.email);
    await page.fill('#password', TEST_USER.password);
    await page.click('button[type="submit"]');
    
    // Wait for login to complete
    await page.waitForTimeout(3000);
    await page.screenshot({ path: 'test-results/01-login-complete.png', fullPage: true });

    // Step 2: Navigate to AI Assistant
    console.log('🤖 Navigating to AI Assistant...');
    await page.goto('http://localhost:3001/ai-assistant');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    await page.screenshot({ path: 'test-results/02-ai-assistant-loaded.png', fullPage: true });

    // Step 3: Find chat interface elements
    console.log('🔍 Finding chat interface elements...');
    const messageInput = page.locator('#message-input, input[placeholder*="message"], textarea[placeholder*="message"]').first();
    const sendButton = page.locator('button:has-text("Send"), button[type="submit"]').last();
    
    await expect(messageInput).toBeVisible({ timeout: 10000 });
    console.log('✅ Message input found');

    // Step 4: Count messages before sending
    const messagesBefore = await page.locator('[data-testid="chat-message"]').count();
    console.log(`📊 Messages before sending: ${messagesBefore}`);

    // Step 5: Send test message
    const testMessage = 'Hello, please respond with a simple greeting.';
    console.log(`📝 Sending test message: "${testMessage}"`);
    
    await messageInput.fill(testMessage);
    await page.screenshot({ path: 'test-results/03-message-typed.png', fullPage: true });
    
    await sendButton.click();
    console.log('✅ Message sent');

    // Step 6: Wait for user message to appear
    console.log('⏳ Waiting for user message to appear...');
    await page.waitForTimeout(2000);
    
    const userMessage = page.locator('[data-testid="chat-message"][data-role="user"]').last();
    await expect(userMessage).toBeVisible({ timeout: 5000 });
    
    const userMessageText = await userMessage.textContent();
    expect(userMessageText).toContain(testMessage);
    console.log('✅ User message appeared and contains correct text');
    
    await page.screenshot({ path: 'test-results/04-user-message-appeared.png', fullPage: true });

    // Step 7: Wait for AI response to appear
    console.log('🤖 Waiting for AI response...');
    
    // Wait for assistant message to appear (up to 30 seconds)
    const assistantMessage = page.locator('[data-testid="chat-message"][data-role="assistant"]').last();
    await expect(assistantMessage).toBeVisible({ timeout: 30000 });
    console.log('✅ AI assistant message appeared');

    // Step 8: Verify AI response has content
    await page.waitForTimeout(2000); // Allow streaming to complete
    
    const assistantMessageText = await assistantMessage.textContent();
    console.log(`🔍 AI response text: "${assistantMessageText.substring(0, 100)}..."`);
    
    // Verify the response is not empty and not an error message
    expect(assistantMessageText.length).toBeGreaterThan(5);
    expect(assistantMessageText).not.toContain('Error');
    expect(assistantMessageText).not.toContain('timeout');
    expect(assistantMessageText).not.toContain('No response');
    console.log('✅ AI response has valid content');

    // Step 9: Verify message styling and positioning
    console.log('🎨 Verifying message styling...');
    
    // Check user message styling (should be right-aligned/blue)
    const userMessageBox = userMessage.locator('..'); // Parent container
    const userMessageStyle = await userMessageBox.evaluate(el => getComputedStyle(el).flexDirection);
    expect(userMessageStyle).toBe('row-reverse'); // Right-aligned
    console.log('✅ User message is right-aligned');

    // Check assistant message styling (should be left-aligned/gray)
    const assistantMessageBox = assistantMessage.locator('..');
    const assistantMessageStyle = await assistantMessageBox.evaluate(el => getComputedStyle(el).flexDirection);
    expect(assistantMessageStyle).toBe('row'); // Left-aligned
    console.log('✅ Assistant message is left-aligned');

    // Step 10: Verify no loading spinners are stuck
    const loadingSpinners = page.locator('.MuiCircularProgress-root');
    const spinnerCount = await loadingSpinners.count();
    console.log(`🔄 Loading spinners found: ${spinnerCount}`);
    
    if (spinnerCount > 0) {
      // Wait a bit more to see if they disappear
      await page.waitForTimeout(3000);
      const finalSpinnerCount = await loadingSpinners.count();
      expect(finalSpinnerCount).toBe(0);
    }
    console.log('✅ No stuck loading spinners');

    // Step 11: Final verification screenshot
    await page.screenshot({ path: 'test-results/05-final-chat-state.png', fullPage: true });

    // Step 12: Count final messages
    const messagesAfter = await page.locator('[data-testid="chat-message"]').count();
    console.log(`📊 Messages after: ${messagesAfter}`);
    expect(messagesAfter).toBe(messagesBefore + 2); // User message + AI response

    console.log('🎉 Chat functionality test PASSED!');
    console.log('✅ User message displayed correctly (blue, right-aligned)');
    console.log('✅ AI response displayed correctly (gray, left-aligned)');
    console.log('✅ Both messages have valid content');
    console.log('✅ No loading spinners stuck');
    console.log('✅ Message count increased by 2 as expected');
  });
});
