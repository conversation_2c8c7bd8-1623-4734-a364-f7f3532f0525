#!/bin/bash

# Standard development startup script (fallback if fast mode has issues)
echo "🚀 Starting Writer v2 in STANDARD development mode..."

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down --remove-orphans

# Start services normally
echo "🎯 Starting services..."
docker-compose up --build

echo "✅ Development environment started!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:5000/api"
echo "💾 Health Check: http://localhost:5000/api/health"