# Central config for all rules
version: 1.0.0

lint:
  frontend: "npm run lint --prefix frontend
             npm run format --prefix frontend"
  backend:  "ruff check .
             ruff format ."

logging:
  rotateAfterKB: 512        # Log rotation threshold
placeholders:
  failIfFound: true         # Abort on any [PLACEHOLDER] token
askThrottle:
  perTddStep: 3             # Max clarifying questions per TDD step
toolFail:
  extraRetryIf: ["network", "transient"]   # Labels that allow a 3rd retry
riskMatrix:
  low:
    description: "Docs, comments, styling, non‑executed assets"
  medium:
    description: "Utility functions, config tweaks, refactor w/out logic change"
  high:
    description: "Business logic, APIs, DB, security, data models"
testsFile: "memory-bank/tests.md"
