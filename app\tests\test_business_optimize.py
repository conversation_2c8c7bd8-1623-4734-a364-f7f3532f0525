import json
import pytest
from unittest.mock import patch, MagicMock

from app.utils.ai_integration import MockAIGenerator


@pytest.fixture
def mock_ai_generator():
    """Mock AI generator for testing"""
    generator = MockAIGenerator()
    return generator


@pytest.fixture
def mock_business_context():
    """Mock business context for testing"""
    return {
        "id": "test-id",
        "user_id": "test-user",
        "name": "Test Profile",
        "profile_overview": "This is a test profile",
        "content_focus": ["Marketing", "Sales"],
        "primary_objectives": "Increase leads",
        "offer_description": "Our product helps businesses grow",
        "target_audience": "Small business owners",
        "audience_motivation": "Looking to scale their business",
        "brand_voice": "Professional but friendly",
        "key_benefits": "Save time, increase revenue",
        "unique_value_proposition": "All-in-one solution",
        "audience_decision_style": "Analytical",
        "audience_preference_structure": "Prefers detailed information",
        "audience_decision_speed": "Takes time to decide",
        "audience_reaction_to_change": "Open to new ideas",
        "recommended_platforms": ["LinkedIn", "Email"],
        "recommended_content_formats": ["Blog posts", "Case studies"],
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z",
    }


@pytest.fixture
def mock_optimized_fields():
    """Mock optimized fields returned by AI"""
    return {
        "profile_overview": {
            "value": "Optimized profile overview",
            "why": "More concise and compelling"
        },
        "brand_voice": {
            "value": "Authoritative yet approachable",
            "why": "Better aligns with target audience"
        }
    }


def test_optimize_business_context(client, auth_headers, mock_business_context, mock_optimized_fields):
    """Test the optimize business context endpoint"""
    context_id = "test-id"
    
    # Mock the repository get_by_id method
    with patch("app.repositories.supabase_business_context_repository.BusinessContextRepository.get_by_id") as mock_get:
        mock_get.return_value = mock_business_context
        
        # Mock the AI generator
        with patch("app.ai_assistant.common.get_ai_generator") as mock_get_ai:
            mock_ai = MagicMock()
            mock_ai.generate_content.return_value = {
                "generated_text": json.dumps(mock_optimized_fields),
                "tokens_used": 350
            }
            mock_get_ai.return_value = mock_ai
            
            # Make the request
            response = client.post(
                f"/api/business/{context_id}/optimize",
                headers=auth_headers,
                json={"message": "Please optimize this profile"}
            )
            
            # Check the response
            assert response.status_code == 200
            data = json.loads(response.data)
            assert "original" in data
            assert "optimized" in data
            assert data["original"] == mock_business_context
            assert data["optimized"] == mock_optimized_fields
            assert data["tokens_used"] == 350
            
            # Verify the AI generator was called with the right parameters
            mock_ai.generate_content.assert_called_once()
            call_args = mock_ai.generate_content.call_args[1]
            assert call_args["business_context"] == mock_business_context
            assert "system_prompt" in call_args
            assert "max_tokens" in call_args


def test_optimize_business_context_not_found(client, auth_headers):
    """Test the optimize business context endpoint when context not found"""
    context_id = "nonexistent-id"
    
    # Mock the repository get_by_id method to return None
    with patch("app.repositories.supabase_business_context_repository.BusinessContextRepository.get_by_id") as mock_get:
        mock_get.return_value = None
        
        # Make the request
        response = client.post(
            f"/api/business/{context_id}/optimize",
            headers=auth_headers,
            json={"message": "Please optimize this profile"}
        )
        
        # Check the response
        assert response.status_code == 404
        data = json.loads(response.data)
        assert "error" in data
        assert "not found" in data["error"].lower()


def test_optimize_business_context_invalid_json(client, auth_headers, mock_business_context):
    """Test the optimize business context endpoint when AI returns invalid JSON"""
    context_id = "test-id"
    
    # Mock the repository get_by_id method
    with patch("app.repositories.supabase_business_context_repository.BusinessContextRepository.get_by_id") as mock_get:
        mock_get.return_value = mock_business_context
        
        # Mock the AI generator to return invalid JSON
        with patch("app.ai_assistant.common.get_ai_generator") as mock_get_ai:
            mock_ai = MagicMock()
            mock_ai.generate_content.return_value = {
                "generated_text": "This is not valid JSON",
                "tokens_used": 350
            }
            mock_get_ai.return_value = mock_ai
            
            # Make the request
            response = client.post(
                f"/api/business/{context_id}/optimize",
                headers=auth_headers,
                json={"message": "Please optimize this profile"}
            )
            
            # Check the response
            assert response.status_code == 500
            data = json.loads(response.data)
            assert "error" in data
            assert "parse" in data["error"].lower()
            assert "raw_result" in data
