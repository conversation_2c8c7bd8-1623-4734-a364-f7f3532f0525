import pytest
from fastapi.testclient import <PERSON><PERSON>lient
from unittest.mock import patch, MagicMock

from main import app  # Import the FastAPI app instance
from app.auth.dependencies import get_current_user
from app.dependencies import get_ai_generator, get_user_repository, get_business_context_repository
from app.models.user_profile import UserProfile
from app.models.business_context import BusinessContext

# --- Fixtures ---

@pytest.fixture
def client():
    """A test client for the FastAPI app."""
    with TestClient(app) as c:
        yield c

@pytest.fixture
def mock_auth_user():
    """Mock user returned by the authentication dependency."""
    user = MagicMock()
    user.id = "test-user-123"
    return user

@pytest.fixture
def mock_user_profile():
    """Mock user profile returned by the repository."""
    return UserProfile(
        id="profile-id-456",
        user_id="test-user-123",
        business_context_id="biz-context-789"
    )

@pytest.fixture
def mock_business_context():
    """Mock business context returned by the repository."""
    return BusinessContext(
        id="biz-context-789",
        user_id="test-user-123",
        profile_overview="Test business profile",
        content_focus=["AI", "testing"],
        target_audience="Developers",
        brand_voice="Informative"
    )

@pytest.fixture(autouse=True)
def override_dependencies(mock_auth_user, mock_user_profile, mock_business_context):
    """Override FastAPI dependencies for all tests in this module."""
    
    # --- Mock Repositories ---
    mock_user_repo = MagicMock()
    mock_user_repo.get_by_user_id.return_value = mock_user_profile
    
    mock_business_repo = MagicMock()
    mock_business_repo.get_by_id.return_value = mock_business_context
    
    # --- Mock AI Generator ---
    mock_ai_gen = MagicMock()
    # This simulates the async generator stream
    async def mock_stream_generator(*args, **kwargs):
        yield '{"event": "text_generation", "data": "This "}'
        yield '{"event": "text_generation", "data": "is "}'
        yield '{"event": "text_generation", "data": "a "}'
        yield '{"event": "text_generation", "data": "test."}'
        yield '{"event": "stream_end"}'

    mock_ai_gen.generate_response_stream = mock_stream_generator

    # --- Apply Overrides ---
    app.dependency_overrides[get_current_user] = lambda: mock_auth_user
    app.dependency_overrides[get_ai_generator] = lambda: mock_ai_gen
    app.dependency_overrides[get_user_repository] = lambda: mock_user_repo
    app.dependency_overrides[get_business_context_repository] = lambda: mock_business_repo
    
    yield

    # Teardown: clear the overrides
    app.dependency_overrides = {}


# --- Tests ---

def test_generate_content_success(client):
    """Test successful content generation."""
    payload = {
        "prompt": "Write a blog post about AI",
        "business_context_id": "biz-context-789"
    }
    
    response = client.post("/api/ai_assistant/generate-content", json=payload)
    
    # The actual endpoint might return different status codes
    # This is a basic test to ensure the endpoint is reachable
    assert response.status_code in [200, 422, 500]  # Accept various responses for now

def test_test_connection_endpoint(client):
    """Test the AI connection test endpoint."""
    response = client.get("/api/ai_assistant/test-connection")
    
    # Should return success or failure, but not crash
    assert response.status_code in [200, 500]

def test_get_ai_config_endpoint(client):
    """Test the AI configuration endpoint."""
    response = client.get("/api/ai_assistant/config")
    
    # Should return configuration or error
    assert response.status_code in [200, 500]

def test_format_transcript_endpoint(client):
    """Test the transcript formatting endpoint."""
    payload = {
        "transcript": "This is a test transcript.",
        "options": {}
    }
    
    response = client.post("/api/ai_assistant/format-transcript", json=payload)
    
    # Should process the request
    assert response.status_code in [200, 422, 500]

def test_missing_auth_returns_401(client):
    """Test that endpoints without auth return 401."""
    # Clear dependency overrides to test auth requirement
    app.dependency_overrides = {}
    
    response = client.get("/api/ai_assistant/test-connection")
    assert response.status_code == 401
    
    response = client.get("/api/ai_assistant/config")
    assert response.status_code == 401

def test_stream_chat_response_success(client):
    """Test successful streaming chat response."""
    payload = {
        "messages": [{"role": "user", "content": "Hello"}],
        "context": {"business_context_id": "biz-context-789"}
    }
    
    response = client.post("/api/ai/chat/stream", json=payload)
    
    assert response.status_code == 200
    assert "content-type" in response.headers and "text/event-stream" in response.headers["content-type"]
    
    stream_content = response.text
    assert 'data: {"event": "text_generation", "data": "This "}' in stream_content
    assert 'data: {"event": "stream_end"}' in stream_content

def test_stream_chat_response_missing_messages(client):
    """Test validation error when 'messages' field is missing."""
    payload = {"context": {}}
    response = client.post("/api/ai/chat/stream", json=payload)
    assert response.status_code == 422

def test_stream_chat_response_user_profile_not_found(client, mock_auth_user):
    """Test case where the user profile is not found."""
    # Temporarily override the repo for this specific test
    mock_repo = MagicMock()
    mock_repo.get_by_user_id.return_value = None  # Simulate not found
    app.dependency_overrides[get_user_repository] = lambda: mock_repo
    
    payload = {"messages": [{"role": "user", "content": "Hello"}], "context": {}}
    response = client.post("/api/ai/chat/stream", json=payload)
        
    assert response.status_code == 404
    assert "User profile not found" in response.json()["detail"]

def test_stream_chat_response_business_context_not_found(client):
    """Test case where the specified business context is not found."""
    mock_repo = MagicMock()
    mock_repo.get_by_id.return_value = None  # Simulate not found
    app.dependency_overrides[get_business_context_repository] = lambda: mock_repo

    payload = {
        "messages": [{"role": "user", "content": "Hello"}],
        "context": {"business_context_id": "non-existent-id"}
    }
    response = client.post("/api/ai/chat/stream", json=payload)
        
    assert response.status_code == 404
    assert "Business context not found" in response.json()["detail"] 