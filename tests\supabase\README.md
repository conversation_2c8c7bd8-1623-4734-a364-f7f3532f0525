# Supabase Testing Suite

This directory contains the comprehensive testing suite for the Writer v2 Supabase migration project. The testing tools have been designed to validate all aspects of the migration and ensure a smooth transition from PostgreSQL to Supabase.

## Test Categories

The test suite includes several categories of tests:

1. **Repository Unit Tests** - Tests for individual repository implementations
2. **Repository Integration Tests** - Tests for cross-repository interactions
3. **Data Migration Tests** - Tests for data integrity during migration
4. **API Comparison Tests** - Tests comparing API responses between PostgreSQL and Supabase
5. **Performance Benchmark Tests** - Tests measuring performance differences
6. **Docker Configuration Tests** - Tests for Docker setup with Supabase

## Test Files

### Repository Unit Tests

- `test_activity_log_repository.py` - Tests for ActivityLogRepository
- `test_content_repository.py` - Tests for ContentRepository
- `test_business_context_repository.py` - Tests for BusinessContextRepository
- `test_chat_session_repository.py` - Tests for ChatSessionRepository
- `test_youtube_video_repository.py` - Tests for YouTubeVideoRepository
- `test_feedback_repository.py` - Tests for FeedbackRepository

### Integration Tests

- `test_repository_integration.py` - Tests for interactions between repositories

### Common Test Fixtures

- `conftest.py` - Common fixtures and utilities for Supabase tests

## Test Runners

The following batch files automate the running of tests:

### `run_repository_tests.bat`

Runs all repository unit tests and generates a summary report.

```
run_repository_tests.bat
```

Features:
- Creates a timestamped results directory
- Runs individual tests for each repository
- Generates logs for each test run
- Compiles a summary of passed/failed tests
- Returns appropriate exit code based on test results

### `run_performance_benchmark.bat`

Runs performance benchmarks comparing PostgreSQL and Supabase.

```
run_performance_benchmark.bat [options]
```

Options:
- `--url URL` - The base URL of the API (default: http://localhost:5000)
- `--iterations N` - Number of iterations per endpoint (default: 10)
- `--concurrency N` - Concurrency level (default: 1)
- `--mode MODE` - Test mode: quick, standard, comprehensive, stress (default: comprehensive)

Features:
- Multiple testing modes for different scenarios
- Concurrent request testing to simulate load
- Detailed performance reports with comparisons
- Statistical analysis and recommendations

### `benchmark_performance.py`

Python script that performs the actual benchmarking:

```
python benchmark_performance.py --url URL --iterations N --concurrency N
```

Features:
- Tests multiple API endpoints
- Compares response times between PostgreSQL and Supabase
- Generates detailed CSV data and summary reports
- Provides statistical analysis of performance differences

## Main Test Runner

The `run_supabase_tests.bat` file in the project root runs all tests:

```
run_supabase_tests.bat
```

This comprehensive test runner:
1. Runs a dry run of the data migration
2. Performs data validation tests
3. Runs dual database API tests
4. Tests Docker configuration
5. Runs unit tests for all repositories
6. Runs a quick performance benchmark
7. Generates a complete summary report

## Setup for Testing

### Prerequisites

- Python 3.8+
- pytest installed (`pip install pytest`)
- Supabase project set up
- Environment variables configured:
  - `SUPABASE_URL`
  - `SUPABASE_KEY`

### Environment Setup

1. Create a `.env.test` file with test configuration
2. Set `USE_SUPABASE=true` to enable Supabase for testing
3. Ensure test database is properly initialized

## Test Results

Test results are stored in:
- `test_results/` - For repository tests
- `benchmark_results/` - For performance benchmarks

Each test run creates a timestamped directory with:
- Log files for each test
- Summary report with pass/fail information
- Performance statistics (if applicable)

## Adding New Tests

To add new tests:

1. Create a new test file following the naming convention `test_*.py`
2. Use the fixtures from `conftest.py` as needed
3. Update `run_repository_tests.bat` if it's a repository test
4. Follow the existing patterns for test structure

## Troubleshooting Common Issues

### Authentication Failures

If tests fail with authentication errors:
- Verify your Supabase URL and key in the environment variables
- Check that RLS policies are properly configured
- Ensure the test user has appropriate permissions

### Test Data Conflicts

If tests fail due to conflicting test data:
- Each test should use unique IDs and cleanup after itself
- Use the `test_*_id` fixtures to generate unique IDs
- Verify the `test_user` fixture is being used properly

### Performance Test Variations

If performance tests show inconsistent results:
- Increase the number of iterations for more stable averages
- Run tests at different times of day to account for system load
- Use the comprehensive mode to get a better overall picture

## Additional Documentation

For more details, see:
- `Set Up/Supabase_Testing_Summary.md` - Complete testing strategy
- `Set Up/Supabase_Migration_Plan.md` - Overall migration plan
- `Set Up/Supabase_Migration_Progress_Summary.md` - Current progress 