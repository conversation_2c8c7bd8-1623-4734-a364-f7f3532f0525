# app/repositories/wizard_session_repository.py
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from uuid import UUID

from .supabase_repository import SupabaseRepository, SupabaseRepositoryError

logger = logging.getLogger(__name__)

class WizardSessionRepository(SupabaseRepository):
    """Repository for managing wizard session data in Supabase."""

    def __init__(self, supabase_client=None):
        super().__init__(table_name="wizard_sessions")
        self._in_memory_sessions = {}

    async def create_session(self, user_id: UUID, business_context_id: UUID, content_idea: str) -> Optional[Dict[str, Any]]:
        """Creates a new wizard session based on a business context and content idea asynchronously."""
        try:
            table_exists = await self._check_table_exists()
            if not table_exists:
                logger.warning(f"wizard_sessions table doesn't exist or is missing required columns. Using in-memory session.")
                session_id = str(uuid.uuid4())
                in_memory_session = {
                    "id": session_id,
                    "user_id": str(user_id),
                    "business_context_id": str(business_context_id), # Store context id for in-memory
                    "content_idea": content_idea,
                    "initial_topic": content_idea,
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "_in_memory": True
                }
                in_memory_key = f"{user_id}:{session_id}"
                self._in_memory_sessions[in_memory_key] = in_memory_session
                return in_memory_session

            session_data = {
                "user_id": str(user_id),
                "business_context_id": str(business_context_id),
                "content_idea": content_idea,
                "initial_topic": content_idea,
            }
            result = await self.create(session_data) # Call async base method
            logger.info(f"Created wizard session for user {user_id} with context ID {business_context_id} and idea: {content_idea}")
            return result
        except Exception as e:
            logger.error(f"Error creating wizard session: {e}")
            logger.warning(f"Using in-memory session due to database error.")
            session_id = str(uuid.uuid4())
            in_memory_session = {
                "id": session_id,
                "user_id": str(user_id),
                "business_context_id": str(business_context_id),
                "content_idea": content_idea,
                "initial_topic": content_idea,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "_in_memory": True
            }
            in_memory_key = f"{user_id}:{session_id}"
            self._in_memory_sessions[in_memory_key] = in_memory_session
            return in_memory_session

    async def _check_table_exists(self) -> bool:
        """Check if the wizard_sessions table exists asynchronously."""
        try:
            table = await self._get_table()
            await table.select("id").limit(1).execute()
            return True
        except Exception as e:
            logger.error(f"Error checking wizard_sessions table: {e}")
            return False

    async def get_session(self, session_id: UUID, user_id: UUID) -> Optional[Dict[str, Any]]:
        """Retrieves a specific wizard session by its ID asynchronously, ensuring user ownership."""
        try:
            if hasattr(self, '_in_memory_sessions'):
                in_memory_key = f"{user_id}:{session_id}"
                if in_memory_key in self._in_memory_sessions:
                    logger.info(f"Retrieved in-memory session {session_id} for user {user_id}")
                    return self._in_memory_sessions[in_memory_key]
            try:
                table = await self._get_table()
                query = table.select("*")\
                          .eq("id", str(session_id))\
                          .eq("user_id", str(user_id))\
                          .limit(1)
                response = await query.execute()
                return response.data[0] if response.data else None
            except Exception as db_e:
                logger.error(f"Database error fetching wizard session {session_id}: {db_e}")
                if hasattr(self, '_in_memory_sessions'):
                    in_memory_key = f"{user_id}:{session_id}"
                    if in_memory_key in self._in_memory_sessions:
                        logger.warning(f"Falling back to in-memory session {session_id} after database error")
                        return self._in_memory_sessions[in_memory_key]
                return None
        except Exception as e:
            logger.error(f"Error fetching wizard session {session_id} for user {user_id}: {e}")
            return None # Or raise

    async def update_stage(self, session_id: UUID, user_id: UUID, stage_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Updates a specific stage's data within a wizard session asynchronously."""
        try:
            session = await self.get_session(session_id, user_id)
            if not session:
                logger.warning(f"Session {session_id} not found or doesn't belong to user {user_id}")
                return None

            if session.get("_in_memory", False):
                logger.warning(f"Using in-memory session {session_id} for update")
                session.update(stage_data)
                session["updated_at"] = datetime.now().isoformat()
                in_memory_key = f"{user_id}:{session_id}"
                self._in_memory_sessions[in_memory_key] = session
                return session

            if "user_id" in stage_data: del stage_data["user_id"]
            if "id" in stage_data: del stage_data["id"]

            try:
                table = await self._get_table()
                query = table.update(stage_data)\
                           .eq("id", str(session_id))\
                           .eq("user_id", str(user_id))
                response = await query.execute()
                logger.info(f"Updated stage data for wizard session {session_id}, keys: {list(stage_data.keys())}")
                return response.data[0] if response.data else None
            except Exception as db_e:
                logger.error(f"Database error updating wizard session {session_id}: {db_e}")
                session.update(stage_data)
                session["updated_at"] = datetime.now().isoformat()
                session["_in_memory"] = True
                in_memory_key = f"{user_id}:{session_id}"
                self._in_memory_sessions[in_memory_key] = session
                logger.warning(f"Falling back to in-memory session update for {session_id}")
                return session
        except Exception as e:
            logger.error(f"Error updating wizard session {session_id}: {e}")
            return None # Or raise

    async def delete_session(self, session_id: UUID, user_id: UUID) -> bool:
        """Deletes a wizard session asynchronously, ensuring user ownership."""
        try:
            # Also remove from in-memory cache if present
            if hasattr(self, '_in_memory_sessions'):
                in_memory_key = f"{user_id}:{session_id}"
                if in_memory_key in self._in_memory_sessions:
                    del self._in_memory_sessions[in_memory_key]
                    logger.info(f"Deleted in-memory session {session_id} for user {user_id}")
                    # If it was only in-memory, no DB delete needed
                    # This depends on whether an in-memory session can exist if DB op failed earlier or table missing
                    # For now, assume if in-memory deleted, it might be sufficient for that specific state.
                    # However, a DB delete should still be attempted if it might exist there.

            table = await self._get_table()
            response = await (table.delete()\
                       .eq("id", str(session_id))\
                       .eq("user_id", str(user_id))\
                       .execute())
            # Check if response.data is populated or if count is available for delete
            # Supabase delete often returns the deleted items (if select() was chained, or by default for some clients)
            # If data is present and non-empty, it was deleted.
            # If not, it might not have existed or RLS prevented delete.
            if response.data: 
                 logger.info(f"Deleted wizard session {session_id} from DB for user {user_id}")
                 return True
            # If response.data is empty, it means no rows matched the delete criteria.
            # This could mean it was already deleted, or never existed for this user in DB.
            # If an in-memory version was deleted above, consider it a success for that part.
            # Returning False if DB delete yielded no data.
            logger.warning(f"No wizard session {session_id} found in DB for user {user_id} to delete, or RLS prevented.")
            return False
        except Exception as e:
            self.handle_error(e, f"deleting wizard session {session_id}") # handle_error takes (exception, message)
            raise # Re-raise SupabaseRepositoryError

    async def list_sessions(self, user_id: UUID, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
         """Lists wizard sessions for a user asynchronously."""
         db_sessions = []
         try:
             table = await self._get_table()
             response = await (table.select("*")\
                       .eq("user_id", str(user_id))\
                       .order("created_at", desc=True)\
                       .range(offset, offset + limit - 1)\
                       .execute())
             db_sessions = response.data if response.data else []
         except Exception as e:
             self.handle_error(e, f"listing wizard sessions for user {user_id}")
             # Don't return [], let error propagate or be handled by caller
             raise # Re-raise

         # Combine with in-memory sessions for this user, if any (this part is tricky with pagination)
         # For simplicity, this example won't merge paginated DB results with all in-memory results.
         # A more robust solution would require careful merging or always relying on DB after initial creation attempt.
         # For now, just returning DB sessions.
         # If in-memory sessions are critical to list, they need a separate listing mechanism or a unified view.
         return db_sessions