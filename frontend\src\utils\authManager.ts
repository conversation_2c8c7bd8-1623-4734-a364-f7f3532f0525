/**
 * Centralized Authentication Manager
 * 
 * This utility provides centralized management of authentication state
 * and coordinates refresh attempts across the application to prevent
 * rate limiting issues.
 */

import { supabase } from '../lib/supabase';

interface AuthState {
  isRefreshing: boolean;
  lastRefreshAttempt: number;
  consecutiveFailures: number;
  refreshPromise: Promise<any> | null;
}

// Centralized auth state
const authState: AuthState = {
  isRefreshing: false,
  lastRefreshAttempt: 0,
  consecutiveFailures: 0,
  refreshPromise: null,
};

// Rate limiting constants - Reduced for better UX
const REFRESH_COOLDOWN = 1000; // 1 second (reduced from 5)
const MAX_CONSECUTIVE_FAILURES = 5; // Increased tolerance
const FAILURE_BACKOFF_BASE = 1000; // 1 second (reduced from 2)

// Event listeners for auth state changes
type AuthEventListener = (event: 'refresh_start' | 'refresh_success' | 'refresh_failure', data?: any) => void;
const listeners: AuthEventListener[] = [];

/**
 * Add an event listener for auth state changes
 */
export const addAuthEventListener = (listener: AuthEventListener): (() => void) => {
  listeners.push(listener);
  
  // Return cleanup function
  return () => {
    const index = listeners.indexOf(listener);
    if (index > -1) {
      listeners.splice(index, 1);
    }
  };
};

/**
 * Emit auth events to all listeners
 */
const emitAuthEvent = (event: 'refresh_start' | 'refresh_success' | 'refresh_failure', data?: any) => {
  listeners.forEach(listener => {
    try {
      listener(event, data);
    } catch (error) {
      console.error('Error in auth event listener:', error);
    }
  });
};

/**
 * Check if a refresh attempt is allowed based on rate limiting
 */
const canAttemptRefresh = (): { allowed: boolean; reason?: string; waitTime?: number } => {
  const now = Date.now();
  
  // Check cooldown period
  if (now - authState.lastRefreshAttempt < REFRESH_COOLDOWN) {
    const waitTime = REFRESH_COOLDOWN - (now - authState.lastRefreshAttempt);
    return {
      allowed: false,
      reason: 'cooldown',
      waitTime
    };
  }
  
  // Check consecutive failures with linear backoff (not exponential)
  if (authState.consecutiveFailures > 0) {
    const backoffTime = FAILURE_BACKOFF_BASE * authState.consecutiveFailures; // Linear instead of exponential
    if (now - authState.lastRefreshAttempt < backoffTime) {
      const waitTime = backoffTime - (now - authState.lastRefreshAttempt);
      return {
        allowed: false,
        reason: 'backoff',
        waitTime
      };
    }
  }
  
  // Check max failures
  if (authState.consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
    return {
      allowed: false,
      reason: 'max_failures'
    };
  }
  
  return { allowed: true };
};

/**
 * Coordinated session refresh that prevents multiple simultaneous attempts
 */
export const coordinatedSessionRefresh = async (): Promise<any> => {
  // If already refreshing, wait for the existing promise
  if (authState.refreshPromise) {
    console.log('Waiting for existing refresh operation...');
    return authState.refreshPromise;
  }
  
  // Check if refresh is allowed
  const canRefresh = canAttemptRefresh();
  if (!canRefresh.allowed) {
    const waitTime = canRefresh.waitTime ? Math.ceil(canRefresh.waitTime / 1000) : 0;
    let errorMessage = 'Authentication refresh not allowed';
    
    switch (canRefresh.reason) {
      case 'cooldown':
        errorMessage = `Authentication refresh rate limited. Please wait ${waitTime} seconds.`;
        break;
      case 'backoff':
        errorMessage = `Authentication refresh in backoff period. Please wait ${waitTime} seconds.`;
        break;
      case 'max_failures':
        errorMessage = 'Maximum authentication refresh attempts exceeded. Please log in again.';
        break;
    }
    
    throw new Error(errorMessage);
  }
  
  // Start refresh process
  authState.isRefreshing = true;
  authState.lastRefreshAttempt = Date.now();
  
  emitAuthEvent('refresh_start');
  
  // Create and store the refresh promise
  authState.refreshPromise = (async () => {
    try {
      console.log('Starting coordinated session refresh...');
      
      // Get current session
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      
      if (!currentSession?.refresh_token) {
        throw new Error('No refresh token available');
      }
      
      // Attempt refresh
      const { data, error } = await supabase.auth.refreshSession(currentSession);
      
      if (error) {
        throw new Error(`Session refresh failed: ${error.message}`);
      }
      
      if (!data.session) {
        throw new Error('Session refresh returned no session');
      }
      
      // Success - reset failure count
      authState.consecutiveFailures = 0;
      console.log('Coordinated session refresh successful');
      
      emitAuthEvent('refresh_success', data.session);
      return data.session;
      
    } catch (error) {
      // Increment failure count
      authState.consecutiveFailures++;
      console.error(`Session refresh failed (attempt ${authState.consecutiveFailures}):`, error);
      
      emitAuthEvent('refresh_failure', error);
      throw error;
    }
  })();
  
  try {
    const result = await authState.refreshPromise;
    return result;
  } finally {
    // Clean up
    authState.isRefreshing = false;
    authState.refreshPromise = null;
  }
};

/**
 * Get current auth state for debugging
 */
export const getAuthState = () => ({
  ...authState,
  canRefresh: canAttemptRefresh()
});

/**
 * Reset auth state (useful for testing or after successful login)
 */
export const resetAuthState = () => {
  authState.isRefreshing = false;
  authState.lastRefreshAttempt = 0;
  authState.consecutiveFailures = 0;
  authState.refreshPromise = null;
  console.log('Auth state reset');
};

/**
 * Check if currently refreshing
 */
export const isCurrentlyRefreshing = () => authState.isRefreshing;

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).authManager = {
    getAuthState,
    resetAuthState,
    coordinatedSessionRefresh,
    isCurrentlyRefreshing
  };
  
  console.log('🔧 Auth manager available at window.authManager');
}
