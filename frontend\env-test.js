// Load environment variables from .env files
require('dotenv').config();

console.log('=== ENVIRONMENT VARIABLES TEST ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('All environment variables:', Object.keys(process.env));
console.log('All REACT_APP_ variables:', Object.keys(process.env).filter(key => key.startsWith('REACT_APP_')));
// console.log('REACT_APP_OPENROUTER_API_KEY exists:', !!process.env.REACT_APP_OPENROUTER_API_KEY);
// console.log('REACT_APP_OPENROUTER_API_KEY value:', process.env.REACT_APP_OPENROUTER_API_KEY);
console.log('=== END TEST ==='); 