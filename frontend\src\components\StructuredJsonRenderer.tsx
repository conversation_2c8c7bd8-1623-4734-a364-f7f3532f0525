import React from "react";
import { Typography, List, ListItem, Box } from "@mui/material";
import { stripFences } from "../utils/stripFences";
import MarkdownRenderer from "./MarkdownRenderer";

interface Props {
  text: string;
}

interface ParsedScript {
  hook?: string;
  outline?: string[];
  cta?: string;
  [key: string]: any; // Allow other potential keys
}

const StructuredJsonRenderer: React.FC<Props> = ({ text }) => {
  let data: ParsedScript | null = null;
  let parseError = false;

  try {
    const cleanedText = stripFences(text);
    // Attempt to parse only if it looks like JSO<PERSON> (starts with { or [)
    if (cleanedText.startsWith("{") || cleanedText.startsWith("[")) {
      data = JSON.parse(cleanedText);
    } else {
      // If it doesn't look like JSO<PERSON>, treat it as plain text for MarkdownRenderer
      parseError = true;
    }
  } catch (error) {
    console.warn("Failed to parse message content as JSON:", error);
    parseError = true;
  }

  // If parsing failed or it wasn't J<PERSON><PERSON>, fall back to <PERSON><PERSON><PERSON><PERSON><PERSON>
  if (parseError || !data || typeof data !== "object") {
    return <MarkdownRenderer content={text} />;
  }

  // Render the known structured fields
  return (
    <Box>
      {data.hook && (
        <Box mb={2}>
          <Typography
            variant="overline"
            display="block"
            gutterBottom
            sx={{ fontWeight: "bold" }}
          >
            Hook
          </Typography>
          <Typography variant="body2">{data.hook}</Typography>
        </Box>
      )}
      {data.outline &&
        Array.isArray(data.outline) &&
        data.outline.length > 0 && (
          <Box mb={2}>
            <Typography
              variant="overline"
              display="block"
              gutterBottom
              sx={{ fontWeight: "bold" }}
            >
              Outline
            </Typography>
            <List
              dense
              sx={{
                pl: 2,
                listStyleType: "disc",
                "& .MuiListItem-root": { display: "list-item", p: 0 },
              }}
            >
              {data.outline.map((item, index) => (
                <ListItem key={index}>
                  <Typography variant="body2">{item}</Typography>
                </ListItem>
              ))}
            </List>
          </Box>
        )}
      {data.cta && (
        <Box>
          <Typography
            variant="overline"
            display="block"
            gutterBottom
            sx={{ fontWeight: "bold" }}
          >
            CTA
          </Typography>
          <Typography variant="body2">{data.cta}</Typography>
        </Box>
      )}
      {/* Optional: Render any other keys found in the JSON */}
      {/* {Object.keys(data).filter(key => !['hook', 'outline', 'cta'].includes(key)).map(key => (...)) } */}
    </Box>
  );
};

export default StructuredJsonRenderer;
