import React, { useState, useEffect } from "react";
import TextField from "@mui/material/TextField";
import Autocomplete, { createFilterOptions } from "@mui/material/Autocomplete";
import Chip from "@mui/material/Chip";

interface TagInputProps {
  label: string;
  value: string[];
  onChange: (newValue: string[]) => void;
  placeholder?: string;
  suggestions?: string[]; // Optional predefined suggestions
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
}

// Filter options for Autocomplete, allows creating new tags
const filter = createFilterOptions<string>();

const TagInput: React.FC<TagInputProps> = ({
  label,
  value = [], // Default to empty array
  onChange,
  placeholder = "Type and press Enter",
  suggestions = [],
  disabled = false,
  error = false,
  helperText = "",
}) => {
  const handleAutocompleteChange = (
    event: React.SyntheticEvent,
    newValue: (string | { inputValue: string; title: string })[], // Can be string or object for new values
    reason: string
  ) => {
    const processedValues = newValue
      .map((item) => {
        if (typeof item === "string") {
          return item.trim();
        } else if (item && item.inputValue) {
          // Handle adding a new tag created via input
          return item.inputValue.trim();
        }
        return ""; // Should not happen with standard config
      })
      .filter((tag) => tag !== ""); // Filter out empty tags

    // Remove duplicates before calling onChange
    const uniqueValues = Array.from(new Set(processedValues));
    onChange(uniqueValues);
  };

  return (
    <Autocomplete
      multiple
      freeSolo // Allows typing values not in suggestions
      options={suggestions} // Provide suggestions if available
      value={value}
      onChange={handleAutocompleteChange}
      disabled={disabled}
      filterOptions={(options, params) => {
        const filtered = filter(options, params);
        const { inputValue } = params;
        // Suggest the creation of a new value
        const isExisting = options.some((option) => inputValue === option);
        if (inputValue !== "" && !isExisting) {
          filtered.push(inputValue); // Simplified: just push the string
          // Or use the object format if needed for display:
          // filtered.push({
          //   inputValue: inputValue,
          //   title: `Add "${inputValue}"`,
          // });
        }
        return filtered;
      }}
      getOptionLabel={(option) => {
        // Handle both string options and potential creation option objects
        if (typeof option === "string") {
          return option;
        }
        // if (option.inputValue) {
        //   return option.title; // Display text for the creation option
        // }
        return option; // Fallback
      }}
      renderTags={(tagValue, getTagProps) =>
        tagValue.map((option, index) => (
          <Chip
            label={option}
            size="small"
            variant="outlined"
            {...getTagProps({ index })}
            disabled={disabled}
          />
        ))
      }
      renderInput={(params) => (
        <TextField
          {...params}
          variant="outlined" // Use outlined variant for consistency
          label={label}
          placeholder={placeholder}
          error={error}
          helperText={helperText}
          disabled={disabled}
        />
      )}
      sx={{ width: "100%" }} // Ensure it takes full width
    />
  );
};

export default TagInput;
