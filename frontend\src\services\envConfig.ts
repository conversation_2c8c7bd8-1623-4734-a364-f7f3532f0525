/**
 * Environment configuration utility
 * This file provides a consistent way to access environment variables
 * across the application, whether they come from process.env or window.ENV
 */

// Define the shape of our environment variables
interface EnvConfig {
  REACT_APP_API_URL: string;
  REACT_APP_ENV: string;
  // DOCKER_ENV removed
  SUPABASE_URL: string;
  SUPABASE_KEY: string;
}

// Get environment variables from window.ENV or process.env
const getEnvConfig = (): EnvConfig => {
  // Check if window.ENV is available (set by env-config.js)
  if (typeof window !== "undefined" && window.ENV) {
    return {
      REACT_APP_API_URL:
        window.ENV.REACT_APP_API_URL ||
        process.env.REACT_APP_API_URL ||
        "/api", // Changed to relative URL for production
      REACT_APP_ENV:
        window.ENV.REACT_APP_ENV || process.env.REACT_APP_ENV || "development",
      // DOCKER_ENV removed
      SUPABASE_URL:
        window.ENV.SUPABASE_URL ||
        process.env.REACT_APP_SUPABASE_URL ||
        (() => {
          throw new Error("SUPABASE_URL is required but not provided");
        })(),
      SUPABASE_KEY:
        window.ENV.SUPABASE_KEY ||
        process.env.REACT_APP_SUPABASE_KEY ||
        (() => {
          throw new Error("SUPABASE_KEY is required but not provided");
        })(),
    };
  }

  // Fallback to process.env
  return {
    REACT_APP_API_URL: process.env.REACT_APP_API_URL || "/api", // Changed to relative URL
    REACT_APP_ENV: process.env.REACT_APP_ENV || "development",
    // DOCKER_ENV removed
    SUPABASE_URL:
      process.env.REACT_APP_SUPABASE_URL ||
      (() => {
        throw new Error("SUPABASE_URL is required but not provided");
      })(),
    SUPABASE_KEY:
      process.env.REACT_APP_SUPABASE_KEY ||
      (() => {
        throw new Error("SUPABASE_KEY is required but not provided");
      })(),
  };
};

// Export the environment config
export const envConfig = getEnvConfig();

// Log the configuration on load (for debugging)
console.log("Environment configuration loaded:", {
  API_URL: envConfig.REACT_APP_API_URL,
  ENV: envConfig.REACT_APP_ENV,
  // DOCKER_ENV removed from log
  SUPABASE_URL: envConfig.SUPABASE_URL,
  SUPABASE_KEY: envConfig.SUPABASE_KEY
    ? `${envConfig.SUPABASE_KEY.substring(0, 10)}...`
    : "not set",
});

// Export individual environment variables for convenience
export const API_URL = envConfig.REACT_APP_API_URL;
export const ENV = envConfig.REACT_APP_ENV;
// IS_DOCKER removed
export const SUPABASE_URL = envConfig.SUPABASE_URL;
export const SUPABASE_KEY = envConfig.SUPABASE_KEY;

// Add TypeScript declaration for window.ENV
declare global {
  interface Window {
    ENV?: {
      REACT_APP_API_URL?: string;
      REACT_APP_ENV?: string;
      // DOCKER_ENV removed from Window interface
      SUPABASE_URL?: string;
      SUPABASE_KEY?: string;
    };
  }
}
