import React from 'react';
import { Box } from '@mui/material';
import BusinessContextSidebar from './BusinessContextSidebar';
import WizardErrorBoundary from './WizardErrorBoundary';

interface WizardLayoutProps {
  children: React.ReactNode;
  businessContextId: string;
  onBusinessContextChange: (id: string | null) => void;
}

export const WizardLayout: React.FC<WizardLayoutProps> = ({
  children,
  businessContextId,
  onBusinessContextChange,
}) => {
  // Create a stub context for the sidebar
  const contextStub = {
    id: businessContextId,
    name: `Context ${businessContextId.substring(0, 6)}`
  };

  return (
    <WizardErrorBoundary>
      <Box sx={{ display: 'flex' }}>
        <BusinessContextSidebar
          contexts={[contextStub]}
          currentContextId={businessContextId}
          onContextSelect={onBusinessContextChange}
          onNewContextClick={() => {}}
          onDeleteContexts={() => {}}
        />
        <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
          {children}
        </Box>
      </Box>
    </WizardErrorBoundary>
  );
};