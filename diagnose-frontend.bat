@echo off
echo ========================================
echo FRONTEND DIAGNOSTIC SCRIPT
echo ========================================
echo.

echo [1/6] Checking container status...
docker-compose ps
echo.

echo [2/6] Checking port bindings...
docker port writerv2-frontend-1 2>nul || echo Frontend container not running
docker port writerv2-backend-1 2>nul || echo Backend container not running
echo.

echo [3/6] Testing network connectivity...
echo Testing frontend connection...
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:3000 2>nul || echo FAILED - Cannot reach frontend
echo.
echo Testing backend connection...
curl -s -o nul -w "HTTP Status: %%{http_code}" http://localhost:5000/api/health 2>nul || echo FAILED - Cannot reach backend
echo.

echo [4/6] Getting frontend logs (last 30 lines)...
docker-compose logs --tail=30 frontend
echo.

echo [5/6] Checking React dev server binding...
docker-compose exec frontend printenv | findstr HOST 2>nul || echo HOST environment variable not set
echo.

echo [6/6] DIAGNOSIS COMPLETE
echo ========================================
echo NEXT STEPS:
echo 1. If you see "webpack compiled successfully" above = BUILD OK
echo 2. If you see "Local: http://0.0.0.0:3000" = BINDING OK  
echo 3. If HTTP Status shows 200 = NETWORK OK
echo 4. If any step fails, the issue is identified above
echo ========================================
pause