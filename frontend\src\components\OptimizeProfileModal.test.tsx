import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import OptimizeProfileModal from './OptimizeProfileModal';
import { api } from '../services/api';

// Mock the API
jest.mock('../services/api', () => ({
  api: {
    post: jest.fn(),
  },
}));

describe('OptimizeProfileModal', () => {
  const mockBusinessContext = {
    id: 'test-id',
    user_id: 'user-123',
    name: 'Test Profile',
    profile_overview: 'This is a test profile',
    content_focus: ['Marketing', 'Sales'],
    primary_objectives: 'Increase leads',
    offer_description: 'Our product helps businesses grow',
    target_audience: 'Small business owners',
    audience_motivation: 'Looking to scale their business',
    brand_voice: 'Professional but friendly',
    key_benefits: 'Save time, increase revenue',
    unique_value_proposition: 'All-in-one solution',
    audience_decision_style: 'Analytical',
    audience_preference_structure: 'Prefers detailed information',
    audience_decision_speed: 'Takes time to decide',
    audience_reaction_to_change: 'Open to new ideas',
    recommended_platforms: ['LinkedIn', 'Email'],
    recommended_content_formats: ['Blog posts', 'Case studies'],
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
  };

  const mockOptimizedFields = {
    profile_overview: {
      value: 'Optimized profile overview',
      why: 'More concise and compelling',
    },
    brand_voice: {
      value: 'Authoritative yet approachable',
      why: 'Better aligns with target audience',
    },
  };

  const mockOnApplyChanges = jest.fn().mockResolvedValue(undefined);
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (api.post as jest.Mock).mockResolvedValue({
      data: {
        optimized: mockOptimizedFields,
        tokens_used: 350,
      },
    });
  });

  it('renders loading state initially', () => {
    render(
      <OptimizeProfileModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
        onApplyChanges={mockOnApplyChanges}
      />
    );

    expect(screen.getByText('Optimizing your business profile...')).toBeInTheDocument();
  });

  it('fetches optimization suggestions when opened', async () => {
    render(
      <OptimizeProfileModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
        onApplyChanges={mockOnApplyChanges}
      />
    );

    await waitFor(() => {
      expect(api.post).toHaveBeenCalledWith(
        `/business/${mockBusinessContext.id}/optimize`,
        expect.any(Object)
      );
    });
  });

  it('displays optimized fields after loading', async () => {
    render(
      <OptimizeProfileModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
        onApplyChanges={mockOnApplyChanges}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Profile Overview')).toBeInTheDocument();
      expect(screen.getByText('Brand Voice')).toBeInTheDocument();
      expect(screen.getByText('Optimized profile overview')).toBeInTheDocument();
      expect(screen.getByText('Authoritative yet approachable')).toBeInTheDocument();
    });
  });

  it('allows selecting and deselecting fields', async () => {
    render(
      <OptimizeProfileModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
        onApplyChanges={mockOnApplyChanges}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Profile Overview')).toBeInTheDocument();
    });

    // All checkboxes should be selected by default
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach(checkbox => {
      expect(checkbox).toBeChecked();
    });

    // Deselect all
    fireEvent.click(screen.getByText('Deselect All'));
    checkboxes.forEach(checkbox => {
      expect(checkbox).not.toBeChecked();
    });

    // Select all
    fireEvent.click(screen.getByText('Select All'));
    checkboxes.forEach(checkbox => {
      expect(checkbox).toBeChecked();
    });
  });

  it('applies selected changes when Apply button is clicked', async () => {
    render(
      <OptimizeProfileModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
        onApplyChanges={mockOnApplyChanges}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Apply Selected Changes')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Apply Selected Changes'));

    await waitFor(() => {
      expect(mockOnApplyChanges).toHaveBeenCalledWith({
        profile_overview: 'Optimized profile overview',
        brand_voice: 'Authoritative yet approachable',
      });
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it('closes the modal when Cancel is clicked', async () => {
    render(
      <OptimizeProfileModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
        onApplyChanges={mockOnApplyChanges}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Cancel'));
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles API errors gracefully', async () => {
    (api.post as jest.Mock).mockRejectedValue({
      response: { data: { error: 'API error' } },
    });

    render(
      <OptimizeProfileModal
        open={true}
        onClose={mockOnClose}
        businessContext={mockBusinessContext}
        onApplyChanges={mockOnApplyChanges}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('API error')).toBeInTheDocument();
    });
  });
});
