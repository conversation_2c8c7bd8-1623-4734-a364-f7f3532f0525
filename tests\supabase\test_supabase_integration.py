import pytest
import os
from dotenv import load_dotenv
from supabase import create_client

# Load environment variables from .env file
load_dotenv()

# Get Supabase credentials from environment
supabase_url = os.environ.get("SUPABASE_URL")
supabase_key = os.environ.get("SUPABASE_KEY")
skip_supabase_tests = not (supabase_url and supabase_key)


@pytest.mark.skipif(skip_supabase_tests, reason="Supabase not configured")
class TestSupabaseIntegration:
    def test_supabase_client_connection(self):
        """Test that Supabase client can connect"""
        # Create Supabase client directly instead of using Flask's g object
        supabase = create_client(supabase_url, supabase_key)
        assert supabase is not None

        # Test a simple query - this will fail until tables are created
        try:
            response = supabase.table("users").select("*").limit(1).execute()
            assert response is not None
        except Exception as e:
            pytest.skip(f"Table 'users' does not exist yet: {e}")

    def test_user_repository(self):
        """Test that user repository works"""
        # Set up environment variables for the repository
        os.environ["SUPABASE_URL"] = supabase_url
        os.environ["SUPABASE_KEY"] = supabase_key

        from app.repositories.supabase_user_repository import (
            UserRepository,
        )  # Corrected path
        from app.models.supabase_client import set_test_client

        # Create Supabase client and set it in the client module
        supabase = create_client(supabase_url, supabase_key)
        set_test_client(supabase)

        # Create repository without passing client (it will use the one from the module)
        repo = UserRepository()

        # Check that the repository was initialized correctly
        assert repo.table_name == "users"

        # Further tests would depend on your specific repository implementation
        # Temporarily skip table operations until tables are created
        pytest.skip("Skipping table operations until tables are created")
