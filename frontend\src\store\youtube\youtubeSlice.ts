import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";

// Define types
interface VideoResult {
  videoId: string;
  title: string;
  description: string;
  channelId: string;
  channelTitle: string;
  publishDate: string;
  views: number;
  likes: number;
  comments: number;
  channelSubscribers: number;
  outlierScore?: number;
  transcript?: string;
}

interface YouTubeState {
  searchResults: VideoResult[];
  selectedVideo: VideoResult | null;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: YouTubeState = {
  searchResults: [],
  selectedVideo: null,
  isLoading: false,
  error: null,
};

// Slice
const youtubeSlice = createSlice({
  name: "youtube",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Add extra reducers when implementing API calls
  },
});

// Actions
export const { clearError } = youtubeSlice.actions;

// Selectors
export const selectYouTube = (state: RootState) => state.youtube;
export const selectSearchResults = (state: RootState) =>
  state.youtube.searchResults;
export const selectSelectedVideo = (state: RootState) =>
  state.youtube.selectedVideo;
export const selectYouTubeLoading = (state: RootState) =>
  state.youtube.isLoading;
export const selectYouTubeError = (state: RootState) => state.youtube.error;

// Reducer
export default youtubeSlice.reducer;
