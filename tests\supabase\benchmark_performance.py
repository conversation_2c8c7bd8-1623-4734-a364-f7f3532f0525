import requests
import time
import statistics
import argparse
import csv
import os
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from tabulate import tabulate


class PerformanceBenchmark:
    def __init__(
        self, base_url, num_iterations=10, concurrency=1, output_dir="benchmark_results"
    ):
        self.base_url = base_url
        self.num_iterations = num_iterations
        self.concurrency = concurrency
        self.output_dir = output_dir
        self.results = {}

        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Create a timestamped directory for this run
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = os.path.join(output_dir, f"run_{timestamp}")
        os.makedirs(self.run_dir)

    def run_test(self, endpoint, payload=None, method="GET", auth_token=None):
        url = f"{self.base_url}{endpoint}"

        headers = {"Content-Type": "application/json"}

        if auth_token:
            headers["Authorization"] = f"Bearer {auth_token}"

        def single_request(use_supabase):
            req_headers = headers.copy()
            if use_supabase:
                req_headers["X-Use-Supabase"] = "true"

            start_time = time.time()

            try:
                if method == "GET":
                    response = requests.get(url, headers=req_headers, params=payload)
                elif method == "POST":
                    response = requests.post(url, headers=req_headers, json=payload)
                elif method == "PUT":
                    response = requests.put(url, headers=req_headers, json=payload)
                elif method == "DELETE":
                    response = requests.delete(url, headers=req_headers, json=payload)
                else:
                    raise ValueError(f"Unsupported method: {method}")

                end_time = time.time()
                elapsed = (end_time - start_time) * 1000  # convert to ms

                status_code = response.status_code
                response_size = len(response.content)

                return {
                    "elapsed_ms": elapsed,
                    "status_code": status_code,
                    "response_size": response_size,
                    "success": 200 <= status_code < 300,
                }
            except Exception as e:
                end_time = time.time()
                elapsed = (end_time - start_time) * 1000  # convert to ms
                return {
                    "elapsed_ms": elapsed,
                    "status_code": 0,
                    "response_size": 0,
                    "success": False,
                    "error": str(e),
                }

        def run_iteration(backend):
            use_supabase = backend == "supabase"
            return single_request(use_supabase)

        # Run tests for PostgreSQL
        print(
            f"Running {self.num_iterations} iterations for {endpoint} (PostgreSQL)..."
        )
        pg_results = []
        if self.concurrency > 1:
            with ThreadPoolExecutor(max_workers=self.concurrency) as executor:
                pg_results = list(
                    executor.map(
                        lambda _: run_iteration("postgresql"),
                        range(self.num_iterations),
                    )
                )
        else:
            for _ in range(self.num_iterations):
                pg_results.append(run_iteration("postgresql"))

        # Run tests for Supabase
        print(f"Running {self.num_iterations} iterations for {endpoint} (Supabase)...")
        sb_results = []
        if self.concurrency > 1:
            with ThreadPoolExecutor(max_workers=self.concurrency) as executor:
                sb_results = list(
                    executor.map(
                        lambda _: run_iteration("supabase"), range(self.num_iterations)
                    )
                )
        else:
            for _ in range(self.num_iterations):
                sb_results.append(run_iteration("supabase"))

        # Store results
        endpoint_key = f"{method} {endpoint}"
        self.results[endpoint_key] = {
            "postgresql": pg_results,
            "supabase": sb_results,
            "method": method,
            "endpoint": endpoint,
            "payload": payload,
        }

        # Save detailed results to file
        self._save_detailed_results(endpoint_key)

        # Return success rate
        pg_success = (
            sum(1 for r in pg_results if r["success"]) / len(pg_results)
            if pg_results
            else 0
        )
        sb_success = (
            sum(1 for r in sb_results if r["success"]) / len(sb_results)
            if sb_results
            else 0
        )

        return pg_success, sb_success

    def _save_detailed_results(self, endpoint_key):
        """Save detailed results for an endpoint to CSV file"""
        safe_endpoint = endpoint_key.replace("/", "_").replace(" ", "_")
        filename = os.path.join(self.run_dir, f"{safe_endpoint}.csv")

        with open(filename, "w", newline="") as csvfile:
            fieldnames = [
                "backend",
                "iteration",
                "elapsed_ms",
                "status_code",
                "response_size",
                "success",
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for backend in ["postgresql", "supabase"]:
                for i, result in enumerate(self.results[endpoint_key][backend]):
                    row = {
                        "backend": backend,
                        "iteration": i + 1,
                        "elapsed_ms": result["elapsed_ms"],
                        "status_code": result["status_code"],
                        "response_size": result["response_size"],
                        "success": result["success"],
                    }
                    writer.writerow(row)

    def calculate_statistics(self):
        """Calculate statistics for all endpoints"""
        stats = []

        for endpoint_key, data in self.results.items():
            pg_times = [r["elapsed_ms"] for r in data["postgresql"] if r["success"]]
            sb_times = [r["elapsed_ms"] for r in data["supabase"] if r["success"]]

            pg_success_rate = (
                sum(1 for r in data["postgresql"] if r["success"])
                / len(data["postgresql"])
                if data["postgresql"]
                else 0
            )
            sb_success_rate = (
                sum(1 for r in data["supabase"] if r["success"]) / len(data["supabase"])
                if data["supabase"]
                else 0
            )

            endpoint_stats = {
                "endpoint": endpoint_key,
                "postgresql": {
                    "min": min(pg_times) if pg_times else 0,
                    "max": max(pg_times) if pg_times else 0,
                    "mean": statistics.mean(pg_times) if pg_times else 0,
                    "median": statistics.median(pg_times) if pg_times else 0,
                    "p95": self._percentile(pg_times, 95) if pg_times else 0,
                    "success_rate": pg_success_rate * 100,
                },
                "supabase": {
                    "min": min(sb_times) if sb_times else 0,
                    "max": max(sb_times) if sb_times else 0,
                    "mean": statistics.mean(sb_times) if sb_times else 0,
                    "median": statistics.median(sb_times) if sb_times else 0,
                    "p95": self._percentile(sb_times, 95) if sb_times else 0,
                    "success_rate": sb_success_rate * 100,
                },
                "comparison": {
                    "mean_diff_pct": (
                        (
                            (statistics.mean(sb_times) - statistics.mean(pg_times))
                            / statistics.mean(pg_times)
                        )
                        * 100
                        if pg_times and sb_times
                        else 0
                    ),
                    "median_diff_pct": (
                        (
                            (statistics.median(sb_times) - statistics.median(pg_times))
                            / statistics.median(pg_times)
                        )
                        * 100
                        if pg_times and sb_times
                        else 0
                    ),
                },
            }

            stats.append(endpoint_stats)

        return stats

    def _percentile(self, data, percentile):
        """Calculate the given percentile of a list of numbers"""
        if not data:
            return 0
        size = len(data)
        sorted_data = sorted(data)
        index = (size * percentile) // 100
        return sorted_data[index]

    def generate_report(self):
        """Generate a summary report of all benchmarks"""
        stats = self.calculate_statistics()

        # Save full stats as JSON
        with open(os.path.join(self.run_dir, "stats.json"), "w") as f:
            json.dump(stats, f, indent=2)

        # Generate summary table
        table_data = []
        for stat in stats:
            pg_mean = stat["postgresql"]["mean"]
            sb_mean = stat["supabase"]["mean"]
            diff_pct = stat["comparison"]["mean_diff_pct"]
            pg_success = stat["postgresql"]["success_rate"]
            sb_success = stat["supabase"]["success_rate"]

            row = [
                stat["endpoint"],
                f"{pg_mean:.2f}ms",
                f"{sb_mean:.2f}ms",
                f"{diff_pct:+.2f}%" if diff_pct != 0 else "0.00%",
                f"{pg_success:.1f}%",
                f"{sb_success:.1f}%",
            ]
            table_data.append(row)

        headers = [
            "Endpoint",
            "PostgreSQL (mean)",
            "Supabase (mean)",
            "Difference",
            "PG Success",
            "SB Success",
        ]
        table = tabulate(table_data, headers=headers, tablefmt="pipe")

        # Save summary report
        with open(os.path.join(self.run_dir, "summary.md"), "w") as f:
            f.write("# Performance Benchmark Summary\n\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Iterations per endpoint: {self.num_iterations}\n")
            f.write(f"Concurrency level: {self.concurrency}\n\n")
            f.write("## Results Summary\n\n")
            f.write(table)
            f.write("\n\n")
            f.write("## Analysis\n\n")

            # Calculate overall stats
            all_pg_means = [s["postgresql"]["mean"] for s in stats]
            all_sb_means = [s["supabase"]["mean"] for s in stats]
            overall_diff = (
                ((sum(all_sb_means) - sum(all_pg_means)) / sum(all_pg_means)) * 100
                if sum(all_pg_means) > 0
                else 0
            )

            f.write(
                f"Overall, Supabase is {overall_diff:+.2f}% compared to PostgreSQL in response time.\n\n"
            )

            # Identify slowest endpoints
            slowest_endpoints = sorted(
                stats, key=lambda x: x["supabase"]["mean"], reverse=True
            )[:3]
            f.write("### Slowest Endpoints (Supabase)\n\n")
            for endpoint in slowest_endpoints:
                f.write(
                    f"- {endpoint['endpoint']}: {endpoint['supabase']['mean']:.2f}ms "
                )
                f.write(
                    f"({endpoint['comparison']['mean_diff_pct']:+.2f}% vs PostgreSQL)\n"
                )

            f.write("\n### Recommendations\n\n")
            if overall_diff > 10:
                f.write(
                    "- Supabase performance is significantly slower than PostgreSQL. Consider optimizing the following:\n"
                )
                for endpoint in slowest_endpoints:
                    if endpoint["comparison"]["mean_diff_pct"] > 10:
                        f.write(
                            f"  - Review implementation for {endpoint['endpoint']}\n"
                        )
            elif overall_diff < -10:
                f.write(
                    "- Supabase performance is significantly faster than PostgreSQL. No immediate action needed.\n"
                )
            else:
                f.write(
                    "- Performance is comparable between PostgreSQL and Supabase. No immediate action needed.\n"
                )

        print(f"Report generated at {os.path.join(self.run_dir, 'summary.md')}")
        return os.path.join(self.run_dir, "summary.md")


def run_standard_benchmark(base_url, iterations=10, concurrency=1):
    """Run a standard set of benchmarks across common endpoints"""
    benchmark = PerformanceBenchmark(
        base_url, num_iterations=iterations, concurrency=concurrency
    )

    # Content endpoints
    benchmark.run_test("/api/content/list", method="GET")
    benchmark.run_test(
        "/api/content/create",
        method="POST",
        payload={
            "title": "Benchmark Test Content",
            "content_type": "article",
            "text": "This is a benchmark test content item.",
        },
    )
    benchmark.run_test(
        "/api/content/search", method="GET", payload={"query": "benchmark"}
    )

    # Business context endpoints
    benchmark.run_test("/api/business/contexts", method="GET")
    benchmark.run_test(
        "/api/business/context/create",
        method="POST",
        payload={
            "name": "Benchmark Test Context",
            "description": "This is a benchmark test business context.",
        },
    )

    # Chat session endpoints
    benchmark.run_test("/api/ai/sessions", method="GET")
    benchmark.run_test(
        "/api/ai/session/create",
        method="POST",
        payload={
            "name": "Benchmark Test Session",
            "system_prompt": "This is a benchmark test.",
        },
    )

    # Activity log endpoints
    benchmark.run_test("/api/activity/logs", method="GET")

    # Generate and return the report
    return benchmark.generate_report()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Performance benchmark for PostgreSQL vs Supabase"
    )
    parser.add_argument(
        "--url", type=str, default="http://localhost:5000", help="Base URL for the API"
    )
    parser.add_argument(
        "--iterations", type=int, default=10, help="Number of iterations per endpoint"
    )
    parser.add_argument(
        "--concurrency",
        type=int,
        default=1,
        help="Concurrency level (number of parallel requests)",
    )
    args = parser.parse_args()

    print(f"Running performance benchmark against {args.url}")
    print(
        f"Using {args.iterations} iterations per endpoint with concurrency level {args.concurrency}"
    )

    report_path = run_standard_benchmark(args.url, args.iterations, args.concurrency)
    print(f"Benchmark complete. Report available at: {report_path}")
