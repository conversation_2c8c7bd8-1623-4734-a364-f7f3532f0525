# app/ai_assistant/prompts/audience_analysis.py
"""Audience analysis prompts and utilities based on <PERSON><PERSON>'s NHB+ framework."""

from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)

# --- Audience Analysis Prompt Configuration ---

AUDIENCE_ANALYSIS_INTRO = """
You are an expert audience analyst using <PERSON><PERSON>'s NHB+ framework. Your task is to analyze the business profile data and provide actionable recommendations.
"""

AUDIENCE_ANALYSIS_HINT = """
Analyze the provided business profile to determine:

1. Motivation Direction (Towards vs. Away):
   - Towards: Motivated by moving towards desirable outcomes (gains, benefits, pleasure)
   - Away: Motivated by moving away from undesirable outcomes (problems, losses, pain)

2. Preference Structure (Optional vs. Procedural):
   - Optional: Prefers flexibility and multiple options
   - Procedural: Prefers clear, step-by-step guidance

3. Reference Point (Internal vs. External):
   - Internal: Primarily trusts own intuition and experience
   - External: Relies more on external sources like experts and social proof

4. Thinking Style (General vs. Specific):
   - General: Prefers big-picture concepts and broad strategies
   - Specific: Prefers detailed information and specific tactics

5. Convincer Channels (Visual, Auditory, Kinesthetic):
   - Rank these in order of preference based on the profile data

6. Convincer Strategy (Automatic, Frequency, Consistent, Period of Time):
   - Automatic: Quick, impulsive decisions based on initial appeal
   - Frequency: Needs to see multiple examples or hear the message repeatedly
   - Consistent: Needs to be convinced every time they encounter the offer
   - Period of Time: Needs a specific duration to consider before deciding

7. Change Model Preference (Sameness, Sameness with Exception, Difference):
   - Sameness: Prefers things to stay the same, resists change
   - Sameness with Exception: Open to change within a familiar framework
   - Difference: Actively seeks novelty and radical change

8. Stress Response in Decision Making (Feeling, Choice, Thinking/Logical):
   - Feeling: Decisions driven by gut feeling and emotional comfort
   - Choice: Reduces stress by having multiple options and feeling in control
   - Thinking/Logical: Reduces stress by analyzing information logically

9. Emotional State Analysis (Worthless, Helpless, Hopeless):
   - Worthless (5%): Feeling fundamentally inadequate, lacking value, judged
   - Helpless (35%): Feeling overwhelmed, lacking agency, unsure how to solve problems
   - Hopeless (20%): Feeling pessimistic, lacking belief in solutions, resigned to failure

For each criterion, provide:
1. The dominant characteristic based on the profile data
2. The evidence from the profile that supports this conclusion
3. Specific, actionable recommendations for content creation and marketing

Based on the emotional state analysis, provide specific content recommendations:
- For Helpless audiences: Focus on Solution (How) with step-by-step guides and actionable strategies
- For Hopeless audiences: Focus on Problem (Why) and Solution (How), validating their frustration before offering solutions
- For Worthless audiences: Focus on Problem (Why) with empathetic understanding before solutions

Also provide strategic guidance on using the 6 Question Types (What, Why, How, Who, Where, When) based on the audience's emotional state.

Format your response as a valid JSON object with the following structure:
{
    "summary": "A brief overview of the audience analysis",
    "nhb_criteria": {
        "motivation_direction": {
            "value": "Towards/Away",
            "evidence": "Evidence from the profile",
            "recommendations": ["Recommendation 1", "Recommendation 2"]
        },
        "preference_structure": {
            "value": "Optional/Procedural",
            "evidence": "Evidence from the profile",
            "recommendations": ["Recommendation 1", "Recommendation 2"]
        },
        // Other NHB+ criteria following the same structure
    },
    "emotional_state": {
        "dominant_state": "Helpless/Hopeless/Worthless",
        "evidence": "Evidence from the profile",
        "content_approach": "Detailed approach based on the emotional state",
        "recommendations": ["Recommendation 1", "Recommendation 2"]
    },
    "question_strategy": {
        "what": "How to use 'What' questions with this audience",
        "why": "How to use 'Why' questions with this audience",
        "how": "How to use 'How' questions with this audience",
        "who": "How to use 'Who' questions with this audience",
        "when": "How to use 'When' questions with this audience",
        "where": "How to use 'Where' questions with this audience"
    },
    "content_recommendations": {
        "content_types": ["Recommended content type 1", "Recommended content type 2"],
        "messaging_approach": "Recommended messaging approach",
        "content_structure": "Recommended content structure"
    }
}

IMPORTANT RULES:
1. Your response MUST be a valid JSON object. Do not include any text outside the JSON object.
2. Do not include markdown code fences (```) in your response - just return the raw JSON object.
3. Make sure your JSON is properly formatted with double quotes around all keys and string values.
4. Keep your recommendations specific, actionable, and tailored to the business profile.
5. Base your analysis on the actual profile data, not assumptions.
"""

AUDIENCE_ANALYSIS_JSON_CLAUSE = """
Respond with ONLY a JSON object using the exact format specified above.
Your response MUST be a valid JSON object. Do not include any text outside the JSON object.
Do not include markdown code fences (```) in your response - just return the raw JSON object.
Make sure your JSON is properly formatted with double quotes around all keys and string values.
"""

def _format_business_profile_for_analysis(profile: Dict[str, Any]) -> str:
    """Format the business profile data for audience analysis.
    
    Args:
        profile: The business profile data to format
        
    Returns:
        Formatted string with the business profile data
    """
    if not profile:
        return "No business profile data provided."
    
    profile_lines = ["Business Profile Data:"]
    
    # Map of profile keys to display labels
    field_mapping = [
        ("name", "Profile Name"),
        ("profile_overview", "Profile Overview"),
        ("offer_description", "Offer Description"),
        ("target_audience", "Target Audience"),
        ("audience_motivation", "Audience Motivation"),
        ("brand_voice", "Brand Voice"),
        ("key_benefits", "Key Benefits"),
        ("unique_value_proposition", "Unique Value Proposition"),
        ("primary_objectives", "Primary Objectives"),
        ("audience_decision_style", "Audience Decision Style"),
        ("audience_preference_structure", "Audience Preference Structure"),
        ("audience_decision_speed", "Audience Decision Speed"),
        ("audience_reaction_to_change", "Audience Reaction to Change"),
        ("content_focus", "Content Focus"),
        ("recommended_platforms", "Recommended Platforms"),
        ("recommended_content_formats", "Recommended Content Formats"),
    ]
    
    for key, label in field_mapping:
        value = profile.get(key)
        if value:
            if isinstance(value, list):
                # Join array/list values with commas
                profile_lines.append(f"- {label}: {', '.join(map(str, value))}")
            else:
                profile_lines.append(f"- {label}: {value}")
        else:
            profile_lines.append(f"- {label}: Not provided")
    
    return "\n".join(profile_lines)

def build_audience_analysis_prompt(business_profile: Dict[str, Any]) -> str:
    """Build the complete audience analysis prompt.
    
    Args:
        business_profile: The business profile data to analyze
        
    Returns:
        Complete prompt for audience analysis
    """
    segments = [
        AUDIENCE_ANALYSIS_INTRO,
        _format_business_profile_for_analysis(business_profile),
        AUDIENCE_ANALYSIS_HINT,
        AUDIENCE_ANALYSIS_JSON_CLAUSE
    ]
    
    return "\n\n".join(segments).strip()
