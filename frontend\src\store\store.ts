import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./auth/authSlice";
// Removed import for deleted middleware
// import supabaseAuthMiddleware from './middleware/supabaseAuthMiddleware';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    // Add other reducers here as needed
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types from serializable check
        ignoredActions: ["auth/setSupabaseSession"],
        // Ignore these paths in the state from serializable check
        ignoredPaths: ["auth.supabaseSession", "auth.supabaseUser"],
      },
      // Removed concatenation of deleted middleware
      // }).concat(supabaseAuthMiddleware),
    }),
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
