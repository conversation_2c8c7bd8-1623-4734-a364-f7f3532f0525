/**
 * Authentication Fallback Utility
 * 
 * This utility provides a fallback authentication mechanism when Supabase is not accessible.
 * It creates a mock session for testing purposes.
 */

import { Session, User } from "@supabase/supabase-js";

// Test user credentials
const TEST_CREDENTIALS = {
  email: "<EMAIL>",
  password: "MK*9v9sseuu3#Z3qgypf"
};

// Create a mock session for testing
export const createMockSession = (email: string): Session => {
  const now = Math.floor(Date.now() / 1000);
  const expiresAt = now + (60 * 60); // 1 hour from now
  
  const mockUser: User = {
    id: "mock-user-id-12345",
    aud: "authenticated",
    role: "authenticated",
    email: email,
    email_confirmed_at: new Date().toISOString(),
    phone: "",
    confirmed_at: new Date().toISOString(),
    last_sign_in_at: new Date().toISOString(),
    app_metadata: {
      provider: "email",
      providers: ["email"]
    },
    user_metadata: {
      email: email
    },
    identities: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const mockSession: Session = {
    access_token: "mock-access-token-for-testing-" + Date.now(),
    refresh_token: "mock-refresh-token-for-testing-" + Date.now(),
    expires_in: 3600,
    expires_at: expiresAt,
    token_type: "bearer",
    user: mockUser
  };

  return mockSession;
};

// Check if credentials match test user
export const isTestUser = (email: string, password: string): boolean => {
  return email === TEST_CREDENTIALS.email && password === TEST_CREDENTIALS.password;
};

// Fallback authentication function
export const fallbackAuth = async (email: string, password: string): Promise<{
  success: boolean;
  session?: Session;
  error?: string;
}> => {
  console.log("[AUTH_FALLBACK] Attempting fallback authentication for:", email);
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  if (isTestUser(email, password)) {
    const session = createMockSession(email);
    console.log("[AUTH_FALLBACK] Mock session created successfully");
    return { success: true, session };
  } else {
    return { 
      success: false, 
      error: "Invalid credentials. Use test credentials: <EMAIL> / MK*9v9sseuu3#Z3qgypf" 
    };
  }
};

// Check if we should use fallback authentication
export const shouldUseFallback = (): boolean => {
  // Use fallback in development when Supabase is not accessible
  return process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost';
};

// Store mock session in localStorage for persistence
export const storeMockSession = (session: Session): void => {
  try {
    const sessionData = {
      access_token: session.access_token,
      refresh_token: session.refresh_token,
      expires_at: session.expires_at,
      user: session.user
    };
    localStorage.setItem('mock-auth-session', JSON.stringify(sessionData));
    console.log("[AUTH_FALLBACK] Mock session stored in localStorage");
  } catch (error) {
    console.error("[AUTH_FALLBACK] Failed to store mock session:", error);
  }
};

// Retrieve mock session from localStorage
export const retrieveMockSession = (): Session | null => {
  try {
    const stored = localStorage.getItem('mock-auth-session');
    if (!stored) return null;
    
    const sessionData = JSON.parse(stored);
    
    // Check if session is expired
    if (sessionData.expires_at && sessionData.expires_at < Math.floor(Date.now() / 1000)) {
      localStorage.removeItem('mock-auth-session');
      console.log("[AUTH_FALLBACK] Mock session expired, removed from storage");
      return null;
    }
    
    console.log("[AUTH_FALLBACK] Mock session retrieved from localStorage");
    return sessionData as Session;
  } catch (error) {
    console.error("[AUTH_FALLBACK] Failed to retrieve mock session:", error);
    return null;
  }
};

// Clear mock session
export const clearMockSession = (): void => {
  try {
    localStorage.removeItem('mock-auth-session');
    console.log("[AUTH_FALLBACK] Mock session cleared from localStorage");
  } catch (error) {
    console.error("[AUTH_FALLBACK] Failed to clear mock session:", error);
  }
};
