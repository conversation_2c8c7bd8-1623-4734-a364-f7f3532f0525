"""
Debug routes for AI assistant configuration - FastAPI version.
Migrated from Flask app/ai_assistant/debug_routes.py to use FastAPI dependency injection.
Helps diagnose configuration issues.
"""

import logging
import os
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/ai", tags=["debug"])


class AIConfigResponse(BaseModel):
    provider: str
    api_key_present: bool
    api_key_length: int
    api_url: str
    models: Dict[str, str]
    max_tokens: Dict[str, int]
    model_params: Dict[str, Any]
    environment_vars: Dict[str, Any]


class ConnectionTestResponse(BaseModel):
    provider: str
    connection_test: Dict[str, Any]


class GenerationTestResponse(BaseModel):
    provider: str
    generation_test: Dict[str, Any]


@router.get("/config", response_model=AIConfigResponse)
async def get_ai_config() -> AIConfigResponse:
    """Get current AI configuration for debugging"""
    try:
        from app.ai_assistant.config import (
            get_model_for_purpose,
            get_api_provider,
            get_api_key,
            get_api_url,
            get_max_tokens,
            get_model_params
        )

        config_info = AIConfigResponse(
            provider=get_api_provider(),
            api_key_present=bool(get_api_key()),
            api_key_length=len(get_api_key()) if get_api_key() else 0,
            api_url=get_api_url(),
            models={
                "default": get_model_for_purpose("default"),
                "general_chat": get_model_for_purpose("general_chat"),
                "chat_stream": get_model_for_purpose("chat_stream"),
            },
            max_tokens={
                "default": get_max_tokens("default"),
                "chat": get_max_tokens("chat"),
                "chat_stream": get_max_tokens("chat_stream"),
            },
            model_params=get_model_params(),
            environment_vars={
                "AI_PROVIDER": os.environ.get("AI_PROVIDER"),
                "OPENROUTER_API_KEY_PRESENT": bool(os.environ.get("OPENROUTER_API_KEY")),
                "OPENAI_API_KEY_PRESENT": bool(os.environ.get("OPENAI_API_KEY")),
                "AI_DEFAULT_MODEL": os.environ.get("AI_DEFAULT_MODEL"),
            }
        )
        
        return config_info
        
    except Exception as e:
        logger.error(f"Error getting AI config: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting AI config: {str(e)}"
        )


@router.get("/test-connection", response_model=ConnectionTestResponse)
async def test_ai_connection() -> ConnectionTestResponse:
    """Test AI provider connection"""
    try:
        from app.ai_assistant.common import get_ai_generator
        
        ai_generator = get_ai_generator()
        
        if hasattr(ai_generator, 'test_connection'):
            result = await ai_generator.test_connection() if hasattr(ai_generator.test_connection, '__await__') else ai_generator.test_connection()
            return ConnectionTestResponse(
                provider=ai_generator.provider if hasattr(ai_generator, 'provider') else "unknown",
                connection_test=result
            )
        else:
            return ConnectionTestResponse(
                provider=ai_generator.__class__.__name__ if ai_generator else "none",
                connection_test={
                    "success": False,
                    "message": "test_connection method not available"
                }
            )
            
    except Exception as e:
        logger.error(f"Error testing AI connection: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error testing AI connection: {str(e)}"
        )


@router.get("/test-simple-generation", response_model=GenerationTestResponse)
async def test_simple_generation() -> GenerationTestResponse:
    """Test simple AI generation"""
    try:
        from app.ai_assistant.common import get_ai_generator
        
        ai_generator = get_ai_generator()
        
        # Try a simple generation
        if hasattr(ai_generator, 'generate_chat_response'):
            result = await ai_generator.generate_chat_response(
                messages=[{"role": "user", "content": "Say 'Hello' and nothing else."}],
                content_format="general",
                max_tokens=10
            ) if hasattr(ai_generator.generate_chat_response, '__await__') else ai_generator.generate_chat_response(
                messages=[{"role": "user", "content": "Say 'Hello' and nothing else."}],
                content_format="general",
                max_tokens=10
            )
            
            return GenerationTestResponse(
                provider=ai_generator.provider if hasattr(ai_generator, 'provider') else "unknown",
                generation_test=result
            )
        else:
            return GenerationTestResponse(
                provider=ai_generator.__class__.__name__ if ai_generator else "none",
                generation_test={
                    "error": True,
                    "error_message": "generate_chat_response method not available"
                }
            )
            
    except Exception as e:
        logger.error(f"Error testing AI generation: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error testing AI generation: {str(e)}"
        ) 