"""
FeedbackRepository for Supabase implementation
Handles operations for user feedback on AI-generated content
"""

import logging
from datetime import datetime, timezone
from app.repositories.supabase_repository import (
    SupabaseRepository,
    SupabaseRepositoryError,
)

logger = logging.getLogger(__name__)


class FeedbackRepository(SupabaseRepository):
    """Repository for managing user feedback in Supabase"""

    def __init__(self):
        """Initialize with Supabase client"""
        super().__init__(table_name="feedback")

    async def get_by_user_id(self, user_id, limit=20, offset=0):
        """Get feedback for a specific user with pagination asynchronously."""
        try:
            table = await self._get_table()
            result = await (
                table
                .select("*")
                .eq("user_id", user_id)
                .order("created_at", desc=True)
                .range(offset, offset + limit - 1)
                .execute()
            )
            return result.data
        except Exception as e:
            self.handle_error(f"Error getting feedback for user {user_id}", e)
            raise # Re-raise

    async def get_by_id(self, feedback_id, user_id):
        """Get a specific feedback item by ID asynchronously, ensuring it belongs to the user."""
        try:
            table = await self._get_table()
            result = await (
                table
                .select("*")
                .eq("id", feedback_id)
                .eq("user_id", user_id)
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None # Item not found for user
        except Exception as e:
            self.handle_error(
                f"Error getting feedback {feedback_id} for user {user_id}", e
            )
            raise # Re-raise

    async def create_content_feedback(
        self,
        user_id,
        content_item_id,
        rating,
        feedback_text=None,
        feedback_type="content_quality",
    ):
        """Create feedback for a content item asynchronously."""
        try:
            if not isinstance(rating, int) or rating < 1 or rating > 5:
                raise ValueError("Rating must be an integer between 1 and 5")
            if not content_item_id:
                raise SupabaseRepositoryError("Content item ID is required for content feedback", status_code=400)

            now = datetime.now(timezone.utc).isoformat()
            data = {
                "user_id": user_id,
                "content_item_id": content_item_id,
                "chat_session_id": None,
                "rating": rating,
                "feedback_text": feedback_text,
                "feedback_type": feedback_type,
                "created_at": now,
                "updated_at": now,
                "is_resolved": False,
            }
            table = await self._get_table()
            result = await table.insert(data).execute()
            if result.data and len(result.data) > 0:
                return result.data[0]
            raise SupabaseRepositoryError("Failed to create content feedback: No data returned")
        except ValueError as ve:
            self.handle_error(str(ve), ve)
            raise
        except Exception as e:
            self.handle_error(f"Error creating feedback for content {content_item_id}", e)
            raise # Re-raise

    async def create_chat_feedback(
        self,
        user_id,
        chat_session_id,
        rating,
        feedback_text=None,
        feedback_type="conversation_quality",
    ):
        """Create feedback for a chat session asynchronously."""
        try:
            if not isinstance(rating, int) or rating < 1 or rating > 5:
                raise ValueError("Rating must be an integer between 1 and 5")
            if not chat_session_id:
                raise SupabaseRepositoryError("Chat session ID is required for chat feedback", status_code=400)

            now = datetime.now(timezone.utc).isoformat()
            data = {
                "user_id": user_id,
                "content_item_id": None,
                "chat_session_id": chat_session_id,
                "rating": rating,
                "feedback_text": feedback_text,
                "feedback_type": feedback_type,
                "created_at": now,
                "updated_at": now,
                "is_resolved": False,
            }
            table = await self._get_table()
            result = await table.insert(data).execute()
            if result.data and len(result.data) > 0:
                return result.data[0]
            raise SupabaseRepositoryError("Failed to create chat feedback: No data returned")
        except ValueError as ve:
            self.handle_error(str(ve), ve)
            raise
        except Exception as e:
            self.handle_error(f"Error creating feedback for chat session {chat_session_id}", e)
            raise # Re-raise

    async def update_feedback(self, feedback_id, user_id, update_data):
        """Update an existing feedback asynchronously."""
        try:
            existing = await self.get_by_id(feedback_id, user_id)
            if not existing:
                raise ValueError(f"Feedback {feedback_id} not found or does not belong to user {user_id}")

            allowed_fields = ["rating", "feedback_text"]
            filtered_data = {k: v for k, v in update_data.items() if k in allowed_fields}
            if not filtered_data: # Nothing to update from allowed fields
                 return existing # Or raise error if update was expected

            if existing["is_resolved"]:
                raise ValueError("Cannot update feedback that has already been resolved")
            if "rating" in filtered_data:
                rating = filtered_data["rating"]
                if not isinstance(rating, int) or rating < 1 or rating > 5:
                    raise ValueError("Rating must be an integer between 1 and 5")
            filtered_data["updated_at"] = datetime.now(timezone.utc).isoformat()

            table = await self._get_table()
            result = await (
                table.update(filtered_data)
                .eq("id", feedback_id)
                .eq("user_id", user_id)
                .eq("is_resolved", False)
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            # If update returns no data but existing was found, it might be due to is_resolved becoming True concurrently
            # or RLS. Re-fetch or raise error.
            # For now, assume if existing was found and not resolved, update should yield data.
            raise SupabaseRepositoryError(f"Failed to update feedback {feedback_id}: No data returned from update operation")
        except ValueError as ve:
            self.handle_error(str(ve), ve)
            raise
        except Exception as e:
            self.handle_error(f"Error updating feedback {feedback_id}", e)
            raise # Re-raise

    async def get_feedback_for_content(self, content_item_id, limit=10):
        """Get feedback for a specific content item asynchronously."""
        try:
            table = await self._get_table()
            result = await (
                table.select("*")
                .eq("content_item_id", content_item_id)
                .order("created_at", desc=True)
                .limit(limit)
                .execute()
            )
            return result.data
        except Exception as e:
            self.handle_error(f"Error getting feedback for content {content_item_id}", e)
            raise # Re-raise

    async def get_feedback_for_chat(self, chat_session_id, limit=10):
        """Get feedback for a specific chat session asynchronously."""
        try:
            table = await self._get_table()
            result = await (
                table.select("*")
                .eq("chat_session_id", chat_session_id)
                .order("created_at", desc=True)
                .limit(limit)
                .execute()
            )
            return result.data
        except Exception as e:
            self.handle_error(f"Error getting feedback for chat {chat_session_id}", e)
            raise # Re-raise

    async def get_feedback_by_type(self, feedback_type, limit=50, offset=0):
        """Get feedback by type with pagination asynchronously (admin)."""
        try:
            table = await self._get_table()
            result = await (
                table.select("*")
                .eq("feedback_type", feedback_type)
                .order("created_at", desc=True)
                .range(offset, offset + limit - 1)
                .execute()
            )
            return result.data
        except Exception as e:
            self.handle_error(f"Error getting feedback by type {feedback_type}", e)
            raise # Re-raise

    async def get_unresolved_feedback(self, limit=50, offset=0):
        """Get unresolved feedback with pagination asynchronously (admin)."""
        try:
            table = await self._get_table()
            result = await (
                table.select("*")
                .eq("is_resolved", False)
                .order("created_at", desc=True)
                .range(offset, offset + limit - 1)
                .execute()
            )
            return result.data
        except Exception as e:
            self.handle_error("Error getting unresolved feedback", e)
            raise # Re-raise

    async def resolve_feedback(self, feedback_id, admin_notes=None):
        """Mark feedback as resolved asynchronously (admin)."""
        try:
            update_data = {
                "is_resolved": True,
                "resolved_at": datetime.now(timezone.utc).isoformat(),
                "updated_at": datetime.now(timezone.utc).isoformat(),
            }
            if admin_notes:
                update_data["admin_notes"] = admin_notes
            
            table = await self._get_table()
            result = await (
                table.update(update_data)
                .eq("id", feedback_id)
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            # If no data, means feedback_id was not found or already resolved (if RLS allows seeing it)
            # Or RLS prevented seeing the update.
            raise SupabaseRepositoryError(f"Failed to resolve feedback {feedback_id}: No data returned or item not found.")
        except Exception as e:
            self.handle_error(f"Error resolving feedback {feedback_id}", e)
            raise # Re-raise
