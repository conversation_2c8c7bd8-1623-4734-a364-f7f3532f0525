"""
AI Assistant common utilities - FastAPI version.
Migrated from Flask to use dependency injection for configuration.
"""

from app.ai_assistant.openrouter_adapter import OpenRouterAdapter
from app.youtube.youtube_service import YouTubeService
import logging

# Set up logger
logger = logging.getLogger(__name__)

# Global AI generator instance for caching
_ai_generator = None


def get_ai_generator(ai_provider: str = "openrouter"):
    """
    Initialize and return the AI generator based on configuration.
    
    Args:
        ai_provider: AI provider to use ('openai' or 'openrouter')
        
    Returns:
        AI generator instance
    """
    global _ai_generator

    if _ai_generator is None:
        try:
            # Use dependency injection pattern - get settings from environment
            from app.dependencies import get_settings
            settings = get_settings()
            
            # Override with parameter if provided
            provider = getattr(settings, 'ai_provider', ai_provider).lower() if hasattr(settings, 'ai_provider') else ai_provider.lower()
            youtube_service = YouTubeService()

            if provider == 'openai':
                from app.ai_assistant.openai_adapter import OpenAIAdapter
                _ai_generator = OpenAIAdapter(youtube_service=youtube_service)
                logger.info("OpenAIAdapter initialized successfully with YouTubeService")
            else:  # Default to openrouter
                _ai_generator = OpenRouterAdapter(youtube_service=youtube_service)
                logger.info("OpenRouterAdapter initialized successfully with YouTubeService")
                
        except Exception as e:
            logger.error("Failed to initialize AI adapter: %s", e)
            raise

    return _ai_generator


def get_combined_business_context(current_user_id, dedicated_business_context=None):
    """
    Gather and combine business context from dedicated context and flagged content items.
    
    This function is kept for backward compatibility but no longer uses Flask's g object.

    Args:
        current_user_id (str): The ID of the current user
        dedicated_business_context (dict, optional): Dedicated business context from request

    Returns:
        dict: Combined business context with both dedicated info and flagged content examples
    """
    logger.debug(
        "[CONTEXT_GATHERING] Starting context gathering for user %s", current_user_id
    )

    combined_business_context = {}

    # Add dedicated business context if provided (filter out None values)
    if dedicated_business_context:
        logger.debug("[CONTEXT_GATHERING] Adding dedicated business context")
        combined_business_context = {
            k: v for k, v in dedicated_business_context.items() if v is not None
        }

    # Note: For flagged content items, this would require repository injection
    # In FastAPI, this should be handled at the router level with proper dependency injection
    logger.debug("[CONTEXT_GATHERING] Skipping flagged context items (requires repository injection)")

    logger.debug(
        "[CONTEXT_GATHERING] Completed, context keys: %s",
        list(combined_business_context.keys()),
    )

    return combined_business_context


def reset_ai_generator():
    """Reset the global AI generator instance (useful for testing)."""
    global _ai_generator
    _ai_generator = None
    logger.debug("AI generator instance reset")
