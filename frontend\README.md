# Writer v2 Frontend

A modern, user-friendly frontend for the Writer v2 content creation platform. This application provides an intuitive interface for managing content, business context, and leveraging AI assistance for content generation.

## Features

- **User Authentication**: Secure login and registration system
- **Dashboard**: Overview of all features and quick actions
- **Content Library**: Manage your content examples and templates
- **Business Context**: Define your brand voice and target audience
- **AI Assistant**: Generate content with AI assistance
- **YouTube Analytics**: Analyze YouTube videos and find trends

## Design Philosophy

The UI is designed to be:
- **Approachable & Bubbly**: Soft rounded corners, friendly animations, and a warm color palette
- **Techy but User-Friendly**: Clean interfaces with subtle tech-inspired elements
- **Apple-like but Better**: Minimalist design with improved usability and more personality
- **Intuitive Experience**: Clear navigation and contextual guidance

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (v6 or higher)

### Installation

1. Install dependencies:
   ```
   npm install
   ```

2. Start the development server:
   ```
   npm start
   ```

3. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

### Available Scripts

- `npm start`: Runs the app in development mode
- `npm test`: Launches the test runner
- `npm run build`: Builds the app for production
- `npm run lint`: Runs ESLint to check for code issues
- `npm run format`: Formats code using Prettier

## Technology Stack

- **React**: UI library
- **TypeScript**: Type-safe JavaScript
- **Redux Toolkit**: State management
- **React Router**: Navigation
- **Material UI**: Component library
- **Framer Motion**: Animations
- **Axios**: API requests

## Project Structure

- `/src/components`: Reusable UI components
- `/src/pages`: Page components
- `/src/store`: Redux store and slices
- `/src/services`: API services
- `/src/styles`: Global styles and theme

## Backend Integration

The frontend is designed to work with the Writer v2 backend API. The API endpoints are proxied through the development server to `http://localhost:5000`.

## Dark Mode

The application supports both light and dark modes, which can be toggled from the user interface. The theme preference is saved in local storage.

## Responsive Design

The UI is fully responsive and works well on devices of all sizes, from mobile phones to desktop computers. 