@echo off
echo ========================================
echo RESTARTING FRONTEND WITH HOST FIX
echo ========================================
echo.

echo [1/3] Stopping frontend container...
docker-compose stop frontend
echo.

echo [2/3] Rebuilding frontend with host binding fix...
docker-compose build --no-cache frontend
echo.

echo [3/3] Starting frontend...
docker-compose up -d frontend
echo.

echo ========================================
echo RESTART COMPLETE!
echo ========================================
echo.
echo Wait 30 seconds then check: http://localhost:3000
echo.
echo Look for this in logs (run diagnose-frontend.bat):
echo   "Local: http://0.0.0.0:3000" (GOOD)
echo   NOT "Local: http://localhost:3000" (BAD)
echo ========================================
timeout /t 30 /nobreak
start http://localhost:3000
pause