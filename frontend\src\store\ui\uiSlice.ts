import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";

interface UIState {
  sidebarOpen: boolean;
  darkMode: boolean;
  notifications: Notification[];
  unreadNotificationsCount: number;
}

interface Notification {
  id: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  read: boolean;
  timestamp: string;
}

// Get initial dark mode preference from local storage or system preference
const getInitialDarkMode = (): boolean => {
  const savedMode = localStorage.getItem("darkMode");
  if (savedMode !== null) {
    return savedMode === "true";
  }
  return (
    window.matchMedia &&
    window.matchMedia("(prefers-color-scheme: dark)").matches
  );
};

const initialState: UIState = {
  sidebarOpen: false,
  darkMode: getInitialDarkMode(),
  notifications: [],
  unreadNotificationsCount: 0,
};

const uiSlice = createSlice({
  name: "ui",
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    toggleDarkMode: (state) => {
      state.darkMode = !state.darkMode;
      localStorage.setItem("darkMode", state.darkMode.toString());
    },
    setDarkMode: (state, action: PayloadAction<boolean>) => {
      state.darkMode = action.payload;
      localStorage.setItem("darkMode", state.darkMode.toString());
    },
    addNotification: (
      state,
      action: PayloadAction<Omit<Notification, "read" | "timestamp">>
    ) => {
      const newNotification = {
        ...action.payload,
        read: false,
        timestamp: new Date().toISOString(),
      };
      state.notifications.unshift(newNotification);
      state.unreadNotificationsCount += 1;
    },
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(
        (n) => n.id === action.payload
      );
      if (notification && !notification.read) {
        notification.read = true;
        state.unreadNotificationsCount -= 1;
      }
    },
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach((notification) => {
        notification.read = true;
      });
      state.unreadNotificationsCount = 0;
    },
    clearNotifications: (state) => {
      state.notifications = [];
      state.unreadNotificationsCount = 0;
    },
  },
});

// Actions
export const {
  toggleSidebar,
  setSidebarOpen,
  toggleDarkMode,
  setDarkMode,
  addNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  clearNotifications,
} = uiSlice.actions;

// Selectors
export const selectSidebarOpen = (state: RootState) => state.ui.sidebarOpen;
export const selectDarkMode = (state: RootState) => state.ui.darkMode;
export const selectNotifications = (state: RootState) => state.ui.notifications;
export const selectUnreadNotificationsCount = (state: RootState) =>
  state.ui.unreadNotificationsCount;

// Reducer
export default uiSlice.reducer;
