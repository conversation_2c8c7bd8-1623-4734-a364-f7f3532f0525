/**
 * Utility functions for handling errors and warnings
 */

/**
 * Suppresses the ResizeObserver loop warning in development
 * This warning is common with Material-UI and other libraries that use ResizeObserver
 * and doesn't indicate a functional problem
 */
export const suppressResizeObserverWarning = (): void => {
  // Store the original console error function
  const originalConsoleError = console.error;

  // Override console.error to filter out ResizeObserver warnings
  console.error = (...args: any[]) => {
    // Check if this is a ResizeObserver warning
    if (
      args.length > 0 &&
      typeof args[0] === "string" &&
      args[0].includes("ResizeObserver loop")
    ) {
      // Ignore this specific warning
      return;
    }

    // Pass all other errors to the original console.error
    originalConsoleError.apply(console, args);
  };
};

/**
 * Debounce function to limit how often a function can be called
 * Useful for resize handlers and other frequently triggered events
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>): void => {
    if (timeout) {
      clearTimeout(timeout);
    }

    timeout = setTimeout(() => {
      func(...args);
    }, wait);
  };
};
