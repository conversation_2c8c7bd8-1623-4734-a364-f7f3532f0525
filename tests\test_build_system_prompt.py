import pytest
from syrupy.extensions.amber import AmberSnapshotExtension
from app.ai_assistant.prompts.generic import (
    build_system_prompt,
    PROMPT_CONFIG,
    ContentType,
    YOUTUBE_CAPS_BLOCK,
    OPTIMIZATION_JSON_CLAUSE,
)

# Expected fragments for different configurations
EXPECTED_DEFAULT_INTRO = (
    "You are a professional content writer assistant specializing in general content."
)
EXPECTED_OPTIMIZATION_INTRO = PROMPT_CONFIG.get("business_optimization", "")
EXPECTED_YOUTUBE = YOUTUBE_CAPS_BLOCK
EXPECTED_BLOG_HINT = PROMPT_CONFIG.get("blog", "")
EXPECTED_ARTICLE_HINT = PROMPT_CONFIG.get("article", "")
EXPECTED_SOCIAL_HINT = PROMPT_CONFIG.get("social_media", "")
EXPECTED_JSON_CLAUSE = OPTIMIZATION_JSON_CLAUSE
BUSINESS_CONTEXT_HEADER = "\n\nBusiness Context:"
CONTENT_EXAMPLES_HEADER = "\n\nContent Examples:"
CONTENT_LIBRARY_HEADER = "\n\nContent from Knowledge Library:"
VIDEO_DATA_HEADER = "\n\nYouTube Video Data:"


# Define kwargs for various test scenarios
test_cases = [
    # Test case 1: Default invocation
    {},
    # Test case 2: Optimization mode enabled (no specific context)
    {"optimization_mode": True},
    # Test case 3: YouTube capabilities enabled
    {"include_youtube_capabilities": True},
    # Test case 4: Blog format specified
    {"content_format": "blog"},
    # Test case 5: Article format specified
    {"content_format": "article"},
    # Test case 5a: Social Media format specified
    {"content_format": "social_media"},
    # Test case 5b: Email format specified
    {"content_format": "email"},
    # Test case 6: Combined flags (Optimization + YouTube)
    {"optimization_mode": True, "include_youtube_capabilities": True},
    # Test case 7: Combined flags (Blog + YouTube)
    {"content_format": "blog", "include_youtube_capabilities": True},
    # Test case 8: Default with *only* business context
    {"business_context": {"name": "Test Biz", "brand_voice": "Professional"}},
    # Test case 9: Default with *only* content library items
    {"content_items": [{"title": "Lib Item 1", "content": "Lib Content 1"}]},
    # Test case 10: Default with multiple context items
    {
        "business_context": {"name": "Multi Biz"},
        "content_items": [{"title": "Multi Lib 1", "content": "Multi Content 1"}],
        "video_data": {"title": "Multi Vid Title"},
        "content_examples": [{"content": "Example Content 1"}],
    },
    # Test case 11: Optimization mode with specific optimization_context
    {
        "optimization_mode": True,
        "optimization_context": {"offer_description": "Opt Offer"},
    },
    # Test case 12: Optimization mode with optimization_context AND general business context
    {
        "optimization_mode": True,
        "optimization_context": {"offer_description": "Opt Offer 2"},
        "business_context": {"name": "General Biz Info 2"},
    },
    # Test case 13: Optimization mode with optimization_context AND original user message
    {
        "optimization_mode": True,
        "optimization_context": {"offer_description": "Opt Offer 3"},
        "original_user_message_for_optimization": "Improve my profile please",
    },
]


@pytest.mark.parametrize("kwargs", test_cases)
def test_build_system_prompt_snapshots(snapshot: AmberSnapshotExtension, kwargs):
    """Verify the generated prompt structure using snapshot testing."""
    # Syrupy automatically generates/updates snapshots in __snapshots__ directory
    assert build_system_prompt(**kwargs) == snapshot


def test_build_system_prompt_excludes_sections_when_data_is_none_or_empty():
    """Tests that context/library sections are omitted when data is None or empty."""
    # Test without business context
    prompt_no_biz = build_system_prompt(
        content_items=[{"title": "Lib Item", "content": "Lib Content"}]
    )
    assert BUSINESS_CONTEXT_HEADER.strip() not in prompt_no_biz
    assert CONTENT_LIBRARY_HEADER.strip() in prompt_no_biz
    assert CONTENT_EXAMPLES_HEADER.strip() not in prompt_no_biz
    assert VIDEO_DATA_HEADER.strip() not in prompt_no_biz

    # Test without library items (using content_items for this test)
    prompt_no_lib = build_system_prompt(business_context={"name": "Biz Context"})
    assert BUSINESS_CONTEXT_HEADER.strip() in prompt_no_lib
    assert CONTENT_LIBRARY_HEADER.strip() not in prompt_no_lib

    # Test with both explicitly None
    prompt_both_none = build_system_prompt(
        business_context=None,
        content_items=None,
        content_examples=None,
        video_data=None,
    )
    assert BUSINESS_CONTEXT_HEADER.strip() not in prompt_both_none
    assert CONTENT_LIBRARY_HEADER.strip() not in prompt_both_none
    assert CONTENT_EXAMPLES_HEADER.strip() not in prompt_both_none
    assert VIDEO_DATA_HEADER.strip() not in prompt_both_none

    # Test with empty library list
    prompt_empty_lib = build_system_prompt(
        business_context={"name": "Biz Context"}, content_items=[]
    )
    assert BUSINESS_CONTEXT_HEADER.strip() in prompt_empty_lib
    assert CONTENT_LIBRARY_HEADER.strip() not in prompt_empty_lib

    # Test with empty examples list
    prompt_empty_examples = build_system_prompt(
        business_context={"name": "Biz Context"}, content_examples=[]
    )
    assert (
        BUSINESS_CONTEXT_HEADER.strip() in prompt_empty_examples
    )  # Should still be present
    assert CONTENT_EXAMPLES_HEADER.strip() not in prompt_empty_examples
