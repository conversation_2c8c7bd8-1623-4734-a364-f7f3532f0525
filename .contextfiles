# Jinni Context Rules

# --- Inclusions ---

# Include application source
app/**/*.py
/app.py

# Include other source dirs if applicable
# Adjust or uncomment if these contain relevant source code
# src/
# frontend/
# scripts/
# jinni/
# graphiti/

# Include tests
tests/
playwright/
*.spec.js
/pytest.ini

# Include configuration
/requirements.txt
/package.json
/docker-compose*.yml
/Dockerfile
/.dockerignore
/.env.example
/.env.supabase.example
/playwright.config.js
# /pyproject.toml # Uncomment if you use it

# Include database related
migrations/
supabase/
/*.sql # Include SQL files at root too

# Include documentation
docs/
# /README.md # Uncomment if it exists and needed
# /DESIGN.md # Uncomment if it exists and needed

# Include specific meta/rule files if desired
# /.clinerules*
# /.roomodes
# /.roorules

# --- Exclusions ---
# Standard ignores (<PERSON><PERSON> has defaults, but explicit is clearer)
!.git/
!venv/
!node_modules/
!__pycache__/
!*.pyc

# Logs and Temp Files
!logs/
!*.log
!temp_*.txt
!test_output.log
!*.bak
!*~
!*.swp

# Build/Report Artifacts
!playwright-report/
!auth_benchmark_results/
!temp_report.xml

# Backups
!backup/
!backup_files/

# Media/Large Data Files (adjust as needed)
!*.png
!*.jpg
!*.jpeg
!*.gif
!*.mp4
!*.zip
!*.gz
!*.tar
!*.sqlite # Add other local DB file types if used

# Specific large/temp/secret JSONs observed (refine if necessary)
!package-lock.json # Very large, usually not needed for context
!youtube-debug-log.json
!outliers_results.json
!token.json # Potentially sensitive
!search_results.json
!profile-error.json
!login-response.json
!data_migration.log # This seems like a log file

# Other potentially irrelevant directories
!.roo/
# /Set Up/ # Review if this contains context-worthy files 