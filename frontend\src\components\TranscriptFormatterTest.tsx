import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  CircularProgress,
  Snackbar,
  Alert,
  Card,
  CardContent,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
} from "@mui/material";
import {
  AutoAwesome as AutoAwesomeIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
} from "@mui/icons-material";
import {
  formatTranscriptWithAI,
  checkOpenRouterApiKeySync,
  verifyOpenRouterApiKey,
} from "../services/openRouterService";
import MarkdownRenderer from "./MarkdownRenderer";

// Sample transcript
const SAMPLE_TRANSCRIPT = `John: Welcome to our podcast on content marketing strategies for 2023. I'm <PERSON>, and I'm joined by my colleague <PERSON>.

Sarah: Hi everyone! Today we're going to discuss how AI is transforming content creation and what that means for marketers.

John: Exactly. At (00:15) we'll start with the basics, then move on to advanced strategies, and finally share some case studies.

[Background music]

Sarah: Let's begin with the fundamentals. Content marketing has evolved significantly over the past decade.

<PERSON>: That's right.AI tools are now capable of generating blog posts, social media content, and even video scripts.But human creativity is still essential for strategy and emotional connection.

Sarah: Absolutely. At (05:30) I want to emphasize that while AI can help with production, the strategic thinking still needs to come from experienced marketers.

[Applause]

John: Let's look at some statistics. According to recent studies, companies using AI in their content marketing see a 40% increase in productivity and a 25% reduction in costs.

Sarah: Those are impressive numbers. And at (10:45) we should note that early adopters are seeing the biggest benefits.

John: Now, let's move on to some practical examples.Company X implemented an AI content strategy last year and saw their organic traffic increase by 200%.

Sarah: That's remarkable.They combined AI-generated content with human editing and strategic planning.

John: At (15:20) I think it's worth mentioning that they didn't just replace their content team with AI. Instead, they augmented their team's capabilities.

Sarah: Exactly.It's about finding the right balance between automation and human creativity.

[Music]

John: As we wrap up, I'd like to share three key takeaways. First, AI is a tool, not a replacement for human creativity. Second, start small and experiment. And third, always prioritize quality over quantity.

Sarah: Great summary, John. And for our listeners who want to learn more, we've put together a resource guide available on our website.

John: Thanks for listening, everyone! Join us next week when we'll be discussing video marketing trends for 2023.

[Applause]`;

const TranscriptFormatterTest: React.FC = () => {
  const [transcript, setTranscript] = useState<string>(SAMPLE_TRANSCRIPT);
  const [formattedTranscript, setFormattedTranscript] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [apiKeyStatus, setApiKeyStatus] = useState<{
    isConfigured: boolean;
    message: string;
  }>({
    isConfigured: true,
    message: "",
  });
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "error" | "info" | "warning";
  }>({
    open: false,
    message: "",
    severity: "info",
  });
  const [verifyingApiKey, setVerifyingApiKey] = useState<boolean>(false);

  // Check API key when component mounts
  useEffect(() => {
    const status = checkOpenRouterApiKeySync();
    setApiKeyStatus(status);

    if (!status.isConfigured) {
      setNotification({
        open: true,
        message: status.message,
        severity: "error",
      });
    }
  }, []);

  const handleFormatTranscript = async () => {
    if (!transcript.trim()) {
      setNotification({
        open: true,
        message: "Please enter a transcript to format",
        severity: "warning",
      });
      return;
    }

    // Store the original transcript
    const originalTranscript = transcript;

    setLoading(true);

    try {
      // Show notification that formatting has started
      setNotification({
        open: true,
        message: "Formatting transcript with AI...",
        severity: "info",
      });

      // Removed sending log

      // Format the transcript using the OpenRouter service
      const formatted = await formatTranscriptWithAI(originalTranscript);

      // Check if the formatted content is valid
      if (!formatted || formatted.trim().length === 0) {
        console.error("Received empty formatted content");
        throw new Error("Received empty response from formatting service");
      }

      // Removed received log

      // Update the formatted transcript
      setFormattedTranscript(formatted);

      // Show success notification
      setNotification({
        open: true,
        message: "Transcript formatted successfully with AI",
        severity: "success",
      });
    } catch (error: any) {
      console.error("Error formatting transcript with AI:", error);

      // Show specific error notification based on the error message
      let errorMessage = "Error formatting transcript. Please try again.";

      if (error.message) {
        if (error.message.includes("API key")) {
          errorMessage =
            "OpenRouter API key error. Please check your API key configuration.";
        } else if (error.message.includes("Authentication failed")) {
          errorMessage =
            "Authentication failed with OpenRouter API. Please check your API key.";
        } else if (error.message.includes("OpenRouter API error")) {
          errorMessage = error.message;
        }
      }

      setNotification({
        open: true,
        message: errorMessage,
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationClose = () => {
    setNotification({
      ...notification,
      open: false,
    });
  };

  // Add function to verify API key
  const handleVerifyApiKey = async () => {
    setVerifyingApiKey(true);

    try {
      const result = await verifyOpenRouterApiKey();

      setApiKeyStatus({
        isConfigured: result.isValid,
        message: result.message,
      });

      setNotification({
        open: true,
        message: result.message,
        severity: result.isValid ? "success" : "error",
      });
    } catch (error) {
      console.error("Error verifying API key:", error);

      setNotification({
        open: true,
        message: "Error verifying API key. Please try again.",
        severity: "error",
      });
    } finally {
      setVerifyingApiKey(false);
    }
  };

  return (
    <Box sx={{ p: 4, maxWidth: 1200, mx: "auto" }}>
      <Typography variant="h4" gutterBottom>
        Transcript Formatter Test
      </Typography>

      <Typography variant="body1" paragraph>
        This is a test component to verify that the OpenRouter integration works
        correctly. Edit the transcript below and click the "Format with AI"
        button to see the results.
      </Typography>

      {!apiKeyStatus.isConfigured ? (
        <Card
          sx={{ mb: 4, bgcolor: "error.light", color: "error.contrastText" }}
        >
          <CardContent>
            <Typography variant="h6" gutterBottom>
              API Key Configuration Error
            </Typography>
            <Typography variant="body1">{apiKeyStatus.message}</Typography>
            <Typography variant="body2" sx={{ mt: 2 }}>
              Please add your OpenRouter API key to the .env file as
              REACT_APP_OPENROUTER_API_KEY.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              onClick={handleVerifyApiKey}
              disabled={verifyingApiKey}
              startIcon={
                verifyingApiKey ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  <CheckCircleIcon />
                )
              }
              sx={{ mt: 2 }}
            >
              {verifyingApiKey ? "Verifying..." : "Verify API Key"}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card
          sx={{
            mb: 4,
            bgcolor: "success.light",
            color: "success.contrastText",
          }}
        >
          <CardContent
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Box>
              <Typography variant="h6" gutterBottom>
                API Key Configured Successfully
              </Typography>
              <Typography variant="body2">
                Your OpenRouter API key is properly configured.
              </Typography>
            </Box>
            <Button
              variant="contained"
              color="primary"
              onClick={handleVerifyApiKey}
              disabled={verifyingApiKey}
              startIcon={
                verifyingApiKey ? (
                  <CircularProgress size={20} color="inherit" />
                ) : (
                  <CheckCircleIcon />
                )
              }
            >
              {verifyingApiKey ? "Verifying..." : "Verify API Key"}
            </Button>
          </CardContent>
        </Card>
      )}

      <Box sx={{ display: "flex", gap: 2, mb: 4 }}>
        <Box sx={{ flex: 1 }}>
          <Typography variant="h6" gutterBottom>
            Original Transcript
          </Typography>

          <TextField
            fullWidth
            multiline
            minRows={15}
            value={transcript}
            onChange={(e) => setTranscript(e.target.value)}
            placeholder="Enter transcript here..."
            sx={{
              "& .MuiOutlinedInput-root": {
                fontFamily: "Consolas, monospace",
                fontSize: "0.9rem",
                lineHeight: 1.5,
              },
            }}
          />

          <Button
            variant="contained"
            color="primary"
            startIcon={
              loading ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <AutoAwesomeIcon />
              )
            }
            onClick={handleFormatTranscript}
            disabled={loading || !transcript.trim()}
            sx={{ mt: 2 }}
          >
            {loading ? "Formatting..." : "Format with AI"}
          </Button>
        </Box>

        <Box sx={{ flex: 1 }}>
          <Typography variant="h6" gutterBottom>
            Formatted Transcript
          </Typography>

          <Paper
            sx={{
              p: 3,
              minHeight: "400px",
              maxHeight: "600px",
              overflow: "auto",
              fontFamily: "Georgia, serif",
              fontSize: "1rem",
              lineHeight: 1.6,
              "& strong": { fontWeight: "bold" },
              "& code": {
                backgroundColor: "rgba(0, 0, 0, 0.05)",
                padding: "2px 4px",
                borderRadius: "3px",
                fontFamily: "Consolas, monospace",
                fontSize: "0.9em",
              },
            }}
          >
            {formattedTranscript ? (
              <MarkdownRenderer
                content={formattedTranscript}
                className="markdown-content"
              />
            ) : (
              <Typography color="text.secondary" sx={{ fontStyle: "italic" }}>
                Formatted transcript will appear here...
              </Typography>
            )}
          </Paper>
        </Box>
      </Box>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleNotificationClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      >
        <Alert
          onClose={handleNotificationClose}
          severity={notification.severity}
          variant="filled"
          sx={{ width: "100%" }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TranscriptFormatterTest;
