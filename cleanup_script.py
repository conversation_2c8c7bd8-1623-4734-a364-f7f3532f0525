#!/usr/bin/env python3
"""
Vault Cleanup Script
Purpose: Clean up after initial refactoring - remove empty folders and consolidate remaining resources
"""

import os
import shutil
from pathlib import Path
from typing import List

class VaultCleanup:
    def __init__(self, vault_path: str):
        self.vault_path = Path(vault_path)
        
    def remove_empty_folders(self) -> int:
        """Remove all empty folders recursively"""
        removed_count = 0
        
        # Multiple passes to handle nested empty folders
        for _ in range(5):  # Max 5 passes should be enough
            folders_removed_this_pass = 0
            
            for root, dirs, files in os.walk(self.vault_path, topdown=False):
                for dir_name in dirs:
                    dir_path = Path(root) / dir_name
                    try:
                        # Check if directory is empty
                        if not any(dir_path.iterdir()):
                            dir_path.rmdir()
                            print(f"[REMOVED] Empty folder: {dir_path.relative_to(self.vault_path)}")
                            removed_count += 1
                            folders_removed_this_pass += 1
                    except (OSError, PermissionError) as e:
                        print(f"[WARNING] Could not remove {dir_path}: {e}")
            
            if folders_removed_this_pass == 0:
                break  # No more empty folders found
                
        return removed_count

    def consolidate_remaining_resources(self) -> None:
        """Move remaining resource folders into the 6 target categories"""
        resources_path = self.vault_path / "3. Resources"
        
        if not resources_path.exists():
            print("[WARNING] Resources folder not found")
            return
            
        # Target categories that should exist
        target_categories = {
            "Business Strategy",
            "Content Creation", 
            "People & Network",
            "AI & Tech Tools",
            "Learning Materials",
            "Personal Interests"
        }
        
        # Create target categories if they don't exist
        for category in target_categories:
            category_path = resources_path / category
            category_path.mkdir(exist_ok=True)
        
        # Map old folders to new categories
        folder_mappings = {
            # Move to Business Strategy
            "Business": "Business Strategy",
            "Operations": "Business Strategy", 
            "Marketing": "Business Strategy",
            "Finance": "Business Strategy",
            "Research & Analysis": "Business Strategy",
            
            # Move to Content Creation
            "Content": "Content Creation",
            "Templates": "Content Creation",
            "Creative": "Content Creation",
            
            # Move to People & Network  
            "People": "People & Network",
            "Contacts": "People & Network",
            
            # Move to AI & Tech Tools
            "AI": "AI & Tech Tools",
            "Tech": "AI & Tech Tools",
            "Tools": "AI & Tech Tools",
            "Processes": "AI & Tech Tools",
            "Troubleshooting": "AI & Tech Tools",
            
            # Move to Learning Materials
            "Learning": "Learning Materials",
            "Education": "Learning Materials", 
            "Reading": "Learning Materials",
            "Personal Development": "Learning Materials",
            "Self": "Learning Materials",
            
            # Move to Personal Interests
            "Gaming": "Personal Interests",
            "Music": "Personal Interests", 
            "Sports": "Personal Interests",
            "Hobbies": "Personal Interests",
            "Entertainment": "Personal Interests",
            "MMA Strategies": "Personal Interests",
            "Training Techniques": "Personal Interests",
            "Web3": "Personal Interests"
        }
        
        # Move folders according to mapping
        for item in resources_path.iterdir():
            if item.is_dir() and item.name not in target_categories:
                target_category = folder_mappings.get(item.name)
                if target_category:
                    target_path = resources_path / target_category / item.name
                    try:
                        # Create target directory if it doesn't exist
                        target_path.parent.mkdir(exist_ok=True)
                        
                        # Move the folder
                        shutil.move(str(item), str(target_path))
                        print(f"[MOVED] {item.name} -> {target_category}/{item.name}")
                    except Exception as e:
                        print(f"[ERROR] Failed to move {item.name}: {e}")
                else:
                    print(f"[UNMAPPED] Folder not mapped: {item.name}")

    def update_allowed_root_files(self) -> None:
        """Update the allowed root files list to include script"""
        # This is just for reference - the verification should allow our script
        pass
        
    def clean_vault(self) -> None:
        """Execute complete cleanup process"""
        print("=" * 60)
        print("VAULT CLEANUP SCRIPT")
        print("=" * 60)
        print(f"Vault: {self.vault_path}")
        print()
        
        print("Phase 1: Consolidating remaining resource folders...")
        self.consolidate_remaining_resources()
        print()
        
        print("Phase 2: Removing empty folders...")
        removed_count = self.remove_empty_folders()
        print(f"[SUCCESS] Removed {removed_count} empty folders")
        print()
        
        print("Phase 3: Final verification...")
        self.verify_cleanup()
        print()
        
        print("=" * 60)
        print("CLEANUP COMPLETE!")
        print("=" * 60)

    def verify_cleanup(self) -> None:
        """Verify the cleanup results"""
        resources_path = self.vault_path / "3. Resources"
        
        if resources_path.exists():
            resource_categories = [d.name for d in resources_path.iterdir() if d.is_dir()]
            print(f"Resource categories: {len(resource_categories)}")
            for category in sorted(resource_categories):
                print(f"  - {category}")
        
        # Count root files (excluding allowed ones)
        allowed_root_files = {
            "00 - Master Index.md", 
            "0. System",
            "vault_refactor_script.py",
            "cleanup_script.py"
        }
        
        root_files = [
            item.name for item in self.vault_path.iterdir() 
            if item.is_file() and item.name not in allowed_root_files
        ]
        
        print(f"Unexpected root files: {len(root_files)}")
        for file in root_files:
            print(f"  - {file}")
            
        # Count empty folders
        empty_folders = []
        for root, dirs, files in os.walk(self.vault_path):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                if not any(dir_path.iterdir()):
                    empty_folders.append(dir_path.relative_to(self.vault_path))
        
        print(f"Empty folders remaining: {len(empty_folders)}")
        for folder in empty_folders[:10]:  # Show first 10
            print(f"  - {folder}")
        if len(empty_folders) > 10:
            print(f"  ... and {len(empty_folders) - 10} more")

if __name__ == "__main__":
    import sys
    
    # Vault path
    vault_path = sys.argv[1] if len(sys.argv) > 1 else r"F:\ValentinMarketing\ModernMeditations"
    
    if not Path(vault_path).exists():
        print(f"[ERROR] Vault path does not exist: {vault_path}")
        sys.exit(1)
    
    cleanup = VaultCleanup(vault_path)
    cleanup.clean_vault() 