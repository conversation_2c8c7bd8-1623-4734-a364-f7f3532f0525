[build-system]
requires = [ "setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "workspace-mcp"
version = "1.0.2"
description = "Comprehensive, highly performant Google Workspace Streamable HTTP & SSE MCP Server for Calendar, Gmail, Docs, Sheets, Slides & Drive"
readme = "README.md"
keywords = [ "mcp", "google", "workspace", "llm", "ai", "claude", "model", "context", "protocol", "server"]
requires-python = ">=3.11"
dependencies = [
 "fastapi>=0.115.12",
 "fastmcp>=2.3.3",
 "google-api-python-client>=2.168.0",
 "google-auth-httplib2>=0.2.0",
 "google-auth-oauthlib>=1.2.2",
 "httpx>=0.28.1",
 "pyjwt>=2.10.1",
 "tomlkit",
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Environment :: Console",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Natural Language :: English",
    "Operating System :: OS Independent",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Dynamic Content",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Communications :: Chat",
    "Topic :: Office/Business",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Typing :: Typed"
]

[[project.authors]]
name = "Taylor Wilsdon"
email = "<EMAIL>"

[project.license]
text = "MIT"

[project.urls]
Homepage = "https://workspacemcp.com"
Repository = "https://github.com/taylorwilsdon/google_workspace_mcp"
Documentation = "https://github.com/taylorwilsdon/google_workspace_mcp#readme"
Issues = "https://github.com/taylorwilsdon/google_workspace_mcp/issues"
Changelog = "https://github.com/taylorwilsdon/google_workspace_mcp/releases"

[project.scripts]
workspace-mcp = "main:main"

[tool.setuptools]
packages = [ "auth", "gcalendar", "core", "gdocs", "gdrive", "gmail", "gchat", "gsheets", "gforms", "gslides"]
py-modules = [ "main"]
