# Writer v2 Environment Configuration Template
# Copy this content to .env.local in the root directory and fill in your actual values

# Backend Security
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ACCESS_TOKEN_EXPIRES=31536000

# Supabase Configuration (REQUIRED - all these are needed)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
SUPABASE_JWT_SECRET=your-supabase-jwt-secret

# AI Services (at least one required)
AI_PROVIDER=openrouter
OPENROUTER_API_KEY=your-openrouter-api-key
# OR use OpenAI instead:
# AI_PROVIDER=openai
# OPENAI_API_KEY=your-openai-api-key

# YouTube API (optional)
YOUTUBE_API_KEY=your-youtube-api-key

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5000

# Logging
LOG_LEVEL=INFO

# Caching
CACHE_DEFAULT_TTL=300
CACHE_DEFAULT_MAX_SIZE=100

# Development vs Production
FASTAPI_ENV=development 