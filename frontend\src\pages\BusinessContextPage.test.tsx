import React from "react";
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import "@testing-library/jest-dom/extend-expect";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import { Provider } from "react-redux";
import { configureStore as createMockStore } from "@reduxjs/toolkit"; // Using Redux Toolkit to create a mock store

import BusinessContextPage from "./BusinessContextPage";
import { AuthProvider } from "../contexts/AuthContext"; // Assuming AuthContext provides necessary user info
import { BusinessContextSelectionProvider } from "../contexts/BusinessContextSelectionContext";

// --- Mocks ---
// Mock API calls used by BusinessContextPage and its children
jest.mock("../services/api", () => ({
  api: {
    get: jest.fn((url) => {
      if (url.includes("/business-contexts")) {
        return Promise.resolve({
          data: {
            contexts: [
              {
                id: "ctx1",
                name: "Context One",
                user_id: "test-user" /* other fields */,
              },
              {
                id: "ctx2",
                name: "Context Two",
                user_id: "test-user" /* other fields */,
              },
            ],
          },
        });
      }
      return Promise.resolve({ data: {} }); // Default mock
    }),
    delete: jest.fn().mockResolvedValue({ data: {} }), // Mock the delete endpoint
  },
}));

// Mock the actual delete function if it's separate or within a hook/service
// For now, we'll spy on the api.delete mock above.

// --- Mock API setup ---
const mockApiGet = jest.fn();
const mockApiPost = jest.fn();
const mockApiPut = jest.fn();
const mockApiDelete = jest.fn();

jest.mock("../services/api", () => ({
  api: {
    get: mockApiGet,
    post: mockApiPost,
    put: mockApiPut,
    delete: mockApiDelete,
  },
}));

// Mock initial context data
const mockContexts = [
  {
    id: "ctx1",
    user_id: "test-user",
    name: "Context One",
    profile_overview: "Overview for Context One",
    content_focus: ["Topic A", "Topic B"],
    primary_objectives: "Objectives for One",
    offer_description: "Offer One",
    target_audience: "Audience One",
    audience_motivation: "Motivation One",
    brand_voice: "Voice One",
    key_benefits: "Benefits One",
    unique_value_proposition: "UVP One",
    audience_decision_style: "Style One",
    audience_preference_structure: "Structure One",
    audience_decision_speed: "Speed One",
    audience_reaction_to_change: "Reaction One",
    recommended_platforms: ["Platform X", "Platform Y"],
    recommended_content_formats: ["Format 1", "Format 2"],
    created_at: "2023-01-01T00:00:00Z",
    updated_at: "2023-01-01T00:00:00Z",
  },
  {
    id: "ctx2",
    user_id: "test-user",
    name: "Context Two",
    profile_overview: "Overview for Context Two",
    // ... other fields
    created_at: "2023-01-02T00:00:00Z",
    updated_at: "2023-01-02T00:00:00Z",
  },
];

// Initial API Mock Setup
beforeEach(() => {
  mockApiGet.mockClear();
  mockApiPost.mockClear();
  mockApiPut.mockClear();
  mockApiDelete.mockClear();

  // Default GET mock for initial load
  mockApiGet.mockResolvedValue({ data: { contexts: mockContexts } });
  // Default POST mock for wizard
  mockApiPost.mockResolvedValue({
    data: {
      context: { ...mockContexts[0], id: "new-ctx", name: "New Context" },
    },
  });
  // Default DELETE mock
  mockApiDelete.mockResolvedValue({ data: {} });
  // Default PUT mock for save
  mockApiPut.mockResolvedValue({ data: {} });
});

// Create a simple mock Redux store using Redux Toolkit
const initialState = {
  ui: { darkMode: false, sidebarOpen: true }, // Adjust based on your actual initial state
  activity: { logs: [] },
};
let store = createMockStore({ reducer: () => initialState });

// Mock AuthContext
jest.mock("../contexts/AuthContext", () => ({
  useAuth: () => ({ session: { user: { id: "test-user" } }, loading: false }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ), // Simple pass-through for wrapper
}));

// Mock Selection Context
jest.mock("../contexts/BusinessContextSelectionContext", () => ({
  useBusinessContextSelection: () => ({
    selectedBusinessContextId: "ctx1",
    setSelectedBusinessContextId: jest.fn(),
    contexts: [
      { id: "ctx1", name: "Context One" },
      { id: "ctx2", name: "Context Two" },
    ],
    setContexts: jest.fn(),
    loading: false,
    error: null,
  }),
  BusinessContextSelectionProvider: ({
    children,
  }: {
    children: React.ReactNode;
  }) => <div>{children}</div>,
}));

// Mock the ConfirmationDialog component itself to verify props passed to it
jest.mock("../components/ConfirmationDialog", () => ({
  __esModule: true,
  default: jest.fn(({ open, title, content, onConfirm, onCancel }) => {
    if (!open) return null;
    return (
      <div data-testid="mock-confirmation-dialog">
        <h1>{title}</h1>
        <div>{content}</div>
        <button data-testid="confirm-button" onClick={onConfirm}>
          Confirm
        </button>
        <button data-testid="cancel-button" onClick={onCancel}>
          Cancel
        </button>
      </div>
    );
  }),
}));

const MockConfirmationDialog =
  require("../components/ConfirmationDialog").default;

// Mock TagInput so we can inspect add/remove behavior
jest.mock("../components/TagInput", () => ({
  __esModule: true,
  default: jest.fn(({ label, value, onChange, placeholder, disabled }) => (
    <>
      <label>{label}</label> {/* Add label for finding */}
      <input
        data-testid={`tag-input-${label.replace(/\s+/g, "-")}`}
        value={value.join(",")}
        placeholder={placeholder}
        disabled={disabled}
        onChange={(e) =>
          onChange(e.target.value.split(",").filter((tag) => tag.trim() !== ""))
        }
      />
    </>
  )),
}));
const MockTagInput = require("../components/TagInput").default;

// Mock Wizard so we can detect it opening
jest.mock("../components/CreateProfileWizard", () => ({
  __esModule: true,
  default: jest.fn(({ onClose, onSubmit }) => (
    <div data-testid="mock-wizard">
      <button
        data-testid="wizard-submit"
        onClick={() =>
          onSubmit({
            name: "New Wizard Context",
            profile_overview: "Wizard overview",
          })
        }
      >
        Submit Wizard
      </button>
      <button data-testid="wizard-close" onClick={onClose}>
        Close Wizard
      </button>
    </div>
  )),
}));
const MockWizard = require("../components/CreateProfileWizard").default;

// Helper to render with providers
const renderWithProviders = (ui: React.ReactElement) => {
  store = createMockStore({ reducer: () => initialState });
  const theme = createTheme();
  return render(
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <AuthProvider>
          <BusinessContextSelectionProvider>
            {ui}
          </BusinessContextSelectionProvider>
        </AuthProvider>
      </ThemeProvider>
    </Provider>
  );
};

// Mock the sidebar to control its props easily
jest.mock("../components/BusinessContextSidebar", () => ({
  __esModule: true,
  default: jest.fn((props) => (
    <div data-testid="mock-sidebar">
      {/* Simulate delete button click via a test button */}
      <button
        data-testid="sidebar-delete-trigger"
        onClick={() => props.onDeleteContexts(props.selectedIds || ["ctx1"])} // Use selectedIds if passed, else default
      >
        Trigger Delete
      </button>
      {/* Add New Context Trigger */}
      <button
        data-testid="sidebar-new-trigger"
        onClick={() => props.onNewContextClick()}
      >
        New Context
      </button>
      {/* Display contexts for debugging */}
      {props.contexts?.map((c: any) => (
        <div
          key={c.id}
          onClick={() => props.onContextSelect(c.id)}
          data-testid={`sidebar-context-${c.id}`}
        >
          {c.name}
        </div>
      ))}
    </div>
  )),
}));
const MockSidebar = require("../components/BusinessContextSidebar").default;

// --- Tests ---

describe("BusinessContextPage - Delete Confirmation", () => {
  beforeEach(() => {
    MockConfirmationDialog.mockClear();
    MockSidebar.mockClear();
    // Reset API mocks specific to this suite if needed, but global reset might be okay
  });

  // Helper to get the props passed to our mocked dialog
  const getDialogProps = () => MockConfirmationDialog.mock.calls[0][0];

  test("confirmation dialog is not visible initially", async () => {
    // No need to wrap simple render in act unless it triggers async updates *immediately*
    renderWithProviders(<BusinessContextPage />);
    // Wait for initial data fetch to complete using waitFor
    await waitFor(() => expect(mockApiGet).toHaveBeenCalled());
    expect(
      screen.queryByTestId("mock-confirmation-dialog")
    ).not.toBeInTheDocument();
  });

  test("clicking sidebar delete trigger opens confirmation dialog with correct text", async () => {
    renderWithProviders(<BusinessContextPage />);
    // Wait for initial fetch before interacting
    await waitFor(() => expect(mockApiGet).toHaveBeenCalled());

    const triggerButton = screen.getByTestId("sidebar-delete-trigger");

    // fireEvent is often synchronous, but the *result* (state update causing dialog) might be async.
    // Using findBy* implicitly handles waiting.
    fireEvent.click(triggerButton);

    // Check if the mocked dialog is rendered using findBy*
    await screen.findByTestId("mock-confirmation-dialog");

    // Check props passed to the mocked dialog (synchronous check after dialog is found)
    expect(MockConfirmationDialog).toHaveBeenCalledWith(
      expect.objectContaining({
        open: true,
        title: "Confirm Deletion",
        content: expect.stringContaining(
          "Are you sure you want to delete 1 context? This action cannot be undone."
        ),
      }),
      {}
    );
  });

  test('clicking "Cancel" in dialog closes it and does not call delete API', async () => {
    renderWithProviders(<BusinessContextPage />);
    await waitFor(() => expect(mockApiGet).toHaveBeenCalled());

    // Open the dialog
    const triggerButton = screen.getByTestId("sidebar-delete-trigger");
    fireEvent.click(triggerButton);
    // Use findBy* to ensure dialog is present before interacting
    const cancelButton = await screen.findByTestId("cancel-button");

    // Click Cancel in the mocked dialog
    // No act needed here if the effects are awaited below
    fireEvent.click(cancelButton);

    // Assert dialog is closed using waitFor with queryBy*
    await waitFor(() => {
      expect(
        screen.queryByTestId("mock-confirmation-dialog")
      ).not.toBeInTheDocument();
    });
    expect(mockApiDelete).not.toHaveBeenCalled();
  });

  test('clicking "Confirm" in dialog calls delete API with correct IDs and closes dialog', async () => {
    renderWithProviders(<BusinessContextPage />);
    await waitFor(() => expect(mockApiGet).toHaveBeenCalled());

    const idsToDelete = ["ctx1"];

    // Open the dialog
    const triggerButton = screen.getByTestId("sidebar-delete-trigger");
    fireEvent.click(triggerButton);
    // Use findBy* to ensure dialog is present
    const confirmButton = await screen.findByTestId("confirm-button");

    // Click Confirm in the mocked dialog
    // No act needed here if effects are awaited
    fireEvent.click(confirmButton);

    // Assert API called correctly (waitFor needed as API call is async)
    await waitFor(() => {
      expect(mockApiDelete).toHaveBeenCalledTimes(1);
    });
    expect(mockApiDelete).toHaveBeenCalledWith("/business/batch", {
      data: { ids: idsToDelete },
    });

    // Assert dialog is closed
    await waitFor(() => {
      expect(
        screen.queryByTestId("mock-confirmation-dialog")
      ).not.toBeInTheDocument();
    });
  });
});

/*** HEADER & GUIDANCE ***/
describe("BusinessContextPage – Header & Guidance", () => {
  test("renders context title, overview and guidance caption when context is selected", async () => {
    renderWithProviders(<BusinessContextPage />);
    // Wait for initial data and context selection
    await waitFor(() => {
      expect(mockApiGet).toHaveBeenCalledWith("/business/");
      expect(screen.getByRole("heading", { level: 4 })).toBeInTheDocument();
    });

    // Title (using mock data)
    expect(screen.getByRole("heading", { level: 4 })).toHaveTextContent(
      "Context One"
    );
    // Subtitle (overview)
    expect(screen.getByText("Overview for Context One")).toBeInTheDocument();
    // Guidance caption
    expect(
      screen.getByText(
        "Review or edit the profile details in the sections below."
      )
    ).toBeInTheDocument();
  });

  test("does not render header when no context is selected", async () => {
    // Override mock for this specific test to return no contexts initially
    mockApiGet.mockResolvedValueOnce({ data: { contexts: [] } });
    renderWithProviders(<BusinessContextPage />);
    await waitFor(() => expect(mockApiGet).toHaveBeenCalledWith("/business/"));

    expect(screen.queryByRole("heading", { level: 4 })).not.toBeInTheDocument();
    expect(
      screen.queryByText(
        "Review or edit the profile details in the sections below."
      )
    ).not.toBeInTheDocument();
    expect(screen.getByText(/No contexts found/)).toBeInTheDocument();
  });
});

/*** ACCORDION EXPAND/COLLAPSE ***/
describe("BusinessContextPage – Accordion Expand/Collapse", () => {
  const sections = [
    { name: /Core Profile/, firstFieldLabel: /Profile Name/ },
    { name: /Brand & Voice/, firstFieldLabel: /Brand Voice/ },
    {
      name: /Target Audience & Decision Style/,
      firstFieldLabel: /Target Audience Profile/,
    },
    { name: /Channels & Formats/, firstFieldLabel: /Recommended Channels/ }, // Using TagInput label
  ];

  test.each(sections)(
    "renders section $name and toggles its visibility",
    async ({ name, firstFieldLabel }) => {
      renderWithProviders(<BusinessContextPage />);
      await waitFor(() => expect(mockApiGet).toHaveBeenCalled());

      const summary = screen.getByText(name);
      expect(summary).toBeInTheDocument();

      // Details should be collapsed initially
      // Use queryBy* because it shouldn't exist or be visible yet
      expect(screen.queryByLabelText(firstFieldLabel)).not.toBeVisible();

      // Expand
      fireEvent.click(summary);

      // Now details contain the first field and it should be visible
      // Use findBy* or getBy* after click, as it should now exist
      await waitFor(() => {
        expect(screen.getByLabelText(firstFieldLabel)).toBeVisible();
      });

      // Collapse again
      fireEvent.click(summary);
      await waitFor(() => {
        expect(screen.queryByLabelText(firstFieldLabel)).not.toBeVisible();
      });
    }
  );
});

/*** EDIT MODE TOGGLE & TEXTFIELD STATES ***/
describe("BusinessContextPage – Edit Mode & TextField States", () => {
  beforeEach(async () => {
    renderWithProviders(<BusinessContextPage />);
    // Wait for initial data load
    await waitFor(() => expect(mockApiGet).toHaveBeenCalled());
    // Ensure a context is loaded and displayed
    await screen.findByText(/Core Profile/);
  });

  test("clicking edit icon enables fields in that section and disables others", async () => {
    // Find edit button specifically for Core Profile (might need better selector if icons aren't unique)
    const coreSummary = screen.getByText(/Core Profile/).closest("button"); // Get summary button
    const coreEditBtn = coreSummary?.querySelector(
      'button[aria-label*="Edit"]'
    ); // Find edit button within
    expect(coreEditBtn).toBeInTheDocument();

    act(() => {
      fireEvent.click(coreEditBtn!);
    });

    // Wait for fields to become visible/enabled after edit click
    await waitFor(() => {
      expect(screen.getByLabelText(/Profile Name/)).toBeEnabled();
    });
    expect(screen.getByLabelText(/Profile Overview/)).toBeEnabled();

    // Expand another section (e.g., Brand & Voice)
    const brandVoiceSummary = screen.getByText(/Brand & Voice/);
    act(() => {
      fireEvent.click(brandVoiceSummary);
    });

    // Wait for its fields to appear and check if they are disabled
    await waitFor(() => {
      expect(screen.getByLabelText(/Brand Voice/)).toBeVisible();
      expect(screen.getByLabelText(/Brand Voice/)).toBeDisabled();
      expect(screen.getByLabelText(/Key Benefits/)).toBeDisabled();
    });

    // Check save/cancel footer appears
    expect(
      screen.getByRole("button", { name: /Save Changes/ })
    ).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /Cancel/ })).toBeInTheDocument();
  });

  test("clicking cancel reverts changes and exits edit mode", async () => {
    const coreSummary = screen.getByText(/Core Profile/).closest("button");
    const coreEditBtn = coreSummary?.querySelector(
      'button[aria-label*="Edit"]'
    );
    act(() => {
      fireEvent.click(coreEditBtn!);
    });
    await waitFor(() =>
      expect(screen.getByLabelText(/Profile Name/)).toBeEnabled()
    );

    // Simulate changing a value
    const profileNameInput = screen.getByLabelText(/Profile Name/);
    act(() => {
      fireEvent.change(profileNameInput, { target: { value: "Updated Name" } });
    });
    expect(profileNameInput).toHaveValue("Updated Name");

    // Click Cancel
    const cancelButton = screen.getByRole("button", { name: /Cancel/ });
    act(() => {
      fireEvent.click(cancelButton);
    });

    // Wait for fields to become disabled again
    await waitFor(() => {
      expect(screen.getByLabelText(/Profile Name/)).toBeDisabled();
    });
    // Check if value reverted (This depends on implementation, might need form reset logic)
    // For now, just check edit mode is exited (footer disappears)
    expect(
      screen.queryByRole("button", { name: /Save Changes/ })
    ).not.toBeInTheDocument();
  });

  test("clicking save sends PUT request and exits edit mode", async () => {
    const coreSummary = screen.getByText(/Core Profile/).closest("button");
    const coreEditBtn = coreSummary?.querySelector(
      'button[aria-label*="Edit"]'
    );
    act(() => {
      fireEvent.click(coreEditBtn!);
    });
    await waitFor(() =>
      expect(screen.getByLabelText(/Profile Name/)).toBeEnabled()
    );

    // Simulate changing a value
    const profileNameInput = screen.getByLabelText(/Profile Name/);
    act(() => {
      fireEvent.change(profileNameInput, {
        target: { value: "Updated Name for Save" },
      });
    });

    // Click Save
    const saveButton = screen.getByRole("button", { name: /Save Changes/ });
    act(() => {
      fireEvent.click(saveButton);
    });

    // Wait for PUT request and potential UI update
    await waitFor(() => {
      expect(mockApiPut).toHaveBeenCalledWith(
        "/business/ctx1",
        expect.objectContaining({ name: "Updated Name for Save" })
      );
      // Check edit mode exited
      expect(
        screen.queryByRole("button", { name: /Save Changes/ })
      ).not.toBeInTheDocument();
      expect(screen.getByLabelText(/Profile Name/)).toBeDisabled();
    });
  });
});

/*** TAG INPUT ADD/REMOVE ***/
describe("BusinessContextPage – TagInput Add/Remove", () => {
  beforeEach(async () => {
    renderWithProviders(<BusinessContextPage />);
    await waitFor(() => expect(mockApiGet).toHaveBeenCalled());
    await screen.findByText(/Core Profile/);
    // Enter edit mode for Core Profile
    const coreSummary = screen.getByText(/Core Profile/).closest("button");
    const coreEditBtn = coreSummary?.querySelector(
      'button[aria-label*="Edit"]'
    );
    act(() => {
      fireEvent.click(coreEditBtn!);
    });
    await waitFor(() =>
      expect(screen.getByLabelText(/Core Content Topics/)).toBeInTheDocument()
    );
  });

  test("allows adding and removing tags in Core Content Topics", async () => {
    const tagInput = screen.getByTestId("tag-input-Core-Content-Topics");
    expect(tagInput).toBeEnabled();

    // Initial value from mock data
    expect(tagInput).toHaveValue("Topic A,Topic B");

    // Add a tag
    act(() => {
      fireEvent.change(tagInput, {
        target: { value: "Topic A,Topic B,New Topic" },
      });
    });
    // Mocked input directly reflects the change
    expect(tagInput).toHaveValue("Topic A,Topic B,New Topic");

    // Simulate removing (clear input by setting value)
    act(() => {
      fireEvent.change(tagInput, { target: { value: "Topic A" } });
    });
    expect(tagInput).toHaveValue("Topic A");
  });
});

/*** WIZARD SUBMISSION ***/
describe("BusinessContextPage – CreateProfileWizard Flow", () => {
  beforeEach(async () => {
    renderWithProviders(<BusinessContextPage />);
    await waitFor(() => expect(mockApiGet).toHaveBeenCalled());
    MockWizard.mockClear(); // Clear wizard mock calls
  });

  test("clicking 'New Context' opens wizard, submission calls POST and refreshes list", async () => {
    // Trigger new-context via Sidebar mock button
    const newContextButton = screen.getByTestId("sidebar-new-trigger");
    act(() => {
      fireEvent.click(newContextButton);
    });

    // Wizard appears
    await waitFor(() => {
      expect(screen.getByTestId("mock-wizard")).toBeInTheDocument();
    });

    // Check that the main context view (accordions) is hidden
    expect(screen.queryByText(/Core Profile/)).not.toBeInTheDocument();

    // Submit wizard
    const submitWizardButton = screen.getByTestId("wizard-submit");
    act(() => {
      fireEvent.click(submitWizardButton);
    });

    // Wait for POST request and subsequent GET request (refresh)
    await waitFor(() => {
      expect(mockApiPost).toHaveBeenCalledWith("/business/", {
        name: "New Wizard Context",
        profile_overview: "Wizard overview",
      });
      // Expect a fetch *after* the post to refresh the list
      expect(mockApiGet).toHaveBeenCalledTimes(2); // Initial load + refresh
    });

    // Wizard should disappear after successful submission
    expect(screen.queryByTestId("mock-wizard")).not.toBeInTheDocument();
    // Main context view should reappear (check for an accordion title)
    expect(screen.getByText(/Core Profile/)).toBeInTheDocument();
    // Check if the sidebar was updated (assuming fetch returns new context)
    // await screen.findByTestId('sidebar-context-new-ctx'); // Check if new item appears
  });

  test("closing the wizard hides it and shows the context view", async () => {
    const newContextButton = screen.getByTestId("sidebar-new-trigger");
    act(() => {
      fireEvent.click(newContextButton);
    });
    await waitFor(() =>
      expect(screen.getByTestId("mock-wizard")).toBeInTheDocument()
    );

    const closeWizardButton = screen.getByTestId("wizard-close");
    act(() => {
      fireEvent.click(closeWizardButton);
    });

    await waitFor(() => {
      expect(screen.queryByTestId("mock-wizard")).not.toBeInTheDocument();
    });
    expect(screen.getByText(/Core Profile/)).toBeInTheDocument(); // Main view returns
  });
});
