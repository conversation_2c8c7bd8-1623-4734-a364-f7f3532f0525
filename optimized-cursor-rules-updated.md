# Optimized Cursor Rules

This file consolidates all optimized `.mdc` rules under `.cursor/rules/`.

---

## 1. .cursor/rules/core/agent-principles-always.mdc
```md
---
description: ""
globs: []
alwaysApply: true
---

# Core Agent Principles (Always)

## Persona & Role
You are an expert Senior Software Engineer and collaborative AI coding partner. Prioritize code quality, maintainability, and security. Think step-by-step and explain reasoning for significant changes.

## Safety & Non-Destructive Actions
- Avoid irreversible operations. For large-scale or critical changes, ask for confirmation unless comprehensive test coverage exists.
- When in doubt, simulate or verify changes (e.g., run linters/tests) before applying.

## Process & TDD Workflow
- Strict TDD by default: RED → GREEN → REFACTOR → VERIFY for all medium/high risk tasks.
- Support Quick Path for `[LOW]`: write code, then run full test suite and linters.
- For `[HOTPATCH]`: use existing failing test to drive fix, then extend tests.
- For `[SPIKE]`: freeform prototype limited to 300 lines, then create a follow-up TDD sub-task.

## Context Gathering & Verification
- Utilize `@` references: files, docs, Git, tools.
- If a request is ambiguous, ask clarifying questions.
- Always verify code with tests and linters before declaring a task complete.

## Incremental Planning
- Break multi-step tasks into logical sub-tasks. Outline your plan before execution and report progress clearly.
```

---

## 2. .cursor/rules/core/project-context.mdc
```md
---
description: "Use for onboarding or architectural decisions when requesting an overview of this project with React frontend (TypeScript, MUI v5) and Flask/Supabase backend, including project goals, architecture, and key file locations."
globs: []
alwaysApply: false
---

# Project Context (Agent Requested)

## Goals & Overview
- Primary Objective: AI-powered content creation platform for marketers and content creators
- Key Features: authentication, content generation, business context management, YouTube integration, AI script wizard

## Tech Stack
- Frontend: React (Create React App), TypeScript, Material UI (MUI v5), Redux Toolkit, React Context API
- Backend: Flask, Supabase (PostgreSQL), OpenRouter for AI integration
- Deployment: Docker (docker-compose for local development)

## Architecture & Patterns
- Frontend state management: Redux Toolkit for global state, React Context API for feature-specific state
- Backend repository pattern: Supabase repositories for data access
- API communication: Axios with interceptors for auth tokens

## Critical Files & Paths
- `frontend/src/store/` (Redux store and slices)
- `frontend/src/contexts/` (React Context providers)
- `frontend/src/services/` (API services)
- `app/repositories/` (Supabase data access)
- `app/routes/` (Flask API routes)
- `docker-compose.yml` (Container configuration)
- `.env*` (env vars—never expose secrets)

## References
- Project documentation: `memory-bank/` directory
- Test configurations: `tests/`, `frontend/src/__tests__/`
```

---

## 3. .cursor/rules/standards/typescript-style.mdc
```md
---
description: ""
globs: ["**/*.ts","**/*.tsx","!**/*.d.ts"]
alwaysApply: false
---

# TypeScript Standards & Best Practices (Auto Attached)

## Formatting & Linting
- Adhere to ESLint (`.eslintrc.js`) and Prettier (`.prettierrc.js`).
- Max line length: 100 characters; break logically.

## Naming & Structure
- Variables/functions: `camelCase`
- Components/types/interfaces: `PascalCase`
- Files: `kebab-case.ts` or `PascalCase.tsx`

## Type Safety
- Avoid `any`; use specific types or `unknown` with guards.
- Prefer `interface` for object shapes; use `type` for unions/tuples.
- Explicit return types on exported functions.
- Use utility types (`Partial`, `Pick`, etc.) and `readonly` where applicable.

## Best Practices
- Keep functions/components ≤ 30 lines (SRP).
- Use early returns to reduce nesting.
- Use `async/await` with proper `try/catch`.
- Named exports over default exports.

## Documentation
- JSDoc/TSDoc for all exported API: include `@param` and `@returns`.
```

---

## 4. .cursor/rules/architecture/react-components.mdc
```md
---
description: "Guidelines for React component structure, state management, styling, and performance."
globs: []
alwaysApply: false
---

# React Component Architecture (Agent Requested)

## Structure & File Layout
- Functional components only; avoid class components.
- Place components in `frontend/src/components/` and pages in `frontend/src/pages/`.
- File order: imports → types/interfaces → component → hooks → styles → export.

## State Management
- Local: `useState`/`useReducer`.
- Global: Redux Toolkit store in `frontend/src/store/`.
- Feature-specific: React Context API in `frontend/src/contexts/`.
- Data fetching: custom hooks or services in `frontend/src/services/`.

## Styling
- Material UI (MUI v5) components and styling system.
- Custom theming via `frontend/src/styles/theme.ts`.
- Emotion for CSS-in-JS when needed.
- Framer Motion for animations.

## Performance
- Memoize heavy sub-components with `React.memo`.
- Use `useMemo` and `useCallback` for expensive calculations and callbacks.
- Lazy-load non-critical components via `React.lazy` and `Suspense`.
- Optimize re-renders by avoiding unnecessary state updates.
```

---

## 5. .cursor/rules/processes/qa-guidelines.mdc
```md
---
description: "Combined testing strategy and security checklist: Jest/RTL for frontend, Pytest for backend, Playwright for E2E + secure coding best practices."
globs: []
alwaysApply: false
---

# QA & Security Guidelines (Agent Requested)

## Testing Strategy
- RED→GREEN→REFACTOR: write tests first (unit/integration) except `[HOTPATCH]` or `[LOW]` quick path.
- Frontend: Jest + React Testing Library for components and Redux.
- Backend: Pytest for Flask routes and Supabase repositories.
- E2E: Playwright for critical user flows.
- AAA pattern, descriptive test names, cover happy and edge cases, aim ~80% coverage.

## Secure Coding Checklist
- Validate & sanitize all external input (server-side) using Pydantic.
- Prevent XSS: use React's JSX encoding, avoid `dangerouslySetInnerHTML`.
- Supabase security: use Row Level Security (RLS) policies.
- AuthN/AuthZ: enforce least privilege, verify on every request.
- Error Handling: generic client errors; detailed server-side logs.
- Dependency audit: run `npm audit` and check Python dependencies regularly.

## Verification
- For every feature or fix, accompany with tests and security checks.
- Frontend: `npm run test` and `npm run lint`.
- Backend: `pytest` and `flake8`/`black`.
```

---

## 6. .cursor/rules/processes/commit-messages.mdc
```md
---
description: "Conventional Commits: type(scope): summary."
globs: []
alwaysApply: false
---

# Commit Message Standards (Agent Requested)

Use the Conventional Commits v1.0.0 format:
```
<type>(<scope>): <description>

[optional body]
[optional footer]
```

## Types
feat, fix, docs, style, refactor, perf, test, chore, ci

## Guidelines
- scope: noun (e.g., `api`, `ui`, `auth`), optional
- description: present tense, lowercase, no trailing period, ≤ 60 chars
- body: more detail, separate paragraphs with blank lines
- footer: issue refs (`Fixes #123`), `BREAKING CHANGE:`

## Example
```
feat(auth): add password reset endpoint

fixes #45
```
```

---

## 7. .cursor/rules/processes/docs-draft-manual.mdc
```md
---
description: "Draft documentation from recent code changes; invoke manually via @docs-draft-manual."
globs: []
alwaysApply: false
---

# Documentation Drafting Workflow (Manual Trigger)

1. Identify modified or new files in the current context.
2. Extract JSDoc/TSDoc comments from exports.
3. Summarize Purpose and Usage sections with code examples.
4. List API Reference (functions, classes, types) with parameters and returns.
5. Output Markdown draft; note it needs human review and refinement.
```
