from flask import g, current_app

# Import the hook function we want to test
# It's better to import it inside the test or setup where the app context is active
# to ensure the app is fully configured.


def test_init_supabase_repositories_populates_g(app):
    """
    Tests that the init_supabase_repositories hook correctly populates
    the Flask 'g' object with all expected repository instances when
    a mock Supabase client is configured.
    """
    # List of expected repository attribute names on 'g'
    expected_repositories = [
        "user_repository",
        "activity_log_repository",
        "content_item_repository",
        "business_context_repository",
        "chat_session_repository",
        "feedback_repository",
        "video_data_repository",
    ]

    with app.app_context():
        # Import the hook function *within* the app context
        # This ensures that the app configuration (like USE_SUPABASE) is loaded
        # before the module containing the hook is potentially cached by Python.
        from app.utils.supabase_hooks import init_supabase_repositories

        # Call the hook function directly
        init_supabase_repositories()

        # Assert that all expected repository attributes exist on 'g'
        for repo_name in expected_repositories:
            assert hasattr(g, repo_name), f"'{repo_name}' attribute missing from g"

        # Assert that none of the repository attributes on 'g' are None
        for repo_name in expected_repositories:
            repo_instance = getattr(g, repo_name)
            assert repo_instance is not None, f"g.{repo_name} should not be None"

        # Optional: Add a basic check that the repositories got the mock client
        # This assumes repositories store the client, e.g., as self.client or self.db
        # Adjust the attribute name ('client' or 'db') based on your repository implementation
        mock_client = current_app.config.get(
            "_test_supabase_client"
        )  # Retrieve the mock client if stored in config or use get_supabase_client()
        if not mock_client:
            # If not in config, try getting it via the utility function if appropriate
            from app.models.supabase_client import get_supabase_client

            mock_client = get_supabase_client()

        assert (
            mock_client is not None
        ), "Mock client could not be retrieved for verification"

        for repo_name in expected_repositories:
            repo_instance = getattr(g, repo_name)
            # Check common attribute names for the client in repositories
            if hasattr(repo_instance, "client"):
                assert (
                    repo_instance.client is mock_client
                ), f"g.{repo_name}.client is not the mock client"
            elif hasattr(repo_instance, "db"):
                assert (
                    repo_instance.db is mock_client
                ), f"g.{repo_name}.db is not the mock client"
            else:
                # If the repository doesn't store the client directly, this check might need adjustment
                # or be removed if it's not critical for this specific test's goal.
                print(
                    f"Warning: Could not verify mock client usage for g.{repo_name} - client attribute not found."
                )
