// This file injects environment variables into the window object
// It will be loaded before the React app
console.log('Loading environment variables from env-config.js');

window.ENV = {
  // This value should be provided by the environment (e.g., Docker)
  // Defaulting to localhost for safety if not provided.
  REACT_APP_API_URL: 'http://localhost:5000', // This should be overridden at runtime
  REACT_APP_ENV: 'development',
  // Real Supabase configuration
  SUPABASE_URL: 'https://zhtmqikyqgawninsgiup.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpodG1xaWt5cWdhd25pbnNnaXVwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI0MjI4MDEsImV4cCI6MjA1Nzk5ODgwMX0.LrREivYGkJNJLxJynBOw2VM9nm7_MzpTEiJgX9PLbRY'
  // Add other necessary environment variables here
};

console.log('Environment variables loaded:');
console.log('API URL (may be overridden):', window.ENV.REACT_APP_API_URL);
console.log('Supabase URL:', window.ENV.SUPABASE_URL);
console.log('Supabase Key:', window.ENV.SUPABASE_KEY ? 'Set' : 'Not set');