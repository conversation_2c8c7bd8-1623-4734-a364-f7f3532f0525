"""
Tests for FastAPI authentication dependencies.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from fastapi.security import HTTPAuthorizationCredentials

from app.auth.dependencies import get_current_user, CurrentUser
from app.repositories.supabase_user_repository import UserRepository, SupabaseAuthError
from app.utils.supabase_auth import AuthenticationException


@pytest.fixture
def mock_user_repo():
    """Mock user repository."""
    return Mock(spec=UserRepository)


@pytest.fixture
def mock_credentials():
    """Mock HTTP authorization credentials."""
    return HTTPAuthorizationCredentials(scheme="Bearer", credentials="valid_token")


@pytest.fixture
def mock_user_data():
    """Mock user data returned from token verification."""
    return {
        "id": "user123",
        "email": "<EMAIL>",
        "username": "testuser",
        "first_name": "Test",
        "last_name": "User"
    }


@pytest.mark.asyncio
async def test_get_current_user_success(mock_credentials, mock_user_repo, mock_user_data, monkeypatch):
    """Test successful user authentication."""
    # Mock the verify_supabase_token function
    async def mock_verify_token(token):
        return mock_user_data
    
    monkeypatch.setattr("app.auth.dependencies.verify_supabase_token", mock_verify_token)
    
    # Call the dependency
    result = await get_current_user(mock_credentials, mock_user_repo)
    
    # Assertions
    assert isinstance(result, CurrentUser)
    assert result.user_id == "user123"
    assert result.id == "user123"  # Backward compatibility
    assert result.get("email") == "<EMAIL>"
    assert result["username"] == "testuser"


@pytest.mark.asyncio
async def test_get_current_user_no_token():
    """Test authentication failure with no token."""
    credentials = HTTPAuthorizationCredentials(scheme="Bearer", credentials="")
    mock_user_repo = Mock(spec=UserRepository)
    
    with pytest.raises(HTTPException) as exc_info:
        await get_current_user(credentials, mock_user_repo)
    
    assert exc_info.value.status_code == 401
    assert "Authorization token required" in exc_info.value.detail


@pytest.mark.asyncio
async def test_get_current_user_invalid_token(mock_credentials, mock_user_repo, monkeypatch):
    """Test authentication failure with invalid token."""
    # Mock the verify_supabase_token function to return None
    async def mock_verify_token(token):
        return None
    
    monkeypatch.setattr("app.auth.dependencies.verify_supabase_token", mock_verify_token)
    
    with pytest.raises(HTTPException) as exc_info:
        await get_current_user(mock_credentials, mock_user_repo)
    
    assert exc_info.value.status_code == 401
    assert "Invalid or expired token" in exc_info.value.detail


@pytest.mark.asyncio
async def test_get_current_user_supabase_auth_error(mock_credentials, mock_user_repo, monkeypatch):
    """Test authentication failure with SupabaseAuthError."""
    # Mock the verify_supabase_token function to raise SupabaseAuthError
    async def mock_verify_token(token):
        raise SupabaseAuthError("Token expired")
    
    monkeypatch.setattr("app.auth.dependencies.verify_supabase_token", mock_verify_token)
    
    with pytest.raises(HTTPException) as exc_info:
        await get_current_user(mock_credentials, mock_user_repo)
    
    assert exc_info.value.status_code == 401
    assert "Authentication error: Token expired" in exc_info.value.detail


@pytest.mark.asyncio
async def test_get_current_user_authentication_exception(mock_credentials, mock_user_repo, monkeypatch):
    """Test authentication failure with AuthenticationException."""
    # Mock the verify_supabase_token function to raise AuthenticationException
    async def mock_verify_token(token):
        raise AuthenticationException("Invalid token format")
    
    monkeypatch.setattr("app.auth.dependencies.verify_supabase_token", mock_verify_token)
    
    with pytest.raises(HTTPException) as exc_info:
        await get_current_user(mock_credentials, mock_user_repo)
    
    assert exc_info.value.status_code == 401
    assert "Invalid token format" in exc_info.value.detail


@pytest.mark.asyncio
async def test_get_current_user_unexpected_error(mock_credentials, mock_user_repo, monkeypatch):
    """Test authentication failure with unexpected error."""
    # Mock the verify_supabase_token function to raise unexpected error
    async def mock_verify_token(token):
        raise ValueError("Unexpected error")
    
    monkeypatch.setattr("app.auth.dependencies.verify_supabase_token", mock_verify_token)
    
    with pytest.raises(HTTPException) as exc_info:
        await get_current_user(mock_credentials, mock_user_repo)
    
    assert exc_info.value.status_code == 500
    assert "Authentication failed due to an internal server error" in exc_info.value.detail


def test_current_user_backward_compatibility(mock_user_data):
    """Test CurrentUser backward compatibility with dict access."""
    user = CurrentUser(user_data=mock_user_data, user_id="user123")
    
    # Test dict-like access
    assert user.get("email") == "<EMAIL>"
    assert user.get("nonexistent", "default") == "default"
    assert user["username"] == "testuser"
    
    # Test direct attribute access
    assert user.user_id == "user123"
    assert user.id == "user123"
