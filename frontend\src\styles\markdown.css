.markdown-content {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Aria<PERSON>, sans-serif;
  line-height: 1.6;
  color: var(--mui-palette-text-primary);
  max-width: 100%;
}

.markdown-preview {
  font-size: 0.875rem;
  line-height: 1.5;
  color: #666;
  white-space: pre-wrap;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
  white-space: normal;
}

.markdown-content h1 {
  font-size: 2em;
  padding-bottom: 0.3em;
  border-bottom: 1px solid var(--mui-palette-divider);
}

.markdown-content h2 {
  font-size: 1.5em;
  padding-bottom: 0.3em;
  border-bottom: 1px solid var(--mui-palette-divider);
}

.markdown-content p {
  margin-bottom: 16px;
}

.markdown-content code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: var(--mui-palette-action-hover);
  border-radius: 3px;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier,
    monospace;
  white-space: pre;
  color: var(--mui-palette-text-secondary);
}

.markdown-content pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: var(--mui-palette-action-selected);
  border-radius: 3px;
  white-space: pre;
  border: 1px solid var(--mui-palette-divider);
}

.markdown-content pre code {
  padding: 0;
  margin: 0;
  font-size: 100%;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
  color: inherit;
}

.markdown-content blockquote {
  padding: 0 1em;
  color: var(--mui-palette-text-secondary);
  border-left: 0.25em solid var(--mui-palette-divider);
  margin: 0 0 16px 0;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 2em;
  margin-bottom: 16px;
  white-space: normal;
}

.markdown-content li {
}

.markdown-content table {
  display: block;
  width: 100%;
  overflow: auto;
  margin-bottom: 16px;
  border-spacing: 0;
  border-collapse: collapse;
  white-space: normal;
}

.markdown-content table th,
.markdown-content table td {
  padding: 6px 13px;
  border: 1px solid var(--mui-palette-divider);
}

.markdown-content table tr {
  background-color: var(--mui-palette-background-paper);
  border-top: 1px solid var(--mui-palette-divider);
}

.markdown-content table tr:nth-child(2n) {
  background-color: var(--mui-palette-action-hover);
}

/* Add styles for line breaks */
.markdown-content br {
  display: block;
  content: "";
  margin-top: 0.5em;
}
