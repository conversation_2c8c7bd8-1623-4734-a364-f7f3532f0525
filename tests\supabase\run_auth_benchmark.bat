@echo off
SETLOCAL EnableDelayedExpansion

echo ===== Supabase Auth Performance Benchmark =====
echo.

REM Check if server is running
set SERVER_RUNNING=0
netstat -ano | findstr ":5000" > nul
if %ERRORLEVEL% EQU 0 (
    set SERVER_RUNNING=1
    echo [INFO] Server detected on port 5000
) else (
    echo [INFO] No server detected on port 5000
)

REM If server is not running, start it
if %SERVER_RUNNING% EQU 0 (
    echo [INFO] Starting server in background...
    start /B python app.py > server_output.log 2>&1
    echo [INFO] Waiting 5 seconds for server to start...
    timeout /t 5 /nobreak > nul
)

REM Run the benchmark with different concurrency levels
echo.
echo ===== Running auth performance benchmark =====
echo.

REM Create results directory if it doesn't exist
if not exist "benchmark_results" mkdir benchmark_results
if not exist "benchmark_results\auth" mkdir benchmark_results\auth

echo [INFO] Running benchmark with concurrency level 1...
python tests/supabase/auth_performance_benchmark.py --url http://localhost:5000 --iterations 20 --concurrency 1

echo.
echo [INFO] Running benchmark with concurrency level 5...
python tests/supabase/auth_performance_benchmark.py --url http://localhost:5000 --iterations 20 --concurrency 5

echo.
echo [INFO] Running benchmark with concurrency level 10...
python tests/supabase/auth_performance_benchmark.py --url http://localhost:5000 --iterations 20 --concurrency 10

echo.
echo ===== Benchmark complete =====
echo.
echo Results are available in the auth_benchmark_results directory

REM If we started the server, stop it
if %SERVER_RUNNING% EQU 0 (
    echo [INFO] Stopping server...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":5000"') do (
        taskkill /F /PID %%a > nul 2>&1
    )
)

echo.
echo ===== Benchmark process completed =====
ENDLOCAL 