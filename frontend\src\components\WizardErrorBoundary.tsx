import React, { Component, ErrorInfo } from 'react';
import { Box, Button, Typography, Paper } from '@mui/material';
import { useNavigate } from 'react-router-dom';

// Error fallback component
const WizardErrorFallback = ({ error, resetErrorBoundary }: { error: Error, resetErrorBoundary: () => void }) => {
  const navigate = useNavigate();

  return (
    <Paper
      elevation={3}
      sx={{
        p: 4,
        m: 2,
        maxWidth: '800px',
        mx: 'auto',
        borderLeft: '4px solid #f44336',
      }}
    >
      <Typography variant="h5" color="error" gutterBottom>
        Something went wrong in the Wizard
      </Typography>
      <Typography variant="body1" paragraph>
        {error.message || 'An unexpected error occurred.'}
      </Typography>
      <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
        <Button variant="contained" onClick={resetErrorBoundary}>
          Try Again
        </Button>
        <Button variant="outlined" onClick={() => navigate('/')}>
          Go to Home
        </Button>
      </Box>
    </Paper>
  );
};

// Custom error boundary class component
interface ErrorBoundaryProps {
  children: React.ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundaryComponent extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to console or an error reporting service
    console.error('Wizard Error:', error);
    console.error('Component Stack:', errorInfo.componentStack);
  }

  resetErrorBoundary = (): void => {
    // Clear any wizard-related state if needed
    console.log('Resetting wizard error state');
    localStorage.removeItem('wizardProgress');
    this.setState({ hasError: false, error: null });
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return <WizardErrorFallback error={this.state.error!} resetErrorBoundary={this.resetErrorBoundary} />;
    }

    return this.props.children;
  }
}

// Main error boundary component
export const WizardErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundaryComponent>
      {children}
    </ErrorBoundaryComponent>
  );
};

export default WizardErrorBoundary;
