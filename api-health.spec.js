// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('API Health Tests', () => {
  test('health endpoint returns 200 and correct structure', async ({ request }) => {
    const response = await request.get('http://localhost:5000/health');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('status', 'healthy');
    expect(data).toHaveProperty('message');
    expect(data).toHaveProperty('timestamp');
    expect(data).toHaveProperty('environment');
  });

  test('api/health endpoint returns 200 and correct structure', async ({ request }) => {
    const response = await request.get('http://localhost:5000/api/health');
    expect(response.ok()).toBeTruthy();
    
    const data = await response.json();
    expect(data).toHaveProperty('status', 'healthy');
    expect(data).toHaveProperty('message');
    expect(data).toHaveProperty('timestamp');
    expect(data).toHaveProperty('environment');
    expect(data).toHaveProperty('config');
    
    // Verify config structure
    expect(data.config).toHaveProperty('database_url');
    expect(data.config).toHaveProperty('jwt_expires');
    expect(data.config).toHaveProperty('ai_provider');
    expect(data.config).toHaveProperty('ai_model');
  });
});

test.describe('Authentication API Tests', () => {
  const testUser = {
    email: `test.user.${Date.now()}@example.com`,
    password: 'Test1234!',
    name: 'Test User'
  };
  
  let authToken = null;
  
  test('registration endpoint works correctly', async ({ request }) => {
    const response = await request.post('http://localhost:5000/api/auth/register', {
      data: {
        email: testUser.email,
        password: testUser.password,
        name: testUser.name
      }
    });
    
    // If the user already exists, that's ok for testing purposes
    if (response.status() === 409) {
      console.log('Test user already exists, proceeding with login tests');
      return;
    }
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data).toHaveProperty('message');
    expect(data).toHaveProperty('user');
    expect(data.user).toHaveProperty('email', testUser.email);
  });
  
  test('login endpoint returns token', async ({ request }) => {
    const response = await request.post('http://localhost:5000/api/auth/login', {
      data: {
        email: testUser.email,
        password: testUser.password
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data).toHaveProperty('access_token');
    expect(data).toHaveProperty('user');
    expect(data.user).toHaveProperty('email', testUser.email);
    
    // Save the token for subsequent tests
    authToken = data.access_token;
  });
  
  test('profile endpoint returns user data with valid token', async ({ request }) => {
    // Skip if login test failed
    test.skip(!authToken, 'No auth token available');
    
    const response = await request.get('http://localhost:5000/api/auth/profile', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(data).toHaveProperty('email', testUser.email);
  });
  
  test('profile endpoint rejects invalid token', async ({ request }) => {
    const response = await request.get('http://localhost:5000/api/auth/profile', {
      headers: {
        'Authorization': 'Bearer invalid-token'
      }
    });
    
    expect(response.ok()).toBeFalsy();
    expect(response.status()).toBe(401);
  });
});

test.describe('Content API Tests', () => {
  // This test requires a valid auth token
  test('content endpoint returns list with valid token', async ({ request }) => {
    // First login to get token
    const loginResponse = await request.post('http://localhost:5000/api/auth/login', {
      data: {
        email: '<EMAIL>',
        password: 'Test1234!'
      }
    });
    
    // Skip if login failed
    if (!loginResponse.ok()) {
      test.skip(true, 'Login failed, cannot test content API');
      return;
    }
    
    const loginData = await loginResponse.json();
    const authToken = loginData.access_token;
    
    const response = await request.get('http://localhost:5000/api/content', {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const data = await response.json();
    expect(Array.isArray(data)).toBeTruthy();
  });
});

test.describe('Error Handling Tests', () => {
  test('non-existent endpoint returns 404', async ({ request }) => {
    const response = await request.get('http://localhost:5000/api/non-existent-endpoint');
    expect(response.status()).toBe(404);
  });
  
  test('unauthorized access returns 401', async ({ request }) => {
    const response = await request.get('http://localhost:5000/api/auth/profile');
    expect(response.status()).toBe(401);
  });
  
  test('invalid input returns 400', async ({ request }) => {
    const response = await request.post('http://localhost:5000/api/auth/login', {
      data: {
        // Missing required fields
      }
    });
    
    expect(response.status()).toBe(400);
  });
}); 