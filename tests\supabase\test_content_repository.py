"""
Unit tests for the Supabase ContentRepository implementation
Tests the functionality of the ContentRepository class
"""

import os
import pytest
import uuid

# Import the repository class
from app.repositories.supabase_content_item_repository import ContentItemRepository

# Check if Supabase is configured
supabase_url = os.environ.get("SUPABASE_URL")
supabase_key = os.environ.get("SUPABASE_KEY")
skip_supabase_tests = not (supabase_url and supabase_key)


@pytest.mark.skipif(skip_supabase_tests, reason="Supabase not configured")
class TestContentRepository:
    """Test the ContentRepository class"""

    def test_initialization(self, supabase_client):
        """Test that the repository initializes correctly"""
        repo = ContentItemRepository(supabase_client)

        assert repo is not None
        assert repo.supabase is not None
        assert repo.table_name == "content_items"

    def test_create_content_item(
        self, app, supabase_client, test_user_id
    ):  # Add app fixture
        """Test creating content"""
        repo = ContentItemRepository(supabase_client)

        # Create test content
        # Create test content data dictionary including user_id
        content_data = {
            "user_id": test_user_id,  # Add user_id to the dictionary
            "title": "Test Content",
            "content": "This is test content",
            "content_type": "article",
            # Removed metadata field as it doesn't exist in the schema
        }

        # Wrap in app context
        with app.app_context():
            content = repo.create_content_item(data=content_data)

        # Verify content was created
        assert content is not None
        assert content.get("user_id") == test_user_id
        assert content.get("title") == content_data["title"]
        assert content.get("content") == content_data["content"]
        assert content.get("content_type") == content_data["content_type"]
        # assert content.get("metadata", {}).get("status") == content_data["metadata"]["status"] # Removed check for non-existent metadata

        # Clean up
        try:
            supabase_client.table("content_items").delete().eq(
                "id", content.get("id")
            ).execute()
        except Exception as e:
            print(f"Error cleaning up test content: {e}")

    def test_get_by_id(
        self, app, supabase_client, test_user_id, test_content_item
    ):  # Add app fixture
        """Test getting content by ID"""
        repo = ContentItemRepository(supabase_client)

        # Get the content within app context
        with app.app_context():
            content = repo.get_by_id(test_content_item.get("id"), test_user_id)

        # Verify content was retrieved
        assert content is not None
        assert content.get("id") == test_content_item.get("id")
        assert content.get("user_id") == test_user_id
        assert content.get("title") == test_content_item.get("title")

    def test_update_content_item(
        self, app, supabase_client, test_user_id, test_content_item
    ):  # Renamed test, Add app fixture
        """Test updating content"""
        repo = ContentItemRepository(supabase_client)

        # Update data
        update_data = {
            "title": "Updated Title",
            "content": "Updated content text",
            # status is likely in metadata, not a direct field
            # "metadata": {"status": "published"} # Removed metadata field
        }

        # Update the content within app context
        with app.app_context():
            updated_content = repo.update_content_item(
                id=test_content_item.get("id"), user_id=test_user_id, data=update_data
            )

        # Verify content was updated
        assert updated_content is not None
        assert updated_content.get("id") == test_content_item.get("id")
        assert updated_content.get("title") == update_data["title"]
        assert updated_content.get("content") == update_data["content"]
        # assert updated_content.get("metadata", {}).get("status") == update_data["metadata"]["status"] # Removed check for non-existent metadata

        # Get the content to confirm update within app context
        with app.app_context():
            content = repo.get_by_id(test_content_item.get("id"), test_user_id)
        assert content.get("title") == update_data["title"]

    def test_delete_content_item(
        self, app, supabase_client, test_user_id
    ):  # Add app fixture
        """Test deleting content"""
        repo = ContentItemRepository(supabase_client)

        # Create content to delete
        # Pass data as dictionary
        delete_data = {
            "user_id": test_user_id,
            "title": "Delete Test",
            "content": "Content to delete",
            "content_type": "article",
            # "metadata": {"status": "draft"} # Removed metadata field
        }
        with app.app_context():  # Wrap in app context
            content = repo.create_content_item(data=delete_data)

        content_id = content.get("id")

        # Delete the content within app context
        with app.app_context():
            result = repo.delete_content_item(content_id, test_user_id)

        # Verify deletion was successful
        assert result is True

        # Try to get the deleted content within app context
        with app.app_context():
            deleted_content = repo.get_by_id(content_id, test_user_id)
        assert deleted_content is None

    def test_get_by_user_id(
        self, app, supabase_client, test_user_id
    ):  # Add app fixture
        """Test getting all content for a user"""
        repo = ContentItemRepository(supabase_client)

        # Create multiple content items
        content_items = []
        for i in range(3):
            # Pass data as dictionary
            loop_data = {
                "user_id": test_user_id,
                "title": f"Test Content {i}",
                "content": f"Content {i}",
                "content_type": "article",
                # "metadata": {"status": "draft"} # Removed metadata field
            }
            with app.app_context():  # Wrap in app context
                content = repo.create_content_item(data=loop_data)
            content_items.append(content)

        # Get user content within app context
        with app.app_context():
            user_content = repo.get_by_user_id(test_user_id)

        # Verify content was retrieved
        assert user_content is not None
        assert len(user_content) >= 3

        # Clean up
        for content in content_items:
            try:
                supabase_client.table("content_items").delete().eq(
                    "id", content.get("id")
                ).execute()
            except Exception as e:
                print(f"Error cleaning up test content: {e}")

    def test_get_filtered_items(
        self, app, supabase_client, test_user_id
    ):  # Add app fixture
        """Test getting user content with filters"""
        repo = ContentItemRepository(supabase_client)

        # Create content with different types
        content_types = ["article", "social", "email", "article"]
        content_items = []

        for content_type in content_types:
            # Pass data as dictionary
            filter_data = {
                "user_id": test_user_id,
                "title": f"Test {content_type}",
                "content": f"Content for {content_type}",
                "content_type": content_type,
                # "metadata": {"status": "draft"} # Removed metadata field
            }
            with app.app_context():  # Wrap in app context
                content = repo.create_content_item(data=filter_data)
            content_items.append(content)

        # Get user content filtered by type using get_filtered_items within app context
        with app.app_context():
            articles = repo.get_filtered_items(
                user_id=test_user_id, content_type="article"
            )

        # Verify filtered content
        assert articles is not None
        assert len(articles) >= 2
        for article in articles:
            assert article.get("content_type") == "article"

        # Clean up
        for content in content_items:
            try:
                supabase_client.table("content_items").delete().eq(
                    "id", content.get("id")
                ).execute()
            except Exception as e:
                print(f"Error cleaning up test content: {e}")

    def test_search(
        self, app, supabase_client, test_user_id
    ):  # Add app fixture (though test is skipped)
        """Test searching content"""
        # This test requires a tsvector column 'content_tsv' and potentially FTS setup
        pytest.skip("Skipping search test, requires FTS setup (content_tsv column)")

        # repo = ContentItemRepository(supabase_client)
        # ... (rest of test setup if FTS is configured) ...
        # apple_results = repo.search(
        #     search_term="apple",
        #     user_id=test_user_id
        # )
        # ... (assertions) ...

    def test_get_recent_content_items(
        self, app, supabase_client, test_user_id
    ):  # Add app fixture
        """Test getting recent content"""
        repo = ContentItemRepository(supabase_client)

        # Create multiple content items
        content_items = []
        for i in range(5):
            # Pass data as dictionary
            recent_data = {
                "user_id": test_user_id,
                "title": f"Recent Test {i}",
                "content": f"Recent content {i}",
                "content_type": "article",
                # "metadata": {"status": "draft"} # Removed metadata field
            }
            with app.app_context():  # Wrap in app context
                content = repo.create_content_item(data=recent_data)
            content_items.append(content)

        # Get recent content using correct method within app context
        with app.app_context():
            recent_content = repo.get_recent_content_items(test_user_id, limit=3)

        # Verify recent content
        assert recent_content is not None
        assert len(recent_content) == 3  # Should limit to 3 items

        # Check order (should be newest first)
        for i in range(1, len(recent_content)):
            assert recent_content[i - 1].get("created_at") >= recent_content[i].get(
                "created_at"
            )

        # Clean up
        for content in content_items:
            try:
                supabase_client.table("content_items").delete().eq(
                    "id", content.get("id")
                ).execute()
            except Exception as e:
                print(f"Error cleaning up test content: {e}")

    # Removing test_get_content_by_status as the method doesn't exist
    # def test_get_content_by_status(self, supabase_client, test_user_id):
    #     """Test getting content by status"""
    #     pass # Test removed

    def test_error_handling(self, app, supabase_client):  # Add app fixture
        """Test error handling in the repository"""
        repo = ContentItemRepository(supabase_client)

        # Test with invalid content ID
        invalid_id = str(uuid.uuid4())
        # Pass user_id to get_by_id within app context
        with app.app_context():
            content = repo.get_by_id(
                invalid_id, str(uuid.uuid4())
            )  # Use a valid UUID format

        # Should return None for non-existent content
        assert content is None

        # Test update with invalid ID
        # Use update_content_item and expect error
        error_update_data = {"title": "Won't Update"}
        with app.app_context():  # Wrap in app context
            with pytest.raises(Exception):  # Expect SupabaseRepositoryError or similar
                repo.update_content_item(
                    id=invalid_id,  # Use id parameter name
                    user_id="test-user",  # Use actual test_user_id if needed
                    data=error_update_data,
                )

        # Should return None on error
        # Asserting exception is raised is sufficient
