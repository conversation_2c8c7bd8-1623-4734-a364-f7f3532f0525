import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import WizardPage from './WizardPage';
import * as api from '../services/api';
import { SaveContentPayload } from '../services/api';

// Mock the API functions
jest.mock('../services/api', () => ({
  getBusinessContextsList: jest.fn().mockResolvedValue([
    { id: 'context-1', name: 'Test Context 1' },
    { id: 'context-2', name: 'Test Context 2' },
  ]),
  wizardBrainstorm: jest.fn().mockResolvedValue({
    data: {
      session_id: 'test-session-id',
      titles: ['Title 1', 'Title 2', 'Title 3'],
    },
  }),
  wizardSelectHook: jest.fn().mockResolvedValue({
    data: {
      hooks: ['Hook 1', 'Hook 2', 'Hook 3'],
    },
  }),
  wizardSelectIntro: jest.fn().mockResolvedValue({
    data: {
      intro_ctas: ['Intro 1', 'Intro 2', 'Intro 3'],
    },
  }),
  wizardGenerateOutline: jest.fn().mockResolvedValue({
    data: {
      outlines: ['Outline 1', 'Outline 2'],
    },
  }),
  wizardDraftScript: jest.fn().mockResolvedValue({
    data: {
      draft: 'This is a test draft script.',
    },
  }),
  wizardEditScript: jest.fn().mockResolvedValue({
    data: {
      results: 'This is the edited script with improvements.',
    },
  }),
  saveContentItem: jest.fn().mockResolvedValue({ content_item: { id: 'new-content-id' } }),
}));

// Mock the SaveToLibraryDialog component
jest.mock('../components/dialogs/SaveToLibraryDialog', () => {
  return {
    __esModule: true,
    default: ({
      open,
      onClose,
      onSave
    }: {
      open: boolean;
      onClose: () => void;
      onSave: (details: SaveContentPayload) => Promise<void>;
      contentPreview?: string;
      initialTitle?: string;
    }) => (
      open ? (
        <div data-testid="mock-save-dialog">
          <button data-testid="mock-save-button" onClick={() => onSave({
            title: 'Test Script',
            content: 'Test content',
            content_type: 'script',
            tags: ['test', 'ai-generated'],
          })}>
            Save
          </button>
          <button data-testid="mock-close-button" onClick={onClose}>
            Close
          </button>
        </div>
      ) : null
    ),
  };
});

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('WizardPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderWizardPage = () => {
    return render(
      <BrowserRouter>
        <WizardPage />
      </BrowserRouter>
    );
  };

  test('renders the wizard page with initial step', async () => {
    renderWizardPage();

    // Check for the title
    expect(screen.getByText('AI Script Writing Wizard')).toBeInTheDocument();

    // Check for the first step content
    await waitFor(() => {
      expect(screen.getByLabelText('Business Context')).toBeInTheDocument();
      expect(screen.getByLabelText('Content Idea / Topic')).toBeInTheDocument();
    });
  });

  test('completes the wizard flow and shows save button', async () => {
    renderWizardPage();

    // Wait for business contexts to load
    await waitFor(() => {
      expect(api.getBusinessContextsList).toHaveBeenCalled();
    });

    // Step 1: Fill in the brainstorm form and click Next
    fireEvent.change(screen.getByLabelText('Business Context'), { target: { value: 'context-1' } });
    fireEvent.change(screen.getByLabelText('Content Idea / Topic'), { target: { value: 'Test idea' } });
    fireEvent.click(screen.getByText('Next'));

    // Wait for brainstorm API call
    await waitFor(() => {
      expect(api.wizardBrainstorm).toHaveBeenCalledWith({
        business_context_id: 'context-1',
        content_idea: 'Test idea',
      });
    });

    // Simulate completing all steps by setting activeStep to the end
    // This is a simplified approach for testing purposes
    for (let i = 0; i < 5; i++) {
      fireEvent.click(screen.getByText('Next'));
      await waitFor(() => {
        // Wait for each API call to complete
      });
    }

    // Check if we're at the final screen
    await waitFor(() => {
      expect(screen.getByText('All steps completed - you\'re finished!')).toBeInTheDocument();
      expect(screen.getByText('Save to Library')).toBeInTheDocument();
    });

    // Click the Save to Library button
    fireEvent.click(screen.getByText('Save to Library'));

    // Check if the save dialog is shown
    await waitFor(() => {
      expect(screen.getByTestId('mock-save-dialog')).toBeInTheDocument();
    });

    // Click the Save button in the dialog
    fireEvent.click(screen.getByTestId('mock-save-button'));

    // Check if saveContentItem was called
    await waitFor(() => {
      expect(api.saveContentItem).toHaveBeenCalledWith({
        title: 'Test Script',
        content: 'Test content',
        content_type: 'script',
        tags: ['test', 'ai-generated'],
      });
    });

    // Check if navigation was triggered after save
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/content-library');
    });
  });
});
