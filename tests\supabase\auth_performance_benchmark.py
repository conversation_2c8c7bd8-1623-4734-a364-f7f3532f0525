#!/usr/bin/env python
import requests
import time
import statistics
import argparse
import csv
import os
import json
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from tabulate import tabulate


class AuthPerformanceBenchmark:
    """Benchmark tool for measuring auth flow performance"""

    def __init__(
        self,
        base_url,
        num_iterations=10,
        concurrency=1,
        output_dir="auth_benchmark_results",
    ):
        self.base_url = base_url
        self.num_iterations = num_iterations
        self.concurrency = concurrency
        self.output_dir = output_dir
        self.results = {}

        # Create output directory if it doesn't exist
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Create a timestamped directory for this run
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.run_dir = os.path.join(output_dir, f"run_{timestamp}")
        os.makedirs(self.run_dir)

        # Define test credentials
        self.test_user = {
            "email": "<EMAIL>",
            "password": "Password123!",  # Test password
        }

        # Store refresh token for token refresh tests
        self.refresh_token = None

    def measure_performance(self, operation, callback, payload=None):
        """Measure performance of an auth operation"""
        results = []

        def run_iteration():
            start_time = time.time()
            try:
                result = callback(payload)
                success = True
                data = result
            except Exception as e:
                success = False
                data = str(e)

            end_time = time.time()
            elapsed = (end_time - start_time) * 1000  # Convert to ms

            return {"elapsed_ms": elapsed, "success": success, "data": data}

        print(f"Running {self.num_iterations} iterations for {operation}...")

        if self.concurrency > 1:
            with ThreadPoolExecutor(max_workers=self.concurrency) as executor:
                results = list(
                    executor.map(lambda _: run_iteration(), range(self.num_iterations))
                )
        else:
            for _ in range(self.num_iterations):
                results.append(run_iteration())

        # Store results
        self.results[operation] = results

        # Calculate summary stats
        successful_times = [r["elapsed_ms"] for r in results if r["success"]]

        if successful_times:
            stats = {
                "min": min(successful_times),
                "max": max(successful_times),
                "mean": statistics.mean(successful_times),
                "median": statistics.median(successful_times),
                "p95": self._percentile(successful_times, 95),
                "success_rate": len(successful_times) / len(results) * 100,
            }
        else:
            stats = {
                "min": 0,
                "max": 0,
                "mean": 0,
                "median": 0,
                "p95": 0,
                "success_rate": 0,
            }

        # Save results to file
        self._save_results(operation, results, stats)

        return stats

    def _percentile(self, data, percentile):
        """Calculate the given percentile of a list of numbers"""
        if not data:
            return 0
        size = len(data)
        sorted_data = sorted(data)
        index = (size * percentile) // 100
        return sorted_data[index]

    def _save_results(self, operation, results, stats):
        """Save detailed results for an operation to CSV file"""
        safe_operation = operation.replace("/", "_").replace(" ", "_")
        filename = os.path.join(self.run_dir, f"{safe_operation}.csv")

        with open(filename, "w", newline="") as csvfile:
            fieldnames = ["iteration", "elapsed_ms", "success"]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for i, result in enumerate(results):
                row = {
                    "iteration": i + 1,
                    "elapsed_ms": result["elapsed_ms"],
                    "success": result["success"],
                }
                writer.writerow(row)

        # Save summary stats as JSON
        stats_filename = os.path.join(self.run_dir, f"{safe_operation}_stats.json")
        with open(stats_filename, "w") as f:
            json.dump(stats, f, indent=2)

    def login(self, credentials=None):
        """Perform login operation and measure performance"""
        if credentials is None:
            credentials = self.test_user

        def login_request(_):
            response = requests.post(
                f"{self.base_url}/api/auth/login",
                json=credentials,
                headers={"Content-Type": "application/json"},
            )

            if response.status_code != 200:
                raise Exception(
                    f"Login failed: {response.status_code} - {response.text}"
                )

            result = response.json()

            # Store refresh token for later use
            if "refresh_token" in result:
                self.refresh_token = result["refresh_token"]

            return result

        return self.measure_performance("login", login_request)

    def token_verification(self, token_payload=None):
        """Measure token verification performance"""
        # First, ensure we have a token
        if token_payload is None and not hasattr(self, "access_token"):
            login_result = self.login()
            if "access_token" not in login_result.get("data", {}):
                raise Exception("Failed to obtain access token")
            self.access_token = login_result["data"]["access_token"]

        token = token_payload or self.access_token

        def verify_request(_):
            response = requests.get(
                f"{self.base_url}/api/auth/profile",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                },
            )

            if response.status_code != 200:
                raise Exception(
                    f"Token verification failed: {response.status_code} - {response.text}"
                )

            return response.json()

        return self.measure_performance("token_verification", verify_request)

    def token_refresh(self, refresh_token=None):
        """Measure token refresh performance"""
        # Ensure we have a refresh token
        if refresh_token is None and not self.refresh_token:
            login_result = self.login()
            if not self.refresh_token:
                raise Exception("Failed to obtain refresh token")

        token = refresh_token or self.refresh_token

        def refresh_request(_):
            response = requests.post(
                f"{self.base_url}/api/auth/refresh",
                json={"refresh_token": token},
                headers={"Content-Type": "application/json"},
            )

            if response.status_code != 200:
                raise Exception(
                    f"Token refresh failed: {response.status_code} - {response.text}"
                )

            result = response.json()

            # Update refresh token for subsequent tests
            if "refresh_token" in result.get("session", {}):
                self.refresh_token = result["session"]["refresh_token"]

            return result

        return self.measure_performance("token_refresh", refresh_request)

    def concurrent_api_requests(self, num_requests=10):
        """Test performance of multiple concurrent authenticated requests"""
        # Make sure we have a token
        if not hasattr(self, "access_token"):
            login_result = self.login()
            if "access_token" not in login_result.get("data", {}):
                raise Exception("Failed to obtain access token")
            self.access_token = login_result["data"]["access_token"]

        token = self.access_token

        def api_request(_):
            response = requests.get(
                f"{self.base_url}/api/auth/profile",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                },
            )

            if response.status_code != 200:
                raise Exception(
                    f"API request failed: {response.status_code} - {response.text}"
                )

            return response.json()

        # Override the number of iterations for this specific test
        original_iterations = self.num_iterations
        self.num_iterations = num_requests
        stats = self.measure_performance("concurrent_api_requests", api_request)
        self.num_iterations = original_iterations

        return stats

    def full_auth_flow(self):
        """Test the full authentication flow end-to-end"""

        def auth_flow(_):
            # Step 1: Login
            login_response = requests.post(
                f"{self.base_url}/api/auth/login",
                json=self.test_user,
                headers={"Content-Type": "application/json"},
            )

            if login_response.status_code != 200:
                raise Exception(
                    f"Login failed: {login_response.status_code} - {login_response.text}"
                )

            login_result = login_response.json()
            token = login_result.get("access_token")

            if not token:
                raise Exception("No access token in login response")

            # Step 2: Use token for authenticated request
            profile_response = requests.get(
                f"{self.base_url}/api/auth/profile",
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {token}",
                },
            )

            if profile_response.status_code != 200:
                raise Exception(
                    f"Profile request failed: {profile_response.status_code} - {profile_response.text}"
                )

            # Step 3: Refresh token
            refresh_token = login_result.get("refresh_token")
            if refresh_token:
                refresh_response = requests.post(
                    f"{self.base_url}/api/auth/refresh",
                    json={"refresh_token": refresh_token},
                    headers={"Content-Type": "application/json"},
                )

                if refresh_response.status_code != 200:
                    raise Exception(
                        f"Token refresh failed: {refresh_response.status_code} - {refresh_response.text}"
                    )

            return {
                "login": login_result,
                "profile": profile_response.json(),
                "refresh": refresh_response.json() if refresh_token else None,
            }

        return self.measure_performance("full_auth_flow", auth_flow)

    def generate_report(self):
        """Generate a summary report of all benchmarks"""
        if not self.results:
            print("No benchmark results available.")
            return

        # Generate summary table
        table_data = []
        for operation, results in self.results.items():
            successful_times = [r["elapsed_ms"] for r in results if r["success"]]

            if successful_times:
                row = [
                    operation,
                    f"{min(successful_times):.2f}ms",
                    f"{max(successful_times):.2f}ms",
                    f"{statistics.mean(successful_times):.2f}ms",
                    f"{statistics.median(successful_times):.2f}ms",
                    f"{self._percentile(successful_times, 95):.2f}ms",
                    f"{len(successful_times) / len(results) * 100:.1f}%",
                ]
            else:
                row = [operation, "N/A", "N/A", "N/A", "N/A", "N/A", "0.0%"]

            table_data.append(row)

        headers = [
            "Operation",
            "Min",
            "Max",
            "Mean",
            "Median",
            "95th %",
            "Success Rate",
        ]
        table = tabulate(table_data, headers=headers, tablefmt="pipe")

        # Save summary report
        report_path = os.path.join(self.run_dir, "auth_performance_summary.md")
        with open(report_path, "w") as f:
            f.write("# Authentication Performance Benchmark Summary\n\n")
            f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Iterations per operation: {self.num_iterations}\n")
            f.write(f"Concurrency level: {self.concurrency}\n\n")
            f.write("## Results Summary\n\n")
            f.write(table)
            f.write("\n\n")
            f.write("## Analysis\n\n")

            # Find the slowest operation
            operation_means = {}
            for operation, results in self.results.items():
                successful_times = [r["elapsed_ms"] for r in results if r["success"]]
                if successful_times:
                    operation_means[operation] = statistics.mean(successful_times)

            if operation_means:
                slowest_operation = max(operation_means.items(), key=lambda x: x[1])
                f.write(
                    f"The slowest operation is '{slowest_operation[0]}' with an average response time of {slowest_operation[1]:.2f}ms.\n\n"
                )

            # Compare with previous implementation if data is available
            f.write("## Comparison with Previous Implementation\n\n")
            f.write(
                "To compare with the previous implementation, run this benchmark script against both implementations and manually compare the results.\n\n"
            )

            # Add recommendations section
            f.write("## Recommendations\n\n")
            f.write(
                "Based on these benchmark results, consider the following optimizations:\n\n"
            )
            f.write(
                "1. **Token Verification**: Optimize token verification process if it's taking longer than expected\n"
            )
            f.write(
                "2. **Token Refresh**: Ensure token refresh is efficient, as it's a critical operation\n"
            )
            f.write(
                "3. **Concurrency**: Evaluate performance under higher concurrency levels\n"
            )

        print(f"Report generated at: {report_path}")
        return report_path


def run_auth_benchmarks(base_url, iterations=10, concurrency=1, test_suite=None):
    """Run a complete suite of auth performance benchmarks"""
    benchmark = AuthPerformanceBenchmark(
        base_url, num_iterations=iterations, concurrency=concurrency
    )

    # Define the test suite
    if test_suite is None:
        test_suite = [
            "login",
            "token_verification",
            "token_refresh",
            "concurrent",
            "full_flow",
        ]

    try:
        # Run selected benchmarks
        if "login" in test_suite:
            print("Benchmarking login performance...")
            benchmark.login()

        if "token_verification" in test_suite:
            print("Benchmarking token verification performance...")
            benchmark.token_verification()

        if "token_refresh" in test_suite:
            print("Benchmarking token refresh performance...")
            benchmark.token_refresh()

        if "concurrent" in test_suite:
            print("Benchmarking concurrent API requests...")
            benchmark.concurrent_api_requests(num_requests=20)

        if "full_flow" in test_suite:
            print("Benchmarking full authentication flow...")
            benchmark.full_auth_flow()

        # Generate and return the report
        return benchmark.generate_report()
    except Exception as e:
        print(f"Error during benchmark: {str(e)}")
        return None


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Authentication performance benchmark")
    parser.add_argument(
        "--url", type=str, default="http://localhost:5000", help="Base URL for the API"
    )
    parser.add_argument(
        "--iterations", type=int, default=10, help="Number of iterations per operation"
    )
    parser.add_argument(
        "--concurrency",
        type=int,
        default=1,
        help="Concurrency level (number of parallel requests)",
    )
    parser.add_argument(
        "--tests",
        type=str,
        default="all",
        help="Comma-separated list of tests to run (login,token_verification,token_refresh,concurrent,full_flow) or 'all'",
    )
    args = parser.parse_args()

    test_suite = args.tests.split(",") if args.tests != "all" else None

    print(f"Running authentication performance benchmark against {args.url}")
    print(
        f"Using {args.iterations} iterations per operation with concurrency level {args.concurrency}"
    )

    report_path = run_auth_benchmarks(
        args.url, args.iterations, args.concurrency, test_suite
    )
    if report_path:
        print(f"Benchmark complete. Report available at: {report_path}")
