import React, { useEffect, useState } from "react";
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  Button,
  CircularProgress,
  Alert,
} from "@mui/material";
import { verifyOpenRouterApiKey } from "../services/openRouterService";
import { api } from "../services/api";

const EnvironmentTest: React.FC = () => {
  const [envVars, setEnvVars] = useState<Record<string, string>>({});
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationResult, setVerificationResult] = useState<{
    isValid?: boolean;
    message?: string;
  }>({});
  const [backendStatus, setBackendStatus] = useState<{
    isConnected: boolean;
    message: string;
  }>({ isConnected: false, message: "Not tested yet" });

  useEffect(() => {
    // Collect all environment variables that start with REACT_APP_
    const vars: Record<string, string> = {};

    Object.keys(process.env).forEach((key) => {
      if (key.startsWith("REACT_APP_")) {
        vars[key] = process.env[key] || "undefined";
      }
    });

    // Check window.ENV if available
    if (window.ENV) {
      Object.keys(window.ENV).forEach((key) => {
        if (key.startsWith("REACT_APP_")) {
          // Use type assertion to fix TypeScript error
          const envValue =
            window.ENV && (window.ENV as Record<string, string>)[key];
          vars[`window.ENV.${key}`] = envValue || "undefined";
        }
      });
    }

    setEnvVars(vars);

    // Test backend connection on component mount
    testBackendConnection();
  }, []);

  const testBackendConnection = async () => {
    try {
      const response = await api.get("/health");
      setBackendStatus({
        isConnected: true,
        message: `Connected to backend: ${response.data.message || "OK"} (${
          response.data.environment
        })`,
      });
    } catch (error: any) {
      console.error("Error connecting to backend:", error);
      setBackendStatus({
        isConnected: false,
        message: `Failed to connect to backend: ${
          error.message || "Unknown error"
        }`,
      });
    }
  };

  const handleVerifyApiKey = async () => {
    setIsVerifying(true);
    try {
      const result = await verifyOpenRouterApiKey();
      setVerificationResult(result);
    } catch (error: any) {
      console.error("Error verifying API key:", error);
      setVerificationResult({
        isValid: false,
        message: `Error verifying API key: ${error.message || "Unknown error"}`,
      });
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <Box sx={{ p: 4, maxWidth: 800, mx: "auto" }}>
      <Typography variant="h4" gutterBottom>
        Environment Variables Test
      </Typography>

      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Backend Connection Status:
        </Typography>
        <Alert severity={backendStatus.isConnected ? "success" : "error"}>
          {backendStatus.message}
        </Alert>

        <Button
          variant="outlined"
          sx={{ mt: 2 }}
          onClick={testBackendConnection}
        >
          Test Backend Connection
        </Button>
      </Paper>

      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Available REACT_APP_ Environment Variables:
        </Typography>

        {Object.keys(envVars).length === 0 ? (
          <Typography color="error">
            No REACT_APP_ environment variables found!
          </Typography>
        ) : (
          <List>
            {Object.entries(envVars).map(([key, value]) => (
              <ListItem key={key} divider>
                <ListItemText
                  primary={key}
                  secondary={
                    key.includes("KEY") || key.includes("SECRET")
                      ? `${value.substring(0, 10)}...`
                      : value
                  }
                />
              </ListItem>
            ))}
          </List>
        )}

        <Box sx={{ mt: 3 }}>
          <Button
            variant="contained"
            onClick={handleVerifyApiKey}
            disabled={isVerifying}
            startIcon={isVerifying ? <CircularProgress size={20} /> : null}
          >
            {isVerifying ? "Verifying..." : "Verify OpenRouter API Key"}
          </Button>

          {verificationResult.message && (
            <Typography
              sx={{ mt: 2 }}
              color={verificationResult.isValid ? "success.main" : "error.main"}
            >
              {verificationResult.message}
            </Typography>
          )}
        </Box>
      </Paper>
    </Box>
  );
};

export default EnvironmentTest;
