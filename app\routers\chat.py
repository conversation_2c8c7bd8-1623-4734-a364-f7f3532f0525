"""
Chat session routes for FastAPI.
Migrated from Flask app/ai_assistant/chat_routes.py to use FastAPI dependency injection.
This handles CRUD operations for chat sessions (not the streaming endpoint yet).
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field

from app.auth.dependencies import CurrentUserIdType
from app.dependencies import ChatSessionRepositoryType

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/chat", tags=["chat"])


class ChatSessionCreate(BaseModel):
    """Request model for creating a chat session."""
    title: str = Field(..., description="Title for the chat session")
    content_format: Optional[str] = Field(default="general", description="Content format/type")


class ChatSessionResponse(BaseModel):
    """Response model for chat session data."""
    id: str
    user_id: str
    title: str
    session_type: Optional[str] = None
    max_tokens: Optional[int] = None
    created_at: str
    updated_at: str


class ChatMessageCreate(BaseModel):
    """Request model for creating a chat message."""
    content: str = Field(..., description="Message content")
    youtube_url: Optional[str] = Field(None, description="Optional YouTube URL")
    business_context_id: Optional[str] = Field(None, description="Optional business context ID")
    content_ids: Optional[List[str]] = Field(default=[], description="Optional content IDs")


class ChatMessageResponse(BaseModel):
    """Response model for chat message data."""
    id: str
    session_id: str
    user_id: str
    role: str
    content: str
    token_count: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: str


class ChatSessionWithMessages(BaseModel):
    """Response model for chat session with messages."""
    session: ChatSessionResponse
    messages: List[ChatMessageResponse]


class BatchDeleteRequest(BaseModel):
    """Request model for batch deleting chat sessions."""
    session_ids: List[str] = Field(..., description="List of session IDs to delete")


@router.get("/sessions", response_model=List[ChatSessionResponse])
async def get_chat_sessions(
    current_user_id: CurrentUserIdType,
    chat_repo: ChatSessionRepositoryType
) -> List[ChatSessionResponse]:
    """Get all chat sessions for the current user."""
    try:
        sessions = await chat_repo.get_by_user_id(current_user_id)
        return sessions or []
        
    except Exception as e:
        logger.error(f"Error fetching chat sessions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch chat sessions: {str(e)}"
        )


@router.post("/sessions", response_model=ChatSessionResponse, status_code=201)
async def create_chat_session(
    session_data: ChatSessionCreate,
    current_user_id: CurrentUserIdType,
    chat_repo: ChatSessionRepositoryType
) -> ChatSessionResponse:
    """Create a new chat session."""
    try:
        # Get max tokens from config (for now, use a default)
        # TODO: Import from app.ai_assistant.config when available
        max_tokens = 100000  # Default value
        
        session = await chat_repo.create_chat_session(
            user_id=current_user_id,
            title=session_data.title,
            session_type=session_data.content_format,
            max_tokens=max_tokens
        )
        
        if not session:
            raise HTTPException(
                status_code=500,
                detail="Failed to create chat session"
            )
        
        return session
        
    except Exception as e:
        logger.error(f"Error creating chat session: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create chat session: {str(e)}"
        )


@router.get("/sessions/{session_id}", response_model=ChatSessionWithMessages)
async def get_chat_session(
    session_id: str,
    current_user_id: CurrentUserIdType,
    chat_repo: ChatSessionRepositoryType
) -> ChatSessionWithMessages:
    """Get a specific chat session with its messages."""
    try:
        result = await chat_repo.get_session_with_messages(session_id, current_user_id)

        if not result:
            raise HTTPException(
                status_code=404,
                detail="Chat session not found"
            )

        # Extract messages from the result and create proper response structure
        messages = result.pop("messages", [])
        session_data = result

        return ChatSessionWithMessages(
            session=ChatSessionResponse(**session_data),
            messages=[ChatMessageResponse(**msg) for msg in messages]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching chat session: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch chat session: {str(e)}"
        )


@router.delete("/sessions/{session_id}")
async def delete_chat_session(
    session_id: str,
    current_user_id: CurrentUserIdType,
    chat_repo: ChatSessionRepositoryType
) -> Dict[str, str]:
    """Delete a chat session and all its messages."""
    try:
        success = await chat_repo.delete_chat_session(session_id, current_user_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Chat session not found or could not be deleted"
            )
        
        return {"message": "Chat session deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting chat session: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete chat session: {str(e)}"
        )


@router.delete("/sessions/batch")
async def delete_multiple_chat_sessions(
    delete_request: BatchDeleteRequest,
    current_user_id: CurrentUserIdType,
    chat_repo: ChatSessionRepositoryType
) -> Dict[str, Any]:
    """Delete multiple chat sessions and all their messages."""
    try:
        if not delete_request.session_ids:
            raise HTTPException(
                status_code=400,
                detail="session_ids cannot be empty"
            )
        
        result = await chat_repo.delete_multiple_chat_sessions(
            delete_request.session_ids, 
            current_user_id
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error batch deleting chat sessions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete chat sessions: {str(e)}"
        )


@router.post("/sessions/{session_id}/messages", response_model=Dict[str, Any], status_code=201)
async def add_chat_message(
    session_id: str,
    message_data: ChatMessageCreate,
    current_user_id: CurrentUserIdType,
    chat_repo: ChatSessionRepositoryType
) -> Dict[str, Any]:
    """Add a new message to a chat session and get AI response."""
    try:
        # Verify session exists
        session = await chat_repo.get_by_id(session_id, current_user_id)
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Chat session not found"
            )
        
        # Add user message
        user_message = await chat_repo.add_message(
            session_id=session_id,
            user_id=current_user_id,
            role="user",
            content=message_data.content
        )
        
        # TODO: Integrate with AI generator for response
        # For now, return a placeholder response
        # This will be completed when we migrate the AI generation logic
        
        # Update session timestamp
        await chat_repo.update_chat_session(
            session_id=session_id,
            user_id=current_user_id,
            update_data={"updated_at": datetime.now(timezone.utc).isoformat()},
        )
        
        return {
            "session_id": session_id,
            "message": user_message,
            "note": "AI response generation will be implemented in the next migration phase"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding chat message: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to add chat message: {str(e)}"
        )


# Import additional dependencies for streaming
import json
from typing import AsyncGenerator
from fastapi.responses import StreamingResponse


class ChatStreamRequest(BaseModel):
    """Request model for streaming chat."""
    prompt: str = Field(..., description="User message prompt")
    business_context: Optional[Dict[str, Any]] = Field(None, description="Dedicated business context")
    conversation_id: Optional[str] = Field(None, description="Existing conversation ID")
    content_format: Optional[str] = Field(default="general", description="Content format for system prompt")
    client_timestamp: Optional[str] = Field(None, description="Client timestamp")


@router.post("/stream")
async def chat_stream(
    stream_request: ChatStreamRequest,
    current_user_id: CurrentUserIdType,
    chat_repo: ChatSessionRepositoryType
) -> StreamingResponse:
    """
    Handle chat requests to the AI assistant and stream responses.
    This is the FastAPI equivalent of the Flask streaming endpoint.
    """
    logger.info(f"[CHAT_STREAM] Starting chat stream request for user {current_user_id}")

    try:
        # Import AI generator and business context helper
        from app.ai_assistant.common import get_ai_generator, get_combined_business_context
        from app.ai_assistant.config import get_max_tokens

        # Get AI generator
        ai_generator = get_ai_generator()

        # Validate prompt
        if not stream_request.prompt:
            raise HTTPException(
                status_code=400,
                detail="Missing prompt in request"
            )

        prompt = stream_request.prompt
        conversation_id = stream_request.conversation_id
        content_format = stream_request.content_format

        # Create new conversation if needed
        if not conversation_id:
            max_tokens = get_max_tokens("chat_stream")
            new_conversation_title = prompt[:50] + "..." if len(prompt) > 50 else prompt

            new_conversation = await chat_repo.create_chat_session(
                user_id=current_user_id,
                title=new_conversation_title,
                session_type=content_format,
                max_tokens=max_tokens
            )
            conversation_id = new_conversation["id"]
            logger.info(f"[CHAT_STREAM] Created new conversation: {conversation_id}")

        # Get conversation messages
        messages_from_db = await chat_repo.get_messages(conversation_id, current_user_id)
        formatted_messages = [{"role": msg["role"], "content": msg["content"]} for msg in messages_from_db]
        formatted_messages.append({"role": "user", "content": prompt})

        # Save user message before streaming AI response
        try:
            await chat_repo.add_message(
                session_id=conversation_id,
                role="user",
                content=prompt,
                user_id=current_user_id,
                metadata={"client_timestamp": stream_request.client_timestamp}
            )
            logger.info(f"[CHAT_STREAM] Saved user message to conversation {conversation_id}")
        except Exception as e_msg_save:
            logger.error(f"Failed to save user message before streaming: {e_msg_save}")
            # Continue streaming but log the error

        # Combine business context
        combined_business_context = get_combined_business_context(
            current_user_id, stream_request.business_context
        )

        # Check if AI generator supports streaming
        if not hasattr(ai_generator, 'generate_chat_response_stream'):
            logger.error(f"[CHAT_STREAM] AI Provider '{ai_generator.provider}' does not support streaming")
            raise HTTPException(
                status_code=500,
                detail=f"Streaming not supported by current AI provider: {ai_generator.provider}"
            )

        # Create async generator for streaming
        async def stream_generator() -> AsyncGenerator[str, None]:
            assistant_response_buffer = []

            try:
                logger.info("[CHAT_STREAM] Starting async stream generator")

                # Stream AI response
                async for chunk in ai_generator.generate_chat_response_stream(
                    messages=formatted_messages,
                    content_format=content_format,
                    business_context=combined_business_context,
                ):
                    assistant_response_buffer.append(chunk)
                    sse_event = f"data: {json.dumps({'delta': chunk})}\n\n"
                    logger.debug(f"[CHAT_STREAM] Yielding chunk: {chunk[:50]}...")
                    yield sse_event

                # Save complete assistant response
                logger.info(f"[CHAT_STREAM] Stream finished, saving {len(assistant_response_buffer)} chunks")
                complete_assistant_response = "".join(assistant_response_buffer)

                if complete_assistant_response:
                    await chat_repo.add_message(
                        session_id=conversation_id,
                        role="assistant",
                        content=complete_assistant_response,
                        user_id=current_user_id,
                        metadata={"tokens_used": None}
                    )
                    logger.info(f"[CHAT_STREAM] Saved assistant response to conversation {conversation_id}")

                # Send completion event
                done_event = f"data: {json.dumps({'status': 'done'})}\n\n"
                yield done_event

            except Exception as e_stream:
                logger.error(f"[CHAT_STREAM] Error during streaming generation: {e_stream}", exc_info=True)
                error_payload = json.dumps({
                    "error": str(e_stream),
                    "details": "Error during AI response generation."
                })
                yield f"data: {error_payload}\n\n"

                # Save error message
                try:
                    await chat_repo.add_message(
                        session_id=conversation_id,
                        role="assistant",
                        content=f"Error during streaming: {str(e_stream)}",
                        user_id=current_user_id,
                        metadata={"is_error": True}
                    )
                except Exception as e_save_error:
                    logger.error(f"Failed to save error message: {e_save_error}")

        # Return streaming response
        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"  # Disable nginx buffering
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[CHAT_STREAM] Unexpected error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while processing your request."
        )
