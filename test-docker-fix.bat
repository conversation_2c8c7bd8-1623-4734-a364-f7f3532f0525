@echo off
echo ========================================
echo DOCKER FIX VERIFICATION SCRIPT
echo ========================================
echo.

echo [1/8] Rebuilding containers with fixes...
docker-compose down
docker-compose build --no-cache
docker-compose up -d
echo.

echo [2/8] Waiting for containers to start...
timeout /t 30 /nobreak
echo.

echo [3/8] Checking container status...
docker-compose ps
echo.

echo [4/8] Testing backend health endpoints...
echo Testing /health endpoint:
curl -s -w "HTTP Status: %%{http_code}\n" http://localhost:5000/health || echo FAILED
echo.
echo Testing /api/health endpoint:
curl -s -w "HTTP Status: %%{http_code}\n" http://localhost:5000/api/health || echo FAILED
echo.

echo [5/8] Testing frontend proxy...
echo Testing frontend proxy test endpoint:
curl -s -w "HTTP Status: %%{http_code}\n" http://localhost:3000/proxy-test || echo FAILED
echo.

echo [6/8] Testing frontend to backend communication...
echo Testing /api/health through frontend proxy:
curl -s -w "HTTP Status: %%{http_code}\n" http://localhost:3000/api/health || echo FAILED
echo.

echo [7/8] Checking container logs for errors...
echo Backend logs (last 10 lines):
docker-compose logs --tail=10 backend
echo.
echo Frontend logs (last 10 lines):
docker-compose logs --tail=10 frontend
echo.

echo [8/8] VERIFICATION COMPLETE
echo ========================================
echo RESULTS SUMMARY:
echo - If all HTTP Status codes are 200: SUCCESS
echo - If any show 000 or FAILED: Check logs above
echo - Frontend should be accessible at http://localhost:3000
echo - Backend API should be accessible at http://localhost:5000/api/health
echo ========================================
pause
