/**
 * API URL Verification and Debugging Utility
 * 
 * This utility helps verify that all API calls are using the correct
 * relative URLs and not absolute URLs that could cause ERR_NAME_NOT_RESOLVED errors.
 */

import { api } from '../services/api';

interface ApiCallVerification {
  endpoint: string;
  method: string;
  expectedUrl: string;
  actualUrl?: string;
  status: 'pending' | 'success' | 'error';
  error?: string;
}

interface VerificationReport {
  timestamp: string;
  environment: {
    userAgent: string;
    location: string;
    apiBaseUrl: string;
    envApiUrl?: string;
  };
  apiCalls: ApiCallVerification[];
  networkRequests: any[];
  recommendations: string[];
}

// Track network requests
const networkRequests: any[] = [];

/**
 * Intercept and log all network requests
 */
const startNetworkMonitoring = (): (() => void) => {
  // Store original fetch
  const originalFetch = window.fetch;
  
  // Override fetch to log requests
  window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
    const url = typeof input === 'string' ? input : input.toString();
    const method = init?.method || 'GET';
    
    console.log(`🌐 Fetch request: ${method} ${url}`);
    
    networkRequests.push({
      timestamp: new Date().toISOString(),
      method,
      url,
      type: 'fetch',
      headers: init?.headers
    });
    
    try {
      const response = await originalFetch(input, init);
      console.log(`✅ Fetch response: ${method} ${url} - ${response.status}`);
      return response;
    } catch (error) {
      console.error(`❌ Fetch error: ${method} ${url}`, error);
      throw error;
    }
  };
  
  // Store original XMLHttpRequest
  const originalXHR = window.XMLHttpRequest;
  
  // Override XMLHttpRequest to log requests
  window.XMLHttpRequest = class extends originalXHR {
    private _method?: string;
    private _url?: string;
    
    open(method: string, url: string | URL, async?: boolean, user?: string | null, password?: string | null) {
      this._method = method;
      this._url = url.toString();

      console.log(`🌐 XHR request: ${method} ${url}`);

      networkRequests.push({
        timestamp: new Date().toISOString(),
        method,
        url: url.toString(),
        type: 'xhr'
      });

      return super.open(method, url, async !== undefined ? async : true, user, password);
    }
    
    send(body?: any) {
      this.addEventListener('load', () => {
        console.log(`✅ XHR response: ${this._method} ${this._url} - ${this.status}`);
      });
      
      this.addEventListener('error', () => {
        console.error(`❌ XHR error: ${this._method} ${this._url}`);
      });
      
      return super.send(body);
    }
  };
  
  // Return cleanup function
  return () => {
    window.fetch = originalFetch;
    window.XMLHttpRequest = originalXHR;
    console.log('🛑 Network monitoring stopped');
  };
};

/**
 * Test common API endpoints to verify they use relative URLs
 */
const testApiEndpoints = async (): Promise<ApiCallVerification[]> => {
  const testEndpoints = [
    { endpoint: '/health', method: 'GET' },
    { endpoint: '/auth/ping', method: 'GET' },
    { endpoint: '/business/', method: 'GET' },
    { endpoint: '/content/', method: 'GET' },
    { endpoint: '/ai_assistant/chat/sessions', method: 'GET' }
  ];
  
  const results: ApiCallVerification[] = [];
  
  for (const test of testEndpoints) {
    const verification: ApiCallVerification = {
      endpoint: test.endpoint,
      method: test.method,
      expectedUrl: `${window.location.origin}/api${test.endpoint}`,
      status: 'pending'
    };
    
    try {
      console.log(`🧪 Testing API endpoint: ${test.method} ${test.endpoint}`);
      
      // Use the configured api instance
      const response = await api.get(test.endpoint);
      
      verification.status = 'success';
      verification.actualUrl = response.config.url;
      
      console.log(`✅ API test successful: ${test.endpoint}`);
    } catch (error: any) {
      verification.status = 'error';
      verification.error = error.message;
      verification.actualUrl = error.config?.url;
      
      console.log(`❌ API test failed: ${test.endpoint} - ${error.message}`);
    }
    
    results.push(verification);
  }
  
  return results;
};

/**
 * Check environment configuration
 */
const checkEnvironmentConfig = () => {
  const env = {
    userAgent: navigator.userAgent,
    location: window.location.href,
    apiBaseUrl: (api.defaults.baseURL || 'not set'),
    envApiUrl: undefined as string | undefined
  };
  
  // Check if environment variables are accessible
  try {
    if (typeof window !== 'undefined' && (window as any).ENV) {
      env.envApiUrl = (window as any).ENV.REACT_APP_API_URL;
    }
  } catch (error) {
    console.warn('Could not access window.ENV:', error);
  }
  
  return env;
};

/**
 * Generate recommendations based on findings
 */
const generateRecommendations = (
  environment: any,
  apiCalls: ApiCallVerification[],
  networkRequests: any[]
): string[] => {
  const recommendations: string[] = [];
  
  // Check for absolute URLs in network requests
  const absoluteUrls = networkRequests.filter(req => 
    req.url.includes('backend:5000') || 
    req.url.includes('localhost:5000') ||
    (req.url.startsWith('http') && !req.url.startsWith(window.location.origin))
  );
  
  if (absoluteUrls.length > 0) {
    recommendations.push(
      `Found ${absoluteUrls.length} requests using absolute URLs. Check for direct fetch/axios calls bypassing the configured api instance.`
    );
  }
  
  // Check API base URL configuration
  if (environment.apiBaseUrl !== '/api') {
    recommendations.push(
      `API base URL is "${environment.apiBaseUrl}" but should be "/api" for relative URLs.`
    );
  }
  
  // Check for failed API calls
  const failedCalls = apiCalls.filter(call => call.status === 'error');
  if (failedCalls.length > 0) {
    recommendations.push(
      `${failedCalls.length} API calls failed. Check authentication and network connectivity.`
    );
  }
  
  // Check environment configuration
  if (environment.envApiUrl && environment.envApiUrl.includes('backend:5000')) {
    recommendations.push(
      `Environment variable REACT_APP_API_URL is set to "${environment.envApiUrl}" which may cause issues. Ensure all API calls use the configured axios instance with relative URLs.`
    );
  }
  
  if (recommendations.length === 0) {
    recommendations.push('✅ All API URL configurations appear correct!');
  }
  
  return recommendations;
};

/**
 * Run comprehensive API URL verification
 */
export const runApiUrlVerification = async (): Promise<VerificationReport> => {
  console.group('🔍 Starting API URL Verification');
  
  // Start network monitoring
  const stopNetworkMonitoring = startNetworkMonitoring();
  
  try {
    // Check environment
    const environment = checkEnvironmentConfig();
    console.log('Environment:', environment);
    
    // Test API endpoints
    const apiCalls = await testApiEndpoints();
    
    // Generate report
    const report: VerificationReport = {
      timestamp: new Date().toISOString(),
      environment,
      apiCalls,
      networkRequests: [...networkRequests],
      recommendations: generateRecommendations(environment, apiCalls, networkRequests)
    };
    
    console.log('📊 Verification Report:', report);
    console.groupEnd();
    
    return report;
  } finally {
    // Stop network monitoring
    stopNetworkMonitoring();
  }
};

/**
 * Display verification results in console
 */
export const displayVerificationResults = (report: VerificationReport) => {
  console.group('📋 API URL Verification Results');
  
  console.group('🌍 Environment');
  console.log('Location:', report.environment.location);
  console.log('API Base URL:', report.environment.apiBaseUrl);
  console.log('Env API URL:', report.environment.envApiUrl || 'not set');
  console.groupEnd();
  
  console.group('🧪 API Call Tests');
  report.apiCalls.forEach(call => {
    const status = call.status === 'success' ? '✅' : '❌';
    console.log(`${status} ${call.method} ${call.endpoint}`, {
      expected: call.expectedUrl,
      actual: call.actualUrl,
      error: call.error
    });
  });
  console.groupEnd();
  
  console.group('🌐 Network Requests');
  console.log(`Total requests monitored: ${report.networkRequests.length}`);
  report.networkRequests.forEach(req => {
    const isAbsolute = req.url.startsWith('http') && !req.url.startsWith(window.location.origin);
    const icon = isAbsolute ? '⚠️' : '✅';
    console.log(`${icon} ${req.method} ${req.url}`);
  });
  console.groupEnd();
  
  console.group('💡 Recommendations');
  report.recommendations.forEach(rec => console.log('•', rec));
  console.groupEnd();
  
  console.groupEnd();
};

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).apiUrlVerifier = {
    runApiUrlVerification,
    displayVerificationResults,
    startNetworkMonitoring
  };
  
  console.log('🔧 API URL verifier available at window.apiUrlVerifier');
}
