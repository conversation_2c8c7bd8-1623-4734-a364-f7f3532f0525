import React, {
  createContext,
  useState,
  useContext,
  ReactN<PERSON>,
  Dispatch,
  SetStateAction,
} from "react";

// Define the shape of the context data
interface BusinessContextSelectionContextProps {
  selectedBusinessContextId: string | null;
  setSelectedBusinessContextId: Dispatch<SetStateAction<string | null>>;
}

// Create the context with a default value
// Using 'null' as the default value to satisfy the type, but it will be overridden by the Provider
const BusinessContextSelectionContext =
  createContext<BusinessContextSelectionContextProps | null>(null);

// Define the props for the Provider component
interface BusinessContextSelectionProviderProps {
  children: ReactNode;
}

// Create the Provider component
export const BusinessContextSelectionProvider: React.FC<
  BusinessContextSelectionProviderProps
> = ({ children }) => {
  const [selectedBusinessContextId, setSelectedBusinessContextId] = useState<
    string | null
  >(null);

  return (
    <BusinessContextSelectionContext.Provider
      value={{ selectedBusinessContextId, setSelectedBusinessContextId }}
    >
      {children}
    </BusinessContextSelectionContext.Provider>
  );
};

// Create the custom hook for consuming the context
export const useBusinessContextSelection =
  (): BusinessContextSelectionContextProps => {
    const context = useContext(BusinessContextSelectionContext);
    if (!context) {
      // Provide a clear error message if the hook is used outside of the provider
      throw new Error(
        "useBusinessContextSelection must be used within a BusinessContextSelectionProvider"
      );
    }
    return context;
  };

// Export the context itself if needed elsewhere (optional, usually the hook is sufficient)
export default BusinessContextSelectionContext;
