"""
Health check routes for FastAPI.
Standardized health endpoints for the application.
"""

from fastapi import APIRouter
from typing import Dict, Any

router = APIRouter(prefix="/api", tags=["health"])

# Also create a router without prefix for root-level health checks
root_router = APIRouter(tags=["health"])


@router.get("/")
async def root() -> Dict[str, str]:
    """Root endpoint returning welcome message."""
    return {"message": "Welcome to Writer v2 API (FastAPI)"}


@root_router.get("/health")
async def root_health_check() -> Dict[str, str]:
    """Root-level health check endpoint at /health (for Docker health checks)."""
    return {"status": "healthy", "framework": "FastAPI", "endpoint": "root"}


@router.get("/health")
async def health_check() -> Dict[str, str]:
    """Health check endpoint at /api/health."""
    return {"status": "healthy", "framework": "FastAPI"}


@router.get("/health/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """Detailed health check with more information."""
    return {
        "status": "healthy",
        "framework": "FastAPI",
        "version": "2.0.0",
        "migration_status": "Complete - FastAPI Migration Finished"
    }
