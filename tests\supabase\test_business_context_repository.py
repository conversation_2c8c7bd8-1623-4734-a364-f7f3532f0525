"""
Unit tests for the Supabase BusinessContextRepository implementation
Tests the functionality of the BusinessContextRepository class
"""

import os
import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import MagicMock

# Import the repository class
from app.repositories.supabase_business_context_repository import (
    BusinessContextRepository,
)
from app.repositories.supabase_repository import SupabaseRepositoryError  # Added import

# Check if Supabase is configured
supabase_url = os.environ.get("SUPABASE_URL")
supabase_key = os.environ.get("SUPABASE_KEY")
skip_supabase_tests = not (supabase_url and supabase_key)


@pytest.mark.skipif(skip_supabase_tests, reason="Supabase not configured")
class TestBusinessContextRepository:
    """Test the BusinessContextRepository class"""

    def test_initialization(self, supabase_client):
        """Test that the repository initializes correctly"""
        repo = BusinessContextRepository(supabase_client)

        assert repo is not None
        assert repo.supabase is not None
        assert repo.table_name == "business_contexts"

    def test_create_business_context(self, setup_mock_supabase_client, test_user_id):
        """Test creating business context with specific mock"""
        # The setup_mock_supabase_client fixture provides the mock client
        mock_client = setup_mock_supabase_client

        # Reset the table mock before the test uses it to avoid counting calls from fixture setup
        mock_client.table.reset_mock()

        # Configure the mock client instance and its chained calls
        mock_insert_response = MagicMock()
        # Simulate returning the data that was supposed to be inserted
        context_data_to_return = {
            "id": str(uuid.uuid4()),  # Generate a mock ID
            "user_id": test_user_id,
            "offer_description": "Test offer description",
            "target_audience": "Developers and IT professionals",
            "brand_voice": "Professional and friendly",
            "key_benefits": "Save time, improve quality, reduce costs",
            "unique_value_proposition": "The most efficient solution for your technical needs",
            # Use timezone-aware UTC time
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat(),
        }
        mock_insert_response.data = [context_data_to_return]
        mock_client.table.return_value.insert.return_value.execute.return_value = (
            mock_insert_response
        )
        # No need to mock_get_client.return_value = mock_client, fixture handles it

        # Now use the mock client indirectly via the repository
        repo = (
            BusinessContextRepository()
        )  # Initialize repo (will use the mocked client)

        # Create test business context data (match what's returned)
        context_data = {
            "offer_description": context_data_to_return["offer_description"],
            "target_audience": context_data_to_return["target_audience"],
            "brand_voice": context_data_to_return["brand_voice"],
            "key_benefits": context_data_to_return["key_benefits"],
            "unique_value_proposition": context_data_to_return[
                "unique_value_proposition"
            ],
        }

        # Pass data as a dictionary
        context = repo.create_business_context(
            user_id=test_user_id, business_context_data=context_data
        )

        # Verify context was created (using the data returned by the mock)
        assert context is not None
        assert (
            context == context_data_to_return
        )  # Check if the returned dict matches mock

        # Verify the mock was called correctly
        mock_client.table.assert_called_once_with("business_contexts")
        # Prepare expected data sent to insert (includes user_id)
        expected_insert_data = {**context_data, "user_id": test_user_id}
        mock_client.table.return_value.insert.assert_called_once_with(
            expected_insert_data
        )
        mock_client.table.return_value.insert.return_value.execute.assert_called_once()

    def test_get_business_context(
        self, supabase_client, test_user_id, test_business_context
    ):
        """Test getting business context by ID"""
        repo = BusinessContextRepository(supabase_client)

        # Get the business context
        # Use get_by_id and pass user_id
        context = repo.get_by_id(test_business_context.get("id"), test_user_id)

        # Verify context was retrieved
        assert context is not None
        assert context.get("id") == test_business_context.get("id")
        assert context.get("user_id") == test_user_id
        # Assert fields that exist in the fixture/repo
        assert context.get("offer_description") == test_business_context.get(
            "offer_description"
        )

    def test_update_business_context(
        self, supabase_client, test_user_id, test_business_context
    ):
        """Test updating business context"""
        repo = BusinessContextRepository(supabase_client)

        # Update data
        update_data = {
            "offer_description": "Updated offer description",
            "target_audience": "Updated target audience",
            "brand_voice": "Casual and conversational",
            "key_benefits": "Updated benefits list",
            "unique_value_proposition": "Updated value proposition",
        }

        # Update the business context
        # Pass update_data as a dictionary
        updated_context = repo.update_business_context(
            context_id=test_business_context.get("id"),
            user_id=test_user_id,
            update_data=update_data,
        )

        # Verify context was updated
        assert updated_context is not None
        assert updated_context.get("id") == test_business_context.get("id")
        assert (
            updated_context.get("offer_description") == update_data["offer_description"]
        )
        assert updated_context.get("target_audience") == update_data["target_audience"]
        assert updated_context.get("brand_voice") == update_data["brand_voice"]
        assert updated_context.get("key_benefits") == update_data["key_benefits"]
        assert (
            updated_context.get("unique_value_proposition")
            == update_data["unique_value_proposition"]
        )

        # Get the context to confirm update
        # Use get_by_id and pass user_id
        context = repo.get_by_id(test_business_context.get("id"), test_user_id)
        assert context.get("offer_description") == update_data["offer_description"]

    def test_delete_business_context(self, supabase_client, test_user_id):
        """Test deleting business context"""
        repo = BusinessContextRepository(supabase_client)

        # Create context to delete
        # Pass data as a dictionary - requires defining fields expected by create_business_context
        delete_context_data = {
            "offer_description": "Delete Test Offer",
            "target_audience": "Delete Test Audience",
            "brand_voice": "Delete Test Voice",
            # Add other required fields if necessary
        }
        context = repo.create_business_context(
            user_id=test_user_id, business_context_data=delete_context_data
        )

        context_id = context.get("id")

        # Delete the context
        result = repo.delete_business_context(context_id, test_user_id)

        # Verify deletion was successful
        assert result is True

        # Try to get the deleted context
        # Use get_by_id and pass user_id
        deleted_context = repo.get_by_id(context_id, test_user_id)
        assert deleted_context is None

    def test_get_user_business_contexts(self, supabase_client, test_user_id):
        """Test getting all business contexts for a user"""
        repo = BusinessContextRepository(supabase_client)

        # Create multiple business contexts
        contexts = []
        for i in range(3):
            # Pass data as a dictionary - requires defining fields expected by create_business_context
            loop_context_data = {
                "offer_description": f"Loop Test Offer {i}",
                "target_audience": f"Loop Test Audience {i}",
                "brand_voice": f"Loop Test Voice {i}",
                # Add other required fields if necessary
            }
            context = repo.create_business_context(
                user_id=test_user_id, business_context_data=loop_context_data
            )
            contexts.append(context)

        # Get user business contexts
        # Use get_by_user_id
        user_contexts = repo.get_by_user_id(test_user_id)

        # Verify contexts were retrieved
        assert user_contexts is not None
        assert len(user_contexts) >= 3

        # Clean up
        for context in contexts:
            try:
                supabase_client.table("business_contexts").delete().eq(
                    "id", context.get("id")
                ).execute()
            except Exception as e:
                print(f"Error cleaning up test business context: {e}")

    # Removing test_get_user_business_contexts_with_filters as get_by_user_id doesn't support arbitrary filters
    # def test_get_user_business_contexts_with_filters(self, supabase_client, test_user_id):
    #     """Test getting business contexts with filters"""
    #     pass # Test removed

    # Removing test_search_business_contexts as the method doesn't exist
    # def test_search_business_contexts(self, supabase_client, test_user_id):
    #    """Test searching business contexts"""
    #    pass # Test removed

    # Removing test_get_recently_used_contexts as the method doesn't exist
    # def test_get_recently_used_contexts(self, supabase_client, test_user_id):
    #     """Test getting recently used contexts"""
    #     pass # Test removed

    def test_error_handling(self, supabase_client):
        """Test error handling in the repository"""
        repo = BusinessContextRepository(supabase_client)

        # Test with invalid context ID (non-existent integer)
        invalid_id = str(uuid.uuid4())  # Use a non-existent UUID string context ID
        dummy_user_id = str(uuid.uuid4())  # Use a non-existent UUID string for user ID

        # Test get_by_id with non-existent ID
        context = repo.get_by_id(invalid_id, dummy_user_id)
        # Should return None for non-existent context
        assert context is None

        # Test update with non-existent ID
        error_update_data = {"offer_description": "Won't Update"}
        with pytest.raises(
            SupabaseRepositoryError, match="not found or does not belong to user"
        ):  # Expect SupabaseRepositoryError for not found
            repo.update_business_context(
                context_id=invalid_id,  # Use integer ID
                user_id=dummy_user_id,  # Use UUID string user ID
                update_data=error_update_data,
            )

        # Test delete with non-existent ID
        with pytest.raises(
            SupabaseRepositoryError, match="not found or does not belong to user"
        ):  # Expect SupabaseRepositoryError for not found
            repo.delete_business_context(invalid_id, dummy_user_id)

        # Test create with missing required field
        invalid_create_data = {
            # Missing "offer_description", "target_audience", "brand_voice"
            "key_benefits": "Some benefits"
        }
        # Expect SupabaseRepositoryError for missing fields during creation
        with pytest.raises(SupabaseRepositoryError, match="Missing required field"):
            repo.create_business_context(dummy_user_id, invalid_create_data)
