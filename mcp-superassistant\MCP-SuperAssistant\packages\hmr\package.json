{"name": "@extension/hmr", "version": "0.4.2", "description": "chrome extension - hot module reload/refresh", "type": "module", "private": true, "sideEffects": true, "files": ["dist/**"], "types": "index.mts", "main": "dist/index.mjs", "scripts": {"clean:bundle": "rimraf dist && pnpm dlx rimraf build", "clean:node_modules": "pnpm dlx rimraf node_modules", "clean:turbo": "rimraf .turbo", "clean": "pnpm clean:bundle && pnpm clean:node_modules && pnpm clean:turbo", "ready": "tsc -b && rollup --config dist/rollup.config.js", "dev": "node dist/lib/initializers/initReloadServer.js", "lint": "eslint .", "lint:fix": "pnpm lint --fix", "prettier": "prettier . --write --ignore-path ../../.prettierignore", "type-check": "tsc --noEmit"}, "devDependencies": {"@extension/tsconfig": "workspace:*", "@extension/env": "workspace:*", "@rollup/plugin-sucrase": "^5.0.2", "@types/ws": "^8.5.13", "esm": "^3.2.25", "rollup": "^4.24.0", "ts-node": "^10.9.2", "ws": "8.18.0"}}