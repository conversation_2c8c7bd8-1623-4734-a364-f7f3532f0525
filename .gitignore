# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.pytest_cache/
pytest.ini
pytestdebug.log

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# Environment variables
.env
.env.*
!.env.example
!.env.test
!.env.local  # Allow .env.local for deployment

# IDE files
.idea/
.vscode/
*.swp
*.swo
.cursor/
*.sublime-project
*.sublime-workspace

# Database
*.sqlite3
*.db
*.sql
instance/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
cover/

# Flask
instance/
.webassets-cache
flask_session/

# Frontend/JavaScript/TypeScript
node_modules/
jspm_packages/
bower_components/
.npm
.eslintcache
.stylelintcache
.node_repl_history
.yarn-integrity
.parcel-cache
.next
.nuxt
.vuepress/dist
.serverless/
.fusebox/
.dynamodb/
.tern-port
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
dist/
build/
*.tsbuildinfo

# Docker
.docker/
docker-compose.override.yml

# macOS
.DS_Store
.AppleDouble
.LSOverride
._*

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.lnk

# Temporary files
*~
*.tmp
*.temp
*.bak
