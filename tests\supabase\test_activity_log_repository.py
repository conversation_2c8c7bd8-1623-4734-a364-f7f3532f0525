"""
Unit tests for the Supabase ActivityLogRepository implementation
Tests the functionality of the ActivityLogRepository class
"""

import os

# Import the repository class

# Check if Supabase is configured
supabase_url = os.environ.get("SUPABASE_URL")
supabase_key = os.environ.get("SUPABASE_KEY")
skip_supabase_tests = not (supabase_url and supabase_key)

# @pytest.mark.skipif(skip_supabase_tests, reason="Supabase not configured")
# class TestActivityLogRepository:
#     """Test the ActivityLogRepository class"""

#     def test_initialization(self, supabase_client):
#         """Test that the repository initializes correctly"""
#         repo = ActivityLogRepository(supabase_client)

#         assert repo is not None
#         assert repo.supabase is not None
#         assert repo.table_name == "user_activities" # Check correct table name

#     def test_log_activity(self, supabase_client, test_user_id):
#         """Test logging an activity"""
#         repo = ActivityLogRepository(supabase_client)

#         # Log a test activity
#         activity = repo.log_activity(
#             user_id=test_user_id,
#             activity_type="test", # Renamed parameter
#             title="Test Activity Logged", # Added required title
#             description="Test details value", # Changed details dict to description string
#             resource_type="test_resource",
#             resource_id="123",
#             # ip_address removed
#         )

#         # Verify the activity was logged
#         assert activity is not None
#         assert activity.get("user_id") == test_user_id
#         assert activity.get("activity_type") == "test" # Check activity_type
#         assert activity.get("title") == "Test Activity Logged" # Check title
#         assert activity.get("description") == "Test details value" # Check description
#         assert activity.get("resource_type") == "test_resource"
#         assert activity.get("resource_id") == "123"
#         # ip_address assertion removed

#         # Clean up
#         try:
#             supabase_client.table("user_activities").delete().eq("id", activity.get("id")).execute() # Use correct table name
#         except Exception as e:
#             print(f"Error cleaning up test activity: {e}")

#     def test_get_user_activities(self, supabase_client, test_user_id):
#         """Test getting user activities"""
#         repo = ActivityLogRepository(supabase_client)

#         # Log multiple test activities
#         activities = []
#         for i in range(3):
#             activity = repo.log_activity(
#                 user_id=test_user_id,
#                 activity_type=f"test_{i}", # Renamed parameter
#                 title=f"Test Activity {i}", # Added title
#                 description=f"Test details value {i}", # Changed details to description
#                 resource_type="test_resource",
#                 resource_id=str(i),
#                 # ip_address removed
#             )
#             activities.append(activity)

#         # Get user activities
#         user_activities = repo.get_user_activities(test_user_id)

#         # Verify activities were retrieved
#         assert user_activities is not None
#         assert len(user_activities) >= 3

#         # Clean up
#         for activity in activities:
#             try:
#                 supabase_client.table("user_activities").delete().eq("id", activity.get("id")).execute() # Use correct table name
#             except Exception as e:
#                 print(f"Error cleaning up test activity: {e}")

#     def test_get_user_activities_with_filters(self, supabase_client, test_user_id):
#         """Test getting user activities with filters"""
#         repo = ActivityLogRepository(supabase_client)

#         # Log activities with different action types
#         activities = []
#         action_types = ["login", "create", "update", "delete"]
#         for action_type in action_types:
#             activity = repo.log_activity(
#                 user_id=test_user_id,
#                 activity_type=action_type, # Renamed parameter
#                 title=f"Test {action_type} Activity", # Added title
#                 description="Test details value", # Changed details to description
#                 resource_type="test_resource",
#                 resource_id="123",
#                 # ip_address removed
#             )
#             activities.append(activity)

#         # Get activities with login action type
#         login_activities = repo.get_user_activities(
#             user_id=test_user_id,
#             activity_type="login" # Use activity_type for filtering
#         )

#         # Verify filtered activities
#         assert login_activities is not None
#         assert len(login_activities) >= 1
#         for activity in login_activities:
#             assert activity.get("activity_type") == "login" # Check activity_type

#         # Clean up
#         for activity in activities:
#             try:
#                 supabase_client.table("user_activities").delete().eq("id", activity.get("id")).execute() # Use correct table name
#             except Exception as e:
#                 print(f"Error cleaning up test activity: {e}")

#     def test_get_resource_activities(self, supabase_client, test_user_id):
#         """Test getting activities for a specific resource"""
#         repo = ActivityLogRepository(supabase_client)

#         # Resource information
#         resource_type = "test_resource"
#         resource_id = str(uuid.uuid4())

#         # Log activities for the resource
#         activities = []
#         for i in range(3):
#             activity = repo.log_activity(
#                 user_id=test_user_id,
#                 activity_type=f"test_{i}", # Renamed parameter
#                 title=f"Test Resource Activity {i}", # Added title
#                 description=f"Test details value {i}", # Changed details to description
#                 resource_type=resource_type,
#                 resource_id=resource_id,
#                 # ip_address removed
#             )
#             activities.append(activity)

#         # Get resource activities
#         resource_activities = repo.get_resource_activities(
#             user_id=test_user_id,
#             resource_type=resource_type,
#             resource_id=resource_id
#         )

#         # Verify resource activities
#         assert resource_activities is not None
#         assert len(resource_activities) >= 3
#         for activity in resource_activities:
#             assert activity.get("resource_type") == resource_type
#             assert activity.get("resource_id") == resource_id

#         # Clean up
#         for activity in activities:
#             try:
#                 supabase_client.table("user_activities").delete().eq("id", activity.get("id")).execute() # Use correct table name
#             except Exception as e:
#                 print(f"Error cleaning up test activity: {e}")

#     def test_get_login_activities(self, supabase_client, test_user_id):
#         """Test getting login activities"""
#         repo = ActivityLogRepository(supabase_client)

#         # Log login activities
#         activities = []
#         for i in range(3):
#             activity = repo.log_activity(
#                 user_id=test_user_id,
#                 activity_type="login", # Renamed parameter
#                 title=f"Test Login {i}", # Added title
#                 description=f"Login source test {i}", # Changed details to description
#                 # ip_address removed
#             )
#             activities.append(activity)

#         # Get login activities
#         login_activities = repo.get_login_activities(test_user_id)

#         # Verify login activities
#         assert login_activities is not None
#         assert len(login_activities) >= 3
#         for activity in login_activities:
#             assert activity.get("activity_type") == "login" # Check activity_type

#         # Clean up
#         for activity in activities:
#             try:
#                 supabase_client.table("user_activities").delete().eq("id", activity.get("id")).execute() # Use correct table name
#             except Exception as e:
#                 print(f"Error cleaning up test activity: {e}")

#     def test_get_activity_counts_by_type(self, supabase_client, test_user_id):
#         """Test getting activity counts by type"""
#         repo = ActivityLogRepository(supabase_client)

#         # Log activities with different action types
#         activities = []
#         action_types = ["login", "create", "update", "create", "login"]
#         for action_type in action_types:
#             activity = repo.log_activity(
#                 user_id=test_user_id,
#                 activity_type=action_type, # Renamed parameter
#                 title=f"Test Count {action_type}", # Added title
#                 description="Test count details", # Changed details to description
#                 resource_type="test_resource",
#                 resource_id="123",
#                 # ip_address removed
#             )
#             activities.append(activity)

#         # Get activity counts
#         counts = repo.get_activity_counts_by_type(test_user_id)

#         # Verify counts
#         assert counts is not None
#         assert "login" in counts
#         assert "create" in counts
#         assert "update" in counts
#         assert counts.get("login") >= 2
#         assert counts.get("create") >= 2
#         assert counts.get("update") >= 1

#         # Clean up
#         for activity in activities:
#             try:
#                 supabase_client.table("user_activities").delete().eq("id", activity.get("id")).execute() # Use correct table name
#             except Exception as e:
#                 print(f"Error cleaning up test activity: {e}")

#     def test_error_handling(self, supabase_client):
#         """Test error handling in the repository"""
#         repo = ActivityLogRepository(supabase_client)

#         # Test with invalid user ID (None)
#         # Test with invalid user ID (None) - should still fail in repo before DB call
#         # Need to provide required 'title' even for error test
#         activity = repo.log_activity(
#             user_id=None,
#             activity_type="test", # Renamed parameter
#             title="Error Test Activity", # Added required title
#             resource_type="test_resource",
#             resource_id="123"
#         )

#         # Should return None on error
#         assert activity is None
