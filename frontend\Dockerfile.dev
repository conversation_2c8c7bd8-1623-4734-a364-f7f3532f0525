# Use Node.js LTS base image (updated from 16 to 18)
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies including curl for health check
RUN apk add --no-cache git curl

# Copy package files first for better caching
COPY package*.json ./

# Set npm configuration for faster installs
RUN npm config set registry https://registry.npmjs.org/
RUN npm config set fetch-retries 5
RUN npm config set fetch-retry-factor 2

# Install dependencies with optimizations (include dev deps for development)
RUN npm ci --include=dev && \
    npm cache clean --force

# Copy environment files (if they exist)
COPY .env* ./

# Copy source code
COPY public/ ./public/
COPY src/ ./src/
COPY tsconfig.json ./

# Create necessary directories and set permissions
RUN mkdir -p /app/node_modules/.cache && \
    chmod -R 755 /app

# Expose port
EXPOSE 3000

# Improved health check with proper start period
HEALTHCHECK --interval=15s --timeout=5s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Set environment for Docker networking
ENV HOST=0.0.0.0
ENV PORT=3000

# Start development server with optimizations
CMD ["npm", "start"]