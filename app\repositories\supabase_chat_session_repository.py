"""
ChatSessionRepository for Supabase implementation
Handles CRUD operations for chat sessions and their messages
"""

import logging
from datetime import datetime, timezone
from postgrest.exceptions import APIError as PostgrestAPIError
from app.ai_assistant.config import get_model_for_purpose
from app.models.supabase_client import get_supabase_client
from app.repositories.supabase_repository import (
    SupabaseRepository,
    SupabaseRepositoryError,
)


logger = logging.getLogger(__name__)


class ChatSessionRepository(SupabaseRepository):
    """Repository for managing chat sessions in Supabase"""

    def __init__(self):
        """Initialize with Supabase client"""
        super().__init__(table_name="chat_sessions")

    async def get_by_user_id(self, user_id, limit=20, offset=0):
        """Get chat sessions for a specific user with pagination asynchronously."""
        try:
            client = await get_supabase_client()
            table = client.table(self.table_name)
            result = await (
                table
                .select("*")
                .eq("user_id", user_id)
                .order("updated_at", desc=True)
                .range(offset, offset + limit - 1)
                .execute()
            )
            return result.data
        except Exception as e:
            self.handle_error(f"Error getting chat sessions for user {user_id}", e)
            raise # Re-raise

    async def get_by_id(self, session_id, user_id):
        """Get a specific chat session by ID asynchronously, ensuring it belongs to the user."""
        try:
            logger.debug(f"Getting chat session with ID: {session_id} for user: {user_id}")
            if not isinstance(session_id, str):
                session_id = str(session_id)

            client = await get_supabase_client()
            table = client.table(self.table_name)
            result = await (
                table
                .select("*")
                .eq("id", session_id)
                .eq("user_id", user_id)
                .maybe_single()
                .execute()
            )
            if result.data:
                logger.debug(f"Chat session found: {result.data}")
                return result.data
            else:
                logger.warning(f"Chat session not found for ID: {session_id}, user: {user_id}")
                return None
        except Exception as e:
            self.handle_error(f"Error getting chat session {session_id} for user {user_id}", e)
            raise # Re-raise

    async def create_chat_session(
        self,
        user_id,
        title,
        model_name=None,
        system_prompt=None,
        temperature=0.7,
        max_tokens=500000,
        content_item_id=None,
        business_context_id=None,
        session_type="general",
    ):
        """Create a new chat session asynchronously."""
        try:
            logger.debug(f"Creating chat session for user: {user_id}")
            if not user_id:
                raise ValueError("User ID is required to create a chat session")
            if model_name is None:
                model_name = get_model_for_purpose()
                logger.info(f"No model specified, using default: {model_name}")
            else:
                logger.info(f"Using specified model: {model_name}")

            now = datetime.now(timezone.utc).isoformat()
            data = {
                "user_id": user_id,
                "title": title,
                "model_name": model_name,
                "system_prompt": system_prompt,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "content_item_id": content_item_id,
                "business_context_id": business_context_id,
                "session_type": session_type,
                "created_at": now,
                "updated_at": now,
            }
            client = await get_supabase_client()
            table = client.table(self.table_name)
            response = await table.insert(data).execute()
            if not response.data:
                logger.error(f"Failed to create chat session: Supabase returned no data. Response: {response}")
                raise SupabaseRepositoryError("Failed to create chat session: No data returned", response=response)
            logger.info(f"Successfully created chat session {response.data[0]['id']} for user {user_id}")
            return response.data[0]
        except PostgrestAPIError as e:
            logger.error(f"Supabase API error creating chat session for user {user_id}: {e.message}")
            if hasattr(e, "details"): logger.error(f"Error Details: {e.details}")
            if hasattr(e, "hint"): logger.error(f"Error Hint: {e.hint}")
            raise SupabaseRepositoryError(f"Supabase API error creating chat session: {e.message}", original_exception=e)
        except ValueError as e:
            logger.error(f"Validation error creating chat session for user {user_id}: {e}")
            raise SupabaseRepositoryError(f"Validation error: {e}", original_exception=e)
        except Exception as e:
            logger.error(f"Unexpected error creating chat session for user {user_id}: {e}", exc_info=True)
            raise SupabaseRepositoryError(f"Unexpected error: {e}", original_exception=e)

    async def update_chat_session(self, session_id, user_id, update_data):
        """Update an existing chat session asynchronously."""
        try:
            existing = await self.get_by_id(session_id, user_id)
            if not existing:
                raise SupabaseRepositoryError(f"Chat session {session_id} not found or does not belong to user {user_id}", status_code=404)
            if "user_id" in update_data: del update_data["user_id"]
            # updated_at will be handled by DB trigger or should be set here if no trigger
            update_data.setdefault("updated_at", datetime.now(timezone.utc).isoformat())

            client = await get_supabase_client()
            table = client.table(self.table_name)
            result = await (
                table.update(update_data)
                .eq("id", session_id)
                .eq("user_id", user_id)
                .execute()
            )
            if result.data and len(result.data) > 0:
                return result.data[0]
            raise SupabaseRepositoryError(f"Failed to update chat session {session_id}: No data returned or RLS issue.")
        except SupabaseRepositoryError: # Re-raise specific repo errors
            raise
        except Exception as e:
            self.handle_error(f"Error updating chat session {session_id} for user {user_id}", e)
            raise # Re-raise

    async def delete_chat_session(self, session_id, user_id):
        """Delete a chat session and its messages asynchronously."""
        try:
            # Optional: Verify ownership first, though RLS should handle it for delete
            # existing = await self.get_by_id(session_id, user_id)
            # if not existing:
            #     logger.warning(f"Chat session {session_id} not found for user {user_id} to delete.")
            #     return False

            # Delete messages associated with the session first (if cascade isn't set up or for explicit control)
            # This requires a Supabase client instance
            client = await get_supabase_client()
            await client.table("chat_messages").delete().eq("session_id", session_id).eq("user_id", user_id).execute()
            logger.info(f"Deleted messages for chat session {session_id}")

            # Then delete the session itself
            table = client.table(self.table_name)
            await (
                table.delete()
                .eq("id", session_id)
                .eq("user_id", user_id)
                .execute()
            )
            # Check if response indicates success (e.g., if data is returned or count > 0)
            # For delete, often response.data is empty even on success. Check for errors instead.
            logger.info(f"Deleted chat session {session_id} for user {user_id}")
            return True # Assume success if no error, or refine based on actual client behavior for delete
        except Exception as e:
            self.handle_error(f"Error deleting chat session {session_id} for user {user_id}", e)
            raise # Re-raise

    async def delete_multiple_chat_sessions(self, session_ids: list, user_id: str):
        """Delete multiple chat sessions and their messages asynchronously."""
        if not session_ids:
            return 0
        deleted_count = 0
        try:
            client = await get_supabase_client()
            # Delete messages for all sessions
            await client.table("chat_messages").delete().in_("session_id", session_ids).eq("user_id", user_id).execute()
            logger.info(f"Deleted messages for sessions: {session_ids}")

            # Delete sessions
            table = client.table(self.table_name)
            response = await table.delete().in_("id", session_ids).eq("user_id", user_id).execute()
            # `execute()` on delete might return the deleted items in `response.data`
            if response.data:
                deleted_count = len(response.data)
            logger.info(f"Deleted {deleted_count} chat sessions for user {user_id}: {session_ids}")
            return deleted_count
        except Exception as e:
            self.handle_error(f"Error deleting multiple chat sessions for user {user_id}", e)
            raise # Re-raise

    async def add_message(
        self, session_id, user_id, role, content, token_count=0, metadata=None
    ):
        """Add a message to a chat session asynchronously."""
        try:
            # Verify session exists and belongs to user
            session = await self.get_by_id(session_id, user_id)
            if not session:
                raise SupabaseRepositoryError(f"Chat session {session_id} not found for user {user_id}", status_code=404)

            now = datetime.now(timezone.utc).isoformat()
            message_data = {
                "session_id": session_id,
                "user_id": user_id,
                "role": role,
                "content": content,
                "token_count": token_count,
                "metadata": metadata or {},
                "created_at": now,
            }
            client = await get_supabase_client()
            response = await client.table("chat_messages").insert(message_data).execute()
            if not response.data:
                raise SupabaseRepositoryError("Failed to add message: No data returned")

            # Update session's updated_at timestamp
            await self.update_chat_session(session_id, user_id, {"updated_at": now})
            return response.data[0]
        except SupabaseRepositoryError: # Re-raise specific repo errors
            raise
        except Exception as e:
            self.handle_error(f"Error adding message to session {session_id}", e)
            raise # Re-raise

    async def get_messages(self, session_id, user_id, limit=50, offset=0):
        """Get messages for a chat session asynchronously."""
        try:
            # Verify session exists and belongs to user
            session = await self.get_by_id(session_id, user_id)
            if not session:
                raise SupabaseRepositoryError(f"Chat session {session_id} not found for user {user_id}", status_code=404)

            client = await get_supabase_client()
            response = await (
                client.table("chat_messages")
                .select("*")
                .eq("session_id", session_id)
                .eq("user_id", user_id) # Ensure messages also respect user_id, though session check is primary
                .order("created_at", desc=False) # Messages usually in ascending order
                .range(offset, offset + limit - 1)
                .execute()
            )
            return response.data if response.data else []
        except SupabaseRepositoryError:
            raise
        except Exception as e:
            self.handle_error(f"Error getting messages for session {session_id}", e)
            raise # Re-raise

    async def get_session_with_messages(self, session_id, user_id, message_limit=50):
        """Get a chat session along with its recent messages asynchronously."""
        try:
            session = await self.get_by_id(session_id, user_id)
            if not session:
                return None
            messages = await self.get_messages(session_id, user_id, limit=message_limit)
            session["messages"] = messages
            return session
        except Exception as e:
            # get_by_id and get_messages already handle_error and raise
            logger.error(f"Error in get_session_with_messages for session {session_id}: {e}")
            raise # Re-raise the underlying SupabaseRepositoryError

    async def get_by_content_item(self, content_item_id, user_id):
        """Get chat sessions linked to a specific content_item_id asynchronously."""
        try:
            client = await get_supabase_client()
            table = client.table(self.table_name)
            response = await (
                table.select("*")
                .eq("user_id", user_id)
                .eq("content_item_id", content_item_id)
                .order("updated_at", desc=True)
                .execute()
            )
            return response.data if response.data else []
        except Exception as e:
            self.handle_error(f"Error getting sessions for content_item {content_item_id}", e)
            raise # Re-raise
