from fastapi import HTTPException

class AppException(HTTPException):
    def __init__(self, message=None, status_code=500, payload=None):
        # For FastAPI HTTPException, we need to pass status_code and detail
        detail = message if message is not None else "An unexpected error occurred."
        super().__init__(status_code=status_code, detail=detail)
        self.payload = payload

    def to_dict(self):
        rv = dict(self.payload or ())
        rv['message'] = self.detail
        rv['type'] = self.__class__.__name__
        return rv

class NotFoundException(AppException):
    def __init__(self, message="Resource not found.", payload=None):
        super().__init__(message=message, status_code=404, payload=payload)

class ValidationException(AppException):
    def __init__(self, message="Validation failed.", payload=None):
        super().__init__(message=message, status_code=400, payload=payload)

class AuthenticationException(AppException):
    def __init__(self, message="Authentication failed.", payload=None):
        super().__init__(message=message, status_code=401, payload=payload)

class AuthorizationException(AppException):
    def __init__(self, message="You are not authorized to perform this action.", payload=None):
        super().__init__(message=message, status_code=403, payload=payload)

class ConflictException(AppException):
    def __init__(self, message="A conflict occurred.", payload=None):
        super().__init__(message=message, status_code=409, payload=payload)

class RateLimitException(AppException):
    def __init__(self, message="Rate limit exceeded. Please try again later.", payload=None):
        super().__init__(message=message, status_code=429, payload=payload)

class SupabaseRepositoryError(AppException):
    """Custom error for Supabase repository operations"""
    def __init__(self, message="Database operation failed.", status_code=500, payload=None, response=None, original_exception=None):
        super().__init__(message=message, status_code=status_code, payload=payload)
        self.response = response
        self.original_exception = original_exception

    def to_dict(self):
        rv = super().to_dict()
        if self.response:
            rv['response'] = str(self.response)
        if self.original_exception:
            rv['original_error'] = str(self.original_exception)
        return rv 