# Build stage
FROM node:16-alpine as build

WORKDIR /app

# Copy package.json and package-lock.json
COPY frontend/package*.json ./

# Install dependencies
RUN npm ci

# Copy the rest of the frontend code
COPY frontend/ /app/

# Build the app
RUN npm run build

# Production stage
FROM nginx:alpine

# Install gettext for envsubst utility
RUN apk update && apk add --no-cache gettext

# Copy built files from build stage
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY frontend/nginx.conf /etc/nginx/conf.d/default.conf

# Copy the entrypoint script and template file
COPY frontend/entrypoint.sh /entrypoint.sh
COPY frontend/public/env-config.js.template /usr/share/nginx/html/env-config.js.template

# Make the entrypoint script executable
RUN chmod +x /entrypoint.sh

# Remove the static config file if it was copied, as it's now generated dynamically
RUN rm -f /usr/share/nginx/html/env-config.js || true

# Expose port
EXPOSE 80

# Set the entrypoint
ENTRYPOINT ["/entrypoint.sh"]