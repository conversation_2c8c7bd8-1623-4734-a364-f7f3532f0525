@echo off
echo ========================================
echo FRONTEND ISSUE AUTO-FIX SCRIPT
echo ========================================
echo.

echo [1/5] Stopping containers...
docker-compose down
echo.

echo [2/5] Cleaning Docker cache...
docker system prune -f
echo.

echo [3/5] Rebuilding frontend with no cache...
docker-compose build --no-cache frontend
echo.

echo [4/5] Starting containers...
docker-compose up -d
echo.

echo [5/5] Waiting for startup (60 seconds)...
timeout /t 60 /nobreak
echo.

echo ========================================
echo AUTO-FIX COMPLETE!
echo ========================================
echo Frontend should now be available at: http://localhost:3000
echo Backend should be available at: http://localhost:5000/api/health
echo.
echo If still not working, run: diagnose-frontend.bat
echo ========================================
pause