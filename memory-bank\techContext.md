## Writer v2 Technical Context

**Backend:**

*   **Framework:** Flask >= 2.3.0
*   **Database:** Supabase (via `supabase-py`)
    *   Tables: `users` (auth schema), `activity_log`, `content_items`, `business_contexts`, `chat_sessions`, `chat_messages`, `feedback`, `youtube_video_data`, `wizard_sessions` (NEW)
*   **Authentication:** Supabase Auth (handled via custom middleware `supabase_auth_required`)
*   **API Integrations:**
    *   Google API Client (`google-api-python-client`) - For YouTube Data API.
    *   OpenAI Client (`openai`) - Used for interacting with LLMs via OpenRouter (or potentially OpenAI directly).
    *   YouTube Transcript API (`youtube-transcript-api`)
*   **Testing:** `pytest`, `pytest-cov`
*   **Utilities:** `python-dotenv`, `pydantic`, `passlib`, `requests`, `numpy`
*   **Formatting/Linting:** `black`, `flake8`
*   **Deployment:** `gunicorn`
*   **Development:** `python-jose`, `python-dateutil`, `sentence-transformers`, `pytest-asyncio`
*   **Dependencies (`requirements.txt`):**
    *   `Flask>=2.3.0`
    *   `supabase-py>=2.5.0` (Latest checked: v2.5.2)
    *   `openai>=1.10.0`
    *   `python-dotenv>=1.0.0`
    *   `gunicorn`
    *   `pytest`, `pytest-cov`
    *   (See `requirements.txt` for the full list and specific versions)

## Setup & Configuration

*   **Python Version:** 3.11+ (as defined in `runtime.txt` for deployment, ensure local env matches)
*   **Virtual Environment:** Managed using `venv` (`python -m venv venv`, `source venv/bin/activate` or `venv\Scripts\activate`)
*   **Dependencies Installation:** `pip install -r requirements.txt`
*   **Environment Variables (.env file - **NOT CHECKED INTO GIT**):**
    *   `FLASK_APP=app.py`
    *   `FLASK_ENV=development` (or `production`)
    *   `SUPABASE_URL`: Your Supabase project URL.
    *   `SUPABASE_KEY`: Your Supabase service role key (used for backend operations).
    *   `YOUTUBE_API_KEY`: Google API key enabled for the YouTube Data API v3.
    *   `OPENROUTER_API_KEY` or `OPENAI_API_KEY`: API Key for the chosen AI provider (used by `config.py`).
    *   `AI_PROVIDER`: (Optional) Specify provider ('openrouter' or 'openai', defaults to 'openrouter').
    *   `AI_DEFAULT_MODEL`: (Optional) Override default model.
    *   `AI_<PURPOSE>_MODEL`: (Optional) Override model for specific purposes (e.g., `AI_GENERAL_CHAT_MODEL`).

## Key Services & Endpoints

*   **Supabase:**
    *   Authentication: Handled via Supabase Auth.
    *   Database: PostgreSQL via Supabase (see tables list above).
    *   Storage: Supabase Storage (if used).
*   **YouTube Data API v3:** Used by `utils/youtube_utils.py` to fetch video details.
*   **OpenRouter/OpenAI:**
    *   **Endpoint:** Configured via `get_api_url()` in `app/ai_assistant/config.py` (defaults to `https://openrouter.ai/api/v1/chat/completions` or OpenAI standard endpoint if `AI_PROVIDER=openai`).
    *   **Authentication:** Bearer Token using API key (`OPENROUTER_API_KEY` or `OPENAI_API_KEY`) from `get_api_key()` in `app/ai_assistant/config.py`.
    *   **Usage:** Accessed via the `openai` Python client (sync: `OpenAI()`, async: `AsyncOpenAI()`). Base URL and API key are typically configured during client initialization based on environment variables or config.
*   **Feedback Endpoint:**
    *   **Endpoint:** `/api/feedback` (defined in `app/feedback/routes.py`)
    *   **Method:** POST
    *   **Auth:** Requires authentication (`@supabase_auth_required`).
    *   **Payload:** JSON body containing `session_id`, `message_id`, `feedback_type` ('script_usability'), `rating` ('up'/'down'), and optional `comment`.
    *   **Purpose:** Receives user feedback submissions from the frontend feedback widget.
*   **AI Script Wizard Endpoints:**
    *   **Blueprint:** `wizard_bp` mounted at `/ai/wizard`
    *   **Endpoints:**
        *   `/brainstorm` (POST): Starts a new wizard session, generates title ideas.
        *   `/hook` (POST): Generates hooks based on chosen title.
        *   `/intro` (POST): Generates intros/CTAs based on chosen hook.
        *   `/outline` (POST): Generates outline based on chosen intro/CTA.
        *   `/draft` (POST): Generates full draft based on outline.
        *   `/edit` (POST): Generates polished script based on draft.
    *   **Auth:** Requires authentication (`@supabase_auth_required`).
    *   **State:** Managed via `wizard_sessions` table, identified by `session_id` passed in requests.

**Frontend:**

*   **Framework:** React (TypeScript) using Create React App
*   **State Management:** Primarily React Context API (`AuthContext` for authentication) supplemented by Redux Toolkit for other global state (UI, activity).
*   **UI Library:** Material UI (MUI v5) (`@mui/material`, `@mui/icons-material`, `@emotion/react`, `@emotion/styled`)
*   **API Communication:** Axios (`axios`) with interceptors for auth tokens (`src/services/api.ts`).
*   **Routing:** React Router (`react-router-dom`)
*   **Dependencies (`frontend/package.json`):**
    *   React stack (`react`, `react-dom`, `react-scripts`, `typescript`) - React 18+
    *   Redux stack (`@reduxjs/toolkit`, `react-redux`)
    *   MUI stack (`@mui/material`, `@mui/icons-material`, `@emotion/react`, `@emotion/styled`) - MUI v5
    *   Supabase JS client (`@supabase/supabase-js`) - v2+
    *   API/Routing (`axios`, `react-router-dom`)
    *   Utilities: `framer-motion`, `react-markdown`, `remark-gfm`, `react-syntax-highlighter`, `chart.js`, `react-chartjs-2`, `web-vitals`
    *   Dev Dependencies: `@testing-library/*`, `@types/*`, `eslint`, `prettier`, `source-map-explorer`
*   **Environment Variables (`frontend/public/env-config.js`, `frontend/src/services/envConfig.ts`):**
    *   Backend API URL (`REACT_APP_API_URL`)
    *   Supabase public credentials (`REACT_APP_SUPABASE_URL`, `REACT_APP_SUPABASE_KEY` - *Note: Use the public anon key here*)
    *   Environment indicator (`REACT_APP_ENV`)
    *   Docker flag (`DOCKER_ENV`)
*   **Setup/Running:**
    *   Primarily via `docker-compose up` using `docker-compose.yml`.
    *   Frontend container uses `frontend/Dockerfile.dev`.
    *   Can be run standalone via `npm start` (or `yarn start`) within the `frontend` directory, assuming the backend is running.
    *   Relies on the backend API being accessible (configured via `REACT_APP_API_URL` and potentially `proxy` in `package.json`).
    *   Environment variables can be supplied via `.env` file (used by `react-scripts`) or dynamically via `public/env-config.js`.
*   **Constraints/Notes:**
    *   Uses TypeScript for type safety.
    *   Linting (`eslint`) and formatting (`prettier`) are set up (`npm run lint`, `npm run format`).
    *   Handles Supabase Auth client-side primarily via `AuthContext`, simplifying previous Redux synchronization.

**General Infrastructure:**

*   **Containerization:** Docker (`docker-compose.yml`, `Dockerfile`, `frontend/Dockerfile.dev`) for development and potentially production.
*   **Database:** PostgreSQL (managed via Supabase platform).
*   **Version Control:** Git (`.git` directory exists).

**External Services:**

*   **Supabase:** Backend-as-a-Service for Auth (JWT) and Database (PostgreSQL).
*   **OpenRouter:** AI model provider (primary, accessed via `openai` library).
*   **OpenAI:** AI model provider (secondary/fallback, accessed via `openai` library, key supported).
*   **Google Cloud / YouTube Data API:** For YouTube video search, details, and transcript fetching.

## Flask Testing & Mocking
*   **`app` vs `client`:** Use `app` fixture for app context (e.g., `with app.app_context():`), use `client` fixture for making requests (`client.get(...)`, `client.post(...)`).
*   **Patching:** Use `unittest.mock.patch` to replace objects during tests.
    *   Patch where the object is *looked up*, not necessarily where it's defined. For example, patch `'app.routes.dependency'` if `app.routes` imports `dependency`.
    *   Patching factory functions (like `get_ai_generator`): Patch the factory function itself. Its `return_value` should be the mock instance you want the route to use. Assert calls on the *returned instance*, not the factory function mock (unless testing the factory).
    *   Patching classes used by hooks (e.g., `ChatSessionRepository` in `supabase_hooks`): Patching the class definition (`@patch('app.utils.supabase_hooks.ChatSessionRepository')`) ensures that when the hook instantiates the class, it gets a mock. Configure the behavior of `MockClass.return_value`.
*   **Fixtures:** Use fixtures (`@pytest.fixture`) for setup (app, client, mock data, mock setup).
    *   `scope='module'` is efficient for setup that doesn't change between tests in a module.
    *   `autouse=True` applies a fixture automatically to all tests in its scope.
*   **Request Context (`g`):**
    *   Be careful when directly manipulating `g` in tests. It's often better to mock the underlying dependencies that populate `g` (e.g., mock the repository classes used by `init_supabase_repositories` hook).
    *   If direct manipulation is needed, use `with client.application.app_context():`.
*   **Authentication Mocking:**
    *   Mock the token verification function (`app.utils.supabase_auth.verify_supabase_token`) using a fixture (`@pytest.fixture(autouse=True, scope='module')`).
    *   Have the mock return a dictionary representing the authenticated user, containing necessary fields like `id` or `sub`.
    *   Pass dummy `Authorization` headers in `client` requests; the actual token value doesn't matter if verification is mocked.
*   **Repository Mocking (Supabase):**
    *   Use `set_test_client(MagicMock())` from `app.models.supabase_client` in a fixture to inject a mock Supabase client globally for testing.
    *   Alternatively, patch repository classes directly in tests or fixtures (`@patch('app.utils.supabase_hooks.ChatSessionRepository')`). Configure the `return_value` of the class mock to control the instance's behavior. 