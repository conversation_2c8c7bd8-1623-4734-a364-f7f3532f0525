import pytest
import os
from unittest.mock import patch, MagicMock, AsyncMock
from app.ai_assistant.openai_adapter import OpenAIAdapter


@pytest.fixture
def mock_openai_client():
    """Fixture to mock the AsyncOpenAI client"""
    with patch("app.ai_assistant.openai_adapter.AsyncOpenAI") as mock_openai:
        mock_client = MagicMock()
        # Make the create method async
        mock_client.chat.completions.create = AsyncMock()
        mock_openai.return_value = mock_client
        yield mock_client


@pytest.fixture
def mock_successful_completion():
    """Fixture for a successful OpenAI completion mock"""
    mock_completion = MagicMock()
    mock_completion.choices = [MagicMock()]
    mock_completion.choices[0].message = MagicMock()
    mock_completion.choices[0].message.content = "Mock OpenAI Response"
    mock_completion.usage = MagicMock()
    mock_completion.usage.total_tokens = 120
    mock_completion.model = "gpt-mock"
    return mock_completion


@pytest.fixture
def mock_error_completion():
    """Fixture for an error during OpenAI completion"""
    mock_client = MagicMock()
    # Make the create method async and raise an exception
    mock_client.chat.completions.create = AsyncMock(side_effect=Exception("OpenAI API Error"))
    with patch("app.ai_assistant.openai_adapter.AsyncOpenAI", return_value=mock_client):
        yield mock_client  # Yield the client whose method raises the error


@pytest.fixture(autouse=True)
def mock_env_vars():
    """Mock environment variables"""
    with patch.dict(os.environ, {"OPENAI_API_KEY": "test-openai-key"}):
        yield


@pytest.fixture
def mock_completion_response():
    """Mock completion response from OpenAI API"""
    mock_response = MagicMock()
    mock_response.choices = [MagicMock()]
    mock_response.choices[0].message = MagicMock()
    mock_response.choices[0].message.content = "This is a generated test content."
    mock_response.usage = MagicMock()
    mock_response.usage.total_tokens = 150
    return mock_response


@pytest.fixture
def test_business_context():
    """Create a test business context dictionary"""
    return {
        "id": 1,
        "user_id": 1,
        "offer_description": "Test product offering",
        "target_audience": "Test target audience",
        "brand_voice": "Professional and friendly",
        "key_benefits": "Key benefits of the test product",
        "unique_value_proposition": "Unique value of test product",
    }


@pytest.fixture
def test_content_examples():
    """Create test content examples"""
    return [
        {
            "id": 1,
            "user_id": 1,
            "title": "Test Content 1",
            "content": "This is test content example 1",
            "content_type": "blog",
            "tags": ["test", "example"],
        },
        {
            "id": 2,
            "user_id": 1,
            "title": "Test Content 2",
            "content": "This is test content example 2",
            "content_type": "blog",
            "tags": ["test", "example"],
        },
    ]


@pytest.fixture
def test_video_data():
    """Create test YouTube video data dictionary"""
    return {
        "id": 1,
        "user_id": 1,
        "youtube_id": "test123",
        "title": "Test YouTube Video",
        "channel_title": "Test Channel",
        "description": "This is a test video description",
        "views": 10000,
        "likes": 500,
        "comments": 100,
        "publish_date": "2023-01-01",
        "transcript": "This is a test transcript for the video. It contains sample content for testing.",
    }


# Test initializing the OpenAI adapter
def test_openai_adapter_init():
    """Test initializing the OpenAI adapter"""
    with patch.dict("os.environ", {"OPENAI_API_KEY": "test-key"}):
        adapter = OpenAIAdapter()
        assert adapter.api_key == "test-key"


# Test initializing the OpenAI adapter with explicit API key
def test_openai_adapter_init_with_key():
    """Test initializing the OpenAI adapter with explicit API key"""
    adapter = OpenAIAdapter(api_key="explicit-test-key")
    assert adapter.api_key == "explicit-test-key"


# Test initializing the OpenAI adapter without API key
# This test doesn't need app context as it should fail before config access
def test_openai_adapter_init_no_key():
    """Test initializing the OpenAI adapter without API key"""
    with patch.dict("os.environ", {}, clear=True):
        with pytest.raises(ValueError) as excinfo:
            # Instantiation happens here, might still trigger config if error handling is complex
            # Let's keep it outside context for now, assuming ValueError is raised early.
            OpenAIAdapter()
        assert "OpenAI API key is required" in str(excinfo.value)


# Test generating content with basic prompt
@pytest.mark.asyncio
async def test_generate_content_basic(mock_openai_client, mock_completion_response):
    """Test generating content with a basic prompt"""
    # Set up the mock
    mock_openai_client.chat.completions.create.return_value = mock_completion_response

    # Initialize adapter and generate content (no Flask app context needed)
    adapter = OpenAIAdapter(api_key="test-key")
    result = await adapter.generate_content(prompt="Test prompt")

    # Verify the result
    assert result["generated_text"] == "This is a generated test content."
    assert result["tokens_used"] == 150
    assert result["context_used"] is False
    assert result["examples_used"] is False

    # Verify the API was called correctly
    mock_openai_client.chat.completions.create.assert_called_once()
    _, kwargs = mock_openai_client.chat.completions.create.call_args
    assert kwargs["messages"][0]["role"] == "system"
    assert kwargs["messages"][1]["role"] == "user"
    assert kwargs["messages"][1]["content"] == "Test prompt"


# Test generating content with business context
@pytest.mark.asyncio
async def test_generate_content_with_business_context(
    mock_openai_client, mock_completion_response, test_business_context
):
    """Test generating content with business context"""
    # Set up the mock
    mock_openai_client.chat.completions.create.return_value = mock_completion_response

    # Initialize adapter and generate content (no Flask app context needed)
    adapter = OpenAIAdapter(api_key="test-key")
    result = await adapter.generate_content(
        prompt="Test prompt with business context",
        business_context=test_business_context,
    )

    # Verify the result
    assert result["generated_text"] == "This is a generated test content."
    assert result["context_used"] is True

    # Verify the API was called with business context
    mock_openai_client.chat.completions.create.assert_called_once()
    _, kwargs = mock_openai_client.chat.completions.create.call_args
    system_message = kwargs["messages"][0]["content"]
    assert "Business Context" in system_message
    assert "Test product offering" in system_message
    assert "Test target audience" in system_message


# Test generating content with content examples
@pytest.mark.asyncio
async def test_generate_content_with_examples(
    mock_openai_client, mock_completion_response, test_content_examples
):
    """Test generating content with content examples"""
    # Set up the mock
    mock_openai_client.chat.completions.create.return_value = mock_completion_response

    # Initialize adapter and generate content (no Flask app context needed)
    adapter = OpenAIAdapter(api_key="test-key")
    result = await adapter.generate_content(
        prompt="Test prompt with examples", content_examples=test_content_examples
    )

    # Verify the result
    assert result["generated_text"] == "This is a generated test content."
    assert result["examples_used"] is True

    # Verify the API was called with examples
    mock_openai_client.chat.completions.create.assert_called_once()
    _, kwargs = mock_openai_client.chat.completions.create.call_args
    assert len(kwargs["messages"]) == 2  # system, user (examples are in system prompt)
    # Examples are part of the system message (index 0)
    system_message = kwargs["messages"][0]["content"]
    assert "Content Examples" in system_message
    # Check system_message for examples content
    assert "This is test content example 1" in system_message
    assert "This is test content example 2" in system_message


# Test generating content with YouTube data
@pytest.mark.asyncio
async def test_generate_content_with_youtube_data(
    mock_openai_client, mock_completion_response, test_video_data
):
    """Test generating content with YouTube video data"""
    # Set up the mock
    mock_openai_client.chat.completions.create.return_value = mock_completion_response

    # Initialize adapter and generate content (no Flask app context needed)
    adapter = OpenAIAdapter(api_key="test-key")
    result = await adapter.generate_content_with_youtube_data(
        prompt="Test prompt with YouTube data", video_data=test_video_data
    )

    # Verify the result
    assert result["generated_text"] == "This is a generated test content."
    assert result["video_data_used"] is True

    # Verify the API was called with YouTube data
    mock_openai_client.chat.completions.create.assert_called_once()
    _, kwargs = mock_openai_client.chat.completions.create.call_args
    assert len(kwargs["messages"]) == 2  # system, user (video data is in system prompt)
    # Video data is part of the system message (index 0)
    system_message = kwargs["messages"][0]["content"]
    assert "YouTube Video Data" in system_message
    # Check system_message for video data content
    assert "Test YouTube Video" in system_message
    assert "This is a test transcript for the video" in system_message


# Test generating content with both business context and YouTube data
@pytest.mark.asyncio
async def test_generate_content_with_context_and_youtube(
    mock_openai_client,
    mock_completion_response,
    test_business_context,
    test_video_data,
):
    """Test generating content with both business context and YouTube data"""
    # Set up the mock
    mock_openai_client.chat.completions.create.return_value = mock_completion_response

    # Initialize adapter and generate content (no Flask app context needed)
    adapter = OpenAIAdapter(api_key="test-key")
    result = await adapter.generate_content_with_youtube_data(
        prompt="Test prompt with context and YouTube",
        business_context=test_business_context,
        video_data=test_video_data,
    )

    # Verify the result
    assert result["generated_text"] == "This is a generated test content."
    assert result["context_used"] is True
    assert result["video_data_used"] is True

    # Verify the API was called with both context and YouTube data
    mock_openai_client.chat.completions.create.assert_called_once()
    _, kwargs = mock_openai_client.chat.completions.create.call_args
    system_message = kwargs["messages"][0]["content"]
    # video_message = kwargs["messages"][1]["content"] # This is the user prompt
    assert "Business Context" in system_message
    # YouTube data is also part of the system message (index 0)
    assert "YouTube Video Data" in system_message


# Test error handling in content generation
@pytest.mark.asyncio
async def test_generate_content_error_handling(mock_openai_client):
    """Test error handling in content generation"""
    # Set up the mock to raise an exception
    mock_openai_client.chat.completions.create.side_effect = Exception("API error")

    # Initialize adapter and generate content (no Flask app context needed for FastAPI)
    adapter = OpenAIAdapter(api_key="test-key")
    result = await adapter.generate_content(prompt="Test prompt")

    # Verify the error was handled
    assert "error" in result
    assert result["error"] is True
    assert "API error" in result["error_message"]
    assert "Error generating content" in result["generated_text"]


# Test chat response generation
@pytest.mark.asyncio
async def test_generate_chat_response(mock_openai_client, mock_completion_response):
    """Test generating a chat response with message history"""
    # Set up the mock
    mock_openai_client.chat.completions.create.return_value = mock_completion_response

    # Create message history
    messages = [
        {"role": "user", "content": "First message"},
        {"role": "assistant", "content": "First response"},
        {"role": "user", "content": "Second message"},
    ]

    # Initialize adapter and generate chat response (no Flask app context needed)
    adapter = OpenAIAdapter(api_key="test-key")
    result = await adapter.generate_chat_response(
        messages=messages, content_format="blog"
    )

    # Verify the result
    assert result["generated_text"] == "This is a generated test content."

    # Verify the API was called with the message history
    mock_openai_client.chat.completions.create.assert_called_once()
    _, kwargs = mock_openai_client.chat.completions.create.call_args
    api_messages = kwargs["messages"]

    # First message should be system message
    assert api_messages[0]["role"] == "system"
    assert "blog" in api_messages[0]["content"]

    # Last three messages should be the conversation history
    assert api_messages[-3:][0]["role"] == "user"
    assert api_messages[-3:][0]["content"] == "First message"
    assert api_messages[-3:][1]["role"] == "assistant"
    assert api_messages[-3:][1]["content"] == "First response"
    assert api_messages[-3:][2]["role"] == "user"
    assert api_messages[-3:][2]["content"] == "Second message"


def test_adapter_init_success():
    """Test successful initialization with API key from env"""
    adapter = OpenAIAdapter()
    assert adapter.api_key == "test-openai-key"
    assert adapter.client is not None


@pytest.mark.asyncio
async def test_generate_content_success(mock_openai_client, mock_successful_completion):
    """Test successful content generation"""
    mock_openai_client.chat.completions.create.return_value = mock_successful_completion
    adapter = OpenAIAdapter()
    result = await adapter.generate_content(prompt="Test prompt")

    assert result["generated_text"] == "Mock OpenAI Response"
    assert result.get("error") is None or result.get("error") is False
    assert result["tokens_used"] == 120
    mock_openai_client.chat.completions.create.assert_called_once()
    _, call_kwargs = mock_openai_client.chat.completions.create.call_args
    assert call_kwargs["messages"][-1]["content"] == "Test prompt"
    assert call_kwargs["model"] is not None  # Check model is passed


@pytest.mark.asyncio
async def test_generate_content_api_error(mock_error_completion):
    """Test content generation with API error"""
    # Note: mock_error_completion already patches OpenAI client to raise error
    adapter = OpenAIAdapter()
    result = await adapter.generate_content(prompt="Test prompt")

    assert result["error"] is True
    assert "OpenAI API Error" in result["error_message"]
    assert "Error generating content" in result["generated_text"]


@pytest.mark.asyncio
async def test_generate_chat_response_success(
    mock_openai_client, mock_successful_completion
):
    """Test successful chat response generation"""
    mock_openai_client.chat.completions.create.return_value = mock_successful_completion
    adapter = OpenAIAdapter()
    messages = [{"role": "user", "content": "Hello"}]
    result = await adapter.generate_chat_response(messages=messages)

    assert result["generated_text"] == "Mock OpenAI Response"
    assert result.get("error") is None or result.get("error") is False
    mock_openai_client.chat.completions.create.assert_called_once()
    _, call_kwargs = mock_openai_client.chat.completions.create.call_args
    # System prompt + user message
    assert len(call_kwargs["messages"]) == 2
    assert call_kwargs["messages"][1]["content"] == "Hello"


@pytest.mark.asyncio
async def test_generate_content_with_youtube_data_success(
    mock_openai_client, mock_successful_completion
):
    """Test successful content generation with YouTube data"""
    mock_openai_client.chat.completions.create.return_value = mock_successful_completion
    adapter = OpenAIAdapter()
    video_data = {"title": "YT Video", "transcript": "YT Transcript"}
    result = await adapter.generate_content_with_youtube_data(
        prompt="Summarize video", video_data=video_data
    )

    assert result["generated_text"] == "Mock OpenAI Response"
    assert result["video_data_used"] is True
    mock_openai_client.chat.completions.create.assert_called_once()
    _, call_kwargs = mock_openai_client.chat.completions.create.call_args
    assert "YouTube Video Data" in call_kwargs["messages"][0]["content"]
    assert "YT Video" in call_kwargs["messages"][0]["content"]
    assert "YT Transcript" in call_kwargs["messages"][0]["content"]
    assert call_kwargs["messages"][1]["content"] == "Summarize video"
