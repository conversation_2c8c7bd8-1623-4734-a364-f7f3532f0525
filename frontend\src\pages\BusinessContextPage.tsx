import React, { useState, useEffect, useCallback } from "react";
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  TextField,
  CircularProgress,
  Divider,
  Snackbar,
  Alert,
  Card,
  CardContent,
  CardHeader,
  IconButton,
  Chip,
  Container,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Tabs,
  Tab,
} from "@mui/material";
import {
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  ExpandMore as ExpandMoreIcon,
  AutoFixHigh as OptimizeIcon,
  Psychology as PsychologyIcon,
  CheckCircleOutline as CheckCircleOutlineIcon,
  Visibility as VisibilityIcon,
} from "@mui/icons-material";
import { motion } from "framer-motion";
import { useAuth } from "../contexts/AuthContext";
import { api } from "../services/api";
import BusinessContextSidebar from "../components/BusinessContextSidebar";
import { useBusinessContextSelection } from "../contexts/BusinessContextSelectionContext";
import TagInput from "../components/TagInput";
import CreateProfileWizard, {
  ProfileWizardData,
} from "../components/CreateProfileWizard";
import OptimizeProfileModal from "../components/OptimizeProfileModal";
import AudienceAnalysisModal from "../components/AudienceAnalysisModal";
import ConfirmationDialog from "../components/ConfirmationDialog";
import AuthErrorBoundary from "../components/AuthErrorBoundary";
import { useAuthenticatedApi } from "../hooks/useAuthenticatedApi";
import { useTheme } from "@mui/material/styles";

interface BusinessContext {
  id: string;
  user_id: string;
  name?: string;
  profile_overview?: string | null;
  content_focus?: string[] | null;
  primary_objectives?: string | null;
  offer_description: string;
  target_audience: string;
  audience_motivation?: string | null;
  brand_voice?: string | null;
  key_benefits?: string | null;
  unique_value_proposition?: string | null;
  audience_decision_style?: string | null;
  audience_preference_structure?: string | null;
  audience_decision_speed?: string | null;
  audience_reaction_to_change?: string | null;
  recommended_platforms?: string[] | null;
  recommended_content_formats?: string[] | null;
  created_at: string;
  updated_at: string;
}

// Define type for section keys
type SectionKey =
  | "core"
  | "audience"
  | "decision"
  | "strategy"
  | "recommendations";

const BusinessContextPage: React.FC = () => {
  const { setSelectedBusinessContextId } = useBusinessContextSelection();
  const { session, loading: authLoading } = useAuth();

  // Utility function to safely extract string from error objects
  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.details) return error.details;
    if (error?.error) return getErrorMessage(error.error);
    if (typeof error === 'object') return JSON.stringify(error);
    return 'An unknown error occurred';
  };
  const [contexts, setContexts] = useState<BusinessContext[]>([]);
  const [currentContextId, setCurrentContextId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  // Removed unused editMode state
  const [formValues, setFormValues] = useState<Partial<BusinessContext>>({});
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: "success" | "error" | "info" | "warning";
  }>({
    open: false,
    message: "",
    severity: "info",
  });
  const [isInitialFetchDone, setIsInitialFetchDone] = useState<boolean>(false);

  // State to track which section is in edit mode
  const [editingSection, setEditingSection] = useState<SectionKey | null>(null);

  // State to track expanded accordions
  const [expandedAccordion, setExpandedAccordion] = useState<
    SectionKey | false
  >(false);

  // State to control wizard visibility (replaces modal state)
  const [isCreateWizardOpen, setIsCreateWizardOpen] = useState(false);
  // createError is used internally for error handling during profile creation
  const [createError, setCreateError] = useState<string | null>(null);

  // --- State for Delete Confirmation ---
  const [openConfirm, setOpenConfirm] = useState(false);
  const [pendingDeleteIds, setPendingDeleteIds] = useState<string[]>([]);
  // --- End State for Delete Confirmation ---

  // --- State for Optimization Modal ---
  const [isOptimizeModalOpen, setIsOptimizeModalOpen] = useState(false);
  // --- End State for Optimization Modal ---

  // --- State for Audience Analysis Modal ---
  const [isAnalysisModalOpen, setIsAnalysisModalOpen] = useState(false);
  // --- End State for Audience Analysis Modal ---

  // --- State for Tabs ---
  const [activeTab, setActiveTab] = useState<'profile' | 'audience'>('profile');
  const [hasAnalysis, setHasAnalysis] = useState<boolean>(false);
  const [checkingAnalysis, setCheckingAnalysis] = useState<boolean>(false);
  const [analysisContent, setAnalysisContent] = useState<any>(null);
  // --- End State for Tabs ---

  const theme = useTheme();

  const currentContext = contexts.find(
    (c: BusinessContext) => c.id === currentContextId
  );

  // Use the authenticated API hook for better error handling
  const businessContextsApi = useAuthenticatedApi<{contexts: BusinessContext[]}>('BusinessContextPage');

  const fetchBusinessContexts = useCallback(
    async (
      selectFirst: boolean = false,
      newlyCreatedId: string | null = null
    ) => {
      if (!session) return;

      setLoading(true);
      setError(null);
      let selectedId = currentContextId;

      try {
        const data = await businessContextsApi.execute("/business/", {
          retryCount: 1, // Reduce retries to prevent cascading failures
          retryDelay: 2000,
        });

        const fetchedContexts = (data.contexts || []).map(
          (c: BusinessContext) => ({
            ...c,
            name:
              c.name ||
              c.profile_overview?.substring(0, 40) +
                (c.profile_overview && c.profile_overview.length > 40
                  ? "..."
                  : "") ||
              `Profile ${c.id.substring(0, 6)}`,
          })
        );
        setContexts(fetchedContexts);

        if (newlyCreatedId) {
          selectedId = newlyCreatedId;
        } else if (
          selectFirst &&
          fetchedContexts.length > 0 &&
          currentContextId === null
        ) {
          selectedId = fetchedContexts[0].id;
        } else if (
          currentContextId &&
          !fetchedContexts.some(
            (c: BusinessContext) => c.id === currentContextId
          )
        ) {
          selectedId =
            fetchedContexts.length > 0 ? fetchedContexts[0].id : null;
        } else if (fetchedContexts.length === 0) {
          selectedId = null;
        }

        if (selectedId !== currentContextId) {
          setCurrentContextId(selectedId);
          if (selectedId === null) {
            setEditingSection(null);
            setIsCreateWizardOpen(false);
          }
        }
      } catch (err: any) {
        console.error("Error fetching business contexts:", err);
        const errorMessage = getErrorMessage(err);
        setError(errorMessage);
        setContexts([]);
        setCurrentContextId(null);
        setIsCreateWizardOpen(false);

        // Don't show auth errors as notifications since they're handled by the error boundary
        if (!errorMessage.toLowerCase().includes('authentication') &&
            !errorMessage.toLowerCase().includes('unauthorized')) {
          setNotification({
            open: true,
            message: errorMessage,
            severity: "error",
          });
        }
      } finally {
        setLoading(false);
        setIsInitialFetchDone(true);
      }
    },
    [session, currentContextId, businessContextsApi]
  );

  useEffect(() => {
    if (!authLoading && session && !isInitialFetchDone) {
      fetchBusinessContexts(true);
    } else if (!authLoading && !session) {
      setContexts([]);
      setCurrentContextId(null);
      setError(null);
      setLoading(false);
      setIsInitialFetchDone(false);
      setIsCreateWizardOpen(false);
    }
  }, [authLoading, session, fetchBusinessContexts, isInitialFetchDone]);

  useEffect(() => {
    setSelectedBusinessContextId(currentContextId);

    // Check for existing analysis when context changes
    if (currentContextId) {
      checkForExistingAnalysis(currentContextId);
    }
  }, [currentContextId, setSelectedBusinessContextId]);

  useEffect(() => {
    if (currentContext && !isCreateWizardOpen) {
      setFormValues({
        name: currentContext.name ?? undefined,
        profile_overview: currentContext.profile_overview ?? "",
        content_focus: currentContext.content_focus ?? [],
        primary_objectives: currentContext.primary_objectives ?? "",
        offer_description: currentContext.offer_description ?? "",
        target_audience: currentContext.target_audience ?? "",
        audience_motivation: currentContext.audience_motivation ?? "",
        brand_voice: currentContext.brand_voice ?? "",
        key_benefits: currentContext.key_benefits ?? "",
        unique_value_proposition: currentContext.unique_value_proposition ?? "",
        audience_decision_style: currentContext.audience_decision_style ?? "",
        audience_preference_structure:
          currentContext.audience_preference_structure ?? "",
        audience_decision_speed: currentContext.audience_decision_speed ?? "",
        audience_reaction_to_change:
          currentContext.audience_reaction_to_change ?? "",
        recommended_platforms: currentContext.recommended_platforms ?? [],
        recommended_content_formats:
          currentContext.recommended_content_formats ?? [],
      });
    } else if (!currentContext) {
      setFormValues({});
    }
  }, [currentContext, isCreateWizardOpen]);

  // Handler for Accordion expand/collapse
  const handleAccordionChange =
    (section: SectionKey) =>
    (_: React.SyntheticEvent, isExpanded: boolean) => {
      setExpandedAccordion(isExpanded ? section : false);
    };

  // Toggle edit mode for a specific section
  const handleSectionEditToggle = (section: SectionKey | null) => {
    if (editingSection === section && section !== null) {
      // Canceling edit
      if (currentContext) {
        /* TODO: Reload initial values for section */
      }
      setEditingSection(null);
    } else if (section !== null) {
      // Entering edit
      if (currentContext) {
        /* Load current values if not already done */
      }
      setEditingSection(section);
      setExpandedAccordion(section);
    } else {
      // Explicit cancel
      if (currentContext) {
        /* Reload initial values */
      }
      setEditingSection(null);
    }
  };

  // Adjusted handleSubmit to save only the edited section's data?
  // OR keep submitting all data but reset edit state on success/cancel.
  // Current implementation submits all formValues.
  const handleSubmit = async () => {
    if (!session || !currentContextId) {
      setNotification({
        open: true,
        message: "Authentication error or no context selected.",
        severity: "error",
      });
      return;
    }
    if (
      !formValues.profile_overview?.trim() ||
      !formValues.target_audience?.trim()
    ) {
      setNotification({
        open: true,
        message: "Profile overview and target audience are required",
        severity: "error",
      });
      return;
    }

    setLoading(true);
    try {
      const payload: Partial<
        Omit<BusinessContext, "id" | "user_id" | "created_at" | "updated_at">
      > = { ...formValues };

      // Remove goals/metrics explicitly if they somehow linger in formValues
      delete (payload as any).goals;
      delete (payload as any).metrics;

      // Clean up potentially undefined/empty values before sending
      Object.keys(payload).forEach((key) => {
        const typedKey = key as keyof typeof payload;
        const value = payload[typedKey];
        if (value === undefined || value === "") {
          if (
            [
              "content_focus",
              "recommended_platforms",
              "recommended_content_formats",
            ].includes(typedKey)
          ) {
            (payload as any)[typedKey] = [];
          } else if (key !== "name") {
            (payload as any)[typedKey] = null;
          }
        }
      });

      if ("name" in formValues) {
        payload.name = formValues.name;
      }

      await api.put(`/business/${currentContextId}`, payload);

      setNotification({
        open: true,
        message: "Business profile updated successfully!",
        severity: "success",
      });
      setEditingSection(null);
      await fetchBusinessContexts(false);
    } catch (err: any) {
      console.error("Error updating business profile:", err);
      setNotification({
        open: true,
        message: getErrorMessage(err.response?.data?.error || err.message || "Failed to update business profile."),
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // Reset form and exit edit mode
  const handleCancelEdit = () => {
    if (currentContext) {
      /* Reload initial values */
    }
    setEditingSection(null);
  };

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setFormValues((prev) => ({ ...prev, [name]: value }));
  };

  const handleArrayChange = (
    name: keyof Pick<
      BusinessContext,
      "recommended_platforms" | "recommended_content_formats" | "content_focus"
    >,
    values: string[]
  ) => {
    setFormValues((prev) => ({ ...prev, [name]: values }));
  };

  const handleNotificationClose = () => {
    setNotification({ ...notification, open: false });
  };

  const handleContextSelect = (id: string | null) => {
    if (id !== currentContextId) {
      setCurrentContextId(id);
      setEditingSection(null); // Exit edit mode
      setIsCreateWizardOpen(false); // Ensure wizard is closed when selecting existing context
    }
  };

  const handleNewContextClick = () => {
    setCreateError(null);
    setEditingSection(null); // Ensure not in edit mode
    setExpandedAccordion(false); // Collapse accordions
    setIsCreateWizardOpen(true); // Open the wizard inline
  };

  // Updated handleCreateSubmit for the wizard data
  const handleCreateSubmit = async (profileData: ProfileWizardData) => {
    if (!session) throw new Error("Authentication required.");
    setCreateError(null);
    setLoading(true);

    try {
      const payload = { ...profileData };

      const response = await api.post("/business/", payload);
      const createdContext = response.data.context;

      setIsCreateWizardOpen(false);

      setNotification({
        open: true,
        message: `Profile "${
          createdContext.name || profileData.name
        }" created successfully!`,
        severity: "success",
      });

      await fetchBusinessContexts(false, createdContext.id);
    } catch (err: any) {
      console.error("Create profile error:", err);
      const errorMessage = getErrorMessage(err.response?.data?.error || err.message || "Failed to create profile.");
      setCreateError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleWizardClose = () => {
    setCreateError(null);
    setIsCreateWizardOpen(false);
  };

  // --- Delete Logic ---
  const handleDeleteContexts = async (ids: string[]) => {
    setLoading(true);
    try {
      await api.delete("/business/batch", { data: { ids } });

      setNotification({
        open: true,
        message: `${ids.length} context${
          ids.length > 1 ? "s" : ""
        } deleted successfully!`, // Dynamic message
        severity: "success",
      });

      const selectFirst = ids.includes(currentContextId || "");
      await fetchBusinessContexts(selectFirst);
    } catch (err: any) {
      console.error("Error deleting business contexts:", err);
      setNotification({
        open: true,
        message: getErrorMessage(err.response?.data?.error || err.message || "Failed to delete contexts."),
        severity: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  // This function is triggered by the sidebar and opens the confirmation dialog
  const handleAttemptDelete = (ids: string[]) => {
    if (ids.length === 0) return; // Don't open if no IDs
    setPendingDeleteIds(ids);
    setOpenConfirm(true);
  };

  // Handler for confirming the delete action in the dialog
  const handleConfirmDelete = () => {
    handleDeleteContexts(pendingDeleteIds);
    setOpenConfirm(false);
    setPendingDeleteIds([]); // Clear pending IDs
  };

  // Handler for canceling the delete action in the dialog
  const handleCancelDelete = () => {
    setOpenConfirm(false);
    setPendingDeleteIds([]); // Clear pending IDs
  };

  // Handler for optimize button click
  const handleOptimizeClick = () => {
    setIsOptimizeModalOpen(true);
  };

  // State to track the analysis modal mode
  const [analysisModalMode, setAnalysisModalMode] = useState<'view' | 'generate'>('view');

  // Handler for analyze audience button click
  const handleAnalyzeAudienceClick = () => {
    setAnalysisModalMode('generate');
    setIsAnalysisModalOpen(true);
  };

  // No longer needed as we'll display analysis directly in the tab

  // Handler for tab change
  const handleTabChange = (_: React.SyntheticEvent, newValue: 'profile' | 'audience') => {
    setActiveTab(newValue);

    // Check if analysis exists when switching to audience tab
    if (newValue === 'audience' && currentContextId && !checkingAnalysis && !hasAnalysis) {
      checkForExistingAnalysis(currentContextId);
    }
  };

  // Helper function to parse JSON from markdown code blocks
  const parseJsonFromMarkdown = (text: string): any => {
    try {
      // Check if the text is wrapped in markdown code blocks
      const jsonRegex = /```(?:json)?\n([\s\S]*?)\n```/;
      const match = text.match(jsonRegex);

      if (match && match[1]) {
        // Extract the JSON content from the code block
        return JSON.parse(match[1]);
      }

      // If not in code blocks, try parsing directly
      return JSON.parse(text);
    } catch (error) {
      console.error('Error parsing JSON from markdown:', error);
      throw new Error('Failed to parse analysis result');
    }
  };

  // Check if analysis exists for the current context and load it if available
  const checkForExistingAnalysis = async (contextId: string) => {
    if (!contextId || checkingAnalysis) return;

    setCheckingAnalysis(true);
    try {
      const response = await api.get(`/business/${contextId}/audience-analysis`);

      // Handle potential markdown-wrapped JSON
      let analysisData = response.data?.analysis;

      // If the server returned an error with raw_result, try to parse it
      if (!analysisData && response.data?.error === 'Failed to parse audience analysis result' && response.data?.raw_result) {
        try {
          analysisData = parseJsonFromMarkdown(response.data.raw_result);
          console.log('Successfully parsed analysis from raw_result:', analysisData);
        } catch (parseError) {
          console.error('Failed to parse raw_result:', parseError);
          throw new Error('Failed to parse analysis result');
        }
      }

      if (analysisData) {
        setHasAnalysis(true);
        setAnalysisContent(analysisData);
      } else {
        setHasAnalysis(false);
        setAnalysisContent(null);
      }
    } catch (err) {
      // 404 is expected if no analysis exists
      console.error('Error checking for analysis:', err);
      setHasAnalysis(false);
      setAnalysisContent(null);
    } finally {
      setCheckingAnalysis(false);
    }
  };

  // Handler for applying optimized changes
  const handleApplyOptimizedChanges = async (updatedFields: Record<string, any>) => {
    if (!session || !currentContextId) {
      setNotification({
        open: true,
        message: "Authentication error or no context selected.",
        severity: "error",
      });
      return;
    }

    setLoading(true);
    try {
      await api.put(`/business/${currentContextId}`, updatedFields);

      setNotification({
        open: true,
        message: "Business profile updated with optimized content!",
        severity: "success",
      });

      await fetchBusinessContexts(false);
    } catch (err: any) {
      console.error("Error updating business profile with optimized content:", err);
      setNotification({
        open: true,
        message: getErrorMessage(err.response?.data?.error || err.message || "Failed to update business profile."),
        severity: "error",
      });
      throw err; // Re-throw to be caught by the modal
    } finally {
      setLoading(false);
    }
  };

  // Render helper functions remain largely the same, but ensure they check editingSection
  const renderTextField = (
    name:
      | keyof Omit<
          BusinessContext,
          | "id"
          | "user_id"
          | "created_at"
          | "updated_at"
          | "recommended_platforms"
          | "recommended_content_formats"
          | "content_focus"
        >
      | "name",
    label: string,
    required = false,
    multiline = false,
    rows = 1
  ) => {
    const value =
      (formValues[name as keyof typeof formValues] as string | undefined) ?? "";
    const isStringField = typeof value === "string";
    const hasError = !!(
      editingSection &&
      required &&
      isStringField &&
      !value.trim()
    );

    // Safer check for section key derivation
    let fieldSection: SectionKey | null = null;
    if (
      [
        "profile_overview",
        "primary_objectives",
        "unique_value_proposition",
        "offer_description",
        "name",
      ].includes(name)
    )
      fieldSection = "core";
    else if (["target_audience", "audience_motivation"].includes(name))
      fieldSection = "audience";
    else if (["brand_voice", "key_benefits"].includes(name))
      fieldSection = "strategy";
    else if (
      [
        "audience_decision_style",
        "audience_preference_structure",
        "audience_decision_speed",
        "audience_reaction_to_change",
      ].includes(name)
    )
      fieldSection = "decision";

    const isCurrentlyEditingThisSection =
      editingSection === fieldSection && fieldSection !== null;
    const isDisabledCalc = !editingSection || !isCurrentlyEditingThisSection;
    const isReadOnlyCalc = isDisabledCalc;

    return (
      <TextField
        key={`${name}-${editingSection}-${currentContext?.id}`}
        label={label}
        name={name}
        value={value}
        onChange={handleInputChange}
        fullWidth
        required={required}
        multiline={multiline}
        rows={rows}
        disabled={false}
        variant={isCurrentlyEditingThisSection ? "outlined" : "filled"}
        placeholder={
          isCurrentlyEditingThisSection ? placeholders[name] || "" : ""
        }
        InputProps={{
          readOnly: Boolean(isReadOnlyCalc),
          sx: {
            color: theme.palette.text.primary,
            opacity: 1,
            backgroundColor: !isCurrentlyEditingThisSection
              ? "transparent"
              : theme.palette.background.paper,
            "&.MuiInputBase-inputMultiline": {
              paddingTop: !isCurrentlyEditingThisSection ? "10px" : undefined,
              paddingBottom: !isCurrentlyEditingThisSection
                ? "10px"
                : undefined,
            },
            "&.MuiFilledInput-input:not(textarea)": {
              paddingTop: "20px",
              paddingBottom: "6px",
            },
          },
        }}
        error={hasError}
        helperText={
          hasError
            ? "Required"
            : isCurrentlyEditingThisSection
            ? helperTexts[name] || ""
            : ""
        }
        sx={{ mb: editingSection ? 2 : 1 }}
      />
    );
  };

  // Updated renderArrayField to use TagInput
  const renderArrayField = (
    name: keyof Pick<
      BusinessContext,
      "recommended_platforms" | "recommended_content_formats" | "content_focus"
    >,
    title: string,
    section: SectionKey,
    suggestions: string[] = [],
    placeholder: string = "",
    helperText: string = ""
  ) => {
    const isEditingThisSection = editingSection === section;
    const currentValues =
      (currentContext?.[name] as string[] | undefined) || [];
    const formInputValues = (formValues[name] as string[] | undefined) || [];

    return (
      <Box sx={{ mb: 2 }}>
        <Typography
          variant="subtitle2"
          gutterBottom
          sx={{ color: "text.secondary" }}
        >
          {title}
        </Typography>
        {isEditingThisSection ? (
          <TagInput
            label={title}
            value={formInputValues}
            onChange={(newValue) => handleArrayChange(name, newValue)}
            placeholder={
              placeholder || `Add ${title.replace("Recommended ", "")}`
            }
            suggestions={suggestions}
            disabled={!isEditingThisSection}
          />
        ) : (
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5, mt: 1 }}>
            {currentValues.map((item: string, index: number) => (
              <Chip
                key={`${item}-${index}-view`}
                label={item}
                size="small"
                variant="outlined"
              />
            ))}
            {!currentValues.length && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ fontStyle: "italic" }}
              >
                None
              </Typography>
            )}
          </Box>
        )}
        {isEditingThisSection && helperText && (
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ mt: 0.5, display: "block" }}
          >
            {helperText}
          </Typography>
        )}
      </Box>
    );
  };

  // Define placeholders and helper texts
  const placeholders: Partial<Record<keyof BusinessContext | "name", string>> =
    {
      name: "e.g., My Main Product Line",
      profile_overview: "Briefly describe this business context.",
      primary_objectives:
        "What are the main goals for content in this context?",
      offer_description: "Describe the product/service offered.",
      target_audience: "Who are you trying to reach?",
      audience_motivation: "What drives their decisions or interest?",
      brand_voice: "e.g., Energetic, Formal, Humorous, Technical",
      key_benefits: "List the main advantages or selling points.",
      unique_value_proposition: "What makes this offer unique?",
      audience_decision_style: "e.g., Analytical, Emotional, Price-sensitive",
      audience_preference_structure:
        "e.g., Prefers lists, Prefers stories, Needs data",
      audience_decision_speed: "e.g., Quick/Impulsive, Slow/Deliberate",
      audience_reaction_to_change: "e.g., Early adopter, Skeptical, Resistant",
    };

  const helperTexts: Partial<Record<keyof BusinessContext | "name", string>> = {
    profile_overview:
      "A concise summary helps the AI understand the core focus.",
    content_focus: "Keywords or topics central to this context.",
    primary_objectives: "E.g., Lead generation, Brand awareness, Education.",
    offer_description:
      "Details about the product or service relevant to content creation.",
    target_audience: "Describe demographics, psychographics, pain points.",
    audience_motivation:
      "Understanding what makes the audience tick helps tailor content.",
    brand_voice: "Define the tone and style of communication.",
    key_benefits: "Focus on the value provided to the audience.",
    unique_value_proposition: "Your unique selling point.",
    audience_decision_style:
      "How does the audience typically make decisions related to your offer?",
    audience_preference_structure: "How do they prefer information presented?",
    audience_decision_speed:
      "How quickly do they move from awareness to decision?",
    audience_reaction_to_change:
      "How open are they to new ideas or approaches?",
    recommended_platforms:
      "Where does your target audience spend time? (Press Enter after each)",
    recommended_content_formats:
      "What types of content resonate best? (Press Enter after each)",
  };

  const sidebarContexts = contexts.map((c: BusinessContext) => ({
    id: c.id,
    // Use profile_overview for display name if name is missing?
    name:
      c.name ||
      c.profile_overview?.substring(0, 40) +
        (c.profile_overview && c.profile_overview.length > 40 ? "..." : "") ||
      `Profile ${c.id.substring(0, 6)}`,
  }));

  if (authLoading || (!isInitialFetchDone && loading)) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "calc(100vh - 64px)",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ display: "flex", height: "calc(100vh - 64px)" }}>
      <BusinessContextSidebar
        contexts={sidebarContexts}
        currentContextId={currentContextId}
        onContextSelect={handleContextSelect}
        onNewContextClick={handleNewContextClick}
        onDeleteContexts={handleAttemptDelete}
        loading={loading && !isInitialFetchDone}
        error={error && !isInitialFetchDone ? "Failed to load" : null}
      />
      <Container
        component="main"
        maxWidth="lg"
        sx={{ flexGrow: 1, p: 3, position: "relative", overflowY: "auto" }}
      >
        {loading && isInitialFetchDone && (
          <CircularProgress sx={{ display: "block", margin: "auto" }} />
        )}
        {!loading && error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {!loading &&
          !error &&
          isInitialFetchDone &&
          contexts.length === 0 &&
          !isCreateWizardOpen && (
            <Typography sx={{ textAlign: "center", mt: 4 }}>
              No contexts found. Create one!
            </Typography>
          )}
        {!loading &&
          !error &&
          isInitialFetchDone &&
          contexts.length > 0 &&
          !currentContext &&
          !isCreateWizardOpen && (
            <Typography sx={{ textAlign: "center", mt: 4 }}>
              Select a context.
            </Typography>
          )}

        {!loading && !error && currentContext && !isCreateWizardOpen && (
          <Box
            component="header"
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              justifyContent: "space-between",
              alignItems: { xs: "flex-start", sm: "center" },
              mb: 4,
              gap: 2,
              borderBottom: `1px solid ${theme.palette.divider}`,
              pb: 2,
            }}
          >
            <Box>
              <Typography variant="h4" color="text.primary" gutterBottom>
                {currentContext.name || "Unnamed Profile"}
              </Typography>
              {currentContext.profile_overview && (
                <Typography variant="subtitle1" color="text.secondary">
                  {currentContext.profile_overview}
                </Typography>
              )}
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ display: "block", mt: 0.5 }}
              >
                Review or edit the profile details in the sections below.
              </Typography>
            </Box>
            <Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="outlined"
                  color="primary"
                  startIcon={<OptimizeIcon />}
                  onClick={handleOptimizeClick}
                  disabled={loading || editingSection !== null}
                  sx={{ mt: { xs: 1, sm: 0 } }}
                >
                  Optimize with AI
                </Button>
              </Box>
            </Box>
          </Box>
        )}

        {isCreateWizardOpen ? (
          <>
            {createError && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {createError}
              </Alert>
            )}
            <CreateProfileWizard
              onClose={handleWizardClose}
              onSubmit={handleCreateSubmit}
            />
          </>
        ) : (
          !loading &&
          currentContext && (
            <motion.div
              key={currentContext.id}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              {/* Tabs for Profile and Audience Analysis */}
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
                <Tabs
                  value={activeTab}
                  onChange={handleTabChange}
                  aria-label="profile tabs"
                >
                  <Tab label="Profile" value="profile" />
                  <Tab
                    label="Audience Analysis"
                    value="audience"
                    icon={<PsychologyIcon sx={{ fontSize: '1rem' }} />}
                    iconPosition="start"
                  />
                </Tabs>
              </Box>

              {/* Profile Tab Content */}
              {activeTab === 'profile' && (
                <Box sx={{ mb: editingSection ? 10 : 3 }}>

                <Accordion
                  expanded={expandedAccordion === "core"}
                  onChange={handleAccordionChange("core")}
                  disableGutters
                  elevation={editingSection === "core" ? 8 : 1}
                  sx={{
                    mb: 3,
                    border:
                      editingSection === "core"
                        ? `1px solid ${theme.palette.primary.main}`
                        : "none",
                    transition:
                      "elevation 0.3s ease, border 0.3s ease, margin-bottom 0.3s ease",
                    overflow: "hidden",
                    backgroundColor: theme.palette.background.paper,
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="core-content"
                    id="core-header"
                    sx={{
                      ".MuiAccordionSummary-content": {
                        justifyContent: "space-between",
                        alignItems: "center",
                      },
                    }}
                  >
                    <Typography sx={{ width: "33%", flexShrink: 0 }}>
                      Core Profile
                    </Typography>
                    <Typography sx={{ color: "text.secondary" }}>
                      Overview & Offer
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent accordion toggle
                        handleSectionEditToggle("core");
                      }}
                      sx={{ ml: "auto" }}
                      aria-label="Edit Section"
                    >
                      {editingSection === "core" ? (
                        <CancelIcon />
                      ) : (
                        <EditIcon />
                      )}
                    </IconButton>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        {renderTextField(
                          "name",
                          "Profile Name (Internal)",
                          true
                        )}
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        {renderTextField(
                          "profile_overview",
                          "Profile Overview",
                          false,
                          true,
                          3
                        )}
                      </Grid>
                      <Grid item xs={12}>
                        {renderTextField(
                          "offer_description",
                          "Offer Description",
                          true,
                          true,
                          4
                        )}
                      </Grid>
                      <Grid item xs={12}>
                        {renderTextField(
                          "primary_objectives",
                          "Primary Objectives",
                          false,
                          true,
                          2
                        )}
                      </Grid>
                      <Grid item xs={12}>
                        {renderArrayField(
                          "content_focus",
                          "Content Focus",
                          "core",
                          [], // Add suggestions if needed
                          "e.g., AI, Marketing Tech, SaaS",
                          helperTexts.content_focus
                        )}
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>

                <Accordion
                  expanded={expandedAccordion === "audience"}
                  onChange={handleAccordionChange("audience")}
                  disableGutters
                  elevation={editingSection === "audience" ? 8 : 1}
                  sx={{
                    mb: 3,
                    border:
                      editingSection === "audience"
                        ? `1px solid ${theme.palette.primary.main}`
                        : "none",
                    transition:
                      "elevation 0.3s ease, border 0.3s ease, margin-bottom 0.3s ease",
                    overflow: "hidden",
                    backgroundColor: theme.palette.background.paper,
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="audience-content"
                    id="audience-header"
                    sx={{
                      ".MuiAccordionSummary-content": {
                        justifyContent: "space-between",
                        alignItems: "center",
                      },
                    }}
                  >
                    <Typography sx={{ width: "33%", flexShrink: 0 }}>
                      Audience
                    </Typography>
                    <Typography sx={{ color: "text.secondary" }}>
                      Target & Motivation
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSectionEditToggle("audience");
                      }}
                      sx={{ ml: "auto" }}
                      aria-label="Edit Section"
                    >
                      {editingSection === "audience" ? (
                        <CancelIcon />
                      ) : (
                        <EditIcon />
                      )}
                    </IconButton>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        {renderTextField(
                          "target_audience",
                          "Target Audience",
                          true,
                          true,
                          4
                        )}
                      </Grid>
                      <Grid item xs={12}>
                        {renderTextField(
                          "audience_motivation",
                          "Audience Motivation",
                          false,
                          true,
                          3
                        )}
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>

                <Accordion
                  expanded={expandedAccordion === "strategy"}
                  onChange={handleAccordionChange("strategy")}
                  disableGutters
                  elevation={editingSection === "strategy" ? 8 : 1}
                  sx={{
                    mb: 3,
                    border:
                      editingSection === "strategy"
                        ? `1px solid ${theme.palette.primary.main}`
                        : "none",
                    transition:
                      "elevation 0.3s ease, border 0.3s ease, margin-bottom 0.3s ease",
                    overflow: "hidden",
                    backgroundColor: theme.palette.background.paper,
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="strategy-content"
                    id="strategy-header"
                    sx={{
                      ".MuiAccordionSummary-content": {
                        justifyContent: "space-between",
                        alignItems: "center",
                      },
                    }}
                  >
                    <Typography sx={{ width: "33%", flexShrink: 0 }}>
                      Strategy
                    </Typography>
                    <Typography sx={{ color: "text.secondary" }}>
                      Voice, Benefits & UVP
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSectionEditToggle("strategy");
                      }}
                      sx={{ ml: "auto" }}
                      aria-label="Edit Section"
                    >
                      {editingSection === "strategy" ? (
                        <CancelIcon />
                      ) : (
                        <EditIcon />
                      )}
                    </IconButton>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        {renderTextField("brand_voice", "Brand Voice")}
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        {renderTextField(
                          "unique_value_proposition",
                          "Unique Value Proposition",
                          false,
                          true,
                          3
                        )}
                      </Grid>
                      <Grid item xs={12}>
                        {renderTextField(
                          "key_benefits",
                          "Key Benefits",
                          false,
                          true,
                          3
                        )}
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>

                <Accordion
                  expanded={expandedAccordion === "decision"}
                  onChange={handleAccordionChange("decision")}
                  disableGutters
                  elevation={editingSection === "decision" ? 8 : 1}
                  sx={{
                    mb: 3,
                    border:
                      editingSection === "decision"
                        ? `1px solid ${theme.palette.primary.main}`
                        : "none",
                    transition:
                      "elevation 0.3s ease, border 0.3s ease, margin-bottom 0.3s ease",
                    overflow: "hidden",
                    backgroundColor: theme.palette.background.paper,
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="decision-content"
                    id="decision-header"
                    sx={{
                      ".MuiAccordionSummary-content": {
                        justifyContent: "space-between",
                        alignItems: "center",
                      },
                    }}
                  >
                    <Typography sx={{ width: "33%", flexShrink: 0 }}>
                      Decision Factors
                    </Typography>
                    <Typography sx={{ color: "text.secondary" }}>
                      Audience Decision Making
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSectionEditToggle("decision");
                      }}
                      sx={{ ml: "auto" }}
                      aria-label="Edit Section"
                    >
                      {editingSection === "decision" ? (
                        <CancelIcon />
                      ) : (
                        <EditIcon />
                      )}
                    </IconButton>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        {renderTextField(
                          "audience_decision_style",
                          "Audience Decision Style"
                        )}
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        {renderTextField(
                          "audience_preference_structure",
                          "Audience Preference Structure"
                        )}
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        {renderTextField(
                          "audience_decision_speed",
                          "Audience Decision Speed"
                        )}
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        {renderTextField(
                          "audience_reaction_to_change",
                          "Audience Reaction to Change"
                        )}
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>

                <Accordion
                  expanded={expandedAccordion === "recommendations"}
                  onChange={handleAccordionChange("recommendations")}
                  disableGutters
                  elevation={editingSection === "recommendations" ? 8 : 1}
                  sx={{
                    mb: 3,
                    border:
                      editingSection === "recommendations"
                        ? `1px solid ${theme.palette.primary.main}`
                        : "none",
                    transition: "elevation 0.3s ease, border 0.3s ease",
                    overflow: "hidden",
                    backgroundColor: theme.palette.background.paper,
                  }}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="recommendations-content"
                    id="recommendations-header"
                    sx={{
                      ".MuiAccordionSummary-content": {
                        justifyContent: "space-between",
                        alignItems: "center",
                      },
                    }}
                  >
                    <Typography sx={{ width: "33%", flexShrink: 0 }}>
                      Recommendations
                    </Typography>
                    <Typography sx={{ color: "text.secondary" }}>
                      Platforms & Formats
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSectionEditToggle("recommendations");
                      }}
                      sx={{ ml: "auto" }}
                      aria-label="Edit Section"
                    >
                      {editingSection === "recommendations" ? (
                        <CancelIcon />
                      ) : (
                        <EditIcon />
                      )}
                    </IconButton>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        {renderArrayField(
                          "recommended_platforms",
                          "Recommended Platforms",
                          "recommendations",
                          ["Blog", "LinkedIn", "Twitter", "YouTube", "Podcast"],
                          "e.g., LinkedIn, Blog",
                          helperTexts.recommended_platforms
                        )}
                      </Grid>
                      <Grid item xs={12}>
                        {renderArrayField(
                          "recommended_content_formats",
                          "Recommended Content Formats",
                          "recommendations",
                          [
                            "How-To Guide",
                            "Case Study",
                            "Listicle",
                            "Interview",
                            "Infographic",
                            "Webinar",
                          ],
                          "e.g., How-To Guide, Case Study",
                          helperTexts.recommended_content_formats
                        )}
                      </Grid>
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              </Box>
                )}

                {/* Audience Analysis Tab Content */}
                {activeTab === 'audience' && (
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="h6">Audience Analysis</Typography>
                        {hasAnalysis && (
                          <Chip
                            label="Analysis Available"
                            color="success"
                            size="small"
                            sx={{ ml: 2 }}
                            icon={<CheckCircleOutlineIcon />}
                          />
                        )}
                        {checkingAnalysis && (
                          <CircularProgress size={20} sx={{ ml: 2 }} />
                        )}
                      </Box>
                      <Box>
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<PsychologyIcon />}
                          onClick={handleAnalyzeAudienceClick}
                          disabled={loading || checkingAnalysis}
                        >
                          {hasAnalysis ? 'Refresh Analysis' : 'Analyze Audience'}
                        </Button>
                      </Box>
                    </Box>

                    <Paper sx={{ p: 3, mb: 3 }}>
                      <Typography variant="body1" paragraph>
                        The audience analysis feature analyzes your audience's psychological drivers and emotional states.
                      </Typography>
                      <Typography variant="body1" paragraph>
                        This analysis helps you create more effective content by understanding your audience's motivation direction, preference structure, reference point, thinking style, and emotional state.
                      </Typography>
                      <Typography variant="body1" paragraph>
                        {hasAnalysis
                          ? 'You already have an audience analysis saved. Click "Refresh Analysis" to generate a new analysis.'
                          : 'Click the "Analyze Audience" button to generate a new audience analysis.'}
                      </Typography>

                      {hasAnalysis && analysisContent && (
                        <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>
                          <Typography variant="h6" gutterBottom>Analysis Results</Typography>
                          <Typography variant="body2" paragraph>
                            {analysisContent.summary}
                          </Typography>

                          {/* Display key analysis sections */}
                          {analysisContent.emotional_state && (
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="subtitle1" fontWeight="bold">Emotional State</Typography>
                              <Typography variant="body2" paragraph>
                                Dominant state: {analysisContent.emotional_state.dominant_state}
                              </Typography>
                              <Typography variant="body2" paragraph>
                                {analysisContent.emotional_state.content_approach}
                              </Typography>
                            </Box>
                          )}

                          {analysisContent.content_recommendations && (
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="subtitle1" fontWeight="bold">Content Recommendations</Typography>
                              <Typography variant="body2" paragraph>
                                {analysisContent.content_recommendations.messaging_approach}
                              </Typography>
                            </Box>
                          )}

                          <Button
                            variant="text"
                            color="primary"
                            onClick={() => {
                              setAnalysisModalMode('view');
                              setIsAnalysisModalOpen(true);
                            }}
                            sx={{ mt: 2 }}
                          >
                            View Full Analysis
                          </Button>
                        </Box>
                      )}
                    </Paper>
                  </Box>
                )}

              {editingSection && (
                <Paper
                  elevation={8}
                  sx={{
                    position: "sticky",
                    bottom: 0,
                    left: 0,
                    right: 0,
                    p: 2,
                    zIndex: 1200,
                    bgcolor: theme.palette.background.paper,
                    borderTop: `1px solid ${theme.palette.divider}`,
                    boxShadow: theme.shadows[8],
                  }}
                >
                  <Box
                    sx={{ display: "flex", justifyContent: "flex-end", gap: 1 }}
                  >
                    <Button
                      onClick={handleCancelEdit}
                      variant="outlined"
                      startIcon={<CancelIcon />}
                      size="small"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSubmit}
                      variant="contained"
                      color="primary"
                      disabled={loading}
                      startIcon={
                        loading ? (
                          <CircularProgress size={20} color="inherit" />
                        ) : (
                          <SaveIcon />
                        )
                      }
                      size="small"
                    >
                      Save Changes
                    </Button>
                  </Box>
                </Paper>
              )}
            </motion.div>
          )
        )}

        <ConfirmationDialog
          open={openConfirm}
          title="Confirm Deletion"
          content={`Are you sure you want to delete ${
            pendingDeleteIds.length
          } context${
            pendingDeleteIds.length !== 1 ? "s" : ""
          }? This action cannot be undone.`}
          onConfirm={handleConfirmDelete}
          onCancel={handleCancelDelete}
        />

        <OptimizeProfileModal
          open={isOptimizeModalOpen}
          onClose={() => setIsOptimizeModalOpen(false)}
          businessContext={currentContext}
          onApplyChanges={handleApplyOptimizedChanges}
        />

        <AudienceAnalysisModal
          open={isAnalysisModalOpen}
          onClose={() => {
            setIsAnalysisModalOpen(false);
            // Refresh analysis status after modal closes
            if (currentContextId) {
              checkForExistingAnalysis(currentContextId);
            }
          }}
          businessContext={currentContext}
          mode={analysisModalMode}
        />

        <Snackbar
          open={notification.open}
          autoHideDuration={4000}
          onClose={handleNotificationClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={handleNotificationClose}
            severity={notification.severity}
            sx={{ width: "100%" }}
            variant="filled"
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </Container>
    </Box>
  );
};

// Wrap the component with AuthErrorBoundary
const BusinessContextPageWithErrorBoundary: React.FC = () => {
  return (
    <AuthErrorBoundary onRetry={() => window.location.reload()}>
      <BusinessContextPage />
    </AuthErrorBoundary>
  );
};

export default BusinessContextPageWithErrorBoundary;
