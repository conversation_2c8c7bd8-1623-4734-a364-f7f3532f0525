"""
Supabase repository management for FastAPI - migrated from Flask hooks.
This module provides lifespan events and utilities for managing Supabase repositories.
"""

import logging

logger = logging.getLogger(__name__)


def init_supabase_repositories_on_startup():
    """
    Initialize Supabase repositories on application startup.
    This replaces the Flask before_request hook.
    In FastAPI, repositories are created via dependency injection per request.
    """
    logger.debug("Supabase repositories will be initialized via dependency injection")
    logger.info("FastAPI Supabase repository system ready")


def cleanup_supabase_repositories_on_shutdown():
    """
    Cleanup Supabase repositories on application shutdown.
    This replaces the Flask teardown hooks.
    """
    logger.debug("Cleaning up Supabase repository resources")
    logger.info("FastAPI Supabase repository system shutdown complete")


# Legacy Flask functions - kept for backward compatibility during migration
def init_supabase_repositories():
    """
    Legacy Flask function - no longer needed in FastAPI.
    Repositories are now created via dependency injection.
    """
    logger.debug("init_supabase_repositories called (legacy function, no-op in FastAPI)")


def register_supabase_hooks(app):
    """
    Legacy Flask function - no longer needed in FastAPI.
    Repository management is now handled by FastAPI lifespan events.
    """
    logger.debug("register_supabase_hooks called (legacy function, no-op in FastAPI)")
