import React, { createContext, useReducer, useEffect } from 'react';
import { WizardState, WizardAction, wizardReducer, initialWizardState, saveWizardProgress } from '../types/wizard';

interface WizardContextValue {
  state: WizardState;
  dispatch: React.Dispatch<WizardAction>;
}

export const WizardContext = createContext<WizardContextValue | undefined>(undefined);

export const WizardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(wizardReducer, initialWizardState);

  // Add auto-save effect
  useEffect(() => {
    if (state.draftText) {
      const timeoutId = setTimeout(() => {
        saveWizardProgress(state);
      }, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [state.draftText]);

  return (
    <WizardContext.Provider value={{ state, dispatch }}>
      {children}
    </WizardContext.Provider>
  );
};