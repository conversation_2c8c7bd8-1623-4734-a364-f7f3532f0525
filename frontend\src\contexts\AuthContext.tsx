import React, { createContext, useContext, useEffect, useState } from "react";
import { Session, User, AuthChangeEvent } from "@supabase/supabase-js";
import { supabase } from "../lib/supabase";
import { useDispatch } from "react-redux";
import { setSupabaseSession } from "../store/auth/authSlice";
import { setAuthContextUtils } from "../services/api";
import {
  fallbackAuth,
  shouldUseFallback,
  storeMockSession,
  retrieveMockSession,
  clearMockSession
} from "../utils/authFallback";

// Types
type AuthContextType = {
  session: Session | null;
  user: User | null;
  loading: boolean;
  isRefreshing: boolean;
  error: string | null;
  signIn: (
    email: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>;
  signUp: (
    email: string,
    password: string,
    userData: any
  ) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  resetPassword: (
    email: string
  ) => Promise<{ success: boolean; error?: string }>;
  updateUserPassword: (password: string) => Promise<{ success: boolean; error?: string }>;
  updateProfile: (data: any) => Promise<{ success: boolean; error?: string }>;
  resendConfirmationEmail: () => Promise<{ success: boolean; error?: string }>;
  refreshSession: () => Promise<Session | null>;
};

// Create context with default value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dispatch = useDispatch();

  // Helper function to validate session
  const isSessionValid = (sessionToCheck: Session | null): boolean => {
    if (!sessionToCheck?.access_token) {
      console.log('❌ Session validation failed: No access token');
      return false;
    }

    // Check if token is expired (with 1 minute buffer)
    const now = Math.floor(Date.now() / 1000);
    const tokenExpiry = sessionToCheck.expires_at;
    const bufferTime = 60; // 1 minute in seconds

    // Debug logging
    console.log('🔍 Session validation check:', {
      now,
      tokenExpiry,
      bufferTime,
      hasExpiry: !!tokenExpiry,
      timeUntilExpiry: tokenExpiry ? tokenExpiry - now : 'N/A',
      isWithinBuffer: tokenExpiry ? (now + bufferTime) >= tokenExpiry : 'N/A'
    });

    // If no expiry time, the session is invalid (all Supabase JWTs should have expiry)
    if (!tokenExpiry || tokenExpiry === 0) {
      console.log('❌ Session validation failed: No valid expiry time');
      return false;
    }

    // Check if token is expired or within buffer time of expiring
    const isValid = (now + bufferTime) < tokenExpiry;
    console.log(`${isValid ? '✅' : '❌'} Session validation result: ${isValid ? 'VALID' : 'INVALID'}`);

    return isValid;
  };

  // Set up integration with api.ts (avoiding circular imports)
  useEffect(() => {
    setAuthContextUtils({
      getCurrentSession: () => {
        // Return session only if it's valid
        return isSessionValid(session) ? session : null;
      },
      refreshSession: refreshSession,
      signOut: signOut,
    });
  }, [session]); // Re-register when session changes

  // Initialize session on load
  useEffect(() => {
    // Get initial session
    const initSession = async () => {
      setLoading(true);
      try {
        // Get current session from Supabase
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          console.error("Error getting session:", error.message);

          // Try fallback session if Supabase fails
          if (shouldUseFallback()) {
            const mockSession = retrieveMockSession();
            if (mockSession) {
              console.log("[AUTH] Using stored mock session due to Supabase error");
              setSession(mockSession);
              setUser(mockSession.user);
              dispatch(setSupabaseSession(mockSession));
            } else {
              setError(error.message);
            }
          } else {
            setError(error.message);
          }
        } else {
          setSession(session);
          if (session?.user) {
            setUser(session.user);
          }
          // Sync with Redux store
          dispatch(setSupabaseSession(session));
        }
      } catch (err: any) {
        console.error("Session init error:", err.message);

        // Try fallback session if Supabase fails
        if (shouldUseFallback()) {
          const mockSession = retrieveMockSession();
          if (mockSession) {
            console.log("[AUTH] Using stored mock session due to Supabase exception");
            setSession(mockSession);
            setUser(mockSession.user);
            dispatch(setSupabaseSession(mockSession));
          } else {
            setError(err.message);
          }
        } else {
          setError(err.message);
        }
      } finally {
        setLoading(false);
      }
    };

    initSession();

    // Subscribe to auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(
      (event: AuthChangeEvent, session: Session | null) => {
        console.log("Supabase auth state changed:", event, !!session);
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        // Log details about the session to help with debugging
        if (session) {
          console.log("New session established, user:", session.user.email);
        } else {
          console.log("Session cleared");
        }

        // Sync with Redux store
        dispatch(setSupabaseSession(session));
      }
    );

    // Cleanup on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, [dispatch]);

  // Function to manually refresh the session, with protection against concurrent refreshes
  const refreshSession = async (): Promise<Session | null> => {
    if (isRefreshing) {
      console.log("Session refresh already in progress.");
      return session;
    }

    setIsRefreshing(true);
    setError(null);
    console.log("Attempting to refresh session...");

    try {
      // First check if we have a current session with refresh token
      const currentSession = session || (await supabase.auth.getSession()).data.session;

      if (!currentSession?.refresh_token) {
        console.warn("No refresh token available for session refresh");
        setError("No refresh token available");
        return null;
      }

      const { data, error } = await supabase.auth.refreshSession(currentSession);

      if (error) {
        console.error("Session refresh failed:", error.message);
        setError(error.message);

        // Handle specific error cases
        if (error.message.includes('refresh_token_not_found') ||
            error.message.includes('invalid_grant') ||
            error.message.includes('refresh token is invalid')) {
          console.log("Refresh token is invalid or expired, signing out");
          await signOut();
          return null;
        }

        return null;
      }

      if (!data.session) {
        console.warn("Session refresh returned no session.");
        return null;
      }

      console.log("Session refreshed successfully.");
      // The onAuthStateChange listener will automatically update the session state
      return data.session;
    } catch (err: any) {
      const errorMessage = err.message || "An unexpected error occurred during session refresh.";
      console.error("Session refresh exception:", errorMessage);
      setError(errorMessage);

      // If it's a network error, don't sign out immediately
      if (err.message?.includes('fetch') || err.message?.includes('network')) {
        console.log("Network error during refresh, will retry later");
        return null;
      }

      // For other errors, consider signing out
      console.log("Critical error during refresh, signing out");
      await signOut();
      return null;
    } finally {
      setIsRefreshing(false);
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);

      console.log("[AUTH] Attempting sign in for:", email);

      // Test Supabase connectivity first
      try {
        const { data: healthCheck } = await supabase.from('users').select('id').limit(1);
        console.log("[AUTH] Supabase connectivity verified");
      } catch (connectError: any) {
        console.error("[AUTH] Supabase connectivity failed:", connectError);

        // Try fallback authentication if Supabase is not accessible
        if (shouldUseFallback()) {
          console.log("[AUTH] Attempting fallback authentication");
          const fallbackResult = await fallbackAuth(email, password);

          if (fallbackResult.success && fallbackResult.session) {
            setSession(fallbackResult.session);
            setUser(fallbackResult.session.user);
            storeMockSession(fallbackResult.session);
            dispatch(setSupabaseSession(fallbackResult.session));
            console.log("[AUTH] Fallback authentication successful");
            return { success: true };
          } else {
            setError(fallbackResult.error || "Authentication failed");
            return { success: false, error: fallbackResult.error || "Authentication failed" };
          }
        }

        const errorMsg = "Cannot connect to authentication service. Please check your internet connection and try again.";
        setError(errorMsg);
        return { success: false, error: errorMsg };
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.error("[AUTH] Sign in error:", error);
        let errorMessage = error.message;

        // Provide user-friendly error messages
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = "Invalid email or password. Please check your credentials and try again.";
        } else if (error.message.includes('Email not confirmed')) {
          errorMessage = "Please check your email and click the confirmation link before signing in.";
        } else if (error.message.includes('Too many requests')) {
          errorMessage = "Too many login attempts. Please wait a few minutes and try again.";
        }

        setError(errorMessage);
        return { success: false, error: errorMessage };
      }

      if (data.session) {
        console.log("[AUTH] Sign in successful, user:", data.user?.email);
        return { success: true };
      } else {
        const errorMsg = "Authentication failed - no session created";
        console.error("[AUTH]", errorMsg);
        setError(errorMsg);
        return { success: false, error: errorMsg };
      }
    } catch (err: any) {
      console.error("[AUTH] Sign in exception:", err);
      let errorMessage = "Authentication service unavailable";

      if (err.message?.includes('Failed to fetch')) {
        errorMessage = "Cannot connect to authentication service. Please check your internet connection and try again.";
      } else if (err.message?.includes('NetworkError')) {
        errorMessage = "Network error. Please check your internet connection and try again.";
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Sign up with email and password
  const signUp = async (email: string, password: string, userData: any) => {
    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      });

      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      // Check if email confirmation is required
      if (data.user && !data.user.email_confirmed_at) {
        return {
          success: true,
          error: "Please check your email for confirmation",
        };
      }

      return { success: true };
    } catch (err: any) {
      const errorMessage = err.message || "Sign up failed";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);

      // Clear mock session if using fallback
      if (shouldUseFallback()) {
        clearMockSession();
      }

      const { error } = await supabase.auth.signOut();

      if (error && !shouldUseFallback()) {
        setError(error.message);
      }

      // Clear local state regardless of Supabase result
      setSession(null);
      setUser(null);
      dispatch(setSupabaseSession(null));

    } catch (err: any) {
      // Clear local state even if signOut fails
      setSession(null);
      setUser(null);
      dispatch(setSupabaseSession(null));

      if (shouldUseFallback()) {
        clearMockSession();
      } else {
        setError(err.message || "Sign out failed");
      }
    } finally {
      setLoading(false);
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.auth.resetPasswordForEmail(email);

      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (err: any) {
      const errorMessage = err.message || "Password reset failed";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Update user password (when already logged in)
  const updateUserPassword = async (password: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.auth.updateUser({ password });

      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (err: any) {
      const errorMessage = err.message || "Password update failed";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Resend confirmation email
  const resendConfirmationEmail = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Note: resend requires the user's email. We assume the user is available from the context.
      if (!user?.email) {
          const message = "User email not available to resend confirmation.";
          setError(message);
          return { success: false, error: message };
      }

      const { error } = await supabase.auth.resend({
          type: 'signup', // or 'email_change'
          email: user.email
      });

      if (error) {
          setError(error.message);
          return { success: false, error: error.message };
      }

      return { success: true };
    } catch (err: any) {
        const errorMessage = err.message || "Failed to resend confirmation email.";
        setError(errorMessage);
        return { success: false, error: errorMessage };
    } finally {
        setLoading(false);
    }
  }

  // Update user profile
  const updateProfile = async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.auth.updateUser({
        data,
      });

      if (error) {
        setError(error.message);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (err: any) {
      const errorMessage = err.message || "Profile update failed";
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  // Context value
  const value = {
    session,
    user,
    loading,
    isRefreshing,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateUserPassword,
    updateProfile,
    resendConfirmationEmail,
    refreshSession,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export default AuthProvider;
