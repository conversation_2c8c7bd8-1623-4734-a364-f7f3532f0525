import json
import pytest
from unittest.mock import patch, MagicMock

@pytest.fixture
def mock_business_context():
    """Mock business context data"""
    return {
        "id": "test-id",
        "user_id": "test-user",
        "name": "Test Profile",
        "profile_overview": "Overview for Test Profile",
        "content_focus": ["Topic A", "Topic B"],
        "primary_objectives": "Objectives for Test",
        "offer_description": "Test Offer",
        "target_audience": "Test Audience",
        "audience_motivation": "Test Motivation",
        "brand_voice": "Test Voice",
        "key_benefits": "Test Benefits",
        "unique_value_proposition": "Test UVP",
        "audience_decision_style": "Test Style",
        "audience_preference_structure": "Test Structure",
        "audience_decision_speed": "Test Speed",
        "audience_reaction_to_change": "Test Reaction",
        "recommended_platforms": ["Platform X", "Platform Y"],
        "recommended_content_formats": ["Format 1", "Format 2"],
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z",
    }


@pytest.fixture
def mock_analysis_result():
    """Mock audience analysis result returned by AI"""
    return {
        "summary": "This audience is primarily motivated by solving problems and prefers structured information.",
        "nhb_criteria": {
            "motivation_direction": {
                "value": "Away",
                "evidence": "The audience motivation indicates they are primarily motivated by avoiding problems.",
                "recommendations": ["Focus on pain points", "Emphasize solutions to problems"]
            },
            "preference_structure": {
                "value": "Procedural",
                "evidence": "The audience prefers structured information as indicated by their preference structure.",
                "recommendations": ["Use step-by-step guides", "Provide clear instructions"]
            }
        },
        "emotional_state": {
            "dominant_state": "Helpless",
            "evidence": "The audience feels overwhelmed by their problems.",
            "content_approach": "Focus on solution-oriented content with clear steps.",
            "recommendations": ["Provide actionable solutions", "Break down complex processes"]
        },
        "question_strategy": {
            "what": "Use 'What' questions to define problems clearly",
            "why": "Use 'Why' questions sparingly",
            "how": "Emphasize 'How' questions to provide solutions",
            "who": "Use 'Who' questions to establish credibility",
            "when": "Use 'When' questions to create urgency",
            "where": "Use 'Where' questions to provide context"
        },
        "content_recommendations": {
            "content_types": ["How-to guides", "Checklists", "Case studies"],
            "messaging_approach": "Direct, solution-focused messaging",
            "content_structure": "Clear, step-by-step structure with visible progress indicators"
        }
    }


def test_analyze_audience(client, auth_headers, mock_business_context, mock_analysis_result):
    """Test the analyze audience endpoint"""
    context_id = "test-id"
    
    # Mock the repository get_by_id method
    with patch("app.repositories.supabase_business_context_repository.BusinessContextRepository.get_by_id") as mock_get:
        mock_get.return_value = mock_business_context
        
        # Mock the AI generator
        with patch("app.ai_assistant.common.get_ai_generator") as mock_get_ai:
            mock_ai = MagicMock()
            mock_ai.generate_content.return_value = {
                "generated_text": json.dumps(mock_analysis_result),
                "tokens_used": 450
            }
            mock_get_ai.return_value = mock_ai
            
            # Make the request
            response = client.post(
                f"/api/business/{context_id}/analyze-audience",
                headers=auth_headers
            )
            
            # Check the response
            assert response.status_code == 200
            data = json.loads(response.data)
            assert "analysis" in data
            assert data["analysis"] == mock_analysis_result
            assert "tokens_used" in data
            assert data["tokens_used"] == 450


def test_analyze_audience_not_found(client, auth_headers):
    """Test the analyze audience endpoint when context not found"""
    context_id = "nonexistent-id"
    
    # Mock the repository get_by_id method to return None
    with patch("app.repositories.supabase_business_context_repository.BusinessContextRepository.get_by_id") as mock_get:
        mock_get.return_value = None
        
        # Make the request
        response = client.post(
            f"/api/business/{context_id}/analyze-audience",
            headers=auth_headers
        )
        
        # Check the response
        assert response.status_code == 404
        data = json.loads(response.data)
        assert "error" in data
        assert "not found" in data["error"].lower()


def test_analyze_audience_ai_error(client, auth_headers, mock_business_context):
    """Test the analyze audience endpoint when AI returns an error"""
    context_id = "test-id"
    
    # Mock the repository get_by_id method
    with patch("app.repositories.supabase_business_context_repository.BusinessContextRepository.get_by_id") as mock_get:
        mock_get.return_value = mock_business_context
        
        # Mock the AI generator to return an error
        with patch("app.ai_assistant.common.get_ai_generator") as mock_get_ai:
            mock_ai = MagicMock()
            mock_ai.generate_content.return_value = {
                "error": True,
                "error_message": "AI service error"
            }
            mock_get_ai.return_value = mock_ai
            
            # Make the request
            response = client.post(
                f"/api/business/{context_id}/analyze-audience",
                headers=auth_headers
            )
            
            # Check the response
            assert response.status_code == 500
            data = json.loads(response.data)
            assert "error" in data
            assert "AI service error" in data["error"]


def test_analyze_audience_json_error(client, auth_headers, mock_business_context):
    """Test the analyze audience endpoint when AI returns invalid JSON"""
    context_id = "test-id"
    
    # Mock the repository get_by_id method
    with patch("app.repositories.supabase_business_context_repository.BusinessContextRepository.get_by_id") as mock_get:
        mock_get.return_value = mock_business_context
        
        # Mock the AI generator to return invalid JSON
        with patch("app.ai_assistant.common.get_ai_generator") as mock_get_ai:
            mock_ai = MagicMock()
            mock_ai.generate_content.return_value = {
                "generated_text": "This is not valid JSON",
                "tokens_used": 450
            }
            mock_get_ai.return_value = mock_ai
            
            # Make the request
            response = client.post(
                f"/api/business/{context_id}/analyze-audience",
                headers=auth_headers
            )
            
            # Check the response
            assert response.status_code == 500
            data = json.loads(response.data)
            assert "error" in data
            assert "Failed to parse" in data["error"]
            assert "raw_result" in data
