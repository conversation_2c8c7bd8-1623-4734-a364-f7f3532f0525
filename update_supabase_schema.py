import os
import sys
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get Supabase credentials from environment variables
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

if not SUPABASE_URL or not SUPABASE_SERVICE_ROLE_KEY:
    print("Error: SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY environment variables are not set.")
    sys.exit(1)

# Read the SQL script
with open("update_wizard_sessions_table.sql", "r") as f:
    sql_script = f.read()

# Execute the SQL script using the Supabase REST API
headers = {
    "apikey": SUPABASE_SERVICE_ROLE_KEY,
    "Authorization": f"Bearer {SUPABASE_SERVICE_ROLE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "params=single-object"
}

data = {
    "query": sql_script
}

try:
    response = requests.post(
        f"{SUPABASE_URL}/rest/v1/rpc/execute_sql",
        headers=headers,
        json=data
    )
    
    if response.status_code == 200:
        print("Successfully updated the wizard_sessions table schema.")
    else:
        print(f"Error updating schema: {response.status_code}")
        print(response.text)
except Exception as e:
    print(f"Error: {e}")
