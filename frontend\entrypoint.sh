#!/bin/sh

# Replace environment variables in env-config.js
# Use default values if environment variables are not set
REACT_APP_API_URL=${REACT_APP_API_URL:-"http://localhost:5000"}
REACT_APP_SUPABASE_URL=${REACT_APP_SUPABASE_URL:-""}
REACT_APP_SUPABASE_ANON_KEY=${REACT_APP_SUPABASE_ANON_KEY:-""}

# Log the configuration for debugging
echo "Frontend Environment Configuration:"
echo "REACT_APP_API_URL: $REACT_APP_API_URL"
echo "REACT_APP_SUPABASE_URL: $REACT_APP_SUPABASE_URL"

# Replace placeholders in env-config.js
cat > /usr/share/nginx/html/env-config.js << EOF
window._env_ = {
  REACT_APP_API_URL: "$REACT_APP_API_URL",
  REACT_APP_SUPABASE_URL: "$REACT_APP_SUPABASE_URL",
  REACT_APP_SUPABASE_ANON_KEY: "$REACT_APP_SUPABASE_ANON_KEY"
};
EOF

# Start nginx
exec "$@" 