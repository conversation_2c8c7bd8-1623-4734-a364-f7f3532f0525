#!/bin/bash

# Clean start script for development
echo "🧹 Performing clean startup..."

# Stop and remove all containers
echo "Stopping containers..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down --remove-orphans

# Remove dangling images to force rebuild
echo "Cleaning images..."
docker image prune -f

# Clear build cache for frontend
echo "Rebuilding frontend with clean cache..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml build --no-cache frontend

# Start services
echo "🚀 Starting services..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

echo "✅ Clean startup complete!"