import React, { useState, useEffect } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  useMediaQuery,
  useTheme,
  Tooltip,
  Button,
} from "@mui/material";
import {
  Menu as MenuIcon,
  ChevronLeft as ChevronLeftIcon,
  Dashboard as DashboardIcon,
  LibraryBooks as LibraryBooksIcon,
  Business as BusinessIcon,
  Chat as ChatIcon,
  YouTube as YouTubeIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  Logout as LogoutIcon,
  Person as PersonIcon,
  DarkMode as DarkModeIcon,
  LightMode as LightModeIcon,
  AutoFixHigh as AutoFixHighIcon,
} from "@mui/icons-material";
import { motion } from "framer-motion";
import { selectCurrentUser } from "../../store/auth/authSlice";
import {
  selectSidebarOpen,
  toggleSidebar,
  toggleDarkMode,
  selectDarkMode,
} from "../../store/ui/uiSlice";
import NotificationCenter from "../notifications/NotificationCenter";
import { useAuth } from "../../contexts/AuthContext";

// Drawer widths
const drawerWidth = 240;
const collapsedDrawerWidth = 60;

// Navigation items
const navItems = [
  { text: "Dashboard", icon: <DashboardIcon />, path: "/" },
  { text: "AI Assistant", icon: <ChatIcon />, path: "/ai-assistant" },
  { text: "Script Wizard", icon: <AutoFixHighIcon />, path: "/wizard" },
  { text: "Search", icon: <YouTubeIcon />, path: "/youtube-analytics" },
  { text: "Library", icon: <LibraryBooksIcon />, path: "/content-library" },
  { text: "Profile", icon: <BusinessIcon />, path: "/business-context" },
];

const MainLayout: React.FC = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const user = useSelector(selectCurrentUser);
  const sidebarOpen = useSelector(selectSidebarOpen);
  const darkMode = useSelector(selectDarkMode);
  const isMobile = useMediaQuery(theme.breakpoints.down("md"));

  // State for user menu
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [notificationAnchorEl, setNotificationAnchorEl] =
    useState<null | HTMLElement>(null);
  const { signOut } = useAuth();
  const [notificationsOpen, setNotificationsOpen] = useState(false);

  // Close sidebar on mobile when navigating
  useEffect(() => {
    if (isMobile && sidebarOpen) {
      dispatch(toggleSidebar());
    }
  }, [location.pathname, isMobile, sidebarOpen, dispatch]);

  // Handle user menu open
  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  // Handle user menu close
  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  // Handle notification menu open
  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setNotificationAnchorEl(event.currentTarget);
  };

  // Handle notification menu close
  const handleNotificationMenuClose = () => {
    setNotificationAnchorEl(null);
  };

  // Handle logout
  const handleLogout = async () => {
    handleUserMenuClose();
    try {
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  // Handle profile navigation
  const handleProfileClick = () => {
    handleUserMenuClose();
    navigate("/profile");
  };

  // Handle settings navigation
  const handleSettingsClick = () => {
    handleUserMenuClose();
    navigate("/settings");
  };

  // Handle dark mode toggle
  const handleDarkModeToggle = () => {
    dispatch(toggleDarkMode());
    handleUserMenuClose();
  };

  // User menu
  const userDisplayName = user
    ? `${user.firstName || ""} ${user.lastName || ""}`.trim() ||
      user.username ||
      "User"
    : "User";
  const userInitials =
    user && user.firstName && user.lastName
      ? `${user.firstName[0] || ""}${user.lastName[0] || ""}`.toUpperCase()
      : user && user.username
      ? user.username[0].toUpperCase()
      : "U";

  return (
    <Box sx={{ display: "flex", height: "100%" }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          // Adjust width and margin based on sidebar state
          width: `calc(100% - ${
            sidebarOpen ? drawerWidth : collapsedDrawerWidth
          }px)`,
          ml: sidebarOpen ? drawerWidth : collapsedDrawerWidth,
          bgcolor: "background.paper",
          color: "text.primary",
          borderBottom: "1px solid",
          borderColor: "divider",
          backdropFilter: "blur(8px)",
          background:
            theme.palette.mode === "dark"
              ? "rgba(22, 28, 36, 0.95)"
              : "rgba(255, 255, 255, 0.95)",
        }}
      >
        <Toolbar
          sx={{ minHeight: "64px", gap: 2, justifyContent: "space-between" }}
        >
          {/* Left section with logo */}
          <Typography
            variant="h6"
            noWrap
            component="div"
            onClick={() => navigate("/")}
            sx={{
              fontWeight: 700,
              cursor: "pointer",
              background:
                theme.palette.mode === "dark"
                  ? "linear-gradient(90deg, #7C3AED 0%, #3B82F6 100%)"
                  : "linear-gradient(90deg, #6366F1 0%, #8B5CF6 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              minWidth: "100px",
            }}
          >
            Writer v2
          </Typography>

          {/* Center section with navigation */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              gap: 0.5,
              flex: 1,
              maxWidth: "800px",
              mx: "auto",
            }}
          >
            {navItems.map((item) => (
              <Button
                key={item.text}
                onClick={() => navigate(item.path)}
                sx={{
                  color: "text.primary",
                  px: 1.5,
                  py: 1,
                  borderRadius: 1.5,
                  textTransform: "none",
                  fontSize: "0.9rem",
                  fontWeight: location.pathname === item.path ? 600 : 400,
                  position: "relative",
                  minWidth: 0,
                  flex: 1,
                  "&:before":
                    location.pathname === item.path
                      ? {
                          content: '""',
                          position: "absolute",
                          bottom: 0,
                          left: "10%",
                          width: "80%",
                          height: "2px",
                          background: theme.palette.primary.main,
                          borderRadius: "2px",
                        }
                      : {},
                  "&:hover": {
                    bgcolor:
                      theme.palette.mode === "dark"
                        ? "rgba(255, 255, 255, 0.08)"
                        : "rgba(0, 0, 0, 0.04)",
                    "&:before": {
                      content: '""',
                      position: "absolute",
                      bottom: 0,
                      left: "10%",
                      width: "80%",
                      height: "2px",
                      background: theme.palette.primary.main,
                      borderRadius: "2px",
                      opacity: 0.5,
                    },
                  },
                }}
                startIcon={
                  <Box
                    sx={{
                      color:
                        location.pathname === item.path
                          ? "primary.main"
                          : "text.secondary",
                      display: "flex",
                      alignItems: "center",
                      minWidth: 24,
                    }}
                  >
                    {item.icon}
                  </Box>
                }
              >
                {item.text}
              </Button>
            ))}
          </Box>

          {/* Right section */}
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              minWidth: "100px",
              justifyContent: "flex-end",
            }}
          >
            {/* Notification Icon */}
            <Tooltip title="Notifications">
              <IconButton
                color="inherit"
                onClick={handleNotificationMenuOpen}
                size="small"
                sx={{
                  bgcolor:
                    theme.palette.mode === "dark"
                      ? "rgba(255, 255, 255, 0.05)"
                      : "rgba(0, 0, 0, 0.03)",
                  "&:hover": {
                    bgcolor:
                      theme.palette.mode === "dark"
                        ? "rgba(255, 255, 255, 0.08)"
                        : "rgba(0, 0, 0, 0.05)",
                  },
                }}
              >
                <NotificationsIcon fontSize="small" />
              </IconButton>
            </Tooltip>

            {/* User Avatar */}
            <Tooltip title={userDisplayName}>
              <IconButton
                onClick={handleUserMenuOpen}
                size="small"
                sx={{
                  p: 0.5,
                  bgcolor:
                    theme.palette.mode === "dark"
                      ? "rgba(255, 255, 255, 0.05)"
                      : "rgba(0, 0, 0, 0.03)",
                  "&:hover": {
                    bgcolor:
                      theme.palette.mode === "dark"
                        ? "rgba(255, 255, 255, 0.08)"
                        : "rgba(0, 0, 0, 0.05)",
                  },
                }}
                aria-controls="user-menu"
                aria-haspopup="true"
              >
                <Avatar
                  alt={userDisplayName}
                  src="/static/images/avatar/1.jpg"
                  sx={{
                    width: 30,
                    height: 30,
                    fontSize: "0.85rem",
                    bgcolor: theme.palette.primary.main,
                    transition: "transform 0.2s",
                    "&:hover": {
                      transform: "scale(1.1)",
                    },
                  }}
                >
                  {userInitials}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>

          {/* User Menu */}
          <Menu
            id="user-menu"
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleUserMenuClose}
            PaperProps={{
              elevation: 3,
              sx: {
                overflow: "visible",
                filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.1))",
                mt: 1.5,
                borderRadius: 2,
                minWidth: 200,
                "& .MuiMenuItem-root": {
                  px: 2,
                  py: 1,
                  borderRadius: 1,
                  mx: 1,
                  my: 0.5,
                  "&:hover": {
                    bgcolor:
                      theme.palette.mode === "dark"
                        ? "rgba(255, 255, 255, 0.08)"
                        : "rgba(0, 0, 0, 0.04)",
                  },
                },
                "& .MuiDivider-root": {
                  my: 1,
                },
                "& .MuiListItemIcon-root": {
                  minWidth: 36,
                  color: "text.secondary",
                },
              },
            }}
            transformOrigin={{ horizontal: "right", vertical: "top" }}
            anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
          >
            <MenuItem onClick={handleProfileClick}>
              <ListItemIcon>
                <PersonIcon fontSize="small" />
              </ListItemIcon>
              Profile
            </MenuItem>
            <MenuItem onClick={handleSettingsClick}>
              <ListItemIcon>
                <SettingsIcon fontSize="small" />
              </ListItemIcon>
              Settings
            </MenuItem>
            <MenuItem onClick={handleDarkModeToggle}>
              <ListItemIcon>
                {darkMode ? (
                  <LightModeIcon fontSize="small" />
                ) : (
                  <DarkModeIcon fontSize="small" />
                )}
              </ListItemIcon>
              {darkMode ? "Light Mode" : "Dark Mode"}
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <LogoutIcon fontSize="small" />
              </ListItemIcon>
              Logout
            </MenuItem>
          </Menu>

          {/* Notification Center */}
          <NotificationCenter
            anchorEl={notificationAnchorEl}
            onClose={handleNotificationMenuClose}
          />
        </Toolbar>
      </AppBar>

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: "100%",
          height: "100%",
          display: "flex",
          flexDirection: "column",
          overflow: "auto",
          mt: "64px", // Height of AppBar
        }}
      >
        <Box
          component={motion.div}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}
        >
          <Outlet />
        </Box>
      </Box>
    </Box>
  );
};

export default MainLayout;
