"""
Business profile routes for FastAPI.
Migrated from Flask app/business/routes.py to use FastAPI dependency injection.
"""

import logging
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field

from app.auth.dependencies import CurrentUserType, CurrentUserIdType
from app.dependencies import BusinessContextRepositoryType

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/business", tags=["business"])


class BusinessContextCreate(BaseModel):
    """Request model for creating a business context."""
    # Core Profile
    profile_overview: str = Field(..., description="Overview of the business profile")
    content_focus: List[str] = Field(..., description="List of content focus areas")
    primary_objectives: Optional[str] = Field(None, description="Primary business objectives")
    
    # Offer (Legacy)
    offer_description: Optional[str] = Field(None, description="Description of the offer")
    
    # Audience
    target_audience: str = Field(..., description="Target audience description")
    audience_motivation: Optional[str] = Field(None, description="What motivates the audience")
    
    # Brand & Voice
    brand_voice: str = Field(..., description="Brand voice and tone")
    key_benefits: Optional[str] = Field(None, description="Key benefits offered")
    unique_value_proposition: Optional[str] = Field(None, description="Unique value proposition")
    
    # Decision Style
    audience_decision_style: Optional[str] = Field(None, description="How the audience makes decisions")
    audience_preference_structure: Optional[str] = Field(None, description="Audience preference structure")
    audience_decision_speed: Optional[str] = Field(None, description="Speed of audience decision making")
    audience_reaction_to_change: Optional[str] = Field(None, description="How audience reacts to change")
    
    # Distribution & Formats
    recommended_platforms: Optional[List[str]] = Field(None, description="Recommended platforms")
    recommended_content_formats: Optional[List[str]] = Field(None, description="Recommended content formats")
    
    # Optional name
    name: Optional[str] = Field(None, description="Name for the business context")


class BusinessContextUpdate(BaseModel):
    """Request model for updating a business context."""
    # All fields are optional for updates
    profile_overview: Optional[str] = None
    content_focus: Optional[List[str]] = None
    primary_objectives: Optional[str] = None
    offer_description: Optional[str] = None
    target_audience: Optional[str] = None
    audience_motivation: Optional[str] = None
    brand_voice: Optional[str] = None
    key_benefits: Optional[str] = None
    unique_value_proposition: Optional[str] = None
    audience_decision_style: Optional[str] = None
    audience_preference_structure: Optional[str] = None
    audience_decision_speed: Optional[str] = None
    audience_reaction_to_change: Optional[str] = None
    recommended_platforms: Optional[List[str]] = None
    recommended_content_formats: Optional[List[str]] = None
    name: Optional[str] = None


class BusinessContextResponse(BaseModel):
    """Response model for business context data."""
    id: str
    user_id: str
    name: Optional[str] = None
    profile_overview: Optional[str] = None
    content_focus: Optional[List[str]] = None
    primary_objectives: Optional[str] = None
    offer_description: Optional[str] = None
    target_audience: Optional[str] = None
    audience_motivation: Optional[str] = None
    brand_voice: Optional[str] = None
    key_benefits: Optional[str] = None
    unique_value_proposition: Optional[str] = None
    audience_decision_style: Optional[str] = None
    audience_preference_structure: Optional[str] = None
    audience_decision_speed: Optional[str] = None
    audience_reaction_to_change: Optional[str] = None
    recommended_platforms: Optional[List[str]] = None
    recommended_content_formats: Optional[List[str]] = None
    created_at: str
    updated_at: str


class GenerateContentRequest(BaseModel):
    """Request model for generating content with business context."""
    prompt: str = Field(..., description="The prompt for content generation")


class OptimizeContextRequest(BaseModel):
    """Request model for optimizing business context."""
    message: Optional[str] = Field(
        default="Please optimize this business profile to make it more compelling and effective.",
        description="Message for the optimization"
    )
    fields_to_optimize: Optional[List[str]] = Field(
        default=None,
        description="Specific fields to optimize"
    )


@router.post("/", response_model=BusinessContextResponse, status_code=201)
async def create_business_context(
    context_data: BusinessContextCreate,
    current_user_id: CurrentUserIdType,
    business_repo: BusinessContextRepositoryType
) -> BusinessContextResponse:
    """Create a new business context (profile)."""
    try:
        # Prepare data for the repository
        business_context_payload = context_data.model_dump(exclude_unset=True)
        
        context = await business_repo.create_business_context(
            user_id=current_user_id,
            business_context_data=business_context_payload,
        )
        
        if not context:
            raise HTTPException(
                status_code=500,
                detail="Failed to create business profile"
            )
        
        return context
        
    except Exception as e:
        logger.error(f"Error creating business profile: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create business profile: {str(e)}"
        )


@router.get("/", response_model=List[BusinessContextResponse])
async def get_business_contexts(
    current_user_id: CurrentUserIdType,
    business_repo: BusinessContextRepositoryType
) -> List[BusinessContextResponse]:
    """Get all business contexts for the current user."""
    try:
        contexts = await business_repo.get_by_user_id(current_user_id)
        return contexts or []
        
    except Exception as e:
        logger.error(f"Error fetching business contexts: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch business contexts: {str(e)}"
        )


@router.get("/{context_id}", response_model=BusinessContextResponse)
async def get_business_context(
    context_id: str,
    current_user_id: CurrentUserIdType,
    business_repo: BusinessContextRepositoryType
) -> BusinessContextResponse:
    """Get a specific business context."""
    try:
        context = await business_repo.get_by_id(context_id, current_user_id)
        
        if not context:
            raise HTTPException(
                status_code=404,
                detail="Business context not found"
            )
        
        return context
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching business context: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch business context: {str(e)}"
        )


@router.put("/{context_id}", response_model=BusinessContextResponse)
async def update_business_context(
    context_id: str,
    context_data: BusinessContextUpdate,
    current_user_id: CurrentUserIdType,
    business_repo: BusinessContextRepositoryType
) -> BusinessContextResponse:
    """Update a specific business context (profile)."""
    try:
        # Check if context exists
        existing_context = await business_repo.get_by_id(context_id, current_user_id)
        if not existing_context:
            raise HTTPException(
                status_code=404,
                detail="Business context not found"
            )

        # Prepare update data (only include fields that were provided)
        update_data = context_data.model_dump(exclude_unset=True)

        if not update_data:
            raise HTTPException(
                status_code=400,
                detail="No fields provided for update"
            )

        # Update business context
        updated_context = await business_repo.update_business_context(
            context_id=context_id,
            user_id=current_user_id,
            update_data=update_data
        )

        if not updated_context:
            raise HTTPException(
                status_code=500,
                detail="Business profile update failed"
            )

        return updated_context

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating business context: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update business context: {str(e)}"
        )


@router.delete("/{context_id}")
async def delete_business_context(
    context_id: str,
    current_user_id: CurrentUserIdType,
    business_repo: BusinessContextRepositoryType
) -> Dict[str, str]:
    """Delete a specific business context."""
    try:
        # Check if context exists
        existing_context = await business_repo.get_by_id(context_id, current_user_id)
        if not existing_context:
            raise HTTPException(
                status_code=404,
                detail="Business context not found"
            )

        # Delete the context
        await business_repo.delete_business_context(context_id, current_user_id)

        return {"message": "Business profile deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting business context: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete business context: {str(e)}"
        )
