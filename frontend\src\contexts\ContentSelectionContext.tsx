import React, { createContext, useContext, useState, useCallback } from "react";

interface ContentSelectionContextType {
  selectedContentIds: string[];
  toggleContentSelection: (id: string) => void;
  clearContentSelection: () => void;
  isContentSelected: (id: string) => boolean;
}

const ContentSelectionContext = createContext<
  ContentSelectionContextType | undefined
>(undefined);

export const ContentSelectionProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const [selectedContentIds, setSelectedContentIds] = useState<string[]>([]);

  const toggleContentSelection = useCallback((id: string) => {
    setSelectedContentIds((prev) => {
      if (prev.includes(id)) {
        return prev.filter((contentId) => contentId !== id);
      } else {
        return [...prev, id];
      }
    });
  }, []);

  const clearContentSelection = useCallback(() => {
    setSelectedContentIds([]);
  }, []);

  const isContentSelected = useCallback(
    (id: string) => {
      return selectedContentIds.includes(id);
    },
    [selectedContentIds]
  );

  return (
    <ContentSelectionContext.Provider
      value={{
        selectedContentIds,
        toggleContentSelection,
        clearContentSelection,
        isContentSelected,
      }}
    >
      {children}
    </ContentSelectionContext.Provider>
  );
};

export const useContentSelection = () => {
  const context = useContext(ContentSelectionContext);
  if (context === undefined) {
    throw new Error(
      "useContentSelection must be used within a ContentSelectionProvider"
    );
  }
  return context;
};
