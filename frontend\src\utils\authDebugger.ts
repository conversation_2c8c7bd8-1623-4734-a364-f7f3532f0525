/**
 * Authentication Debugging Utility
 * 
 * This utility helps debug authentication issues by providing
 * detailed logging and session validation functions.
 */

import { supabase } from '../lib/supabase';

export interface AuthDebugInfo {
  hasSession: boolean;
  sessionValid: boolean;
  tokenExpiry: number | null;
  timeUntilExpiry: number | null;
  refreshTokenExists: boolean;
  currentUrl: string;
  isOnAuthPage: boolean;
  timestamp: string;
}

/**
 * Get comprehensive authentication debug information
 */
export const getAuthDebugInfo = async (): Promise<AuthDebugInfo> => {
  const now = Math.floor(Date.now() / 1000);
  const currentUrl = window.location.pathname;
  const authPages = ["/login", "/register", "/reset-password", "/forgot-password"];
  
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error("Error getting session for debug:", error);
    }
    
    const tokenExpiry = session?.expires_at || null;
    const timeUntilExpiry = tokenExpiry ? tokenExpiry - now : null;
    
    return {
      hasSession: !!session,
      sessionValid: !!session?.access_token && (tokenExpiry === null || tokenExpiry > now),
      tokenExpiry,
      timeUntilExpiry,
      refreshTokenExists: !!session?.refresh_token,
      currentUrl,
      isOnAuthPage: authPages.includes(currentUrl),
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error("Error in getAuthDebugInfo:", error);
    return {
      hasSession: false,
      sessionValid: false,
      tokenExpiry: null,
      timeUntilExpiry: null,
      refreshTokenExists: false,
      currentUrl,
      isOnAuthPage: authPages.includes(currentUrl),
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Log detailed authentication debug information
 */
export const logAuthDebugInfo = async (): Promise<void> => {
  const debugInfo = await getAuthDebugInfo();
  
  console.group("🔐 Authentication Debug Info");
  console.log("Timestamp:", debugInfo.timestamp);
  console.log("Current URL:", debugInfo.currentUrl);
  console.log("Is on auth page:", debugInfo.isOnAuthPage);
  console.log("Has session:", debugInfo.hasSession);
  console.log("Session valid:", debugInfo.sessionValid);
  console.log("Refresh token exists:", debugInfo.refreshTokenExists);
  
  if (debugInfo.tokenExpiry) {
    console.log("Token expiry (unix):", debugInfo.tokenExpiry);
    console.log("Token expiry (date):", new Date(debugInfo.tokenExpiry * 1000).toISOString());
    
    if (debugInfo.timeUntilExpiry !== null) {
      if (debugInfo.timeUntilExpiry > 0) {
        console.log("Time until expiry:", Math.floor(debugInfo.timeUntilExpiry / 60), "minutes");
      } else {
        console.log("Token expired", Math.abs(debugInfo.timeUntilExpiry), "seconds ago");
      }
    }
  }
  
  console.groupEnd();
};

/**
 * Test session refresh functionality
 */
export const testSessionRefresh = async (): Promise<boolean> => {
  console.log("🔄 Testing session refresh...");
  
  try {
    const beforeRefresh = await getAuthDebugInfo();
    console.log("Before refresh:", beforeRefresh);
    
    const { data, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error("Session refresh failed:", error);
      return false;
    }
    
    if (!data.session) {
      console.error("Session refresh returned no session");
      return false;
    }
    
    const afterRefresh = await getAuthDebugInfo();
    console.log("After refresh:", afterRefresh);
    
    console.log("✅ Session refresh successful");
    return true;
  } catch (error) {
    console.error("Session refresh test failed:", error);
    return false;
  }
};

/**
 * Monitor authentication state changes
 */
export const startAuthMonitoring = (): (() => void) => {
  console.log("🔍 Starting authentication monitoring...");
  
  const { data: { subscription } } = supabase.auth.onAuthStateChange(
    async (event, session) => {
      console.group(`🔐 Auth State Change: ${event}`);
      console.log("Event:", event);
      console.log("Session exists:", !!session);
      
      if (session) {
        console.log("User:", session.user.email);
        console.log("Token expires at:", new Date((session.expires_at || 0) * 1000).toISOString());
      }
      
      await logAuthDebugInfo();
      console.groupEnd();
    }
  );
  
  // Return cleanup function
  return () => {
    console.log("🛑 Stopping authentication monitoring");
    subscription.unsubscribe();
  };
};

// Make functions available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).authDebugger = {
    getAuthDebugInfo,
    logAuthDebugInfo,
    testSessionRefresh,
    startAuthMonitoring
  };
  
  console.log("🔧 Auth debugger available at window.authDebugger");
}
