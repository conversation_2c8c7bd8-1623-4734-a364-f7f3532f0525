import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON>alogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Checkbox,
  FormControlLabel,
  Divider,
  Paper,
  Chip,
  Grid,
  IconButton,
  Tooltip,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Switch,
} from '@mui/material';
import {
  Info as InfoIcon,
  Refresh as RefreshIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon,
  Settings as SettingsIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import { api } from '../services/api';

interface OptimizedField {
  value: string;
  why: string;
}

interface OptimizedFields {
  [key: string]: OptimizedField;
}

interface BusinessContext {
  id: string;
  user_id: string;
  name?: string;
  profile_overview?: string | null;
  content_focus?: string[] | null;
  primary_objectives?: string | null;
  offer_description: string;
  target_audience: string;
  audience_motivation?: string | null;
  brand_voice?: string | null;
  key_benefits?: string | null;
  unique_value_proposition?: string | null;
  audience_decision_style?: string | null;
  audience_preference_structure?: string | null;
  audience_decision_speed?: string | null;
  audience_reaction_to_change?: string | null;
  recommended_platforms?: string[] | null;
  recommended_content_formats?: string[] | null;
  created_at: string;
  updated_at: string;
}

interface OptimizeProfileModalProps {
  open: boolean;
  onClose: () => void;
  businessContext: BusinessContext | null | undefined;
  onApplyChanges: (updatedFields: Record<string, any>) => Promise<void>;
}

type OptimizationStep = 'select' | 'loading' | 'review';

// Field display names for better readability
const fieldDisplayNames: Record<string, string> = {
  profile_overview: 'Profile Overview',
  content_focus: 'Content Focus',
  primary_objectives: 'Primary Objectives',
  offer_description: 'Offer Description',
  target_audience: 'Target Audience',
  audience_motivation: 'Audience Motivation',
  brand_voice: 'Brand Voice',
  key_benefits: 'Key Benefits',
  unique_value_proposition: 'Unique Value Proposition',
  audience_decision_style: 'Audience Decision Style',
  audience_preference_structure: 'Audience Preference Structure',
  audience_decision_speed: 'Audience Decision Speed',
  audience_reaction_to_change: 'Audience Reaction to Change',
  recommended_platforms: 'Recommended Platforms',
  recommended_content_formats: 'Recommended Content Formats',
};

const OptimizeProfileModal: React.FC<OptimizeProfileModalProps> = ({
  open,
  onClose,
  businessContext,
  onApplyChanges,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [optimizedFields, setOptimizedFields] = useState<OptimizedFields>({});
  const [selectedFields, setSelectedFields] = useState<Record<string, boolean>>({});
  const [tokensUsed, setTokensUsed] = useState<number | null>(null);
  const [currentStep, setCurrentStep] = useState<OptimizationStep>('select');
  const [fieldsToOptimize, setFieldsToOptimize] = useState<Record<string, boolean>>({});

  // Reset state when modal opens with a new business context
  useEffect(() => {
    if (open && businessContext) {
      setOptimizedFields({});
      setSelectedFields({});
      setError(null);
      setTokensUsed(null);
      setCurrentStep('select');

      // Initialize all fields as selected for optimization
      const initialFieldsToOptimize: Record<string, boolean> = {};
      Object.keys(fieldDisplayNames).forEach((field) => {
        // Check if the field exists in the business context
        if (field in businessContext) {
          initialFieldsToOptimize[field] = true;
        }
      });
      setFieldsToOptimize(initialFieldsToOptimize);
    }
  }, [open, businessContext]);

  // Function to optimize the profile
  const optimizeProfile = async () => {
    if (!businessContext) return;

    setLoading(true);
    setError(null);
    setCurrentStep('loading');

    // Get the list of fields to optimize
    const fieldsToOptimizeList = Object.entries(fieldsToOptimize)
      .filter(([_, selected]) => selected)
      .map(([field]) => field);

    try {
      const response = await api.post(`/business/${businessContext.id}/optimize`, {
        message: 'Please optimize this business profile to make it more compelling, clear, and effective.',
        fields_to_optimize: fieldsToOptimizeList
      }, {
        timeout: 30000, // 30 second timeout (reduced from 65 seconds)
      });

      const { optimized, tokens_used } = response.data;
      setOptimizedFields(optimized);
      setTokensUsed(tokens_used);

      // Initialize all fields as selected for review
      const initialSelectedFields: Record<string, boolean> = {};
      Object.keys(optimized).forEach((field) => {
        initialSelectedFields[field] = true;
      });
      setSelectedFields(initialSelectedFields);

      // Move to review step
      setCurrentStep('review');
    } catch (err: any) {
      console.error('Error optimizing profile:', err);

      // Check if this is a timeout error
      if (err.code === 'ECONNABORTED') {
        setError('The request timed out. The AI service is taking too long to respond. Please try again later.');
      }
      // Check if this is a retryable error
      else if (err.response?.data?.retryable) {
        setError(`${err.response?.data?.error || 'An error occurred'}. This is a temporary issue. Please try again later.`);
      }
      // Generic error
      else {
        setError(err.response?.data?.error || 'Failed to optimize profile. Please try again.');
      }
      // Go back to selection step on error
      setCurrentStep('select');
    } finally {
      setLoading(false);
    }
  };

  // Handle field selection for optimization
  const handleOptimizeFieldToggle = (field: string) => {
    setFieldsToOptimize((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  // Select/deselect all fields for optimization
  const handleSelectAllOptimizeFields = (select: boolean) => {
    const newFieldsToOptimize: Record<string, boolean> = {};
    Object.keys(fieldsToOptimize).forEach((field) => {
      newFieldsToOptimize[field] = select;
    });
    setFieldsToOptimize(newFieldsToOptimize);
  };

  // Handle checkbox changes
  const handleCheckboxChange = (field: string) => {
    setSelectedFields((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  // Handle applying selected changes
  const handleApplyChanges = async () => {
    if (!businessContext) return;

    const updatedFields: Record<string, any> = {};

    // Add selected fields to the update payload
    Object.entries(selectedFields).forEach(([field, isSelected]) => {
      if (isSelected && optimizedFields[field]) {
        // Handle array fields differently
        if (
          field === 'content_focus' ||
          field === 'recommended_platforms' ||
          field === 'recommended_content_formats'
        ) {
          // If the value is a string, split it into an array
          const value = optimizedFields[field].value;
          updatedFields[field] = typeof value === 'string'
            ? value.split(',').map(item => item.trim())
            : value;
        } else {
          updatedFields[field] = optimizedFields[field].value;
        }
      }
    });

    try {
      await onApplyChanges(updatedFields);
      onClose();
    } catch (err: any) {
      console.error('Error applying changes:', err);
      setError(err.response?.data?.error || 'Failed to apply changes. Please try again.');
    }
  };

  // Select/deselect all fields
  const handleSelectAll = (select: boolean) => {
    const newSelectedFields: Record<string, boolean> = {};
    Object.keys(optimizedFields).forEach((field) => {
      newSelectedFields[field] = select;
    });
    setSelectedFields(newSelectedFields);
  };

  // Format field value for display
  const formatFieldValue = (field: string, value: any): string => {
    if (value === null || value === undefined) return 'Not provided';

    if (Array.isArray(value)) {
      return value.join(', ');
    }

    return value.toString();
  };

  // Render the field selection step
  const renderFieldSelectionStep = () => {
    if (!businessContext) return null;

    return (
      <>
        <Box sx={{ mb: 3 }}>
          <Typography variant="body1" gutterBottom>
            Select which sections of your business profile you want the AI to optimize.
            The AI will read all sections for context but will only suggest changes for the selected sections.
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
            <Button
              size="small"
              variant="outlined"
              onClick={() => handleSelectAllOptimizeFields(true)}
            >
              Select All
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={() => handleSelectAllOptimizeFields(false)}
            >
              Deselect All
            </Button>
          </Box>
        </Box>

        <Divider sx={{ mb: 2 }} />

        <Grid container spacing={2}>
          {Object.entries(fieldDisplayNames).map(([field, displayName]) => {
            // Skip fields that don't exist in the business context
            if (!(field in businessContext)) return null;

            return (
              <Grid item xs={12} sm={6} md={4} key={field}>
                <Card
                  variant="outlined"
                  sx={{
                    border: '1px solid',
                    borderColor: fieldsToOptimize[field] ? 'primary.main' : 'divider',
                    bgcolor: fieldsToOptimize[field] ? 'action.selected' : 'background.paper',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      borderColor: 'primary.main',
                      boxShadow: 1
                    }
                  }}
                  onClick={() => handleOptimizeFieldToggle(field)}
                >
                  <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Typography variant="subtitle1" fontWeight="medium">
                        {displayName}
                      </Typography>
                      <Checkbox
                        checked={fieldsToOptimize[field] || false}
                        onChange={() => handleOptimizeFieldToggle(field)}
                        icon={<CheckBoxOutlineBlankIcon />}
                        checkedIcon={<CheckBoxIcon />}
                        onClick={(e) => e.stopPropagation()}
                      />
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontSize: '0.8rem' }}>
                      {formatFieldValue(field, businessContext[field as keyof BusinessContext])}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>
      </>
    );
  };

  // Render the loading step
  const renderLoadingStep = () => (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
      <CircularProgress />
      <Typography variant="body1" sx={{ ml: 2 }}>
        Optimizing your business profile...
      </Typography>
    </Box>
  );

  // Render the review step
  const renderReviewStep = () => (
    <>
      <Box sx={{ mb: 2 }}>
        <Typography variant="body1" gutterBottom>
          The AI has suggested improvements to your business profile. Review the changes below and select which ones to apply.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
          <Button
            size="small"
            variant="outlined"
            onClick={() => handleSelectAll(true)}
          >
            Select All
          </Button>
          <Button
            size="small"
            variant="outlined"
            onClick={() => handleSelectAll(false)}
          >
            Deselect All
          </Button>
        </Box>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {Object.entries(optimizedFields).map(([field, data]) => (
        <Paper
          key={field}
          elevation={1}
          sx={{
            p: 2,
            mb: 2,
            border: '1px solid',
            borderColor: selectedFields[field] ? 'primary.main' : 'divider',
            bgcolor: selectedFields[field] ? 'action.selected' : 'background.paper'
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedFields[field] || false}
                      onChange={() => handleCheckboxChange(field)}
                    />
                  }
                  label={
                    <Typography variant="subtitle1" fontWeight="bold">
                      {fieldDisplayNames[field] || field}
                    </Typography>
                  }
                />
                <Tooltip title={data.why}>
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Current Value:
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5, p: 1, bgcolor: 'action.hover', borderRadius: 1 }}>
                  {formatFieldValue(field, businessContext?.[field as keyof BusinessContext])}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Optimized Value:
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5, p: 1, bgcolor: 'success.light', color: 'success.contrastText', borderRadius: 1 }}>
                  {formatFieldValue(field, data.value)}
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="caption" color="text.secondary">
                Reason for change:
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5 }}>
                {data.why}
              </Typography>
            </Grid>
          </Grid>
        </Paper>
      ))}
    </>
  );

  // Render the error state
  const renderErrorState = () => (
    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
      <Alert severity="error" sx={{ mb: 2, width: '100%' }}>
        {error}
      </Alert>
      <Button
        variant="outlined"
        onClick={() => setCurrentStep('select')}
        startIcon={<SettingsIcon />}
      >
        Back to Selection
      </Button>
      <Button
        variant="outlined"
        onClick={optimizeProfile}
        startIcon={<RefreshIcon />}
      >
        Retry Optimization
      </Button>
    </Box>
  );

  return (
    <Dialog
      open={open}
      onClose={loading ? undefined : onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          height: '80vh',
          display: 'flex',
          flexDirection: 'column'
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h5">Optimize Business Profile with AI</Typography>
          {tokensUsed && (
            <Typography variant="caption" color="text.secondary">
              Tokens used: {tokensUsed}
            </Typography>
          )}
        </Box>
      </DialogTitle>

      <DialogContent sx={{ flexGrow: 1, overflow: 'auto' }}>
        {error ? (
          renderErrorState()
        ) : currentStep === 'select' ? (
          renderFieldSelectionStep()
        ) : currentStep === 'loading' ? (
          renderLoadingStep()
        ) : currentStep === 'review' ? (
          renderReviewStep()
        ) : (
          <Typography>Unknown step</Typography>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>

        {currentStep === 'select' && (
          <Button
            variant="contained"
            color="primary"
            onClick={optimizeProfile}
            disabled={!Object.values(fieldsToOptimize).some(v => v)}
            endIcon={<ArrowForwardIcon />}
          >
            Optimize Selected Sections
          </Button>
        )}

        {currentStep === 'review' && (
          <Button
            variant="contained"
            color="primary"
            onClick={handleApplyChanges}
            disabled={Object.keys(optimizedFields).length === 0 || !Object.values(selectedFields).some(v => v)}
          >
            Apply Selected Changes
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default OptimizeProfileModal;
