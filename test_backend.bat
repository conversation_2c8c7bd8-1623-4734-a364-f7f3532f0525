@echo off
echo ============================================
echo BACKEND DIAGNOSIS SCRIPT
echo ============================================

echo.
echo [1] Checking Docker containers...
docker ps --filter "name=writerv2" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo [2] Starting backend container if not running...
docker-compose up -d backend

echo.
echo [3] Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo.
echo [4] Testing backend health endpoint...
curl -s http://localhost:5000/health

echo.
echo [5] Testing backend from inside container...
docker exec writerv2-backend-1 curl -s http://localhost:5000/health

echo.
echo [6] Checking backend logs...
docker logs writerv2-backend-1 --tail=10

echo.
echo ============================================
echo DIAGNOSIS COMPLETE
echo ============================================
pause
