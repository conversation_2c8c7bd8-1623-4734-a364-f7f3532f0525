"""Helper functions for formatting context data into strings for AI prompts."""

from typing import List, Optional, Dict, Any


def _format_business_context(business_context: Optional[Dict[str, Any]]) -> str:
    """Formats the business context dictionary into a string."""
    if not business_context:
        return ""

    lines: List[str] = ["\n\nBusiness Context:"]

    # Define mapping for key -> human-readable label
    field_mapping = [
        ("name", "Name"),
        ("profile_overview", "Profile Overview"),
        ("offer_description", "Offer Description"),
        ("target_audience", "Target Audience"),
        ("brand_voice", "Brand Voice"),
        ("key_benefits", "Key Benefits"),
        ("unique_value_proposition", "Unique Value Proposition"),
        ("primary_objectives", "Primary Objectives"),
        ("audience_motivation", "Audience Motivation"),
        ("audience_decision_style", "Audience Decision Style"),
        ("audience_preference_structure", "Audience Preference Structure"),
        ("audience_decision_speed", "Audience Decision Speed"),
        ("audience_reaction_to_change", "Audience Reaction to Change"),
        # Array fields that need special handling
        ("recommended_platforms", "Recommended Platforms"),
        ("recommended_content_formats", "Recommended Content Formats"),
        ("content_focus", "Content Focus"),
    ]

    bc = business_context  # Local alias for brevity

    for key, label in field_mapping:
        if value := bc.get(key):
            if isinstance(value, list):
                # Join array/list values with commas
                lines.append(f"**{label}:** {', '.join(map(str, value))}")
            else:
                lines.append(f"**{label}:** {value}")

    # --- Handle additional_context_examples separately ---
    if examples := bc.get("additional_context_examples"):
        if isinstance(examples, list):
            lines.append("\n**Additional Context Examples (from Content Library):**")
            for example_str in examples:
                lines.append(f"- {example_str}")

    # Join all collected lines efficiently
    return "\n".join(lines)


def _format_content_examples(content_examples: Optional[List[Dict[str, Any]]]) -> str:
    """Formats content examples list into a string."""
    if not content_examples:
        return ""
    lines = ["\n\nContent Examples:"]
    for i, example in enumerate(content_examples):
        lines.append(f"Example {i + 1}: {example.get('content', '')}")
    return "\n".join(lines)


def _format_content_items(content_items: Optional[List[Dict[str, Any]]]) -> str:
    """Formats content library items list into a string."""
    if not content_items:
        return ""
    lines = ["\n\nContent from Knowledge Library:"]
    for i, item in enumerate(content_items):
        lines.append(f"\nItem {i + 1} - {item.get('title', 'Untitled')}:")
        lines.append(f"{item.get('content', '')}")
    return "\n".join(lines)


def _format_video_data(video_data: Optional[Dict[str, Any]]) -> str:
    """Formats YouTube video data dictionary into a string."""
    if not video_data:
        return ""

    lines = ["\n\nYouTube Video Data:"]
    vd = video_data  # Local alias

    if title := vd.get("title"):
        lines.append(f"Title: {title}")
    if channel := vd.get("channel_title"):
        lines.append(f"Channel: {channel}")
    if views := vd.get("views"):
        try:
            lines.append(f"Views: {int(views):,}")
        except (ValueError, TypeError):
            lines.append(f"Views: {views}")  # Keep as string if not a number

    # Transcripts
    if transcript := vd.get("transcript"):
        lines.extend(["\nTranscript:", transcript])
    if formatted_transcript := vd.get("formatted_transcript"):
        lines.extend(["\nTranscript (Formatted):", formatted_transcript])

    # Add other potential fields here using similar checks if needed
    # (e.g., likes, comments, description)

    return "\n".join(lines)
