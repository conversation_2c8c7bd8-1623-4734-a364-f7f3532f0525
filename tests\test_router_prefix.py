"""
Test to ensure all routers use the correct /api prefix.
This enforces the architectural requirement that all API endpoints should start with /api.
"""

import inspect
import pytest
from app import routers


def test_router_prefixes():
    """Test that all routers have the correct /api prefix."""
    # List of routers that should have /api prefix
    router_modules = [
        routers.activity,
        routers.ai_assistant,
        routers.auth,
        routers.business,
        routers.chat,
        routers.content,
        routers.debug,
        routers.feedback,
        routers.wizard,
        routers.youtube,
    ]
    
    failed_routers = []
    
    for module in router_modules:
        if hasattr(module, 'router'):
            router = module.router
            prefix = getattr(router, 'prefix', '')
            
            if not prefix.startswith('/api'):
                failed_routers.append(f"{module.__name__}.router has prefix '{prefix}' (should start with '/api')")
    
    if failed_routers:
        pytest.fail(f"Router prefix violations:\n" + "\n".join(failed_routers))


def test_health_router_special_case():
    """Test that health router has both /api and root prefixes available."""
    health_module = routers.health
    
    # Check main health router has /api prefix
    assert hasattr(health_module, 'router'), "health module should have a router"
    main_router = health_module.router
    main_prefix = getattr(main_router, 'prefix', '')
    assert main_prefix == '/api', f"Main health router should have '/api' prefix, got '{main_prefix}'"
    
    # Check alias health router has no prefix (for /health endpoint)
    assert hasattr(health_module, 'health_router'), "health module should have a health_router"
    alias_router = health_module.health_router
    alias_prefix = getattr(alias_router, 'prefix', '')
    assert alias_prefix == '', f"Health alias router should have no prefix, got '{alias_prefix}'"


def test_all_routers_defined():
    """Test that all expected router modules exist and have router attributes."""
    expected_modules = [
        'activity',
        'ai_assistant', 
        'auth',
        'business',
        'chat',
        'content',
        'debug',
        'feedback',
        'health',
        'wizard',
        'youtube',
    ]
    
    missing_modules = []
    missing_routers = []
    
    for module_name in expected_modules:
        try:
            module = getattr(routers, module_name)
        except AttributeError:
            missing_modules.append(module_name)
            continue
            
        if not hasattr(module, 'router'):
            missing_routers.append(f"{module_name} module missing 'router' attribute")
    
    if missing_modules:
        pytest.fail(f"Missing router modules: {', '.join(missing_modules)}")
    
    if missing_routers:
        pytest.fail(f"Router attribute issues:\n" + "\n".join(missing_routers)) 