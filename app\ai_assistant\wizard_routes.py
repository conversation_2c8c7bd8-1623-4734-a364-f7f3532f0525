"""Compatibility shim to provide wizard_routes under app.ai_assistant.*
This simply re-exports objects from app.routers.wizard so tests or legacy code
importing app.ai_assistant.wizard_routes continue to work.
"""

from app.routers.wizard import router as wizard_router  # noqa: F401

def get_wizard_router():
    """Return the FastAPI router for wizard endpoints (compat shim)."""
    return wizard_router 