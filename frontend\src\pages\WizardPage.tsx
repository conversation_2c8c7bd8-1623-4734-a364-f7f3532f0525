import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Box from '@mui/material/Box';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';
import TextField from '@mui/material/TextField';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import InputLabel from '@mui/material/InputLabel';
import Snackbar from '@mui/material/Snackbar';
import SaveIcon from '@mui/icons-material/Save';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';

// Import the SaveToLibraryDialog component
import SaveToLibraryDialog from '../components/dialogs/SaveToLibraryDialog';

// Import the API service functions
import {
  getBusinessContextsList,
  BusinessContextListItem,
  wizardBrainstorm,
  wizardSelectHook,
  wizardSelectIntro,
  wizardGenerateOutline,
  wizardDraftScript,
  wizardEditScript,
  saveContentItem,
  SaveContentPayload,
  // Import payload/response types if needed for state typing
  WizardStepResponse,
  WizardBrainstormPayload,
  WizardSelectionPayload,
  WizardEditPayload,
} from '../services/api'; // Corrected path

// Define the steps for the wizard
const steps = [
  'Brainstorm Title Ideas',
  'Select Title',
  'Create Hook',
  'Select Intro',
  'Generate Outline',
  'Draft Script',
  'Edit & Polish',
];

// Placeholder components for each step's content
const StepContentPlaceholder: React.FC<{ stepIndex: number }> = ({ stepIndex }) => {
  return (
    <Typography sx={{ mt: 2, mb: 1 }}>
      Content for Step {stepIndex + 1}: {steps[stepIndex]} (Placeholder)
    </Typography>
  );
};

const WizardPage: React.FC = () => {
  const navigate = useNavigate();
  
  // Utility function to safely extract string from error objects
  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error;
    if (error?.message) return error.message;
    if (error?.details) return error.details;
    if (error?.error) return getErrorMessage(error.error);
    if (typeof error === 'object') return JSON.stringify(error);
    return 'An unknown error occurred';
  };
  
  const [activeStep, setActiveStep] = useState(0);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [stepResults, setStepResults] = useState<Record<number, any>>({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [businessContextsList, setBusinessContextsList] = useState<BusinessContextListItem[]>([]);
  const [selectedBusinessContextId, setSelectedBusinessContextId] = useState<string>('');
  const [contentIdea, setContentIdea] = useState<string>('');
  const [selectedTitleIndex, setSelectedTitleIndex] = useState<number | null>(null);
  const [selectedHookIndex, setSelectedHookIndex] = useState<number | null>(null);
  const [selectedIntroIndex, setSelectedIntroIndex] = useState<number | null>(null);
  const [editInput, setEditInput] = useState<string>('');

  // State for save to library functionality
  const [saveDialogOpen, setSaveDialogOpen] = useState<boolean>(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState<boolean>(false);
  const [pendingAction, setPendingAction] = useState<'reset' | 'navigate' | null>(null);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info' | 'warning';
  }>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Fetch business contexts on mount
  useEffect(() => {
    const fetchContexts = async () => {
      setIsLoading(true); // Indicate loading while fetching contexts
      setError(null);
      try {
        const contexts = await getBusinessContextsList();
        setBusinessContextsList(contexts);
      } catch (err: any) {
        console.error("Failed to fetch business contexts:", err);
        setError("Could not load business contexts. Please try reloading.");
        setBusinessContextsList([]); // Ensure it's an empty array on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchContexts();
  }, []); // Empty dependency array ensures this runs only once on mount

  // Add beforeunload event listener to warn users before navigating away
  useEffect(() => {
    // Only add the warning if we've started the wizard process
    if (sessionId || activeStep > 0) {
      const handleBeforeUnload = (e: BeforeUnloadEvent) => {
        const message = 'You have unsaved changes. Are you sure you want to leave?';
        e.preventDefault();
        e.returnValue = message;
        return message;
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }
  }, [sessionId, activeStep]);

  const handleApplyEdit = async () => {
    if (!sessionId || !editInput) {
      setError("Session ID is missing or no edit instructions provided.");
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      // Updated to use style_preferences instead of edit_instructions
      const payload: WizardEditPayload = { session_id: sessionId, style_preferences: editInput };
      console.log('Calling editScript with:', payload);
      const apiResponse = await wizardEditScript(payload);
      const response = apiResponse.data;

      if (response) {
        console.log(`Step 7 (Edit Applied) API Response:`, response);
        setStepResults(prev => ({ ...prev, [7]: response?.results }));
        if (response.error) {
          throw new Error(response.error);
        }
        setEditInput('');
      } else {
        throw new Error("No response received from edit API.");
      }

    } catch (err: any) {
      console.error(`API Error during step 5 edit:`, err);

      // Enhanced error handling with more specific messages
      let errorMessage = 'An error occurred applying the edit.';

      // Check for API response error
      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      }
      // Check for network errors
      else if (err.message && err.message.includes('Network Error')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }
      // Check for timeout errors
      else if (err.message && err.message.includes('timeout')) {
        errorMessage = 'Request timed out. The server is taking too long to respond.';
      }
      // Use error message if available
      else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNext = async () => {
    setIsLoading(true);
    setError(null);

    if (activeStep === steps.length - 1) {
      console.log('Finishing wizard...');
      setActiveStep((prevActiveStep) => prevActiveStep + 1);
      setIsLoading(false);
      return;
    }

    try {
      let response: WizardStepResponse | null = null;
      let currentSessionId = sessionId;

      if (activeStep === 0) {
        if (!selectedBusinessContextId) {
          throw new Error('Please select a Business Context.');
        }
        if (!contentIdea.trim()) {
          throw new Error('Please enter a Content Idea.');
        }

        const payload: WizardBrainstormPayload = {
          business_context_id: selectedBusinessContextId,
          content_idea: contentIdea.trim(),
        };
        console.log('Calling brainstorm with:', payload);
        const apiResponse = await wizardBrainstorm(payload);
        response = apiResponse.data;
        if (response?.session_id) setSessionId(response.session_id);
        setSelectedTitleIndex(null);
        setSelectedHookIndex(null);
        setSelectedIntroIndex(null);
        setEditInput('');
      } else if (activeStep === 1) {
        if (!currentSessionId) throw new Error('Session ID not available for Step 2');
        if (selectedTitleIndex === null) throw new Error('Please select a title to continue.');
        // Just store the selected title index and move to next step
        // No API call needed here - we're just selecting from previously generated titles
        setStepResults(prev => ({
          ...prev,
          [activeStep]: stepResults[0][selectedTitleIndex]
        }));
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
        return; // Skip the rest of the function since we're handling the step transition manually
      } else if (activeStep === 2) {
        if (!currentSessionId) throw new Error('Session ID not available for Step 3');
        // Now we generate hooks based on the selected title

        // Using standardized parameter name selected_index
        const payload: WizardSelectionPayload = {
          session_id: currentSessionId,
          selected_index: selectedTitleIndex || 0 // Pass the title index to the hook generation, default to 0 if null
        };
        console.log('Calling selectHook with:', payload);
        const apiResponse = await wizardSelectHook(payload);
        response = apiResponse.data;
        setSelectedHookIndex(null);
        setSelectedIntroIndex(null);
        setEditInput('');
      } else if (activeStep === 3) {
        if (!currentSessionId) throw new Error('Session ID not available for Step 4');
        if (selectedHookIndex === null) throw new Error('Please select a hook to continue.');
        // Using standardized parameter name selected_index
        const payload: WizardSelectionPayload = {
          session_id: currentSessionId,
          selected_index: selectedHookIndex
        };
        console.log('Calling selectIntro with:', payload);
        const apiResponse = await wizardSelectIntro(payload);
        response = apiResponse.data;
        setEditInput('');
      } else if (activeStep === 4) {
        if (!currentSessionId) throw new Error('Session ID not available for Step 5');
        if (selectedIntroIndex === null) throw new Error('Please select an intro to continue.');
        // Using standardized parameter name selected_index
        const payload: WizardSelectionPayload = {
          session_id: currentSessionId,
          selected_index: selectedIntroIndex
        };
        console.log('Calling generateOutline with:', payload);
        const apiResponse = await wizardGenerateOutline(payload);
        response = apiResponse.data;
        setEditInput('');
      } else if (activeStep === 5) {
        if (!currentSessionId) throw new Error('Session ID not available for Step 6');
        // Using standardized parameter name selected_index
        const payload: WizardSelectionPayload = {
          session_id: currentSessionId,
          selected_index: 0
        };
        console.log('Calling draftScript with:', payload);
        const apiResponse = await wizardDraftScript(payload);
        response = apiResponse.data;
        setEditInput('');
      }

      if (response) {
        console.log(`Step ${activeStep + 1} API Response:`, response);

        // Map the response data based on the current step
        let stepResult: any; // Explicitly type as any to avoid TypeScript errors

        console.log(`Processing response for step ${activeStep}:`, response);

        if (activeStep === 0) {
          // For brainstorm step, handle titles array or extract from data
          if (response.titles) {
            console.log("Found titles array in response:", response.titles);
            stepResult = response.titles;
          } else if ('data' in response && response.data) {
            // Using 'in' operator to check if 'data' property exists
            // Use type assertion to tell TypeScript that data might have a titles property
            const responseData = response.data as any;
            if (responseData.titles) {
              console.log("Found titles array in response.data:", responseData.titles);
              stepResult = responseData.titles;
            }
          } else {
            // Try to find any array in the response
            const findArrayInObject = (obj: any): any[] | null => {
              if (!obj || typeof obj !== 'object') return null;

              // Check direct properties
              for (const key in obj) {
                if (Array.isArray(obj[key]) && obj[key].length > 0) {
                  console.log(`Found array in response.${key}:`, obj[key]);
                  return obj[key];
                }
              }

              // Check nested properties (one level deep)
              for (const key in obj) {
                if (obj[key] && typeof obj[key] === 'object') {
                  for (const nestedKey in obj[key]) {
                    if (Array.isArray(obj[key][nestedKey]) && obj[key][nestedKey].length > 0) {
                      console.log(`Found array in response.${key}.${nestedKey}:`, obj[key][nestedKey]);
                      return obj[key][nestedKey];
                    }
                  }
                }
              }

              return null;
            };

            const arrayResult = findArrayInObject(response);
            if (arrayResult) {
              stepResult = arrayResult;
            } else {
              console.warn("Could not find any array in response, using entire response:", response);
              stepResult = response.results || response;
            }
          }
        } else if (activeStep === 2 && response.hooks) {
          stepResult = response.hooks;
        } else if (activeStep === 3 && response.intro_ctas) {
          stepResult = response.intro_ctas;
        } else if (activeStep === 4 && response.outlines) {
          stepResult = response.outlines;
        } else if (activeStep === 5 && response.draft) {
          stepResult = response.draft;
        } else if (activeStep === 6 && response.edited_script) {
          stepResult = response.edited_script;
        } else {
          // Fallback to results field or entire response
          stepResult = response.results || response;
        }

        console.log(`Mapped step result for step ${activeStep}:`, stepResult);

        setStepResults(prev => ({ ...prev, [activeStep]: stepResult }));

        if (response.error) {
          throw new Error(response.error);
        }
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      } else if (activeStep < steps.length - 1) {
        console.warn(`No response/API call for step ${activeStep + 1}, but not final step. Advancing anyway.`);
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }

    } catch (err: any) {
      console.error(`API Error during step ${activeStep + 1}:`, err);

      // Enhanced error handling with more specific messages
      let errorMessage = 'An error occurred processing this step.';

      // Check for API response error
      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      }
      // Check for network errors
      else if (err.message && err.message.includes('Network Error')) {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      }
      // Check for timeout errors
      else if (err.message && err.message.includes('timeout')) {
        errorMessage = 'Request timed out. The server is taking too long to respond.';
      }
      // Use error message if available
      else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    setError(null);
    if (activeStep === 1) setSelectedTitleIndex(null);
    // Step 2 doesn't need to reset anything (Create Hook)
    if (activeStep === 3) setSelectedHookIndex(null);
    if (activeStep === 4) setSelectedIntroIndex(null);
    if (activeStep === 7) setEditInput('');
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  // Show confirmation dialog before resetting
  const handleResetClick = () => {
    // If we're at the beginning or haven't started, no need to confirm
    if (activeStep === 0 && !sessionId) {
      handleReset();
      return;
    }

    setPendingAction('reset');
    setConfirmDialogOpen(true);
  };

  // Actual reset function
  const handleReset = () => {
    setActiveStep(0);
    setSessionId(null);
    setStepResults({});
    setError(null);
    setIsLoading(false);
    setSelectedBusinessContextId('');
    setContentIdea('');
    setSelectedTitleIndex(null);
    setSelectedHookIndex(null);
    setSelectedIntroIndex(null);
    setEditInput('');
    setSaveDialogOpen(false);
    setConfirmDialogOpen(false);
    setPendingAction(null);
  };

  // Handle confirmation dialog close
  const handleConfirmDialogClose = (confirmed: boolean) => {
    if (confirmed) {
      if (pendingAction === 'reset') {
        handleReset();
      } else if (pendingAction === 'navigate') {
        navigate('/content-library');
      }
    }

    setConfirmDialogOpen(false);
    setPendingAction(null);
  };

  // Handle opening the save dialog
  const handleOpenSaveDialog = () => {
    setSaveDialogOpen(true);
  };

  // Handle closing the save dialog
  const handleCloseSaveDialog = () => {
    setSaveDialogOpen(false);
  };

  // Handle saving content to library
  const handleSaveToLibrary = async (details: SaveContentPayload) => {
    try {
      setIsLoading(true);
      await saveContentItem(details);

      // Close the dialog
      setSaveDialogOpen(false);

      // Show success notification
      setNotification({
        open: true,
        message: 'Script saved to library successfully!',
        severity: 'success',
      });

      // Ask if user wants to navigate to content library
      setPendingAction('navigate');
      setConfirmDialogOpen(true);
    } catch (error: any) {
      console.error('Failed to save script to library:', error);

      // Show error notification
      setNotification({
        open: true,
        message: error.message || 'Failed to save script. Please try again.',
        severity: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle closing the notification
  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box sx={{ mt: 3, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <FormControl fullWidth required error={!selectedBusinessContextId && !!error}>
              <InputLabel id="business-context-select-label">Business Context</InputLabel>
              <Select
                labelId="business-context-select-label"
                id="business-context-select"
                value={selectedBusinessContextId}
                label="Business Context"
                onChange={(event: SelectChangeEvent) => {
                  setSelectedBusinessContextId(event.target.value as string);
                  setError(null);
                }}
                disabled={isLoading || businessContextsList.length === 0}
              >
                {businessContextsList.length === 0 && !isLoading && (
                  <MenuItem value="" disabled>No contexts available</MenuItem>
                )}
                {businessContextsList.map((context) => (
                  <MenuItem key={context.id} value={context.id}>
                    {context.name}
                  </MenuItem>
                ))}
              </Select>
              {businessContextsList.length === 0 && !isLoading && (
                <Typography variant="caption" color="textSecondary" sx={{mt: 1}}>
                  Go to Business Contexts page to create one.
                </Typography>
              )}
            </FormControl>
            <TextField
              fullWidth
              required
              label="Content Idea / Topic"
              value={contentIdea}
              onChange={(e) => {
                setContentIdea(e.target.value);
                setError(null);
              }}
              placeholder="e.g., How AI is changing video marketing"
              multiline
              rows={3}
              disabled={isLoading}
              error={!contentIdea.trim() && !!error}
            />
          </Box>
        );
      case 1:
        const brainstormResults = stepResults[0];

        // Show loading state or error message if no results
        if (!brainstormResults) {
          return (
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography color="textPrimary">
                {isLoading ? 'Generating brainstorm ideas...' : 'No brainstorm ideas generated yet. Please go back and try again.'}
              </Typography>
              {isLoading && <CircularProgress size={20} sx={{ ml: 1 }} />}
            </Box>
          );
        }

        // If we have results but they're not in the expected format, try to normalize them
        if (!Array.isArray(brainstormResults)) {
          console.error("Brainstorm results are not an array:", brainstormResults);

          // Try to extract an array if it's a nested object
          let normalizedResults: any[] = [];

          if (typeof brainstormResults === 'object' && brainstormResults !== null) {
            // Check if there's a titles array in the object
            if (Array.isArray(brainstormResults.titles)) {
              normalizedResults = brainstormResults.titles;
              console.log("Extracted titles array from object:", normalizedResults);
            }
            // Check if there's a results array in the object
            else if (Array.isArray(brainstormResults.results)) {
              normalizedResults = brainstormResults.results;
              console.log("Extracted results array from object:", normalizedResults);
            }
            // If it has any array property, use the first one found
            else {
              for (const key in brainstormResults) {
                if (Array.isArray(brainstormResults[key])) {
                  normalizedResults = brainstormResults[key];
                  console.log(`Extracted array from property ${key}:`, normalizedResults);
                  break;
                }
              }
            }
          }

          // If we found a valid array, use it
          if (normalizedResults.length > 0) {
            // Update the step results with the normalized array
            setStepResults(prev => ({
              ...prev,
              0: normalizedResults
            }));

            // Render the normalized results
            return (
              <FormControl component="fieldset" sx={{ mt: 2, mb: 1, width: '100%' }} disabled={isLoading}>
                <FormLabel component="legend">Select the Best Title Idea</FormLabel>
                <RadioGroup
                  aria-label="title selection"
                  name="title-radio-group"
                  value={selectedTitleIndex === null ? '' : selectedTitleIndex.toString()}
                  onChange={(e) => setSelectedTitleIndex(parseInt(e.target.value, 10))}
                >
                  {normalizedResults.map((idea: any, index: number) => (
                    <FormControlLabel
                      key={index}
                      value={index.toString()}
                      control={<Radio />}
                      label={
                        <Typography color="text.primary" sx={{ py: 0.5 }}>
                          {typeof idea === 'string' ? idea : idea.title || JSON.stringify(idea)}
                        </Typography>
                      }
                    />
                  ))}
                </RadioGroup>
              </FormControl>
            );
          }

          // If we couldn't normalize, show the error
          return (
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography color="error">
                Unexpected data format. Please contact support.
              </Typography>
              <Box sx={{ mt: 2, p: 2, border: '1px solid #ccc', borderRadius: 1, bgcolor: '#f5f5f5' }}>
                <Typography component="pre" sx={{ whiteSpace: 'pre-wrap', color: 'text.primary' }}>
                  {JSON.stringify(brainstormResults, null, 2)}
                </Typography>
              </Box>
            </Box>
          );
        }

        // Normal case - display radio buttons for selection
        return (
          <FormControl component="fieldset" sx={{ mt: 2, mb: 1, width: '100%' }} disabled={isLoading}>
            <FormLabel component="legend">Select the Best Title Idea</FormLabel>
            <RadioGroup
              aria-label="title selection"
              name="title-radio-group"
              value={selectedTitleIndex === null ? '' : selectedTitleIndex.toString()}
              onChange={(e) => setSelectedTitleIndex(parseInt(e.target.value, 10))}
            >
              {brainstormResults.map((idea: any, index: number) => (
                <FormControlLabel
                  key={index}
                  value={index.toString()}
                  control={<Radio />}
                  label={
                    <Typography color="text.primary" sx={{ py: 0.5 }}>
                      {typeof idea === 'string' ? idea : idea.title || `Idea ${index + 1}`}
                    </Typography>
                  }
                />
              ))}
            </RadioGroup>
          </FormControl>
        );
      case 2:
        // Display the selected title and prepare for hook creation
        const selectedTitle = stepResults[1];
        return (
          <Box sx={{ mt: 2, mb: 1 }}>
            <Typography variant="h6" gutterBottom>Selected Title</Typography>
            <Box sx={{
              p: 2,
              border: '1px solid',
              borderColor: 'primary.light',
              borderRadius: '4px',
              bgcolor: 'primary.lightest',
              mb: 3
            }}>
              <Typography variant="body1" fontWeight="bold">
                {typeof selectedTitle === 'string' ? selectedTitle : JSON.stringify(selectedTitle)}
              </Typography>
            </Box>

            <Typography variant="body1" gutterBottom>
              Now we'll create hook options for your video based on this title. A hook is the first 5-10 seconds of your video that grabs the viewer's attention.
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Click Next to generate hook options.
            </Typography>
          </Box>
        );

      case 3:
        const hookResults = stepResults[2];
        if (!hookResults || !Array.isArray(hookResults)) {
          return (
            <Typography sx={{ mt: 2, mb: 1 }}>
              Generating hook options... (or previous step failed/returned unexpected data)
              {isLoading && <CircularProgress size={20} sx={{ ml: 1 }} />}
            </Typography>
          );
        }
        return (
          <FormControl component="fieldset" sx={{ mt: 2, mb: 1, width: '100%' }} disabled={isLoading}>
            <FormLabel component="legend">Select the Best Hook</FormLabel>
            <RadioGroup
              aria-label="hook selection"
              name="hook-radio-group"
              value={selectedHookIndex === null ? '' : selectedHookIndex.toString()}
              onChange={(e) => setSelectedHookIndex(parseInt(e.target.value, 10))}
            >
              {hookResults.map((hook: any, index: number) => (
                <FormControlLabel
                  key={index}
                  value={index.toString()}
                  control={<Radio />}
                  label={typeof hook === 'string' ? hook : hook.text || hook.title || `Hook Option ${index + 1}`}
                />
              ))}
            </RadioGroup>
          </FormControl>
        );

      case 4:
        const introResults = stepResults[3];
        if (!introResults || !Array.isArray(introResults)) {
          return (
            <Typography sx={{ mt: 2, mb: 1 }}>
              Generating intro options... (or previous step failed/returned unexpected data)
              {isLoading && <CircularProgress size={20} sx={{ ml: 1 }} />}
            </Typography>
          );
        }
        return (
          <FormControl component="fieldset" sx={{ mt: 2, mb: 1, width: '100%' }} disabled={isLoading}>
            <FormLabel component="legend">Select the Best Intro Option</FormLabel>
            <RadioGroup
              aria-label="intro selection"
              name="intro-radio-group"
              value={selectedIntroIndex === null ? '' : selectedIntroIndex.toString()}
              onChange={(e) => setSelectedIntroIndex(parseInt(e.target.value, 10))}
            >
              {introResults.map((intro: any, index: number) => (
                <FormControlLabel
                  key={index}
                  value={index.toString()}
                  control={<Radio />}
                  label={typeof intro === 'string' ? intro : intro.text || intro.title || `Intro Option ${index + 1}`}
                />
              ))}
            </RadioGroup>
          </FormControl>
        );
      case 5:
        const outlineResult = stepResults[4];
        if (outlineResult) {
          return (
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="h6">Generated Outline:</Typography>
              <Typography component="pre" sx={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word', mt: 1, p: 1, border: '1px solid lightgrey' }}>
                {typeof outlineResult === 'string' ? outlineResult : JSON.stringify(outlineResult, null, 2)}
              </Typography>
            </Box>
          );
        } else {
          return (
            <Typography sx={{ mt: 2, mb: 1 }}>
              Ready to generate the script outline based on your selected hook and intro. Click Next.
              {isLoading && <CircularProgress size={20} sx={{ ml: 1 }} />}
            </Typography>
          );
        }
      case 6:
        const draftResult = stepResults[5];
        if (draftResult) {
          return (
            <Box sx={{ mt: 2, mb: 1 }}>
              <Typography variant="h6">Generated Draft:</Typography>
              <Typography component="pre" sx={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word', mt: 1, p: 1, border: '1px solid lightgrey' }}>
                {typeof draftResult === 'string' ? draftResult : JSON.stringify(draftResult, null, 2)}
              </Typography>
            </Box>
          );
        } else {
          return (
            <Typography sx={{ mt: 2, mb: 1 }}>
              Ready to draft the script based on the generated outline. Click Next.
              {isLoading && <CircularProgress size={20} sx={{ ml: 1 }} />}
            </Typography>
          );
        }
      case 7:
        const latestDraft = stepResults[6] ?? stepResults[5];
        return (
          <Box sx={{ mt: 2, mb: 1 }}>
            <Typography variant="h6" gutterBottom>Edit & Polish Script</Typography>
            {latestDraft ? (
              <Typography component="pre" sx={{ whiteSpace: 'pre-wrap', wordWrap: 'break-word', mt: 1, mb: 2, p: 2, border: '1px solid lightgrey', maxHeight: '300px', overflowY: 'auto' }}>
                {typeof latestDraft === 'string' ? latestDraft : JSON.stringify(latestDraft, null, 2)}
              </Typography>
            ) : (
              <Alert severity="warning" sx={{ mb: 2 }}>No script draft available to edit.</Alert>
            )}
            <TextField
              fullWidth
              label="Style Preferences"
              multiline
              rows={3}
              variant="outlined"
              value={editInput}
              onChange={(e) => setEditInput(e.target.value)}
              disabled={isLoading || !latestDraft}
              placeholder="e.g., Make the tone more enthusiastic, shorten the second paragraph."
              sx={{ mb: 1 }}
              helperText="Enter style preferences for the AI to apply to your script"
            />
            <Button
              variant="contained"
              onClick={handleApplyEdit}
              disabled={isLoading || !editInput || !latestDraft}
              size="small"
            >
              {isLoading ? <CircularProgress size={20} /> : 'Apply Edit'}
            </Button>
            <Typography variant="caption" display="block" sx={{mt: 1}}>
              Apply edits or click Finish to complete.
            </Typography>
          </Box>
        );
      default:
        return <StepContentPlaceholder stepIndex={step} />;
    }
  };

  const isNextDisabled = () => {
    if (isLoading) return true;
    if (activeStep === 0 && !selectedBusinessContextId && !contentIdea && !sessionId) return true;
    if (activeStep === 1 && selectedTitleIndex === null) return true;
    // Step 2 (Create Hook) doesn't need selection, just session
    if (activeStep === 2 && !sessionId) return true;
    if (activeStep === 3 && selectedHookIndex === null) return true;
    if (activeStep === 4 && selectedIntroIndex === null) return true;
    if ((activeStep === 5 || activeStep === 6) && !sessionId) return true;
    return false;
  };

  return (
    <Box sx={{ width: '100%', p: 3 }}>
      <Typography variant="h4" gutterBottom component="div" sx={{ mb: 3 }}>
        AI Script Writing Wizard
      </Typography>

      {isLoading && <CircularProgress sx={{ mb: 2 }} />}

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Stepper activeStep={activeStep}>
        {steps.map((label) => {
          const stepProps: { completed?: boolean } = {};
          const labelProps: {
            optional?: React.ReactNode;
          } = {};
          // Add optional step logic later if needed
          // if (isStepOptional(index)) {
          //   labelProps.optional = (
          //     <Typography variant="caption">Optional</Typography>
          //   );
          // }
          // Add skipped step logic later if needed
          // if (isStepSkipped(index)) {
          //   stepProps.completed = false;
          // }
          return (
            <Step key={label} {...stepProps}>
              <StepLabel {...labelProps}>{label}</StepLabel>
            </Step>
          );
        })}
      </Stepper>
      {activeStep === steps.length ? (
        <React.Fragment>
          <Alert severity="success" sx={{ mt: 2, mb: 2 }}>
            <AlertTitle>Success!</AlertTitle>
            All steps completed - your script is ready to save to the Content Library.
          </Alert>

          {stepResults[steps.length - 1] && (
            <Box sx={{
              mt: 2,
              p: 3,
              border: '1px solid',
              borderColor: 'success.light',
              bgcolor: 'success.lightest',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
            }}>
              <Typography variant="h6" color="text.primary" gutterBottom>Final Script</Typography>
              <Typography component="pre" sx={{
                whiteSpace: 'pre-wrap',
                wordWrap: 'break-word',
                color: 'text.primary',
                mt: 1,
                p: 2,
                backgroundColor: 'white',
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: '4px',
                maxHeight: '400px',
                overflowY: 'auto',
                fontSize: '0.9rem',
                lineHeight: 1.6
              }}>
                {typeof stepResults[steps.length - 1] === 'string'
                  ? stepResults[steps.length - 1]
                  : JSON.stringify(stepResults[steps.length - 1], null, 2)}
              </Typography>
            </Box>
          )}

          <Box sx={{ display: 'flex', flexDirection: 'row', pt: 3, gap: 2 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              onClick={handleOpenSaveDialog}
              size="large"
            >
              Save to Library
            </Button>
            <Button
              variant="outlined"
              onClick={handleResetClick}
              size="large"
            >
              Start Over
            </Button>
          </Box>
        </React.Fragment>
      ) : (
        <React.Fragment>
          {renderStepContent(activeStep)}

          {activeStep > 0 && stepResults[activeStep - 1] && (
            <Box sx={{ mt: 2, p: 2, border: '1px dashed grey', bgcolor: 'grey.100', borderRadius: '4px' }}>
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Previous Step: {steps[activeStep - 1]}
              </Typography>

              {/* Format the previous step results based on the step type */}
              {activeStep === 1 && Array.isArray(stepResults[0]) ? (
                <Typography variant="body2" color="text.primary">
                  Selected Title: <strong>{typeof stepResults[0][selectedTitleIndex as number] === 'string'
                    ? stepResults[0][selectedTitleIndex as number]
                    : JSON.stringify(stepResults[0][selectedTitleIndex as number])}</strong>
                </Typography>
              ) : activeStep === 2 && Array.isArray(stepResults[1]) ? (
                <Typography variant="body2" color="text.primary">
                  Selected Hook: <strong>{typeof stepResults[1][selectedHookIndex as number] === 'string'
                    ? stepResults[1][selectedHookIndex as number]
                    : JSON.stringify(stepResults[1][selectedHookIndex as number])}</strong>
                </Typography>
              ) : activeStep === 3 && Array.isArray(stepResults[2]) ? (
                <Typography variant="body2" color="text.primary">
                  Selected Intro: <strong>{typeof stepResults[2][selectedIntroIndex as number] === 'string'
                    ? stepResults[2][selectedIntroIndex as number]
                    : JSON.stringify(stepResults[2][selectedIntroIndex as number])}</strong>
                </Typography>
              ) : (
                <Typography component="pre" sx={{
                  whiteSpace: 'pre-wrap',
                  wordWrap: 'break-word',
                  color: 'text.primary',
                  mt: 1,
                  fontSize: '0.875rem',
                  maxHeight: '150px',
                  overflowY: 'auto',
                  backgroundColor: 'rgba(0,0,0,0.03)',
                  padding: '8px',
                  borderRadius: '4px'
                }}>
                  {typeof stepResults[activeStep - 1] === 'string'
                    ? stepResults[activeStep - 1]
                    : JSON.stringify(stepResults[activeStep - 1], null, 2)}
                </Typography>
              )}
            </Box>
          )}

          <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
            <Button
              color="inherit"
              disabled={activeStep === 0 || isLoading}
              onClick={handleBack}
              sx={{ mr: 1 }}
            >
              Back
            </Button>
            <Box sx={{ flex: '1 1 auto' }} />
            <Button onClick={handleNext} disabled={isNextDisabled()}>
              {isLoading ? <CircularProgress size={24} /> : (activeStep === steps.length - 1 ? 'Finish' : 'Next')}
            </Button>
          </Box>
        </React.Fragment>
      )}
      {/* Save to Library Dialog */}
      <SaveToLibraryDialog
        open={saveDialogOpen}
        onClose={handleCloseSaveDialog}
        onSave={handleSaveToLibrary}
        contentPreview={typeof stepResults[steps.length - 1] === 'string'
          ? stepResults[steps.length - 1]
          : JSON.stringify(stepResults[steps.length - 1], null, 2)}
        initialTitle={contentIdea ? `Script: ${contentIdea.substring(0, 50)}${contentIdea.length > 50 ? '...' : ''}` : 'AI Generated Script'}
      />

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        message={notification.message}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      />

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={() => handleConfirmDialogClose(false)}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
      >
        <DialogTitle id="confirm-dialog-title">
          {pendingAction === 'reset' ? 'Reset Wizard?' : 'View in Content Library?'}
        </DialogTitle>
        <DialogContent>
          <Typography id="confirm-dialog-description">
            {pendingAction === 'reset'
              ? 'This will reset all progress in the wizard. Are you sure you want to start over?'
              : 'Your script has been saved. Would you like to view it in the Content Library?'}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => handleConfirmDialogClose(false)} color="primary">
            {pendingAction === 'reset' ? 'Cancel' : 'Stay Here'}
          </Button>
          <Button onClick={() => handleConfirmDialogClose(true)} color="primary" variant="contained" autoFocus>
            {pendingAction === 'reset' ? 'Reset' : 'Go to Content Library'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WizardPage;