import React, { useMemo } from "react";
import { Experimental_CssVarsProvider as CssVarsProvider } from "@mui/material/styles";
import { CssBaseline } from "@mui/material";
import { useSelector } from "react-redux";
import { selectDarkMode } from "../store/ui/uiSlice";
import { getTheme } from "../styles/theme";

interface ThemeProviderProps {
  children: React.ReactNode;
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const darkMode = useSelector(selectDarkMode);
  const currentMode = darkMode ? "dark" : "light";

  // Generate the theme. getTheme now returns a theme configured for CSS Variables.
  const theme = useMemo(
    () => getTheme(currentMode),
    [currentMode]
  );

  return (
    <CssVarsProvider theme={theme} defaultMode={currentMode}>
      <CssBaseline />
      {children}
    </CssVarsProvider>
  );
};

export default ThemeProvider;
