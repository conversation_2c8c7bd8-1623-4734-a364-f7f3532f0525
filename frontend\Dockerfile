# Build stage
FROM node:16-alpine as build

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm ci

# Copy the rest of the code
COPY . .

# Build the app
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built files from build stage
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy entrypoint script to container root
COPY entrypoint.sh /entrypoint.sh

# Copy template file to nginx html directory
COPY public/env-config.js.template /usr/share/nginx/html/env-config.js.template

# Make entrypoint script executable
RUN chmod +x /entrypoint.sh

# Expose port
EXPOSE 80

# Use entrypoint script and start nginx
ENTRYPOINT ["/entrypoint.sh"]