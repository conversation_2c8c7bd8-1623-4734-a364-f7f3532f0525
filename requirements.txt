# Core Framework - FastAPI (Migration Complete!)
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic-settings>=2.0.0
asgiref>=3.6.0

# Supabase
supabase>=2.2.0
postgrest>=0.19.0
gotrue>=2.0.0
storage3>=0.10.0
supafunc>=0.9.0
psycopg2-binary>=2.9.0
asyncpg>=0.29.0  # Async PostgreSQL adapter for FastAPI

# API Integration
httpx>=0.25.0  # Async HTTP client for FastAPI
requests>=2.31.0  # Keep for non-async operations that haven't been migrated
google-api-python-client>=2.0.0
openai>=1.10.0 # Updated openai for requesty compatibility
youtube-transcript-api>=0.6.3

# Testing
pytest>=7.4.0
pytest-cov>=4.1.0

# Utilities
python-dotenv>=1.0.0 # Added for env var loading, including requesty key
pydantic[email]>=2.0.0 # Includes email-validator for EmailStr
email-validator>=1.1.0 # Re-added for Pydantic EmailStr
black>=23.0.0
flake8>=6.1.0
numpy>=1.20.0 # Added for YouTube outlier calculation
tenacity>=8.2.0 # Added for retry logic in AI generation

# Deployment - FastAPI
# gunicorn>=21.2.0  # Removed - using uvicorn for FastAPI

# Development
python-jose>=3.3.0
pytest-asyncio

# New dependency
cachetools