import React, { useState } from "react";
import { Button, Fab, Tooltip, useMediaQuery } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import FeedbackIcon from "@mui/icons-material/Feedback";
import FeedbackDialog from "./FeedbackDialog";

interface FeedbackButtonProps {
  variant?: "fab" | "button";
  position?: "fixed" | "static";
  buttonText?: string;
}

const FeedbackButton: React.FC<FeedbackButtonProps> = ({
  variant = "fab",
  position = "fixed",
  buttonText = "Feedback",
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleOpenDialog = () => {
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  if (variant === "fab") {
    return (
      <>
        <Tooltip title="Give Feedback" arrow>
          <Fab
            color="primary"
            aria-label="feedback"
            onClick={handleOpenDialog}
            sx={{
              position: position === "fixed" ? "fixed" : "static",
              bottom: position === "fixed" ? 24 : "auto",
              right: position === "fixed" ? 24 : "auto",
              zIndex: theme.zIndex.speedDial,
            }}
          >
            <FeedbackIcon />
          </Fab>
        </Tooltip>
        <FeedbackDialog open={dialogOpen} onClose={handleCloseDialog} />
      </>
    );
  }

  return (
    <>
      <Button
        variant="outlined"
        color="primary"
        startIcon={<FeedbackIcon />}
        onClick={handleOpenDialog}
        size={isMobile ? "small" : "medium"}
      >
        {buttonText}
      </Button>
      <FeedbackDialog open={dialogOpen} onClose={handleCloseDialog} />
    </>
  );
};

export default FeedbackButton;
