"""
Activity routes for FastAPI.
Migrated from Flask app/activity/routes.py to use FastAPI dependency injection.
"""

import logging
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, Query, Depends
from pydantic import BaseModel, Field

from app.auth.dependencies import CurrentUserType, CurrentUserIdType
from app.dependencies import ActivityLogRepositoryType

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/activity", tags=["activity"])


class ActivityLogRequest(BaseModel):
    """Request model for logging activity."""
    activity_type: str = Field(..., description="Type of activity (e.g., 'login', 'create_content')")
    title: str = Field(..., description="Short title describing the activity")
    description: Optional[str] = Field(None, description="Optional longer description")
    resource_type: Optional[str] = Field(None, description="Type of related resource")
    resource_id: Optional[str] = Field(None, description="ID of related resource")
    link_path: Optional[str] = Field(None, description="Path to view the related resource")


class ActivityResponse(BaseModel):
    """Response model for activity data."""
    id: str
    user_id: str
    activity_type: str
    title: str
    description: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    link_path: Optional[str] = None
    created_at: str


@router.get("/", response_model=List[ActivityResponse])
async def get_user_activities(
    current_user_id: CurrentUserIdType,
    activity_repo: ActivityLogRepositoryType,
    limit: int = Query(default=10, ge=1, le=100, description="Number of activities to return"),
    offset: int = Query(default=0, ge=0, description="Number of activities to skip")
) -> List[ActivityResponse]:
    """
    Get recent activities for the current user.
    Returns the most recent activities with pagination.
    """
    try:
        activities_data = await activity_repo.get_user_activities(
            user_id=current_user_id, 
            limit=limit, 
            offset=offset
        )
        return activities_data
    except Exception as e:
        logger.error(f"Error fetching user activities: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Failed to fetch activities",
                "message": str(e)
            }
        )


@router.post("/", response_model=ActivityResponse, status_code=201)
async def log_activity(
    activity_request: ActivityLogRequest,
    current_user_id: CurrentUserIdType,
    activity_repo: ActivityLogRepositoryType
) -> ActivityResponse:
    """
    Log a new user activity.
    Required fields: activity_type, title
    Optional fields: description, resource_id, resource_type, link_path
    """
    try:
        activity = await activity_repo.log_activity(
            user_id=current_user_id,
            activity_type=activity_request.activity_type,
            title=activity_request.title,
            description=activity_request.description,
            resource_type=activity_request.resource_type,
            resource_id=activity_request.resource_id,
            link_path=activity_request.link_path,
        )
        
        if not activity:
            raise HTTPException(
                status_code=500,
                detail={
                    "success": False,
                    "error": "Failed to log activity in Supabase"
                }
            )
        
        return activity
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error logging user activity: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Failed to log activity",
                "message": str(e)
            }
        )


@router.get("/summary")
async def get_activity_summary(
    current_user_id: CurrentUserIdType,
    activity_repo: ActivityLogRepositoryType,
    from_date: Optional[str] = Query(None, description="ISO format date to filter from")
) -> Dict[str, Any]:
    """Get summary of user activities by type."""
    try:
        summary = await activity_repo.get_activity_counts_by_type(
            current_user_id, from_date
        )
        return summary
    except Exception as e:
        logger.error(f"Error getting activity summary: {e}")
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": "Failed to get activity summary",
                "message": str(e)
            }
        )
