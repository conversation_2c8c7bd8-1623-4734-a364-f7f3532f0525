from datetime import datetime, timezone
import math
import statistics
import numpy as np
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

# --- Constants for Outlier Calculation ---
MIN_VIDEO_AGE_DAYS = 1  # Minimum age in days for a video to be scored reliably
BASELINE_REF_DAYS = (
    30  # Reference day for channel baseline scale (Avg_Views_at_30_days)
)
OUTLIER_SCORE_V_REF = 100000  # Reference view count for ViewFactor scaling
OUTLIER_SCORE_V_MIN = 100  # Minimum view count threshold for ViewFactor scaling
OUTLIER_SCORE_MULTIPLE_LOG_FACTOR = (
    -2.3
)  # Factor for BaseScore_Multiple calculation (approx log(0.1))
OUTLIER_SCORE_VIEWFACTOR_POWER = 2  # Power for ViewFactor calculation

# --- New Outlier Calculation Logic (Based on User Specification) ---


def _parse_iso_datetime(date_str: Optional[str]) -> Optional[datetime]:
    """Safely parse ISO 8601 datetime strings, handling 'Z' suffix."""
    if not date_str:
        return None
    try:
        if date_str.endswith("Z"):
            date_str = date_str[:-1] + "+00:00"
        return datetime.fromisoformat(date_str)
    except ValueError:
        logger.warning(f"Could not parse datetime string: {date_str}")
        return None


def _estimate_channel_baseline(
    historical_videos_data: List[Dict[str, Any]],
) -> Dict[str, Optional[float]]:
    """
    Step 1: Establish the Channel's Time-Adjusted Average View Curve.

    Args:
        historical_videos_data: List of dictionaries, each containing at least
                                'publishedAt' (ISO string) and 'viewCount'.

    Returns:
        A dictionary containing 'p_channel' and 'avg_views_at_30_days', or None if calculation fails.
    """
    logger.info(
        f"Estimating channel baseline from {len(historical_videos_data)} videos."
    )
    valid_videos = []
    current_time_utc = datetime.now(timezone.utc)

    for video in historical_videos_data:
        publish_date = _parse_iso_datetime(video.get("publishedAt"))
        views = video.get("viewCount")

        if publish_date and views is not None and views > 0:
            age_days = (current_time_utc - publish_date).total_seconds() / (
                60 * 60 * 24
            )
            # Only consider videos older than the minimum age for baseline stability
            if age_days >= MIN_VIDEO_AGE_DAYS:
                valid_videos.append({"age_days": age_days, "views": views})

    if len(valid_videos) < 5:  # Need at least a few points for meaningful regression
        logger.warning(
            f"Insufficient valid historical videos ({len(valid_videos)}) for baseline calculation."
        )
        return {"p_channel": None, "avg_views_at_30_days": None}

    # --- Outlier Filtering (Optional but recommended) ---
    # Filter based on views relative to median views at a certain age (e.g., 30 days)
    # For simplicity here, we'll filter based on raw views relative to median
    all_views = [v["views"] for v in valid_videos]
    median_views = statistics.median(all_views)
    if median_views > 0:
        filtered_videos = [
            v
            for v in valid_videos
            if 0.1 * median_views <= v["views"] <= 10 * median_views
        ]
        if len(filtered_videos) < 3:  # Ensure enough points remain after filtering
            logger.warning(
                "Few videos remain after outlier filtering, using original set."
            )
            filtered_videos = valid_videos
    else:
        filtered_videos = valid_videos  # Avoid filtering if median is 0

    if len(filtered_videos) < 2:  # Need at least 2 points for regression
        logger.warning(
            f"Insufficient videos ({len(filtered_videos)}) after filtering for regression."
        )
        return {"p_channel": None, "avg_views_at_30_days": None}

    # --- Power Law Curve Fitting (Views = k * Age^p) using Log-Log Regression ---
    # log(Views) = log(k) + p * log(Age)
    log_age = np.log([v["age_days"] for v in filtered_videos])
    log_views = np.log([v["views"] for v in filtered_videos])

    try:
        # Fit linear model: log_views = intercept + slope * log_age
        slope, intercept = np.polyfit(log_age, log_views, 1)
        p_channel = slope
        log_k = intercept
        median_k = math.exp(log_k)  # k represents the projected views at age=1 day

        # Estimate average views at the reference time (e.g., 30 days) using the fitted curve
        # Using median_k * (BASELINE_REF_DAYS ** p_channel)
        # Alternatively, calculate median views around 30 days if enough data exists
        views_around_30_days = [
            v["views"]
            for v in filtered_videos
            if 0.8 * BASELINE_REF_DAYS <= v["age_days"] <= 1.2 * BASELINE_REF_DAYS
        ]
        if len(views_around_30_days) >= 3:
            avg_views_at_30_days = statistics.median(views_around_30_days)
        else:
            # Fallback to using the curve if not enough data near 30 days
            avg_views_at_30_days = median_k * (BASELINE_REF_DAYS**p_channel)

        # Ensure non-zero baseline
        avg_views_at_30_days = max(1.0, avg_views_at_30_days)  # Avoid zero or negative

        logger.info(
            f"Calculated baseline: p_channel={p_channel:.4f}, avg_views_at_30_days={avg_views_at_30_days:.2f}"
        )
        return {"p_channel": p_channel, "avg_views_at_30_days": avg_views_at_30_days}

    except Exception as e:
        logger.error(f"Error during curve fitting: {e}")
        return {"p_channel": None, "avg_views_at_30_days": None}


def _calculate_time_adjusted_multiple(
    video_data: Dict[str, Any], p_channel: float, avg_views_at_30_days: float
) -> Optional[float]:
    """
    Step 2: Calculate the Video's Time-Adjusted Multiple.

    Args:
        video_data: Dictionary for the video being scored (needs 'publishedAt', 'viewCount').
        p_channel: The channel's typical view curve exponent.
        avg_views_at_30_days: The channel's typical view count at 30 days.

    Returns:
        The time-adjusted multiple, or None if calculation fails.
    """
    publish_date = _parse_iso_datetime(video_data.get("publishedAt"))
    current_views = video_data.get("viewCount")

    if not publish_date or current_views is None:
        logger.warning("Missing publish date or view count for multiple calculation.")
        return None

    current_time_utc = datetime.now(timezone.utc)
    video_age_days = (current_time_utc - publish_date).total_seconds() / (60 * 60 * 24)

    # Handle minimum age
    if video_age_days < MIN_VIDEO_AGE_DAYS:
        logger.info(
            f"Video age ({video_age_days:.1f} days) is less than minimum ({MIN_VIDEO_AGE_DAYS} days). Skipping multiple calc."
        )
        return None  # Or assign a default/pending score indicator

    # Calculate Expected Channel Views at the video's current age
    try:
        # Ensure BASELINE_REF_DAYS is not zero
        if BASELINE_REF_DAYS <= 0:
            logger.error("BASELINE_REF_DAYS must be positive.")
            return None

        # Using the formula: Expected = Avg_Views_at_30_days * (Age / 30) ^ p_channel
        expected_channel_views = avg_views_at_30_days * math.pow(
            video_age_days / BASELINE_REF_DAYS, p_channel
        )
        expected_channel_views = max(
            1.0, expected_channel_views
        )  # Avoid division by zero

        time_adjusted_multiple = current_views / expected_channel_views
        logger.info(
            f"Video Age: {video_age_days:.1f} days, Current Views: {current_views}, Expected Views: {expected_channel_views:.2f}, Multiple: {time_adjusted_multiple:.2f}"
        )
        return time_adjusted_multiple

    except (OverflowError, ValueError) as e:
        logger.error(f"Math error calculating expected views or multiple: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error calculating multiple: {e}")
        return None


def _calculate_final_outlier_score(
    time_adjusted_multiple: float, video_current_views: int
) -> int:
    """
    Step 3: Calculate the Final Outlier Score (0-100).

    Args:
        time_adjusted_multiple: The calculated time-adjusted multiple.
        video_current_views: The current absolute view count of the video.

    Returns:
        The final outlier score (0-100).
    """
    if time_adjusted_multiple <= 0:  # Handle underperforming or error cases
        return 0

    # 1. Calculate Base Score (from Multiple)
    try:
        # BaseScore = 100 * (1 - exp(OUTLIER_SCORE_MULTIPLE_LOG_FACTOR * log10(max(1, Multiple))))
        log10_multiple = math.log10(max(1.0, time_adjusted_multiple))
        base_score_multiple = 100 * (
            1 - math.exp(OUTLIER_SCORE_MULTIPLE_LOG_FACTOR * log10_multiple)
        )
        # Ensure base score is not negative due to floating point issues near multiple=1
        base_score_multiple = max(0.0, base_score_multiple)
    except ValueError as e:
        logger.error(
            f"Error calculating BaseScore_Multiple (Multiple: {time_adjusted_multiple}): {e}"
        )
        base_score_multiple = 0.0  # Default to 0 on error

    # 2. Calculate View Factor (from Absolute Views)
    try:
        # ViewFactor = (max(0, min(1, log10(max(V_min, Views)) / log10(V_ref)))) ^ Power
        log_views = math.log10(max(OUTLIER_SCORE_V_MIN, video_current_views))
        log_ref = math.log10(OUTLIER_SCORE_V_REF)
        if log_ref <= 0:  # Avoid division by zero if V_ref is invalid
            logger.error("OUTLIER_SCORE_V_REF must be > 1.")
            view_factor = 0.0
        else:
            normalized_log_views = max(0.0, min(1.0, log_views / log_ref))
            view_factor = math.pow(normalized_log_views, OUTLIER_SCORE_VIEWFACTOR_POWER)
    except ValueError as e:
        logger.error(
            f"Error calculating ViewFactor (Views: {video_current_views}): {e}"
        )
        view_factor = 0.0  # Default to 0 on error

    # 3. Calculate Final Score
    final_outlier_score = base_score_multiple * view_factor
    final_outlier_score = round(
        max(0.0, min(100.0, final_outlier_score))
    )  # Clamp and round

    logger.info(
        f"Multiple: {time_adjusted_multiple:.2f} -> BaseScore: {base_score_multiple:.2f}, Views: {video_current_views} -> ViewFactor: {view_factor:.2f} => Final Score: {final_outlier_score}"
    )
    return int(final_outlier_score)


def get_performance_category(score: int) -> str:
    """
    Get the performance category based on the Final Outlier Score.
    (Thresholds updated based on user specification)

    Args:
        score: Final Outlier Score (0-100)

    Returns:
        str: Performance category string
    """
    if score >= 91:
        return "Outlier Gold"
    elif score >= 81:
        return "Exceptional Outlier"
    elif score >= 71:
        return "Strong Outlier"
    elif score >= 51:
        return "Mild Outlier"
    elif score >= 31:
        return "Average"
    elif score >= 11:
        return "Underperforming"
    else:  # 0-10
        return "Extremely Underperforming"


def calculate_video_outlier_score(
    video_data: Dict[str, Any], historical_videos_data: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Orchestrates the calculation of the YouTube video outlier score.

    Args:
        video_data: Dictionary for the video being scored.
        historical_videos_data: List of dictionaries for channel's historical videos.

    Returns:
        Dictionary containing the analysis results:
        - Final_Outlier_Score (int, 0-100)
        - Performance_Category (str)
        - Time_Adjusted_Multiple (float, optional)
        - p_channel (float, optional)
        - avg_views_at_30_days (float, optional)
        - error (str, optional)
    """
    results = {
        "Final_Outlier_Score": 0,
        "Performance_Category": "Calculation Pending",
        "Time_Adjusted_Multiple": None,
        "p_channel": None,
        "avg_views_at_30_days": None,
        "error": None,
    }

    # Ensure video_data has minimum required fields
    if (
        not video_data
        or "publishedAt" not in video_data
        or "viewCount" not in video_data
    ):
        results["error"] = (
            "Target video data is incomplete (missing publishedAt or viewCount)."
        )
        results["Performance_Category"] = get_performance_category(0)
        return results

    # Step 1: Estimate Channel Baseline
    baseline = _estimate_channel_baseline(historical_videos_data)
    p_channel = baseline.get("p_channel")
    avg_views_at_30_days = baseline.get("avg_views_at_30_days")

    results["p_channel"] = p_channel
    results["avg_views_at_30_days"] = avg_views_at_30_days

    if p_channel is None or avg_views_at_30_days is None:
        results["error"] = (
            "Failed to establish channel baseline (insufficient historical data or calculation error)."
        )
        results["Performance_Category"] = get_performance_category(0)
        return results

    # Step 2: Calculate Time-Adjusted Multiple
    time_adjusted_multiple = _calculate_time_adjusted_multiple(
        video_data, p_channel, avg_views_at_30_days
    )
    results["Time_Adjusted_Multiple"] = time_adjusted_multiple

    if time_adjusted_multiple is None:
        # Could be due to video age < MIN_VIDEO_AGE_DAYS or calculation error
        if results.get("error") is None:  # Don't overwrite baseline error
            publish_date = _parse_iso_datetime(video_data.get("publishedAt"))
            if publish_date:
                current_time_utc = datetime.now(timezone.utc)
                video_age_days = (current_time_utc - publish_date).total_seconds() / (
                    60 * 60 * 24
                )
                if video_age_days < MIN_VIDEO_AGE_DAYS:
                    results["error"] = (
                        f"Video too young ({video_age_days:.1f} days) for reliable score."
                    )
                else:
                    results["error"] = "Failed to calculate time-adjusted multiple."
            else:
                results["error"] = (
                    "Failed to calculate time-adjusted multiple (missing video data)."
                )
        results["Performance_Category"] = get_performance_category(0)
        return results

    # Step 3: Calculate Final Score
    current_views = video_data.get("viewCount", 0)
    final_score = _calculate_final_outlier_score(time_adjusted_multiple, current_views)
    results["Final_Outlier_Score"] = final_score
    results["Performance_Category"] = get_performance_category(final_score)

    logger.info(
        f"Final outlier score calculated for video {video_data.get('video_id', 'N/A')}: {final_score}"
    )
    return results


# --- Old/Legacy Functions (Commented Out) ---
# def analyze_video_performance(views: int, likes: int, comments: int, subscribers: int) -> Dict[str, float]:
#     """
#     Analyze YouTube video performance metrics and calculate various scores.
#     ... (rest of old function) ...
#     """
#     # ... (old implementation) ...
#     pass

# def calculate_glow_ai_score(
#     video_views: int,
#     channel_subscribers: int,
#     view_multiple: float,
#     channel_avg_views: int,
# ) -> float:
#     """
#     Calculate the glowAI performance score (0-100) based on:
#     ... (rest of old function) ...
#     """
#     # ... (old implementation) ...
#     pass

# def calculate_time_adjusted_average(
#     videos: List[Dict[str, Any]], target_video: Dict[str, Any]
# ) -> float:
#     """
#     Calculate the time-adjusted average views for a channel based on video age.
#     ... (rest of old function) ...
#     """
#     # ... (old implementation) ...
#     pass

# def analyze_channel_videos(videos):
#     """
#     Analyze a list of videos and identify outliers based on the glowAI algorithm.
#     ... (rest of old function) ...
#     """
#     # ... (old implementation) ...
#     pass
