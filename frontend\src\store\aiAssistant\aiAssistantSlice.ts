import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { RootState } from "../index";

// Define types
interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
}

interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  contentFormat: string;
  youtubeVideoId?: string;
  businessContextId?: string;
  createdAt: string;
  updatedAt: string;
}

interface AIAssistantState {
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: AIAssistantState = {
  sessions: [],
  currentSession: null,
  isLoading: false,
  error: null,
};

// Slice
const aiAssistantSlice = createSlice({
  name: "aiAssistant",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Add extra reducers when implementing API calls
  },
});

// Actions
export const { clearError } = aiAssistantSlice.actions;

// Selectors
export const selectAIAssistant = (state: RootState) => state.aiAssistant;
export const selectChatSessions = (state: RootState) =>
  state.aiAssistant.sessions;
export const selectCurrentSession = (state: RootState) =>
  state.aiAssistant.currentSession;
export const selectAIAssistantLoading = (state: RootState) =>
  state.aiAssistant.isLoading;
export const selectAIAssistantError = (state: RootState) =>
  state.aiAssistant.error;

// Reducer
export default aiAssistantSlice.reducer;
