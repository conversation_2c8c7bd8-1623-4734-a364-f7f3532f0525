import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Test configuration
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "MK*9v9sseuu3#Z3qgypf"
BACKEND_URL = "http://localhost:5000"  # Adjust if your backend is on a different URL


def debug_api_endpoints():
    """Debug the problematic API endpoints with detailed logging"""
    print("\n=== DEBUGGING API ENDPOINTS ===\n")

    # Step 1: Get a valid token
    print("--- Getting authentication token ---")
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token, cannot proceed with tests")
        return

    headers = {"Authorization": f"Bearer {token}"}
    print(f"✅ Successfully obtained auth token: {token[:10]}...\n")

    # Step 2: Debug AI Assistant Chat endpoint
    debug_ai_assistant_chat(headers)

    # Step 3: Debug Business Context endpoint
    debug_business_context(headers)

    # Step 4: Debug Content Library endpoint
    debug_content_library(headers)


def get_auth_token():
    """Get an authentication token"""
    try:
        login_data = {"email": TEST_EMAIL, "password": TEST_PASSWORD}

        login_response = requests.post(f"{BACKEND_URL}/api/auth/login", json=login_data)
        if (
            login_response.status_code == 200
            and "access_token" in login_response.json()
        ):
            return login_response.json().get("access_token")
        else:
            print(f"Login failed: {login_response.status_code}")
            try:
                print(f"Error details: {login_response.json()}")
            except Exception:
                print(f"Response text: {login_response.text[:100]}")
            return None
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return None


def debug_ai_assistant_chat(headers):
    """Debug the AI Assistant Chat endpoint"""
    print("--- Debugging AI Assistant Chat Endpoint ---")

    # First check if the config endpoint works
    try:
        config_response = requests.get(
            f"{BACKEND_URL}/api/ai_assistant/config", headers=headers
        )
        print(f"Config endpoint status: {config_response.status_code}")
        if config_response.status_code == 200:
            print(
                f"Config data: {json.dumps(config_response.json(), indent=2)[:200]}..."
            )
    except Exception as e:
        print(f"Error accessing config endpoint: {str(e)}")

    # Now try different payload formats for the chat endpoint
    print("\nTrying different payload formats for chat endpoint:")

    payloads = [
        {"prompt": "Hello, this is a test message"},
        {"message": "Hello, this is a test message"},
        {"query": "Hello, this is a test message"},
        {
            "prompt": "Hello, this is a test message",
            "conversation_id": None,
            "content_ids": [],
            "context_ids": [],
        },
        {"text": "Hello, this is a test message"},
    ]

    for i, payload in enumerate(payloads):
        print(f"\nAttempt {i + 1}: Payload = {payload}")
        try:
            chat_response = requests.post(
                f"{BACKEND_URL}/api/ai_assistant/chat",
                json=payload,
                headers=headers,
                timeout=10,
            )
            print(f"Status: {chat_response.status_code}")

            if chat_response.status_code == 500:
                print(
                    "💡 The server received the request but encountered an internal error."
                )
                print(
                    "This is likely a backend configuration issue, not a client issue."
                )
            else:
                print(f"Chat request failed: {chat_response.status_code}")
                try:
                    print(
                        f"Response: {json.dumps(chat_response.json(), indent=2)[:200]}..."
                    )
                except Exception:
                    print(f"Response text: {chat_response.text[:200]}")
        except Exception as e:
            print(f"Error during request: {str(e)}")

    print("\n✅ AI Assistant Chat debugging complete\n")


def debug_business_context(headers):
    """Debug the Business Context endpoint"""
    print("--- Debugging Business Context Endpoint ---")

    # First check if the list endpoint works
    try:
        list_response = requests.get(
            f"{BACKEND_URL}/api/business/", headers=headers, timeout=10
        )
        print(f"List endpoint status: {list_response.status_code}")

        if list_response.status_code == 200:
            contexts = list_response.json()
            print(f"Found {len(contexts)} contexts")

            if contexts and len(contexts) > 0:
                print(f"First context: {json.dumps(contexts[0], indent=2)}")

                # Try to extract the ID
                context = contexts[0]
                context_id = None

                for field in ["id", "context_id", "business_id", "uuid"]:
                    if field in context:
                        context_id = context[field]
                        print(f"Found ID in field '{field}': {context_id}")
                        break

                if context_id:
                    # Try different URL patterns for getting details
                    detail_urls = [
                        f"{BACKEND_URL}/api/business/{context_id}",
                        f"{BACKEND_URL}/api/business/detail?id={context_id}",
                        f"{BACKEND_URL}/api/business/context/{context_id}",
                        f"{BACKEND_URL}/api/business/contexts/{context_id}",
                    ]

                    print(
                        f"\nTrying different URL patterns for context ID: {context_id}"
                    )

                    for i, url in enumerate(detail_urls):
                        print(f"\nAttempt {i + 1}: URL = {url}")
                        try:
                            detail_response = requests.get(
                                url, headers=headers, timeout=5
                            )
                            print(f"Status: {detail_response.status_code}")

                            if detail_response.status_code == 200:
                                print("Success! Found working detail endpoint")
                                print(
                                    f"Detail request successful: {detail_response.status_code}"
                                )
                                try:
                                    print(
                                        f"Response: {json.dumps(detail_response.json(), indent=2)[:200]}..."
                                    )
                                except Exception:
                                    print(
                                        f"Response text: {detail_response.text[:200]}"
                                    )
                                break
                            else:
                                print(
                                    f"Detail request failed: {detail_response.status_code}"
                                )
                                try:
                                    print(
                                        f"Error response: {detail_response.text[:100]}"
                                    )
                                except Exception:
                                    print("Could not read error response")
                        except requests.exceptions.Timeout:
                            print("⚠️ Detail request timed out")
                        except Exception as e:
                            print(f"⚠️ Detail request error: {str(e)}")
                else:
                    print("❌ Could not find ID field in context object")
            else:
                print("No contexts found")
        else:
            print(f"List endpoint failed: {list_response.status_code}")
            try:
                print(f"List endpoint error: {list_response.text[:100]}")
            except Exception:
                print("Could not read error response")
    except Exception as e:
        print(f"❌ Error testing Business Context API: {str(e)}")

    print("\n✅ Business Context debugging complete\n")


def debug_content_library(headers):
    """Debug the Content Library endpoint"""
    print("--- Debugging Content Library Endpoint ---")

    # First check if the list endpoint works
    try:
        list_response = requests.get(
            f"{BACKEND_URL}/api/content/", headers=headers, timeout=10
        )
        print(f"List endpoint status: {list_response.status_code}")

        if list_response.status_code == 200:
            items = list_response.json()
            print(f"Found {len(items)} content items")

            if items and len(items) > 0:
                print(f"First item: {json.dumps(items[0], indent=2)}")

                # Try to extract the ID
                item = items[0]
                item_id = None

                for field in ["id", "content_id", "item_id", "uuid"]:
                    if field in item:
                        item_id = item[field]
                        print(f"Found ID in field '{field}': {item_id}")
                        break

                if item_id:
                    # Try different URL patterns for getting details
                    detail_urls = [
                        f"{BACKEND_URL}/api/content/{item_id}",
                        f"{BACKEND_URL}/api/content/detail?id={item_id}",
                        f"{BACKEND_URL}/api/content/item/{item_id}",
                        f"{BACKEND_URL}/api/content/items/{item_id}",
                    ]

                    print(f"\nTrying different URL patterns for item ID: {item_id}")

                    for i, url in enumerate(detail_urls):
                        print(f"\nAttempt {i + 1}: URL = {url}")
                        try:
                            detail_response = requests.get(
                                url, headers=headers, timeout=5
                            )
                            print(f"Status: {detail_response.status_code}")

                            if detail_response.status_code == 200:
                                print("Success! Found working detail endpoint")
                                print(
                                    f"Detail request successful: {detail_response.status_code}"
                                )
                                try:
                                    print(
                                        f"Response: {json.dumps(detail_response.json(), indent=2)[:200]}..."
                                    )
                                except Exception:
                                    print(
                                        f"Response text: {detail_response.text[:200]}"
                                    )
                                break
                            else:
                                print(
                                    f"Detail request failed: {detail_response.status_code}"
                                )
                                try:
                                    print(
                                        f"Error response: {detail_response.text[:100]}"
                                    )
                                except Exception:
                                    print("Could not read error response")
                        except requests.exceptions.Timeout:
                            print("⚠️ Detail request timed out")
                        except Exception as e:
                            print(f"⚠️ Detail request error: {str(e)}")
                else:
                    print("❌ Could not find ID field in content item object")
            else:
                print("No content items found")
        else:
            print(f"List endpoint failed: {list_response.status_code}")
            try:
                print(f"List endpoint error: {list_response.text[:100]}")
            except Exception:
                print("Could not read error response")
    except Exception as e:
        print(f"❌ Error testing Content Library API: {str(e)}")

    print("\n✅ Content Library debugging complete\n")


if __name__ == "__main__":
    debug_api_endpoints()
