import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Title,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  <PERSON>,
  Typography,
  CircularProgress,
  <PERSON>ert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Divider,
  Paper,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import ArticleIcon from '@mui/icons-material/Article';
import PsychologyIcon from '@mui/icons-material/Psychology';
import FormatQuoteIcon from '@mui/icons-material/FormatQuote';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { api } from '../services/api';

interface BusinessContext {
  id: string;
  user_id: string;
  name?: string;
  profile_overview?: string | null;
  content_focus?: string[] | null;
  primary_objectives?: string | null;
  offer_description: string;
  target_audience: string;
  audience_motivation?: string | null;
  brand_voice?: string | null;
  key_benefits?: string | null;
  unique_value_proposition?: string | null;
  audience_decision_style?: string | null;
  audience_preference_structure?: string | null;
  audience_decision_speed?: string | null;
  audience_reaction_to_change?: string | null;
  recommended_platforms?: string[] | null;
  recommended_content_formats?: string[] | null;
  created_at: string;
  updated_at: string;
}

interface AudienceAnalysisModalProps {
  open: boolean;
  onClose: () => void;
  businessContext: BusinessContext | null | undefined;
  mode?: 'view' | 'generate';
}

// Define the structure of the audience analysis result
interface AnalysisCriterion {
  value: string;
  evidence: string;
  recommendations: string[];
}

interface EmotionalState {
  dominant_state: string;
  evidence: string;
  content_approach: string;
  recommendations: string[];
}

interface QuestionStrategy {
  what: string;
  why: string;
  how: string;
  who: string;
  when: string;
  where: string;
}

interface ContentRecommendations {
  content_types: string[];
  messaging_approach: string;
  content_structure: string;
}

interface AudienceAnalysisResult {
  summary: string;
  nhb_criteria: {
    motivation_direction: AnalysisCriterion;
    preference_structure: AnalysisCriterion;
    reference_point: AnalysisCriterion;
    thinking_style: AnalysisCriterion;
    convincer_channels: AnalysisCriterion;
    convincer_strategy: AnalysisCriterion;
    change_model_preference: AnalysisCriterion;
    stress_response: AnalysisCriterion;
  };
  emotional_state: EmotionalState;
  question_strategy: QuestionStrategy;
  content_recommendations: ContentRecommendations;
}

const AudienceAnalysisModal: React.FC<AudienceAnalysisModalProps> = ({
  open,
  onClose,
  businessContext,
  mode = 'view', // Default to view mode
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [analysisResult, setAnalysisResult] = useState<AudienceAnalysisResult | null>(null);
  const [tokensUsed, setTokensUsed] = useState<number | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  useEffect(() => {
    if (open && businessContext) {
      if (mode === 'view') {
        // In view mode, try to get saved analysis first
        fetchSavedAnalysis();
      } else if (mode === 'generate') {
        // In generate mode, directly generate new analysis
        generateNewAnalysis();
      }
    }
    // Reset save success when modal opens/closes
    setSaveSuccess(false);
  }, [open, businessContext, mode]);

  const fetchSavedAnalysis = async () => {
    if (!businessContext) return;

    setLoading(true);
    setError(null);

    try {
      // Try to get saved analysis first
      console.log(`Fetching saved analysis for business context ${businessContext.id}`);
      const response = await api.get(`/business/${businessContext.id}/audience-analysis`);
      console.log('Saved analysis response:', response.data);
      setAnalysisResult(response.data.analysis);
      // No tokens used for saved analysis
      setTokensUsed(null);
      setLoading(false); // Important: Set loading to false on success
    } catch (err: any) {
      // If no saved analysis found, generate new analysis
      if (err.response?.status === 404) {
        console.log('No saved analysis found, generating new analysis');
        await generateNewAnalysis();
      } else {
        console.error("Error fetching saved audience analysis:", err);
        setError(err.response?.data?.error || "Failed to fetch audience analysis");
        setLoading(false);
      }
    }
  };

  // Helper function to parse JSON from markdown code blocks
  const parseJsonFromMarkdown = (text: string): any => {
    try {
      // Check if the text is wrapped in markdown code blocks
      const jsonRegex = /```(?:json)?\n([\s\S]*?)\n```/;
      const match = text.match(jsonRegex);

      if (match && match[1]) {
        // Extract the JSON content from the code block
        return JSON.parse(match[1]);
      }

      // If not in code blocks, try parsing directly
      return JSON.parse(text);
    } catch (error) {
      console.error('Error parsing JSON from markdown:', error);
      throw new Error('Failed to parse analysis result');
    }
  };

  const generateNewAnalysis = async () => {
    if (!businessContext) return;

    setLoading(true);
    setError(null);

    try {
      console.log(`Generating new analysis for business context ${businessContext.id}`);
      // Set a timeout to prevent the analysis from running too long
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

      const response = await api.post(
        `/business/${businessContext.id}/analyze-audience`,
        {},
        { signal: controller.signal }
      );

      clearTimeout(timeoutId);

      console.log('Analysis generation response:', response.data);

      // Handle potential markdown-wrapped JSON
      let analysisData = response.data.analysis;

      // If the server returned an error with raw_result, try to parse it
      if (!analysisData && response.data.error === 'Failed to parse audience analysis result' && response.data.raw_result) {
        try {
          analysisData = parseJsonFromMarkdown(response.data.raw_result);
          console.log('Successfully parsed analysis from raw_result:', analysisData);
        } catch (parseError) {
          console.error('Failed to parse raw_result:', parseError);
          throw new Error('Failed to parse analysis result');
        }
      }

      if (!analysisData) {
        throw new Error('No analysis data returned from server');
      }

      setAnalysisResult(analysisData);
      setTokensUsed(response.data.tokens_used);

      // Automatically save the new analysis
      await saveAnalysisWithParam(analysisData);
    } catch (err: any) {
      console.error("Audience analysis generation error:", err);
      if (err.name === 'AbortError') {
        setError('Analysis is taking longer than expected. The request was aborted after 60 seconds. Please try again later.');
      } else if (err.response?.data?.raw_result) {
        // If we have raw_result but couldn't parse it, show a more helpful error
        setError('The analysis was generated but could not be parsed correctly. Please try again.');
      } else {
        setError(err.response?.data?.error || err.message || "Failed to generate audience analysis");
      }
    } finally {
      setLoading(false);
    }
  };

  // Function to save analysis with an optional parameter
  const saveAnalysisWithParam = async (analysisToSave = analysisResult) => {
    if (!businessContext || !analysisToSave) return;

    setIsSaving(true);
    setSaveSuccess(false);
    setError(null);

    try {
      console.log(`Saving analysis for business context ${businessContext.id}`);
      const response = await api.post(`/business/${businessContext.id}/audience-analysis`, {
        analysis: analysisToSave
      });
      console.log('Save analysis response:', response.data);
      setSaveSuccess(true);

      // Notify parent component that analysis was saved (if needed)
      if (onClose) {
        // Optional: Add a slight delay to show the success state before closing
        setTimeout(() => {
          onClose();
        }, 1500);
      }
    } catch (err: any) {
      console.error("Error saving audience analysis:", err);
      setError(err.response?.data?.error || "Failed to save audience analysis");
    } finally {
      setIsSaving(false);
    }
  };

  // Event handler for the save button
  const saveAnalysis = (event: React.MouseEvent) => {
    event.preventDefault();
    saveAnalysisWithParam();
  };

  // Helper function to format criterion names for display
  const formatCriterionName = (key: string) => {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Helper function to get color for emotional state
  const getEmotionalStateColor = (state: string) => {
    switch (state.toLowerCase()) {
      case 'worthless':
        return '#e57373'; // light red
      case 'helpless':
        return '#ffb74d'; // light orange
      case 'hopeless':
        return '#4fc3f7'; // light blue
      default:
        return '#81c784'; // light green
    }
  };

  // Render the analysis summary section
  const renderSummary = () => {
    if (!analysisResult) return null;

    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Audience Analysis Summary
        </Typography>
        <Paper elevation={1} sx={{ p: 2, bgcolor: 'background.paper' }}>
          <Typography variant="body1">
            {analysisResult.summary}
          </Typography>
        </Paper>
      </Box>
    );
  };

  // Render the emotional state section
  const renderEmotionalState = () => {
    if (!analysisResult?.emotional_state) return null;

    const { dominant_state, evidence, content_approach, recommendations } = analysisResult.emotional_state;
    const stateColor = getEmotionalStateColor(dominant_state);

    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Emotional State Analysis
        </Typography>

        <Accordion defaultExpanded sx={{ mb: 1 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
              <Typography fontWeight="bold">
                Dominant State
              </Typography>
              <Chip
                label={dominant_state}
                sx={{ bgcolor: stateColor, fontWeight: 'bold', color: '#fff' }}
                icon={<PsychologyIcon sx={{ color: '#fff !important' }} />}
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="subtitle2" fontWeight="bold">
              Evidence:
            </Typography>
            <Box sx={{ pl: 2, borderLeft: `4px solid ${stateColor}`, mb: 2 }}>
              <Typography variant="body2">
                {evidence}
              </Typography>
            </Box>

            <Typography variant="subtitle2" fontWeight="bold">
              Content Approach:
            </Typography>
            <Box sx={{ pl: 2, mb: 2 }}>
              <Typography variant="body2">
                {content_approach}
              </Typography>
            </Box>

            <Typography variant="subtitle2" fontWeight="bold">
              Recommendations:
            </Typography>
            <List dense>
              {recommendations.map((rec, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <CheckCircleOutlineIcon sx={{ color: stateColor }} />
                  </ListItemIcon>
                  <ListItemText primary={rec} />
                </ListItem>
              ))}
            </List>
          </AccordionDetails>
        </Accordion>
      </Box>
    );
  };

  // Render the NHB+ criteria section
  const renderNHBCriteria = () => {
    if (!analysisResult?.nhb_criteria) return null;

    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Audience Characteristics (NHB+ Framework)
        </Typography>

        {Object.entries(analysisResult.nhb_criteria).map(([key, criterion]) => (
          <Accordion key={key} sx={{ mb: 1 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
                <Typography fontWeight="bold">
                  {formatCriterionName(key)}
                </Typography>
                <Chip
                  label={criterion.value}
                  color="primary"
                  variant="outlined"
                  size="small"
                  sx={{ fontWeight: 'bold' }}
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="subtitle2" fontWeight="bold">
                Evidence:
              </Typography>
              <Box sx={{ pl: 2, borderLeft: '2px solid #e0e0e0', mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  {criterion.evidence}
                </Typography>
              </Box>

              <Typography variant="subtitle2" fontWeight="bold">
                Recommendations:
              </Typography>
              <List dense>
                {criterion.recommendations.map((rec, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <CheckCircleOutlineIcon color="primary" fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={rec} />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        ))}
      </Box>
    );
  };

  // Render the question strategy section
  const renderQuestionStrategy = () => {
    if (!analysisResult?.question_strategy) return null;

    const questionTypes = [
      { key: 'what', label: 'What Questions' },
      { key: 'why', label: 'Why Questions' },
      { key: 'how', label: 'How Questions' },
      { key: 'who', label: 'Who Questions' },
      { key: 'when', label: 'When Questions' },
      { key: 'where', label: 'Where Questions' },
    ];

    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Question Strategy
        </Typography>
        <Paper elevation={1} sx={{ p: 2, bgcolor: 'background.paper' }}>
          <Typography variant="body2" sx={{ mb: 2 }}>
            Strategic use of question types based on your audience's emotional state and characteristics:
          </Typography>

          {questionTypes.map(({ key, label }) => (
            <Box key={key} sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <HelpOutlineIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="subtitle1" fontWeight="bold">
                  {label}:
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ pl: 4 }}>
                {analysisResult.question_strategy[key as keyof QuestionStrategy]}
              </Typography>
            </Box>
          ))}
        </Paper>
      </Box>
    );
  };

  // Render the content recommendations section
  const renderContentRecommendations = () => {
    if (!analysisResult?.content_recommendations) return null;

    const { content_types, messaging_approach, content_structure } = analysisResult.content_recommendations;

    return (
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Content Strategy Recommendations
        </Typography>
        <Paper elevation={1} sx={{ p: 2, bgcolor: 'background.paper' }}>
          <Typography variant="subtitle1" fontWeight="bold">
            Recommended Content Types:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2, mt: 1 }}>
            {content_types.map((type, index) => (
              <Chip
                key={index}
                label={type}
                color="primary"
                variant="outlined"
                icon={<ArticleIcon />}
              />
            ))}
          </Box>

          <Divider sx={{ my: 2 }} />

          <Typography variant="subtitle1" fontWeight="bold">
            Messaging Approach:
          </Typography>
          <Box sx={{ pl: 2, mb: 2, mt: 1 }}>
            <Typography variant="body2">
              {messaging_approach}
            </Typography>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Typography variant="subtitle1" fontWeight="bold">
            Content Structure:
          </Typography>
          <Box sx={{ pl: 2, mt: 1 }}>
            <Typography variant="body2">
              {content_structure}
            </Typography>
          </Box>
        </Paper>
      </Box>
    );
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      aria-labelledby="audience-analysis-dialog-title"
      scroll="paper"
    >
      <DialogTitle id="audience-analysis-dialog-title">
        {mode === 'generate' ? 'Generate New Audience Analysis' : 'Audience Analysis'}
        {tokensUsed && (
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
            Tokens used: {tokensUsed}
          </Typography>
        )}
      </DialogTitle>
      <DialogContent dividers>
        {loading ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 4 }}>
            <CircularProgress />
            <Typography sx={{ mt: 2 }}>
              Analyzing your audience...
            </Typography>
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
        ) : analysisResult ? (
          <Box>
            {renderSummary()}
            {renderEmotionalState()}
            {renderNHBCriteria()}
            {renderQuestionStrategy()}
            {renderContentRecommendations()}
          </Box>
        ) : (
          <Typography>No analysis data available.</Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
        {analysisResult && (
          <Button
            variant="contained"
            color="primary"
            onClick={saveAnalysis}
            disabled={isSaving || saveSuccess}
            startIcon={isSaving ? <CircularProgress size={20} /> : undefined}
          >
            {saveSuccess ? 'Saved!' : 'Save Analysis'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AudienceAnalysisModal;
