#!/usr/bin/env python3
"""
Check and clean Flask environment variables that conflict with FastAPI.
"""

import os
from dotenv import load_dotenv

def check_flask_environment_variables():
    """Check for Flask environment variables in the system."""
    print("🔍 Checking for Flask environment variables...")
    
    flask_vars = [
        'FLASK_APP', 'FLASK_ENV', 'FLASK_DEBUG', 'FLASK_RUN_HOST', 
        'FLASK_RUN_PORT', 'FLASK_SECRET_KEY', 'FLASK_CONFIG'
    ]
    
    found_flask_vars = {}
    
    for var in flask_vars:
        if var in os.environ:
            found_flask_vars[var] = os.environ[var]
            print(f"❌ Found Flask variable: {var}={os.environ[var]}")
    
    if not found_flask_vars:
        print("✅ No Flask environment variables found in system environment")
    else:
        print(f"\n⚠️  Found {len(found_flask_vars)} Flask environment variables that need to be removed")
    
    return found_flask_vars

def check_env_files():
    """Check .env files for Flask variables."""
    print("\n🔍 Checking .env files for Flask variables...")
    
    env_files = ['.env', '.env.test', '.env.local', '.env.development', '.env.production']
    
    for env_file in env_files:
        if os.path.exists(env_file):
            print(f"\n📄 Checking {env_file}:")
            try:
                with open(env_file, 'r') as f:
                    lines = f.readlines()
                    flask_lines = []
                    for i, line in enumerate(lines, 1):
                        if line.strip().startswith(('FLASK_', '#FLASK_')) and not line.strip().startswith('#'):
                            flask_lines.append((i, line.strip()))
                    
                    if flask_lines:
                        print(f"   ❌ Found Flask variables:")
                        for line_num, line in flask_lines:
                            print(f"      Line {line_num}: {line}")
                    else:
                        print(f"   ✅ No Flask variables found")
            except Exception as e:
                print(f"   ❌ Error reading {env_file}: {e}")
        else:
            print(f"📄 {env_file}: Not found")

def load_test_environment():
    """Load test environment and check for conflicts."""
    print("\n🔍 Loading test environment...")
    
    # Load test environment
    load_dotenv(dotenv_path=".env.test", override=True)
    
    # Check what got loaded
    test_vars = {}
    for key, value in os.environ.items():
        if key.startswith(('FLASK_', 'SUPABASE_', 'OPENAI_', 'DEBUG', 'ENVIRONMENT')):
            test_vars[key] = value
    
    print("📋 Test environment variables loaded:")
    for key, value in sorted(test_vars.items()):
        if key.startswith('FLASK_'):
            print(f"   ❌ {key}={value}")
        else:
            print(f"   ✅ {key}={value}")

def clean_flask_environment():
    """Remove Flask environment variables from current session."""
    print("\n🧹 Cleaning Flask environment variables from current session...")
    
    flask_vars = [
        'FLASK_APP', 'FLASK_ENV', 'FLASK_DEBUG', 'FLASK_RUN_HOST', 
        'FLASK_RUN_PORT', 'FLASK_SECRET_KEY', 'FLASK_CONFIG'
    ]
    
    removed_vars = []
    for var in flask_vars:
        if var in os.environ:
            removed_vars.append(f"{var}={os.environ[var]}")
            del os.environ[var]
    
    if removed_vars:
        print("✅ Removed Flask variables:")
        for var in removed_vars:
            print(f"   - {var}")
    else:
        print("✅ No Flask variables to remove")

if __name__ == "__main__":
    print("🚀 Environment Variable Analysis for FastAPI Migration")
    print("=" * 60)
    
    # Check system environment
    flask_vars = check_flask_environment_variables()
    
    # Check .env files
    check_env_files()
    
    # Load test environment
    load_test_environment()
    
    # Clean current session
    clean_flask_environment()
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    
    if flask_vars:
        print("❌ Flask environment variables found - these need to be removed")
        print("   Recommendation: Restart terminal/IDE to clear system environment")
    else:
        print("✅ System environment is clean of Flask variables")
    
    print("\n🎯 NEXT STEPS:")
    print("1. Remove Flask variables from .env files (if any)")
    print("2. Restart terminal/IDE to clear system environment")
    print("3. Run tests with clean FastAPI environment")
