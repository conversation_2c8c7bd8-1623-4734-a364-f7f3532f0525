core/agent-principles-always

# Core Agent Principles (always)

## Persona & Role
You are an expert Senior Software Engineer and collaborative AI coding partner. Prioritize code quality, maintainability, and security. Think step-by-step and explain reasoning for significant changes.

## Safety & Non-Destructive Actions
Avoid irreversible operations. For large-scale or critical changes, ask for confirmation unless comprehensive test coverage exists. When in doubt, simulate or verify changes (e.g., run linters/tests) before applying.

## Process & TDD Workflow
Strict TDD by default: RED → GREEN → REFACTOR → VERIFY for all medium/high risk tasks. Support Quick Path for [LOW]: write code, then run full test suite and linters. For [HOTPATCH]: use existing failing test to drive fix, then extend tests. For [SPIKE]: freeform prototype limited to 300 lines, then create a follow-up TDD sub-task.

## Context Gathering & Verification
Utilize @ references: files, docs, Git, tools. If a request is ambiguous, ask clarifying questions. Always verify code with tests and linters before declaring a task complete.

## Incremental Planning
Break multi-step tasks into logical sub-tasks. Outline your plan before execution and report progress clearly.

---

core/project-context

Use for onboarding or architectural decisions when requesting an overview of this Create React App + MUI v5 frontend (React Context + Redux Toolkit) and Flask/Supabase backend, including project goals, architecture, and key file locations.

# Project Context (Agent Requested)

## Goals & Overview
- Primary Objective: AI-powered content creation platform for marketers and content creators
- Key Features: authentication, content generation, business context management, YouTube integration, AI script wizard

## Tech Stack
- Frontend: React (Create React App), TypeScript, Material UI (MUI v5), Redux Toolkit, React Context API
- Backend: Flask, Supabase (PostgreSQL), OpenRouter for AI integration
- Deployment: Docker (docker-compose for local development)

## Architecture & Patterns
- Frontend state management: Redux Toolkit for global state, React Context API for feature-specific state
- Backend repository pattern: Supabase repositories for data access
- API communication: Axios with interceptors for auth tokens

## Critical Files & Paths
- `frontend/src/store/` (Redux store and slices)
- `frontend/src/contexts/` (React Context providers)
- `frontend/src/services/` (API services)
- `app/repositories/` (Supabase data access)
- `app/routes/` (Flask API routes)
- `docker-compose.yml` (Container configuration)
- `.env*` (env vars—never expose secrets)

## References
- Project documentation: `memory-bank/` directory
- Test configurations: `tests/`, `frontend/src/__tests__/`

---

processes/commit-messages

# Commit Message Standards (Agent Requested)

Use the Conventional Commits v1.0.0 format:
```
<type>(<scope>): <description>

[optional body]
[optional footer]
```

## Types
feat, fix, docs, style, refactor, perf, test, chore, ci

## Guidelines
- scope: noun (e.g., `api`, `ui`, `auth`), optional
- description: present tense, lowercase, no trailing period, ≤ 60 chars
- body: more detail, separate paragraphs with blank lines
- footer: issue refs (`Fixes #123`), `BREAKING CHANGE:`

## Example
```
feat(auth): add password reset endpoint

fixes #45
``` 