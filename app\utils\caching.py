import functools
from cachetools import TTLCache, cached
from hashlib import sha256
import json

# Import FastAPI settings from config module (avoiding circular imports)
from app.config import get_settings

# Default values if not found in app config
_DEFAULT_CACHE_TTL = 300
_DEFAULT_CACHE_MAX_SIZE = 100

# Global cache instance - will be initialized lazily or upon first use
supabase_query_cache = None

def get_cache():
    global supabase_query_cache
    if supabase_query_cache is None:
        settings = get_settings()
        
        # Get cache settings from FastAPI Settings with defaults
        cache_ttl = getattr(settings, 'cache_default_ttl', _DEFAULT_CACHE_TTL)
        cache_max_size = getattr(settings, 'cache_default_max_size', _DEFAULT_CACHE_MAX_SIZE)
        
        supabase_query_cache = TTLCache(maxsize=cache_max_size, ttl=cache_ttl)
    return supabase_query_cache

def generate_cache_key(*args, **kwargs):
    """
    Generates a cache key from function arguments.
    Sorts kwargs to ensure consistent key order.
    Uses sha256 for a fixed-length, collision-resistant key.
    """
    # Serialize args and sorted kwargs to a JSON string
    # Using json.dumps with sort_keys=True for kwargs ensures consistency.
    # We need to handle non-serializable objects if they appear in args/kwargs.
    # For Supabase queries, args/kwargs are often simple types or serializable objects.
    
    key_parts = []
    if args:
        key_parts.append(json.dumps(args, sort_keys=True, default=str))
    if kwargs:
        key_parts.append(json.dumps(kwargs, sort_keys=True, default=str))
    
    # Join parts and encode to bytes for hashing
    serialized_key = "|".join(key_parts).encode('utf-8')
    
    return sha256(serialized_key).hexdigest()

def cache_supabase_query(ttl_seconds=None, max_size=None, cache_instance=None):
    """
    A decorator to cache the results of Supabase query functions.
    Note: ttl_seconds and max_size params on decorator are currently not creating
    separate cache instances or TTLs per decorated function if default cache_instance is used.
    They are more for future extension or if a specific cache_instance is passed.
    The global supabase_query_cache uses TTL/max_size from FastAPI settings or defaults.

    Args:
        ttl_seconds (int, optional): Placeholder for future per-function TTL.
        max_size (int, optional): Placeholder for future per-function max_size.
        cache_instance (TTLCache, optional): A specific TTLCache instance to use.
                                            If None, uses the global supabase_query_cache.
    """
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_cache = cache_instance if cache_instance is not None else get_cache()
            
            key = generate_cache_key(func.__name__, *args, **kwargs)
            
            try:
                return current_cache[key]
            except KeyError:
                pass

            result = func(*args, **kwargs)
            current_cache[key] = result
            return result

        def invalidate(*args, **kwargs):
            current_cache = cache_instance if cache_instance is not None else get_cache()
            key_to_invalidate = generate_cache_key(func.__name__, *args, **kwargs)
            if key_to_invalidate in current_cache:
                del current_cache[key_to_invalidate]
        
        wrapper.invalidate = invalidate
        
        def clear_all_func_cache():
            # This remains a placeholder for true function-specific clearing without iterating all keys.
            # A simple global clear for the default cache is: get_cache().clear()
            pass

        wrapper.clear_cache = clear_all_func_cache
        return wrapper
    return decorator

# Example of a specific cache for a different purpose / TTL
# user_profile_cache = TTLCache(maxsize=50, ttl=600)

# To clear the entire default supabase_query_cache:
# def clear_default_supabase_cache():
#     get_cache().clear() 