import os
import logging
import json
import asyncio
import aiohttp
import httpx
from typing import Dict, List, Optional, Any, AsyncGenerator

import requests
from requests.exceptions import RequestException, HTTPError

from app.ai_assistant.config import (
    get_max_tokens,
    get_model_params,
    get_api_key,
    get_api_url,
    get_model_for_purpose,
)
from . import prompts

# Import YouTubeService
from app.youtube.youtube_service import YouTubeService

logger = logging.getLogger(__name__)


class OpenRouterAdapter:
    """Adapter for OpenRouter API integration"""

    def __init__(
        self,
        api_key: Optional[str] = None,
        youtube_service: Optional[YouTubeService] = None,
    ):
        """Initialize the OpenRouter adapter with API key and YouTube service"""
        # Try to get the API key from the parameter or the centralized config function
        resolved_api_key = api_key or get_api_key()

        # Check if API key is available
        if not resolved_api_key:
            logging.error(
                "No OpenRouter API key found. Please set OPENROUTER_API_KEY or OPENAI_API_KEY in your .env file."
            )
            raise ValueError("OpenRouter API key is required")
        self.api_key = resolved_api_key

        # Get OpenRouter API endpoint from centralized config
        try:
            self.api_url = get_api_url()
            # Optional: Check if the URL is specifically for openrouter if needed
            # if "openrouter.ai" not in self.api_url:
            #     logger.warning(f"Configured API URL '{self.api_url}' might not be for OpenRouter.")
        except ValueError as e:
            logger.error(f"Could not get API URL from config: {e}")
            # Fallback or re-raise depending on desired behavior
            raise ValueError(f"Could not determine OpenRouter API URL from config: {e}")

        # Get the default model from the centralized configuration using purpose
        self.model = get_model_for_purpose("default")  # Use new function

        # Set provider attribute
        self.provider = "openrouter"  # Keep this specific to the adapter

        # Set max tokens and max context length
        self.max_tokens = get_max_tokens("chat")
        # Increased context length for handling large amounts of data
        self.max_context_length = 100000

        # Store the YouTube service instance
        self.youtube_service = (
            youtube_service or YouTubeService()
        )  # Instantiate if not provided

    async def generate_content(
        self,
        prompt: str,
        business_context: Optional[Dict] = None,
        content_examples: Optional[List[Dict]] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Generate content using OpenRouter API with context and examples"""
        # Get max tokens from config if not provided
        if max_tokens is None:
            max_tokens = get_max_tokens("default")

        # Use provided system prompt or build one if not provided
        if system_prompt is None:
            system_message = prompts.build_system_prompt(
                content_format="general",
                business_context=business_context,
                content_examples=content_examples,
            )
        else:
            system_message = system_prompt

        # Construct the messages for the API call
        messages = [
            {"role": "system", "content": system_message},
        ]

        # Add the user prompt
        messages.append({"role": "user", "content": prompt})

        try:
            # Get model parameters from config
            params = get_model_params()

            # Prepare the request payload
            payload = {
                "model": self.model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": params["temperature"],
                "top_p": params["top_p"],
                "frequency_penalty": params["frequency_penalty"],
                "presence_penalty": params["presence_penalty"],
            }

            # Set up headers
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": os.environ.get("APP_URL", "https://writer-v2.app"),
                "X-Title": "Writer v2",
            }

            # Make the API call to OpenRouter
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    self.api_url,
                    json=payload,
                    headers=headers,
                )

                # Raise an exception for HTTP errors
                response.raise_for_status()

                # Parse the response
                response_data = response.json()

            # Log the raw response for debugging
            logger.debug(f"OpenRouter API raw response: {response_data}")

            # Check for top-level error in response
            if 'error' in response_data:
                # Handle API-level errors returned outside of choices
                err_info = response_data.get('error')
                # err_info might be a dict or string
                if isinstance(err_info, dict):
                    msg = err_info.get('message', str(err_info))
                else:
                    msg = str(err_info)
                logger.error(f"OpenRouter API returned top-level error: {msg} | Full response: {response_data}")
                return {
                    "error": True,
                    "error_message": f"API error: {msg}",
                    "generated_text": f"Error generating content: The AI service encountered an error: {msg}. Please try again later."
                }

            # Check if 'choices' exists in the response
            if 'choices' not in response_data or not response_data['choices']:
                logger.error(f"Missing 'choices' in OpenRouter API response: {response_data}")
                return {
                    "error": True,
                    "error_message": "Invalid API response: missing 'choices'",
                    "generated_text": "Error generating content: The AI service returned an invalid response structure."
                }

            # Check if there's an error in the choices
            if 'error' in response_data['choices'][0]:
                error_info = response_data['choices'][0]['error']
                error_message = error_info.get('message', 'Unknown error')
                error_code = error_info.get('code', 'unknown')
                logger.error(f"OpenRouter API returned an error: {error_message} (code: {error_code})")

                # Check if the error is retryable
                is_retryable = error_info.get('metadata', {}).get('raw', {}).get('retryable', False)
                retry_message = " This error is temporary and can be retried." if is_retryable else ""

                return {
                    "error": True,
                    "error_message": f"API error: {error_message} (code: {error_code}){retry_message}",
                    "generated_text": f"Error generating content: The AI service encountered an error: {error_message}.{retry_message} Please try again later."
                }

            # Check if the first choice has a message with content
            if 'message' not in response_data['choices'][0] or 'content' not in response_data['choices'][0]['message']:
                logger.error(f"Missing 'message' or 'content' in OpenRouter API response: {response_data}")
                return {
                    "error": True,
                    "error_message": "Invalid API response: missing 'message' or 'content'",
                    "generated_text": "Error generating content: The AI service returned an invalid response structure."
                }

            # Check if content is empty
            if response_data['choices'][0]['message']['content'] == '':
                logger.error(f"Empty content in OpenRouter API response: {response_data}")
                return {
                    "error": True,
                    "error_message": "Empty content in API response",
                    "generated_text": "Error generating content: The AI service returned an empty response. Please try again later."
                }

            # Extract the generated content
            generated_text = response_data["choices"][0]["message"]["content"]

            # Return the result
            return {
                "generated_text": generated_text,
                "context_used": business_context is not None,
                "examples_used": (
                    content_examples is not None and len(content_examples) > 0
                ),
                "model": self.model,
                "tokens_used": response_data.get("usage", {}).get("total_tokens", 0),
            }

        except httpx.HTTPStatusError as e:
            # Handle HTTP errors (4xx or 5xx status codes)
            error_message = f"OpenRouter API request failed with status {e.response.status_code} for content: {e.response.text}"
            logger.error(f"OpenRouter API HTTPStatusError (generate_content): {error_message}", exc_info=True)
            user_friendly_message = f"The AI service returned an error (status {e.response.status_code}) for content. Please try again later."
            if e.response.status_code == 429:
                user_friendly_message = "API rate limit exceeded for OpenRouter for content. Please try again later."
            elif e.response.status_code >= 500:
                user_friendly_message = "The AI service (OpenRouter) is currently unavailable or experiencing issues for content. Please try again later."

            return {
                "error": True,
                "error_message": error_message,
                "generated_text": user_friendly_message,
            }
        except (httpx.RequestError, httpx.TimeoutException) as e:
            # Handle network-related errors (DNS failure, connection timeout, etc.)
            error_message = f"Could not connect to OpenRouter API for content: {e}"
            logger.error(f"OpenRouter API RequestError (generate_content): {error_message}", exc_info=True)
            return {
                "error": True,
                "error_message": error_message,
                "generated_text": "Error: Could not connect to the AI service (OpenRouter) for content. Please check your network connection.",
            }
        except Exception as e:
            # Handle other unexpected errors, including JSON parsing errors if response.json() fails
            error_message = str(e)
            logger.error(f"OpenRouter API unexpected error (generate_content): {e}", exc_info=True)

            # Check if this is a KeyError related to 'choices' (already handled but good to keep for robustness)
            if isinstance(e, KeyError) and str(e).strip("'") == 'choices':
                return {
                    "error": True,
                    "error_message": "Missing 'choices' in API response",
                    "generated_text": "Error generating content: The AI service returned an invalid response structure. Please try again."
                }
            
            return {
                "error": True,
                "error_message": f"An unexpected error occurred: {error_message}",
                "generated_text": (f"An unexpected error occurred: {error_message}"),
            }

    async def generate_chat_response(
        self,
        messages: List[Dict[str, str]],
        content_format: str = "general",
        business_context: Optional[Dict] = None,
        content_examples: Optional[List[Dict]] = None,
        video_data: Optional[Dict] = None,
        content_items: Optional[List[Dict]] = None,
        max_tokens: Optional[int] = None,
        model_override: Optional[str] = None,
        system_prompt: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Generate a response in a chat conversation with message history"""
        # Get max tokens from config if not provided
        if max_tokens is None:
            max_tokens = get_max_tokens("chat")

        # Use provided system prompt or build one if not provided
        if system_prompt is None:
            system_message = prompts.build_system_prompt(
                content_format=content_format,
                business_context=business_context,
                content_examples=content_examples,
                content_items=content_items,
                video_data=video_data,
                include_youtube_capabilities=True,
            )
        else:
            system_message = system_prompt

        # Construct the messages for the API call
        # The prompt function now includes all context
        api_messages = [{"role": "system", "content": system_message}]

        # Add the conversation history
        api_messages.extend(messages)

        # Determine the model for this specific purpose (chat)
        # Use model_override if provided, otherwise get the model for the purpose
        model_name = model_override if model_override else get_model_for_purpose("chat")
        logger.debug(f"Using model: {model_name} for chat response")

        try:
            # Get model parameters from config
            params = get_model_params()

            # Prepare the request payload
            payload = {
                "model": model_name,
                "messages": api_messages,
                "max_tokens": max_tokens,
                "temperature": params["temperature"],
                "top_p": params["top_p"],
                "frequency_penalty": params["frequency_penalty"],
                "presence_penalty": params["presence_penalty"],
            }

            # Set up headers
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": os.environ.get("APP_URL", "https://writer-v2.app"),
                "X-Title": "Writer v2",
            }

            # Log the request payload (excluding sensitive data)
            payload_log = payload.copy()
            payload_log['messages'] = f"[{len(payload['messages'])} messages, total chars: {sum(len(m.get('content', '')) for m in payload['messages'])}]"
            logger.info(f"OpenRouter API request payload: {payload_log}")

            # Make the API call to OpenRouter
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    self.api_url,
                    json=payload,
                    headers=headers,
                )

                # Log response status and headers
                logger.info(f"OpenRouter API response status: {response.status_code}")
                logger.debug(f"OpenRouter API response headers: {response.headers}")

                # Raise an exception for HTTP errors
                response.raise_for_status()

                # Parse the response
                response_data = response.json()

            # Log the raw response for debugging
            logger.info(f"OpenRouter API raw response: {response_data}")

            # Check for top-level error in response
            if 'error' in response_data:
                # Handle API-level errors returned outside of choices
                err_info = response_data.get('error')
                # err_info might be a dict or string
                if isinstance(err_info, dict):
                    msg = err_info.get('message', str(err_info))
                else:
                    msg = str(err_info)
                logger.error(f"OpenRouter API returned top-level error: {msg} | Full response: {response_data}")
                return {
                    "error": True,
                    "error_message": f"API error: {msg}",
                    "generated_text": f"Error generating content: The AI service encountered an error: {msg}. Please try again later."
                }

            # Check if 'choices' exists in the response
            if 'choices' not in response_data or not response_data['choices']:
                logger.error(f"Missing 'choices' in OpenRouter API response: {response_data}")
                return {
                    "error": True,
                    "error_message": "Invalid API response: missing 'choices'",
                    "generated_text": "Error generating content: The AI service returned an invalid response structure."
                }

            # Check if there's an error in the choices
            if 'error' in response_data['choices'][0]:
                error_info = response_data['choices'][0]['error']
                error_message = error_info.get('message', 'Unknown error')
                error_code = error_info.get('code', 'unknown')
                logger.error(f"OpenRouter API returned an error: {error_message} (code: {error_code})")

                # Check if the error is retryable
                is_retryable = error_info.get('metadata', {}).get('raw', {}).get('retryable', False)
                retry_message = " This error is temporary and can be retried." if is_retryable else ""

                return {
                    "error": True,
                    "error_message": f"API error: {error_message} (code: {error_code}){retry_message}",
                    "generated_text": f"Error generating content: The AI service encountered an error: {error_message}.{retry_message} Please try again later."
                }

            # Check if the first choice has a message with content
            if 'message' not in response_data['choices'][0] or 'content' not in response_data['choices'][0]['message']:
                logger.error(f"Missing 'message' or 'content' in OpenRouter API response: {response_data}")
                return {
                    "error": True,
                    "error_message": "Invalid API response: missing 'message' or 'content'",
                    "generated_text": "Error generating content: The AI service returned an invalid response structure."
                }

            # Check if content is empty
            if response_data['choices'][0]['message']['content'] == '':
                logger.error(f"Empty content in OpenRouter API response: {response_data}")
                return {
                    "error": True,
                    "error_message": "Empty content in API response",
                    "generated_text": "Error generating content: The AI service returned an empty response. Please try again later."
                }

            # Extract the generated content
            generated_text = response_data["choices"][0]["message"]["content"]

            # Return the result
            return {
                "generated_text": generated_text,
                "context_used": business_context is not None,
                "examples_used": (
                    content_examples is not None and len(content_examples) > 0
                ),
                "content_items_used": (
                    content_items is not None and len(content_items) > 0
                ),
                "video_data_used": video_data is not None,
                "model": model_name,
                "tokens_used": response_data.get("usage", {}).get("total_tokens", 0),
            }

        except HTTPError as e:
            # Handle HTTP errors (4xx or 5xx status codes)
            error_message = f"OpenRouter API request failed with status {e.response.status_code} in chat: {e.response.text}"
            logger.error(f"OpenRouter API HTTPError (generate_chat_response): {error_message}", exc_info=True)
            user_friendly_message = f"The AI service returned an error (status {e.response.status_code}) during chat. Please try again later."
            if e.response.status_code == 429:
                user_friendly_message = "API rate limit exceeded for OpenRouter during chat. Please try again later."
            elif e.response.status_code >= 500:
                user_friendly_message = "The AI service (OpenRouter) is currently unavailable or experiencing issues during chat. Please try again later."
            
            return {
                "error": True,
                "error_message": error_message,
                "generated_text": user_friendly_message,
            }
        except RequestException as e:
            # Handle network-related errors (DNS failure, connection timeout, etc.)
            error_message = f"Could not connect to OpenRouter API in chat: {e}"
            logger.error(f"OpenRouter API RequestException (generate_chat_response): {error_message}", exc_info=True)
            return {
                "error": True,
                "error_message": error_message,
                "generated_text": "Error: Could not connect to the AI service (OpenRouter) for chat. Please check your network connection.",
            }
        except Exception as e:
            # Handle other unexpected errors
            error_message = str(e)
            logger.error(f"OpenRouter API unexpected error (generate_chat_response): {e}", exc_info=True)
            return {
                "error": True,
                "error_message": f"An unexpected error occurred during chat: {error_message}",
                "generated_text": (
                    f"An unexpected error occurred during chat: {error_message}"
                ),
            }

    async def generate_chat_response_stream(
        self,
        messages: List[Dict[str, str]],
        content_format: str = "general",
        business_context: Optional[Dict] = None,
        content_examples: Optional[List[Dict]] = None,
        max_tokens: Optional[int] = None,
    ) -> AsyncGenerator[str, None]:
        """Generate a streamed response in a chat conversation using OpenRouter API."""
        logger.info("[OPENROUTER_STREAM] Starting streaming request")

        if max_tokens is None:
            max_tokens = get_max_tokens("chat_stream")

        # Build system message
        system_message = prompts.build_system_prompt(
            content_format=content_format,
            business_context=business_context,
            content_examples=content_examples,
        )

        # Prepare API messages
        api_messages = [{"role": "system", "content": system_message}]
        api_messages.extend(messages)

        # Determine the model for chat streaming
        model_name = get_model_for_purpose("chat_stream")
        logger.info(f"[OPENROUTER_STREAM] Using model: {model_name}")
        logger.info(f"[OPENROUTER_STREAM] Max tokens: {max_tokens}")
        logger.info(f"[OPENROUTER_STREAM] API Key present: {bool(self.api_key)}")
        logger.info(f"[OPENROUTER_STREAM] API Key length: {len(self.api_key) if self.api_key else 0}")
        logger.info(f"[OPENROUTER_STREAM] Total messages: {len(api_messages)}")

        try:
            # Get model parameters from config
            params = get_model_params()

            # Prepare the request payload with streaming enabled
            payload = {
                "model": model_name,
                "messages": api_messages,
                "max_tokens": max_tokens,
                "temperature": params["temperature"],
                "top_p": params["top_p"],
                "frequency_penalty": params["frequency_penalty"],
                "presence_penalty": params["presence_penalty"],
                "stream": True,  # Enable streaming
            }

            # Set up headers
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": os.environ.get("APP_URL", "https://writer-v2.app"),
                "X-Title": "Writer v2",
                "Accept": "text/event-stream",
            }

            logger.info(f"[OPENROUTER_STREAM] Request URL: {self.api_url}")
            logger.info(f"[OPENROUTER_STREAM] Request headers: {dict(headers)}")
            logger.info(f"[OPENROUTER_STREAM] Request payload: {json.dumps(payload, indent=2)}")

            # Use aiohttp for async streaming
            async with aiohttp.ClientSession() as session:
                logger.info("[OPENROUTER_STREAM] Making HTTP request...")
                async with session.post(
                    self.api_url,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=120)
                ) as response:

                    logger.info(f"[OPENROUTER_STREAM] Response status: {response.status}")
                    logger.info(f"[OPENROUTER_STREAM] Response headers: {dict(response.headers)}")

                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"[OPENROUTER_STREAM] API error {response.status}: {error_text}")
                        yield f"Error: OpenRouter API returned status {response.status}. Please try again later."
                        return

                    logger.info("[OPENROUTER_STREAM] Starting to process streaming response...")
                    chunk_count = 0
                    total_content_length = 0

                    # Process the streaming response
                    async for line in response.content:
                        line_str = line.decode('utf-8').strip()

                        if line_str:  # Log all non-empty lines
                            logger.debug(f"[OPENROUTER_STREAM] Raw line: {repr(line_str)}")

                        # Skip empty lines and non-data lines
                        if not line_str or not line_str.startswith('data: '):
                            continue

                        # Extract JSON data
                        json_str = line_str[6:]  # Remove 'data: ' prefix
                        logger.debug(f"[OPENROUTER_STREAM] JSON string: {json_str}")

                        # Skip [DONE] marker
                        if json_str.strip() == '[DONE]':
                            logger.info("[OPENROUTER_STREAM] Received [DONE] marker")
                            break

                        try:
                            chunk_data = json.loads(json_str)
                            chunk_count += 1
                            logger.debug(f"[OPENROUTER_STREAM] Chunk {chunk_count} data: {chunk_data}")

                            # Extract content from the chunk
                            if ('choices' in chunk_data and
                                len(chunk_data['choices']) > 0 and
                                'delta' in chunk_data['choices'][0] and
                                'content' in chunk_data['choices'][0]['delta']):

                                content = chunk_data['choices'][0]['delta']['content']
                                if content:  # Only yield non-empty content
                                    total_content_length += len(content)
                                    logger.info(f"[OPENROUTER_STREAM] Chunk {chunk_count}: yielding '{content}' (length: {len(content)})")
                                    yield content
                                else:
                                    logger.debug(f"[OPENROUTER_STREAM] Chunk {chunk_count}: empty content")
                            else:
                                logger.debug(f"[OPENROUTER_STREAM] Chunk {chunk_count}: no content in expected structure")

                        except json.JSONDecodeError as e:
                            logger.warning(f"[OPENROUTER_STREAM] Failed to parse streaming chunk: {json_str[:100]}... Error: {e}")
                            continue
                        except KeyError as e:
                            logger.warning(f"[OPENROUTER_STREAM] Unexpected chunk structure: {chunk_data}. Missing key: {e}")
                            continue

                    logger.info(f"[OPENROUTER_STREAM] Streaming completed. Total chunks: {chunk_count}, Total content length: {total_content_length}")

        except aiohttp.ClientError as e:
            logger.error(f"OpenRouter streaming connection error: {e}")
            yield f"Error: Could not connect to OpenRouter streaming API. Details: {str(e)}"
        except asyncio.TimeoutError:
            logger.error("OpenRouter streaming request timed out")
            yield "Error: Request timed out. Please try again."
        except Exception as e:
            logger.error(f"Unexpected error during OpenRouter streaming: {e}", exc_info=True)
            yield f"Error: An unexpected error occurred during streaming. Details: {str(e)}"

    async def generate_content_with_youtube_data(
        self,
        prompt: str,
        business_context: Optional[Dict] = None,
        video_data: Optional[Dict] = None,
        max_tokens: Optional[int] = None,
        system_prompt: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Generate content using OpenRouter API with context and YT data."""
        # Get max tokens from config if not provided
        if max_tokens is None:
            max_tokens = get_max_tokens("youtube_content")

        # Determine the model for this specific purpose
        model_name = get_model_for_purpose("youtube_content")

        # Use provided system prompt or build one if not provided
        if system_prompt is None:
            system_message = prompts.build_system_prompt(
                content_format="youtube_content",
                business_context=business_context,
                video_data=video_data,
            )
        else:
            system_message = system_prompt

        # Construct the messages for the API call
        # The prompt function now includes all context
        messages = [
            {"role": "system", "content": system_message},
        ]

        # Add the user prompt
        messages.append({"role": "user", "content": prompt})

        try:
            # Get model parameters from config
            params = get_model_params()

            # Prepare the request payload
            payload = {
                "model": model_name,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": params["temperature"],
                "top_p": params["top_p"],
                "frequency_penalty": params["frequency_penalty"],
                "presence_penalty": params["presence_penalty"],
            }

            # Set up headers
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": os.environ.get("APP_URL", "https://writer-v2.app"),
                "X-Title": "Writer v2",
            }

            # Make the API call to OpenRouter
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    self.api_url,
                    json=payload,
                    headers=headers,
                )

                # Raise an exception for HTTP errors
                response.raise_for_status()

                # Parse the response
                response_data = response.json()

            # Log the raw response for debugging
            logger.debug(f"OpenRouter API raw response: {response_data}")

            # Check for top-level error in response
            if 'error' in response_data:
                # Handle API-level errors returned outside of choices
                err_info = response_data.get('error')
                # err_info might be a dict or string
                if isinstance(err_info, dict):
                    msg = err_info.get('message', str(err_info))
                else:
                    msg = str(err_info)
                logger.error(f"OpenRouter API returned top-level error: {msg} | Full response: {response_data}")
                return {
                    "error": True,
                    "error_message": f"API error: {msg}",
                    "generated_text": f"Error generating content: The AI service encountered an error: {msg}. Please try again later."
                }

            # Check if 'choices' exists in the response
            if 'choices' not in response_data or not response_data['choices']:
                logger.error(f"Missing 'choices' in OpenRouter API response: {response_data}")
                return {
                    "error": True,
                    "error_message": "Invalid API response: missing 'choices'",
                    "generated_text": "Error generating content: The AI service returned an invalid response structure."
                }

            # Check if there's an error in the choices
            if 'error' in response_data['choices'][0]:
                error_info = response_data['choices'][0]['error']
                error_message = error_info.get('message', 'Unknown error')
                error_code = error_info.get('code', 'unknown')
                logger.error(f"OpenRouter API returned an error: {error_message} (code: {error_code})")

                # Check if the error is retryable
                is_retryable = error_info.get('metadata', {}).get('raw', {}).get('retryable', False)
                retry_message = " This error is temporary and can be retried." if is_retryable else ""

                return {
                    "error": True,
                    "error_message": f"API error: {error_message} (code: {error_code}){retry_message}",
                    "generated_text": f"Error generating content: The AI service encountered an error: {error_message}.{retry_message} Please try again later."
                }

            # Check if the first choice has a message with content
            if 'message' not in response_data['choices'][0] or 'content' not in response_data['choices'][0]['message']:
                logger.error(f"Missing 'message' or 'content' in OpenRouter API response: {response_data}")
                return {
                    "error": True,
                    "error_message": "Invalid API response: missing 'message' or 'content'",
                    "generated_text": "Error generating content: The AI service returned an invalid response structure."
                }

            # Check if content is empty
            if response_data['choices'][0]['message']['content'] == '':
                logger.error(f"Empty content in OpenRouter API response: {response_data}")
                return {
                    "error": True,
                    "error_message": "Empty content in API response",
                    "generated_text": "Error generating content: The AI service returned an empty response. Please try again later."
                }

            # Extract the generated content
            generated_text = response_data["choices"][0]["message"]["content"]

            # Return the result
            return {
                "generated_text": generated_text,
                "context_used": business_context is not None,
                "video_data_used": video_data is not None,
                "model": model_name,
                "tokens_used": response_data.get("usage", {}).get("total_tokens", 0),
            }

        except HTTPError as e:
            error_message = f"OpenRouter API request failed with status {e.response.status_code} for YouTube data: {e.response.text}"
            logger.error(f"OpenRouter API HTTPError (generate_content_with_youtube_data): {error_message}", exc_info=True)
            user_friendly_message = f"The AI service returned an error (status {e.response.status_code}) with YouTube data. Please try again later."
            if e.response.status_code == 429:
                user_friendly_message = "API rate limit exceeded for OpenRouter with YouTube data. Please try again later."
            elif e.response.status_code >= 500:
                user_friendly_message = "The AI service (OpenRouter) is currently unavailable or experiencing issues with YouTube data. Please try again later."

            return {
                "error": True,
                "error_message": error_message,
                "generated_text": user_friendly_message,
            }
        except RequestException as e:
            error_message = f"Could not connect to OpenRouter API for YouTube data: {e}"
            logger.error(f"OpenRouter API RequestException (generate_content_with_youtube_data): {error_message}", exc_info=True)
            return {
                "error": True,
                "error_message": error_message,
                "generated_text": "Error: Could not connect to the AI service (OpenRouter) for YouTube data. Please check your network connection.",
            }
        except Exception as e:
            error_message = str(e)
            logger.error(f"OpenRouter API unexpected error (generate_content_with_youtube_data): {e}", exc_info=True)
            return {
                "error": True,
                "error_message": f"An unexpected error occurred with YouTube data: {error_message}",
                "generated_text": (
                    f"An unexpected error occurred with YouTube data: {error_message}"
                ),
            }

    async def test_connection(self, model=None):
        """Test the connection to OpenRouter API with a minimal request

        Args:
            model (str, optional): The model to test with. Defaults to None.

        Returns:
            dict: A dictionary with success status and message
        """
        if not model:
            model = self.model

        try:
            # Make a minimal test request
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    self.api_url,
                    json={
                        "model": model,
                        "messages": [{"role": "user", "content": "Hello"}],
                        "max_tokens": 1,  # Minimal tokens to save costs
                    },
                    headers={
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json",
                        "HTTP-Referer": os.environ.get("APP_URL", "https://writer-v2.app"),
                        "X-Title": "Writer v2",
                    },
                )

            # Check for successful response
            if response.status_code == 200:
                return {
                    "success": True,
                    "message": "Successfully connected to OpenRouter API",
                }

            # Handle authentication errors
            if response.status_code == 401:
                return {
                    "success": False,
                    "message": (
                        "Authentication failed with OpenRouter API. "
                        "The API key is invalid."
                    ),
                }

            # Handle other API errors
            error_message = "Unknown error"
            try:
                error_data = response.json()
                if "error" in error_data:
                    error_message = error_data["error"]
            except Exception:  # Changed bare except to Exception
                pass

            return {
                "success": False,
                "message": f"OpenRouter API error: {error_message}",
            }

        except requests.exceptions.Timeout:
            return {
                "success": False,
                "message": "Connection to OpenRouter API timed out",
            }
        except requests.exceptions.ConnectionError:
            return {
                "success": False,
                "message": (
                    "Failed to connect to OpenRouter API. "
                    "Please check your internet connection."
                ),
            }
        except Exception as e:
            logger.error(f"Error testing OpenRouter connection: {e}")
            return {
                "success": False,
                "message": f"Error testing connection: {str(e)}",
            }

    def format_transcript(
        self,
        transcript: str,
        format_type: str = "blog",
        tone: str = "professional",
        length: str = "medium",
        keywords: Optional[List[str]] = None,
    ):
        """Format a transcript using OpenRouter."""
        # Determine the model for this specific purpose
        model_name = get_model_for_purpose("transcript_formatting")

        # Get max tokens from config
        max_tokens = get_max_tokens("transcript_formatting")

        # Construct the system prompt using the specific transcript formatter
        # Assuming 'prompts' has a function like 'get_transcript_format_prompt'
        try:
            system_message = prompts.get_transcript_format_prompt(
                format_type=format_type,
                tone=tone,
                length=length,
                keywords=keywords,
            )
        except AttributeError:
            logger.error(
                "Missing prompt function: get_transcript_format_prompt. Falling back."
            )
            # Fallback or simple message if specific formatter isn't ready/refactored
            system_message = (
                "You are an AI assistant that reformats text."  # Very basic fallback
            )

        # Construct messages for the API call
        messages = [
            {"role": "system", "content": system_message},
            {
                "role": "user",
                "content": (f"Please format this transcript:\n\n{transcript}"),
            },
        ]

        # Prepare the request payload
        # Use the model determined for this purpose
        payload = {
            "model": model_name,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": 0.3,
        }

        # Set up headers
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": os.environ.get("APP_URL", "https://writer-v2.app"),
            "X-Title": "Writer v2",
        }

        # Make the API call to OpenRouter
        response = requests.post(
            self.api_url,
            json=payload,
            headers=headers,
            timeout=120,
        )

        # Check for successful response
        if response.status_code == 200:
            response_data = response.json()
            if "choices" in response_data and len(response_data["choices"]) > 0:
                formatted_text = response_data["choices"][0]["message"]["content"]
                return {
                    "formatted_text": formatted_text,
                    "model": model_name,
                    "tokens_used": response_data["usage"]["total_tokens"],
                }

        # Handle errors
        error_message = "Unknown error"
        try:
            error_data = response.json()
            if "error" in error_data:
                error_message = error_data["error"]
        except Exception:  # Changed bare except to Exception
            pass

        logger.error(f"Failed to format transcript via OpenRouter: {error_message}")
        return {
            "formatted_text": (f"Error formatting transcript: {error_message}"),
            "model": model_name,
            "tokens_used": 0,
        }

    def _extract_video_id_from_url(self, url: str) -> Optional[str]:
        """Extract YouTube video ID from URL."""
        import re

        patterns = [
            # Standard watch URL
            r"(?:youtube\.com\/watch\?v=)([A-Za-z0-9_-]{11})",
            # Shortened URL
            r"(?:youtu\.be\/)([A-Za-z0-9_-]{11})",
            # Embed URL
            r"(?:youtube\.com\/embed\/)([A-Za-z0-9_-]{11})",
            # Old embed URL
            r"(?:youtube\.com\/v\/)([A-Za-z0-9_-]{11})",
        ]
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        # Check if the input itself is potentially an ID
        if re.match(r"^[A-Za-z0-9_-]{11}$", url):
            return url
        return None

    def handle_youtube_request(
        self,
        prompt: str,
        messages: List[Dict[str, str]],
        business_context: Optional[Dict] = None,
        content_items: Optional[List[Dict]] = None,
        max_tokens: Optional[int] = None,
        user_token: str = None,  # ADDED user_token parameter
    ) -> Dict[str, Any]:
        """
        Handle YouTube-related requests by fetching data using YouTubeService
        and then calling the main chat generation method.

        Args:
            prompt: The user's latest prompt.
            messages: Full conversation history including the latest prompt.
            business_context: Business context data.
            content_items: List of specific content items.
            max_tokens: Max tokens for the AI response.
            user_token: The user's authentication token for internal API calls.

        Returns:
            Dict containing the AI response or an error.
        """
        logger.info(f"Handling potential YouTube request: {prompt[:100]}...")

        # Ensure youtube_service is available
        if not self.youtube_service:
            logger.error("YouTubeService not initialized in OpenRouterAdapter")
            return {"error": True, "error_message": "YouTube processing unavailable."}

        # Ensure user_token is provided for internal calls
        if not user_token:
            logger.error(
                "User token not provided to handle_youtube_request for internal API calls."
            )
            return {"error": True, "error_message": "YouTube processing unavailable."}

        youtube_data_results = {}
        formatted_youtube_data_str = None
        error_message = None

        try:
            # 1. Extract URLs and determine request type
            urls = self._extract_youtube_urls(prompt)
            request_type = self._determine_youtube_request_type(prompt)
            logger.debug(f"Detected URLs: {urls}, Request Type: {request_type}")

            # 2. Fetch data using YouTubeService based on type
            if request_type == "search":
                # Extract search query (simple extraction for now)
                search_query = prompt  # Refine this extraction logic
                search_phrases = [
                    "search youtube for",
                    "find youtube videos about",
                    "look on youtube for",
                    "youtube search:",
                ]
                for phrase in search_phrases:
                    if prompt.lower().startswith(phrase):
                        search_query = prompt[len(phrase) :].strip()
                        break
                logger.info(f"Performing YouTube search for query: '{search_query}'")
                youtube_data_results["search_results"] = (
                    self.youtube_service.search_videos(
                        search_query, user_token=user_token
                    )
                )  # Pass token

            elif urls:
                video_id = self.youtube_service.extract_video_id(urls[0])
                if not video_id:
                    logger.warning(f"Could not extract video ID from URL: {urls[0]}")
                    error_message = "Could not extract a valid YouTube video ID from the provided URL."
                else:
                    logger.info(
                        f"Processing YouTube video ID: {video_id} for request type: {request_type}"
                    )
                    if request_type == "details":
                        youtube_data_results["details"] = (
                            self.youtube_service.get_video_details(
                                video_id, user_token=user_token
                            )
                        )  # Pass token
                    elif request_type == "transcript":
                        youtube_data_results["transcript"] = (
                            self.youtube_service.get_transcript(
                                video_id, user_token=user_token
                            )
                        )  # Pass token
                        # Maybe get details too for context?
                        youtube_data_results["details"] = (
                            self.youtube_service.get_video_details(
                                video_id, user_token=user_token
                            )
                        )  # Pass token
                    elif (
                        request_type == "analysis" or request_type == "summarize"
                    ):  # Treat summarize as analysis
                        youtube_data_results["analysis"] = (
                            self.youtube_service.analyze_video(
                                video_id, user_token=user_token
                            )
                        )  # Pass token
                        # Include details for better context in the summary
                        if not youtube_data_results["analysis"].get("error"):
                            youtube_data_results["details"] = (
                                self.youtube_service.get_video_details(
                                    video_id, user_token=user_token
                                )
                            )  # Pass token
                    else:  # Default to details if type is unclear but URL is present
                        logger.info(
                            f"Defaulting to fetching video details for URL: {urls[0]}"
                        )
                        youtube_data_results["details"] = (
                            self.youtube_service.get_video_details(
                                video_id, user_token=user_token
                            )
                        )  # Pass token
            else:
                # Not a detectable YouTube request, pass through to general chat
                logger.info(
                    "No specific YouTube action detected, proceeding with general chat."
                )
                # Pass through to generate_chat_response without adding YouTube data
            return self.generate_chat_response(
                messages=messages,
                business_context=business_context,
                content_items=content_items,
                max_tokens=max_tokens,
                # video_data will be None here
            )

            # Check for errors during YouTube data fetching
            if any(
                isinstance(v, dict) and v.get("error")
                for v in youtube_data_results.values()
            ):
                error_details = [
                    v.get("error_message", "Unknown YouTube API Error")
                    for v in youtube_data_results.values()
                    if isinstance(v, dict) and v.get("error")
                ]
                error_message = (
                    f"Error fetching YouTube data: {'. '.join(error_details)}"
                )
                logger.error(error_message)
                # Don't proceed to LLM call if YouTube fetch failed critically
                return {
                    "error": True,
                    "error_message": error_message,
                    "generated_text": error_message,
                }

            # 3. Format the fetched data for the LLM prompt
            formatted_youtube_data_str = self._format_youtube_data_for_llm(
                youtube_data_results
            )
            if not formatted_youtube_data_str and not error_message:
                logger.info(
                    "No specific YouTube data found or fetched for the request."
                )
                # Proceed to LLM without specific youtube data
                formatted_youtube_data_str = None  # Ensure it's None

        except Exception as e:
            logger.exception(f"Unexpected error during YouTube request handling: {e}")
            error_message = f"An unexpected error occurred while processing the YouTube request: {e}"
            return {
                "error": True,
                "error_message": error_message,
                "generated_text": error_message,
            }

        # 4. Call the main chat generation method with the formatted YouTube data
        # The system prompt within generate_chat_response should handle incorporating this.
        logger.info(
            "Calling generate_chat_response with formatted YouTube data (if any)."
        )
        return self.generate_chat_response(
            messages=messages,
            business_context=business_context,
            content_items=content_items,
            # Pass the formatted string or results dict - check generate_chat_response expectation
            # Let's assume generate_chat_response expects a dict like youtube_data_results
            video_data=(
                youtube_data_results if youtube_data_results else None
            ),  # Pass the dict
            max_tokens=max_tokens,
        )

    def _format_youtube_data_for_llm(self, youtube_data: Dict) -> Optional[str]:
        """Formats the fetched YouTube data into a string for the LLM context."""
        if not youtube_data:
            return None

        parts = []
        if "details" in youtube_data and youtube_data["details"]:
            details = youtube_data["details"]
            parts.append("**YouTube Video Details:**")
            parts.append(f"- Title: {details.get('title', 'N/A')}")
            parts.append(f"- Channel: {details.get('channel_title', 'N/A')}")
            parts.append(
                f"- Views: {details.get('view_count', 'N/A'):,}"
            )  # Formatted views
            parts.append(
                f"- Likes: {details.get('like_count', 'N/A'):,}"
            )  # Formatted likes
            # Add other relevant details

        if "transcript" in youtube_data and youtube_data["transcript"]:
            parts.append("\n**YouTube Transcript Snippet:**")
            # Include a snippet rather than the whole transcript to save tokens
            snippet = youtube_data["transcript"][:1000]  # Limit snippet size
            parts.append(f"> {snippet}...")

        if "analysis" in youtube_data and youtube_data["analysis"]:
            analysis = youtube_data["analysis"]
            parts.append("\n**YouTube Video Analysis:**")
            if "summary" in analysis:
                parts.append(f"- Summary: {analysis['summary']}")
            if "sentiment" in analysis:
                parts.append(f"- Sentiment: {analysis['sentiment']}")
            # Add other analysis parts

        if "search_results" in youtube_data and youtube_data["search_results"]:
            results = youtube_data["search_results"]
            parts.append("\n**YouTube Search Results:**")
            if results:
                for i, item in enumerate(results[:3]):  # Show top 3 results
                    parts.append(
                        f"- {i + 1}. {item.get('title', 'N/A')} (ID: {item.get('video_id', 'N/A')})"
                    )
            else:
                parts.append("- No results found.")

        return "\n".join(parts) if parts else None

    def _determine_youtube_request_type(self, prompt: str) -> str:
        """Determine the type of YouTube request based on keywords."""
        prompt_lower = prompt.lower()  # Ensure prompt_lower is defined early
        search_keywords = [
            "search youtube for",
            "find youtube videos about",
            "look on youtube for",
            "youtube search:",
        ]
        if any(prompt_lower.startswith(keyword) for keyword in search_keywords):
            return "search"

        # More specific checks first
        # Add details detection before more general keywords
        details_keywords = ["details for", "info about", "information on"]
        # Check if URL is also present (implicitly handled by caller via _extract_youtube_urls)
        if any(
            keyword in prompt_lower for keyword in details_keywords
        ) and self._extract_youtube_urls(prompt):
            # Add checks here if needed to ensure it's not another type like 'analyze'
            # For now, assume if URL + details keyword, it's a details request.
            return "details"

        format_keywords = [
            "format this transcript",
            "structure this text",
            "rewrite this as a blog",
            "make this a social post",
        ]
        if any(keyword in prompt_lower for keyword in format_keywords):
            return "format"

        analytics_keywords = [
            "analyze",
            "performance",
            "metrics",
            "stats",
            "views",
            "likes",
        ]
        if any(keyword in prompt_lower for keyword in analytics_keywords):
            return "analytics"

        generation_keywords = [
            "generate",
            "create",
            "write",
            "make",
            "based on this video",
        ]
        if any(keyword in prompt_lower for keyword in generation_keywords):
            # Could overlap with format, but check if transcript NOT focus
            if "transcript" not in prompt_lower and "format" not in prompt_lower:
                return "content_generation"

        transcript_keywords = ["transcript", "transcribe", "text from video"]
        if any(keyword in prompt_lower for keyword in transcript_keywords):
            # Check if asking FOR transcript vs using it
            other_actions = [
                "format",
                "generate",
                "create",
                "write",
                "summarize",
                "analyze",
            ]
            if not any(k in prompt_lower for k in other_actions):
                return "transcript"  # User likely just wants raw transcript

        summary_keywords = ["summarize", "summary", "key points", "tldr"]
        if any(keyword in prompt_lower for keyword in summary_keywords):
            return "summary"  # Treat summary as a specific generation task

        # Broader checks (might need refinement)
        insight_keywords = ["insights", "meaning", "understand", "topic"]
        if any(keyword in prompt_lower for keyword in insight_keywords):
            return "general"  # General analysis/understanding

        compare_keywords = ["compare", "difference", "vs", "versus"]
        if any(keyword in prompt_lower for keyword in compare_keywords):
            return "comparison"  # Comparison task

        recommend_keywords = ["recommend", "suggest", "ideas", "improve"]
        if any(keyword in prompt_lower for keyword in recommend_keywords):
            return "recommendation"

        extract_keywords = ["extract", "pull", "get", "find"]
        if any(keyword in prompt_lower for keyword in extract_keywords):
            # Could be transcript, or specific info - lean towards general?
            if "transcript" not in prompt_lower:
                return "extraction"  # Specific info extraction

        translate_keywords = ["translate", "language", "subtitle"]
        if any(keyword in prompt_lower for keyword in translate_keywords):
            return "translation"  # Translation task

        # Default logic based on keywords if specific phrases didn't match
        format_check_keywords = [
            "format",
            "structure",
            "rewrite",
            "blog",
            "article",
            "social",
            "summary",
            "script",
        ]
        if "transcript" in prompt_lower and any(
            k in prompt_lower for k in format_check_keywords
        ):
            return "format"

        gen_check_keywords = ["generate", "create", "write", "make"]
        if "video" in prompt_lower and any(
            k in prompt_lower for k in gen_check_keywords
        ):
            return "content_generation"

        return "general"  # Default fallback

    def _extract_youtube_urls(self, text: str) -> List[str]:
        """Extract YouTube URLs from text using regex."""
        import re

        # Regex to find YouTube URLs (handles various formats)
        # Source: https://stackoverflow.com/a/37704433/1317468
        youtube_regex = (
            r"(?:https?:\/\/)?(?:www\.)?"
            r"(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|"
            r"(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)"
            r"([a-zA-Z0-9_-]{11})"
        )
        urls = re.findall(youtube_regex, text)
        # Return full URLs for context, reconstruct common formats
        full_urls = []
        processed_ids = set()
        for video_id in urls:
            if video_id not in processed_ids:
                full_urls.append(f"https://www.youtube.com/watch?v={video_id}")
                processed_ids.add(video_id)

        # Also check for plain video IDs if no full URLs found
        if not full_urls:
            # Match 11-char IDs as whole words
            plain_id_regex = r"\b([a-zA-Z0-9_-]{11})\b"
            potential_ids = re.findall(plain_id_regex, text)
            for pid in potential_ids:
                # Basic check: does it look like a video ID?
                # This is imperfect but helps filter random strings.
                if pid not in processed_ids:
                    # Assume any 11-char alphanumeric string could be an ID
                    full_urls.append(f"https://www.youtube.com/watch?v={pid}")
                    processed_ids.add(pid)

        return full_urls
