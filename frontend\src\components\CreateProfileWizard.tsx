import React, { useState, use<PERSON>allback } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  CircularProgress,
  Alert,
  Paper,
  Container,
  TextField,
  Grid,
} from "@mui/material";
import { Save as SaveIcon, <PERSON>Forward, ArrowBack } from "@mui/icons-material";
import TagInput from "./TagInput";

// Define the structure of the profile data collected across steps
// Export the interface so it can be imported by the parent page
export interface ProfileWizardData {
  // Step 1: Core Profile
  name: string;
  profile_overview: string;
  content_focus: string[];
  primary_objectives: string;
  // Step 2: Brand & Voice
  brand_voice: string;
  key_benefits: string;
  // Step 3: Audience
  target_audience: string;
  audience_motivation: string;
  // Step 4: Channels & Formats
  recommended_platforms: string[];
  recommended_content_formats: string[];
  // Step 5: Decision Style (Optional?) - Add if needed
  audience_decision_style?: string;
  audience_preference_structure?: string;
  audience_decision_speed?: string;
  audience_reaction_to_change?: string;
}

// Define the steps for the wizard
const steps = [
  "Core Profile",
  "Brand & Voice",
  "Audience",
  "Channels & Formats",
  // 'Decision Style' // Add if needed
];

interface CreateProfileWizardProps {
  // open: boolean; // Removed: No longer needed if rendered inline
  onClose: () => void; // Used for cancellation/back from step 0
  onSubmit: (profileData: ProfileWizardData) => Promise<void>;
}

const CreateProfileWizard: React.FC<CreateProfileWizardProps> = ({
  // open, // Removed from destructuring
  onClose,
  onSubmit,
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [profileData, setProfileData] = useState<Partial<ProfileWizardData>>({
    // Initialize with defaults
    name: "",
    profile_overview: "",
    content_focus: [],
    primary_objectives: "",
    target_audience: "",
    audience_motivation: "",
    brand_voice: "",
    key_benefits: "",
    recommended_platforms: ["YouTube", "TikTok/Instagram", "LinkedIn"], // Pre-populate suggestions
    recommended_content_formats: [],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generic handler for text field changes
  const handleTextChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = event.target;
      setProfileData((prev) => ({ ...prev, [name]: value }));
    },
    []
  );

  // Generic handler for array field changes (TagInput)
  const handleArrayChange = useCallback(
    (name: keyof ProfileWizardData, value: string[]) => {
      // Ensure the key is actually one that holds a string array
      if (
        name === "content_focus" ||
        name === "recommended_platforms" ||
        name === "recommended_content_formats"
      ) {
        setProfileData((prev) => ({ ...prev, [name]: value }));
      }
    },
    []
  );

  // Handler for metrics (potentially JSON)
  const handleMetricsChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const rawValue = event.target.value;
      let newValue: string | Record<string, any> | null = rawValue;
      try {
        const parsed = JSON.parse(rawValue);
        if (typeof parsed === "object" && parsed !== null) {
          newValue = parsed;
        }
      } catch (jsonError) {
        /* Ignore, keep as string */
      }
      setProfileData((prev) => ({ ...prev, metrics: newValue }));
    },
    []
  );

  // Placeholder for step validation
  const validateStep = (step: number): boolean => {
    switch (step) {
      case 0: // Core Profile
        return (
          !!profileData.name?.trim() &&
          !!profileData.profile_overview?.trim() &&
          (profileData.content_focus?.length ?? 0) > 0
        );
      case 1: // Brand & Voice
        return !!profileData.brand_voice?.trim();
      case 2: // Audience
        return !!profileData.target_audience?.trim();
      case 3: // Channels & Formats
        return (
          (profileData.recommended_platforms?.length ?? 0) > 0 &&
          (profileData.recommended_content_formats?.length ?? 0) > 0
        );
      // Add validation for other steps as needed
      default:
        return true; // Assume other steps are valid for now
    }
  };

  const handleNext = () => {
    setError(null);
    if (!validateStep(activeStep)) {
      setError("Please fill out all required fields for this step.");
      return;
    }
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setError(null);
    if (activeStep === 0) {
      // If on the first step, treat Back as Cancel/Close
      onClose();
    } else {
      setActiveStep((prevActiveStep) => prevActiveStep - 1);
    }
  };

  const handleFinish = async () => {
    setError(null);
    if (!validateStep(activeStep)) {
      // Validate final step
      setError("Please fill out all required fields for the final step.");
      return;
    }
    setIsSubmitting(true);
    try {
      // Ensure all required fields are present before final submission if not caught by step validation
      const finalData = { ...profileData };
      // Add any final defaults or checks if needed
      if (!finalData.target_audience) finalData.target_audience = ""; // Example: ensure required fields aren't undefined
      if (!finalData.brand_voice) finalData.brand_voice = "";

      await onSubmit(finalData as ProfileWizardData);
    } catch (err: any) {
      console.error("Error submitting profile wizard:", err);
      setError(err.message || "Failed to create profile. Please try again.");
      setIsSubmitting(false);
    }
  };

  // Function to render content for each step
  const getStepContent = (step: number) => {
    switch (step) {
      case 0: // Step 1: Core Profile
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                required
                id="name"
                name="name"
                label="Profile Name"
                fullWidth
                variant="outlined"
                value={profileData.name || ""}
                onChange={handleTextChange}
                helperText="A short name to identify this profile (e.g., 'SaaS Product Launch')"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                required
                id="profile_overview"
                name="profile_overview"
                label="Profile Overview"
                fullWidth
                variant="outlined"
                value={profileData.profile_overview || ""}
                onChange={handleTextChange}
                helperText="Describe your product, service, or personal brand in one sentence."
              />
            </Grid>
            <Grid item xs={12}>
              <Box>
                {" "}
                {/* Wrap TagInput for potential styling needs */}
                <TagInput
                  label="Core Content Topics *"
                  value={profileData.content_focus || []}
                  onChange={(newValue) =>
                    handleArrayChange("content_focus", newValue)
                  }
                  placeholder="Add main topics (e.g., AI Writing, Growth Hacking)"
                  suggestions={[
                    "Marketing",
                    "Sales",
                    "Software Development",
                    "Health & Wellness",
                    "Personal Finance",
                  ]} // Generic Topic Suggestions
                />
                <Typography
                  variant="caption"
                  display="block"
                  sx={{ mt: 0.5, ml: 1.5 }}
                >
                  What are the main themes you create content about? (Required)
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12}>
              <TextField
                id="primary_objectives"
                name="primary_objectives"
                label="Primary Objectives"
                fullWidth
                multiline
                rows={3}
                variant="outlined"
                value={profileData.primary_objectives || ""}
                onChange={handleTextChange}
                helperText="What are you trying to achieve with your content? (e.g., Drive leads, build community)"
              />
            </Grid>
          </Grid>
        );
      case 1: // Step 2: Brand & Voice
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                required
                id="brand_voice"
                name="brand_voice"
                label="Brand Voice"
                fullWidth
                multiline
                rows={4}
                variant="outlined"
                value={profileData.brand_voice || ""}
                onChange={handleTextChange}
                helperText="Describe the tone and style of your brand (e.g., Formal, Witty, Empathetic)."
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                id="key_benefits"
                name="key_benefits"
                label="Key Benefits / Messaging Pillars"
                fullWidth
                multiline
                rows={4}
                variant="outlined"
                value={profileData.key_benefits || ""}
                onChange={handleTextChange}
                helperText="What are the main benefits or messages you want to convey?"
              />
            </Grid>
          </Grid>
        );
      case 2: // Step 3: Audience
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                required
                id="target_audience"
                name="target_audience"
                label="Target Audience Profile"
                fullWidth
                multiline
                rows={4}
                variant="outlined"
                value={profileData.target_audience || ""}
                onChange={handleTextChange}
                helperText="Describe your ideal audience in detail."
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                id="audience_motivation"
                name="audience_motivation"
                label="Audience Motivation"
                fullWidth
                multiline
                rows={4}
                variant="outlined"
                value={profileData.audience_motivation || ""}
                onChange={handleTextChange}
                helperText="What drives or motivates this audience? What are their pain points?"
              />
            </Grid>
          </Grid>
        );
      case 3: // Step 4: Channels & Formats
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box>
                {" "}
                {/* Wrap TagInput */}
                <TagInput
                  label="Recommended Channels"
                  value={profileData.recommended_platforms || []}
                  onChange={(newValue) =>
                    handleArrayChange("recommended_platforms", newValue)
                  }
                  placeholder="Add channels (e.g., LinkedIn, YouTube)"
                  suggestions={[
                    "YouTube",
                    "TikTok/Instagram",
                    "LinkedIn",
                    "Blog",
                    "Newsletter",
                  ]}
                />
                <Typography
                  variant="caption"
                  display="block"
                  sx={{ mt: 0.5, ml: 1.5 }}
                >
                  Where will you primarily distribute your content?
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box>
                {" "}
                {/* Wrap TagInput */}
                <TagInput
                  label="Recommended Content Formats"
                  value={profileData.recommended_content_formats || []}
                  onChange={(newValue) =>
                    handleArrayChange("recommended_content_formats", newValue)
                  }
                  placeholder="Add formats (e.g., Tutorial, Vlog)"
                  suggestions={[
                    "Educational Analysis",
                    "Day in the Life Vlog",
                    "Tutorial/How-To",
                    "Product Demo",
                    "Interview",
                    "Case Study",
                    "Behind the Scenes",
                  ]}
                />
                <Typography
                  variant="caption"
                  display="block"
                  sx={{ mt: 0.5, ml: 1.5 }}
                >
                  What types of content will you create?
                </Typography>
              </Box>
            </Grid>
          </Grid>
        );
      // Add case for Decision Style if included
      default:
        return <Typography>Unknown step</Typography>;
    }
  };

  // Simple check if all steps completed
  const isAllStepsCompleted = activeStep === steps.length;

  return (
    // Remove outer Container/Paper wrapper, let parent handle layout
    <Box sx={{ width: "100%" }}>
      {" "}
      {/* Use Box as the main wrapper */}
      <Typography component="h1" variant="h4" align="center" sx={{ mb: 4 }}>
        Create Your Business Profile
      </Typography>
      <Stepper activeStep={activeStep} sx={{ mb: 5 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>
      {isAllStepsCompleted ? (
        <Box sx={{ textAlign: "center" }}>
          <Typography sx={{ mt: 2, mb: 1 }}>
            All steps completed - you&apos;re finished!
          </Typography>
          {/* Optional: Show summary before final submit? */}
          <Button
            variant="contained"
            onClick={handleFinish}
            disabled={isSubmitting}
            startIcon={
              isSubmitting ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <SaveIcon />
              )
            }
            sx={{ mt: 3 }}
          >
            {isSubmitting ? "Creating Profile..." : "Create Profile"}
          </Button>
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      ) : (
        <React.Fragment>
          {/* Render the content for the current step */}
          <Box sx={{ minHeight: "200px", mb: 3 }}>
            {" "}
            {/* Ensure space for content */}
            {getStepContent(activeStep)}
          </Box>

          {/* Navigation Buttons */}
          <Box sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}>
            <Button
              color="inherit"
              // Always enable back button on step 0 to allow cancellation via onClose
              disabled={isSubmitting} // Only disable when submitting
              onClick={handleBack}
              startIcon={<ArrowBack />}
            >
              {activeStep === 0 ? "Cancel" : "Back"}{" "}
              {/* Change label on first step */}
            </Button>
            <Button
              variant="contained"
              onClick={
                activeStep === steps.length - 1 ? handleFinish : handleNext
              }
              disabled={isSubmitting}
              endIcon={
                activeStep === steps.length - 1 ? (
                  isSubmitting ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <SaveIcon />
                  )
                ) : (
                  <ArrowForward />
                )
              }
            >
              {activeStep === steps.length - 1
                ? isSubmitting
                  ? "Saving..."
                  : "Finish"
                : "Next"}
            </Button>
          </Box>
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </React.Fragment>
      )}
    </Box> // End main Box wrapper
  );
};

export default CreateProfileWizard;
