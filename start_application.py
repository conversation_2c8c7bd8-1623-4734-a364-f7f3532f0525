#!/usr/bin/env python3
"""
Application startup script to ensure proper container initialization and testing.
This script handles the complete startup process for the Writer v2 application.
"""

import subprocess
import time
import requests
import json
import sys
from datetime import datetime

def run_command(command, timeout=30):
    """Run a shell command and return the result."""
    try:
        print(f"Running: {command}")
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0:
            print(f"✅ Success: {result.stdout.strip()}")
            return True, result.stdout
        else:
            print(f"❌ Failed: {result.stderr.strip()}")
            return False, result.stderr
    except subprocess.TimeoutExpired:
        print(f"❌ Command timed out after {timeout} seconds")
        return False, "Timeout"
    except Exception as e:
        print(f"❌ Error: {e}")
        return False, str(e)

def check_container_status():
    """Check if containers are running."""
    print("\n" + "="*50)
    print("CHECKING CONTAINER STATUS")
    print("="*50)
    
    success, output = run_command('docker ps --filter "name=writerv2"')
    if success:
        print(f"Container status:\n{output}")
        return 'writerv2-backend-1' in output
    return False

def start_containers():
    """Start the application containers."""
    print("\n" + "="*50)
    print("STARTING CONTAINERS")
    print("="*50)
    
    # Stop any existing containers
    print("Stopping existing containers...")
    run_command("docker-compose down", timeout=60)
    
    # Build and start containers
    print("Building and starting containers...")
    success, output = run_command("docker-compose up -d --build", timeout=180)
    
    if success:
        print("Waiting for containers to initialize...")
        time.sleep(10)
        return True
    return False

def test_backend_health():
    """Test backend health endpoint."""
    print("\n" + "="*50)
    print("TESTING BACKEND HEALTH")
    print("="*50)
    
    max_retries = 10
    for attempt in range(max_retries):
        try:
            print(f"Attempt {attempt + 1}/{max_retries}: Testing backend health...")
            response = requests.get('http://localhost:5000/health', timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Backend health check successful!")
                print(f"Response: {json.dumps(data, indent=2)}")
                return True
            else:
                print(f"❌ Backend returned status {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection failed (attempt {attempt + 1})")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        if attempt < max_retries - 1:
            print("Waiting 5 seconds before retry...")
            time.sleep(5)
    
    return False

def test_frontend_proxy():
    """Test frontend proxy configuration."""
    print("\n" + "="*50)
    print("TESTING FRONTEND PROXY")
    print("="*50)
    
    try:
        print("Testing frontend proxy to backend...")
        response = requests.get('http://localhost:3000/api/health', timeout=15)
        
        if response.status_code == 200:
            print("✅ Frontend proxy working correctly!")
            return True
        else:
            print(f"❌ Frontend proxy returned status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Frontend not accessible or proxy not working")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return False

def test_api_endpoints():
    """Test specific API endpoints that were failing."""
    print("\n" + "="*50)
    print("TESTING API ENDPOINTS")
    print("="*50)
    
    endpoints = [
        ("/api/ai_assistant/config", "AI Assistant Config"),
        ("/api/content/", "Content Service"),
        ("/api/business/", "Business Service"),
    ]
    
    results = {}
    
    for endpoint, name in endpoints:
        try:
            print(f"Testing {name} ({endpoint})...")
            response = requests.get(f'http://localhost:5000{endpoint}', timeout=10)
            
            if response.status_code in [200, 401, 403]:  # 401/403 are expected without auth
                print(f"✅ {name}: Status {response.status_code} (expected)")
                results[name] = True
            else:
                print(f"❌ {name}: Status {response.status_code}")
                print(f"Response: {response.text[:200]}...")
                results[name] = False
                
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
            results[name] = False
    
    return results

def main():
    """Main startup and testing routine."""
    print("="*60)
    print("WRITER V2 APPLICATION STARTUP")
    print("="*60)
    print(f"Started at: {datetime.now()}")
    
    # Step 1: Start containers
    if not start_containers():
        print("\n❌ Failed to start containers. Exiting.")
        return 1
    
    # Step 2: Check container status
    if not check_container_status():
        print("\n❌ Containers not running properly. Exiting.")
        return 1
    
    # Step 3: Test backend health
    if not test_backend_health():
        print("\n❌ Backend health check failed. Exiting.")
        return 1
    
    # Step 4: Test API endpoints
    api_results = test_api_endpoints()
    
    # Step 5: Test frontend proxy (optional)
    proxy_working = test_frontend_proxy()
    
    # Summary
    print("\n" + "="*60)
    print("STARTUP SUMMARY")
    print("="*60)
    
    print("✅ Containers: Running")
    print("✅ Backend Health: Working")
    
    for service, status in api_results.items():
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {service}: {'Working' if status else 'Failed'}")
    
    proxy_icon = "✅" if proxy_working else "⚠️"
    print(f"{proxy_icon} Frontend Proxy: {'Working' if proxy_working else 'Not tested/failed'}")
    
    all_working = all(api_results.values())
    
    if all_working:
        print("\n🎉 Application startup successful!")
        print("Backend is accessible at: http://localhost:5000")
        print("Frontend is accessible at: http://localhost:3000")
        return 0
    else:
        print("\n⚠️ Some services may have issues, but core functionality is working.")
        print("Check the logs above for details.")
        return 0

if __name__ == "__main__":
    sys.exit(main())
