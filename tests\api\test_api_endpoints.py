import requests
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Test configuration
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "MK*9v9sseuu3#Z3qgypf"
BACKEND_URL = "http://localhost:5000"  # Adjust if your backend is on a different URL


def run_tests():
    results = {
        "auth": {"status": "pending", "message": "Not started"},
        "ai_assistant": {"status": "pending", "message": "Not started"},
        "business_context": {"status": "pending", "message": "Not started"},
        "content_library": {"status": "pending", "message": "Not started"},
        "youtube_analytics": {"status": "pending", "message": "Not started"},
    }

    # Step 1: Test Auth API basic connection
    print("\n--- Testing Auth API Health Check ---")
    try:
        auth_response = requests.get(f"{BACKEND_URL}/api/auth/ping")
        if auth_response.status_code == 200:
            results["auth"] = {
                "status": "success",
                "message": "Auth API health check passed",
            }
            print(
                f"✅ Auth API health check: {auth_response.status_code} - {auth_response.text}"
            )
        else:
            results["auth"] = {
                "status": "error",
                "message": f"Auth API health check failed: {auth_response.status_code}",
            }
            print(
                f"❌ Auth API health check error: {auth_response.status_code} - {auth_response.text}"
            )
            return results  # Exit early if basic health check fails
    except Exception as e:
        results["auth"] = {
            "status": "error",
            "message": f"Auth API health check error: {str(e)}",
        }
        print(f"❌ Auth API health check error: {str(e)}")
        return results  # Exit early if connection fails

    # Step 2: Test login to get token
    print("\n--- Testing Auth API Login ---")
    token = None
    try:
        login_data = {"email": TEST_EMAIL, "password": TEST_PASSWORD}

        login_response = requests.post(f"{BACKEND_URL}/api/auth/login", json=login_data)
        if (
            login_response.status_code == 200
            and "access_token" in login_response.json()
        ):
            token = login_response.json().get("access_token")
            headers = {"Authorization": f"Bearer {token}"}
            results["auth"] = {"status": "success", "message": "Login successful"}
            print("✅ Login successful, token received")

            # Give a moment for token to be fully processed
            time.sleep(1)

            # Step 3: Test AI Assistant API
            run_ai_assistant_tests(headers, results)

            # Step 4: Test Business Context API
            run_business_context_tests(headers, results)

            # Step 5: Test Content Library API
            run_content_library_tests(headers, results)

            # Step 6: Test YouTube Analytics API
            run_youtube_analytics_tests(headers, results)
        else:
            results["auth"] = {
                "status": "error",
                "message": f"Login failed: {login_response.status_code}",
            }
            print(
                f"❌ Login failed: {login_response.status_code} - {login_response.text}"
            )
    except Exception as e:
        results["auth"] = {"status": "error", "message": f"Login error: {str(e)}"}
        print(f"❌ Login error: {str(e)}")

    # Print summary
    print("\n=== TEST SUMMARY ===")
    for service, result in results.items():
        print(f"{service}: {result['status']} - {result['message']}")

    return results


def run_ai_assistant_tests(headers, results):
    """Test AI Assistant API endpoints"""
    print("\n--- Testing AI Assistant API ---")
    try:
        # Test config endpoint
        ai_response = requests.get(
            f"{BACKEND_URL}/api/ai_assistant/config", headers=headers
        )
        if ai_response.status_code == 200:
            print(f"✅ AI Assistant config endpoint: {ai_response.status_code}")

            # Test chat endpoint with a correctly formatted message
            chat_data = {
                "prompt": "Hello, this is a test message",
                "conversation_id": None,  # For a new conversation
                "content_ids": [],  # No content references
                "context_ids": [],  # No business context references
            }
            try:
                chat_response = requests.post(
                    f"{BACKEND_URL}/api/ai_assistant/chat",
                    json=chat_data,
                    headers=headers,
                    timeout=15,  # Longer timeout for AI processing
                )

                if chat_response.status_code == 200:
                    results["ai_assistant"] = {
                        "status": "success",
                        "message": "AI Assistant API working (config and chat)",
                    }
                    print(f"✅ AI Assistant chat endpoint: {chat_response.status_code}")
                else:
                    # Try an alternative format if the first one fails
                    alt_chat_data = {"prompt": "Hello, this is a test message"}
                    alt_response = requests.post(
                        f"{BACKEND_URL}/api/ai_assistant/chat",
                        json=alt_chat_data,
                        headers=headers,
                        timeout=15,
                    )

                    if alt_response.status_code == 200:
                        results["ai_assistant"] = {
                            "status": "success",
                            "message": "AI Assistant API working (with alternative format)",
                        }
                        print(
                            f"✅ AI Assistant chat endpoint (alternative format): {alt_response.status_code}"
                        )
                    else:
                        results["ai_assistant"] = {
                            "status": "partial",
                            "message": f"AI Assistant config works but chat failed: {chat_response.status_code}",
                        }
                        print(
                            f"⚠️ AI Assistant chat endpoint error: {chat_response.status_code} - {chat_response.text[:100]}"
                        )
            except requests.exceptions.Timeout:
                results["ai_assistant"] = {
                    "status": "partial",
                    "message": "AI Assistant config works but chat timed out",
                }
                print("⚠️ AI Assistant chat endpoint timed out")
            except Exception as e:
                results["ai_assistant"] = {
                    "status": "partial",
                    "message": f"AI Assistant config works but chat error: {str(e)}",
                }
                print(f"⚠️ AI Assistant chat error: {str(e)}")
        else:
            results["ai_assistant"] = {
                "status": "error",
                "message": f"AI Assistant API error: {ai_response.status_code}",
            }
            print(
                f"❌ AI Assistant API error: {ai_response.status_code} - {ai_response.text}"
            )
    except Exception as e:
        results["ai_assistant"] = {
            "status": "error",
            "message": f"AI Assistant API error: {str(e)}",
        }
        print(f"❌ AI Assistant API error: {str(e)}")


def run_business_context_tests(headers, results):
    """Test Business Context API endpoints"""
    print("\n--- Testing Business Context API ---")
    try:
        # Test listing contexts with a timeout
        business_response = requests.get(
            f"{BACKEND_URL}/api/business/", headers=headers, timeout=10
        )
        if business_response.status_code == 200:
            print(f"✅ Business Context list endpoint: {business_response.status_code}")
            results["business_context"] = {
                "status": "success",
                "message": "Business Context API working",
            }

            # If contexts exist, try to get a specific one
            contexts = business_response.json()
            print(f"Found {len(contexts)} business contexts")

            if contexts and len(contexts) > 0:
                # Get the first context and extract its ID
                context = contexts[0]
                context_id = context.get("id")
                if not context_id:
                    # Try alternative field names for ID
                    for field in ["context_id", "business_id", "uuid"]:
                        if field in context:
                            context_id = context[field]
                            print(f"Using alternative ID field: {field}")
                            break

                if context_id:
                    print(
                        f"Attempting to get details for context with ID: {context_id}"
                    )
                    try:
                        # Try with ID directly in path
                        detail_response = requests.get(
                            f"{BACKEND_URL}/api/business/{context_id}",
                            headers=headers,
                            timeout=10,
                        )

                        if detail_response.status_code == 200:
                            print(
                                f"✅ Business Context detail endpoint: {detail_response.status_code}"
                            )
                        else:
                            # Try alternative approach with ID as a query parameter
                            print(
                                f"Detail request failed with status {detail_response.status_code}, trying alternative URL format"
                            )
                            alt_response = requests.get(
                                f"{BACKEND_URL}/api/business/detail?id={context_id}",
                                headers=headers,
                                timeout=10,
                            )

                            if alt_response.status_code == 200:
                                print(
                                    f"✅ Business Context detail endpoint (alternative): {alt_response.status_code}"
                                )
                            else:
                                results["business_context"] = {
                                    "status": "partial",
                                    "message": f"Business Context list works but detail failed: {detail_response.status_code}",
                                }
                                print(
                                    f"⚠️ Business Context detail endpoint error: {detail_response.status_code} - {detail_response.text[:100]}"
                                )
                    except requests.exceptions.Timeout:
                        results["business_context"] = {
                            "status": "partial",
                            "message": "Business Context detail request timed out",
                        }
                        print("⚠️ Business Context detail endpoint timed out")
                    except Exception as e:
                        results["business_context"] = {
                            "status": "partial",
                            "message": f"Business Context detail error: {str(e)}",
                        }
                        print(f"⚠️ Business Context detail error: {str(e)}")
                else:
                    print("⚠️ Could not find ID field in business context object")
                    # Print the context data to help debug
                    print(f"Context data: {str(context)[:100]}...")
            else:
                print(
                    "ℹ️ No business contexts found in the database, skipping detail test"
                )
        else:
            results["business_context"] = {
                "status": "error",
                "message": f"Business Context API error: {business_response.status_code}",
            }
            print(
                f"❌ Business Context API error: {business_response.status_code} - {business_response.text[:100]}"
            )
    except requests.exceptions.Timeout:
        results["business_context"] = {
            "status": "error",
            "message": "Business Context API request timed out",
        }
        print("❌ Business Context API request timed out")
    except Exception as e:
        results["business_context"] = {
            "status": "error",
            "message": f"Business Context API error: {str(e)}",
        }
        print(f"❌ Business Context API error: {str(e)}")


def run_content_library_tests(headers, results):
    """Test Content Library API endpoints"""
    print("\n--- Testing Content Library API ---")
    try:
        # Test listing content with a timeout
        content_response = requests.get(
            f"{BACKEND_URL}/api/content/", headers=headers, timeout=10
        )
        if content_response.status_code == 200:
            print(f"✅ Content Library list endpoint: {content_response.status_code}")
            results["content_library"] = {
                "status": "success",
                "message": "Content Library API working",
            }

            # If content items exist, try to get a specific one
            content_items = content_response.json()
            print(f"Found {len(content_items)} content items")

            if content_items and len(content_items) > 0:
                # Get the first content item and extract its ID
                content = content_items[0]
                content_id = content.get("id")
                if not content_id:
                    # Try alternative field names for ID
                    for field in ["content_id", "uuid", "item_id"]:
                        if field in content:
                            content_id = content[field]
                            print(f"Using alternative ID field: {field}")
                            break

                if content_id:
                    print(
                        f"Attempting to get details for content with ID: {content_id}"
                    )
                    try:
                        # Try with ID directly in path
                        detail_response = requests.get(
                            f"{BACKEND_URL}/api/content/{content_id}",
                            headers=headers,
                            timeout=10,
                        )

                        if detail_response.status_code == 200:
                            print(
                                f"✅ Content Library detail endpoint: {detail_response.status_code}"
                            )
                        else:
                            # Try alternative approach with ID as a query parameter
                            print(
                                f"Detail request failed with status {detail_response.status_code}, trying alternative URL format"
                            )
                            alt_response = requests.get(
                                f"{BACKEND_URL}/api/content/detail?id={content_id}",
                                headers=headers,
                                timeout=10,
                            )

                            if alt_response.status_code == 200:
                                print(
                                    f"✅ Content Library detail endpoint (alternative): {alt_response.status_code}"
                                )
                            else:
                                results["content_library"] = {
                                    "status": "partial",
                                    "message": f"Content Library list works but detail failed: {detail_response.status_code}",
                                }
                                print(
                                    f"⚠️ Content Library detail endpoint error: {detail_response.status_code} - {detail_response.text[:100]}"
                                )
                    except requests.exceptions.Timeout:
                        results["content_library"] = {
                            "status": "partial",
                            "message": "Content Library detail request timed out",
                        }
                        print("⚠️ Content Library detail endpoint timed out")
                    except Exception as e:
                        results["content_library"] = {
                            "status": "partial",
                            "message": f"Content Library detail error: {str(e)}",
                        }
                        print(f"⚠️ Content Library detail error: {str(e)}")
                else:
                    print("⚠️ Could not find ID field in content object")
                    # Print the content data to help debug
                    print(f"Content data: {str(content)[:100]}...")
            else:
                print("ℹ️ No content items found in the database, skipping detail test")
        else:
            results["content_library"] = {
                "status": "error",
                "message": f"Content Library API error: {content_response.status_code}",
            }
            print(
                f"❌ Content Library API error: {content_response.status_code} - {content_response.text[:100]}"
            )
    except requests.exceptions.Timeout:
        results["content_library"] = {
            "status": "error",
            "message": "Content Library API request timed out",
        }
        print("❌ Content Library API request timed out")
    except Exception as e:
        results["content_library"] = {
            "status": "error",
            "message": f"Content Library API error: {str(e)}",
        }
        print(f"❌ Content Library API error: {str(e)}")


def run_youtube_analytics_tests(headers, results):
    """Test YouTube Analytics API endpoints"""
    print("\n--- Testing YouTube Analytics API ---")
    try:
        # Test search endpoint with a timeout
        youtube_data = {"query": "marketing tips"}
        youtube_response = requests.post(
            f"{BACKEND_URL}/api/youtube/search",
            json=youtube_data,
            headers=headers,
            timeout=15,  # Longer timeout for YouTube API calls
        )
        if youtube_response.status_code == 200:
            print(
                f"✅ YouTube Analytics search endpoint: {youtube_response.status_code}"
            )
            results["youtube_analytics"] = {
                "status": "success",
                "message": "YouTube Analytics API working",
            }
        else:
            results["youtube_analytics"] = {
                "status": "error",
                "message": f"YouTube Analytics API error: {youtube_response.status_code}",
            }
            print(
                f"❌ YouTube Analytics API error: {youtube_response.status_code} - {youtube_response.text[:100]}"
            )
    except requests.exceptions.Timeout:
        results["youtube_analytics"] = {
            "status": "error",
            "message": "YouTube Analytics API request timed out",
        }
        print("❌ YouTube Analytics API request timed out")
    except Exception as e:
        results["youtube_analytics"] = {
            "status": "error",
            "message": f"YouTube Analytics API error: {str(e)}",
        }
        print(f"❌ YouTube Analytics API error: {str(e)}")


def test_save_to_library_empty_body(client, auth_headers, sample_youtube_video_in_db):
    """Test POST /api/youtube/save-to-library/<id> accepts empty JSON body."""
    # Assuming sample_youtube_video_in_db provides a video object with a 'youtube_id'
    video_id = sample_youtube_video_in_db["youtube_id"]

    # First, ensure the video exists in the video_data table (the save endpoint requires it)
    # The fixture sample_youtube_video_in_db should ideally handle this setup.
    # If not, we might need to call the GET /video/<id> endpoint first or add DB setup here.
    get_resp = client.get(f"/api/youtube/video/{video_id}", headers=auth_headers)
    assert get_resp.status_code == 200  # Verify prerequisite

    # Make the POST request with an empty JSON body
    resp = client.post(
        f"/api/youtube/save-to-library/{video_id}", json={}, headers=auth_headers
    )

    # Assert successful response (200 OK)
    assert resp.status_code == 200
    assert resp.json["message"] == "Video data saved to content library successfully"
    assert "content_item" in resp.json
    assert resp.json["content_item"]["title"] == sample_youtube_video_in_db["title"]


if __name__ == "__main__":
    run_tests()
