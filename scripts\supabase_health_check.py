import os
import sys
from supabase import create_client

url = os.environ.get("SUPABASE_URL")
key = os.environ.get("SUPABASE_KEY")

if not url or not key:
    print("Supabase URL or key not set")
    # Exit 0 because it might be intentional in some environments not to check
    sys.exit(0)

try:
    supabase = create_client(url, key)
    # Try a simple query to ensure connectivity and basic permissions
    # Using users table as it should always exist in Supabase Auth context
    response = supabase.table("users").select("id", count="exact").limit(1).execute()
    print(f"Supabase connection successful. Found {response.count} users.")
    sys.exit(0)
except Exception as e:
    print(f"Supabase connection failed: {str(e)}")
    sys.exit(1)
