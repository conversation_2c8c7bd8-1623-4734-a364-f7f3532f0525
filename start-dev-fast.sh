#!/bin/bash

# Fast development startup script
echo "🚀 Starting Writer v2 in FAST development mode..."

# Clean up any existing containers
echo "🧹 Cleaning up existing containers..."
docker-compose down --remove-orphans

# Remove frontend image to force rebuild with optimizations
echo "🔄 Rebuilding frontend with optimizations..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml build --no-cache frontend

# Start services with development overrides
echo "🎯 Starting services..."
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build

echo "✅ Development environment started!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:5000/api"
echo "💾 Health Check: http://localhost:5000/api/health"