@echo off
echo ===================================================
echo Supabase Migration Safety Check
echo ===================================================
echo.

REM Check for Python
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed or not in PATH.
    echo Please install Python and try again.
    exit /b 1
)

REM Check for required packages
echo Checking for required Python packages...
python -c "import psycopg2, requests, dotenv" >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Installing required packages...
    pip install psycopg2-binary requests python-dotenv
)

REM Check for custom config
set CONFIG_FILE=""
if "%~1"=="" goto :no_config
set CONFIG_FILE=%~1
echo Using custom configuration file: %CONFIG_FILE%
goto :run_check

:no_config
echo Using default configuration.

:run_check
echo.
echo Running safety checks...
echo This may take a few minutes.
echo.

if %CONFIG_FILE%=="" (
    python pre_migration_safety_check.py --verbose
) else (
    python pre_migration_safety_check.py --config %CONFIG_FILE% --verbose
)

if %ERRORLEVEL% equ 0 (
    echo.
    echo ===================================================
    echo SAFETY CHECK PASSED
    echo ===================================================
    echo.
    echo The environment appears to be ready for migration.
    echo Please review the detailed report and summary files.
    echo.
    echo If the check passed with warnings, ensure you review
    echo the warnings before proceeding with migration.
    echo.
    exit /b 0
) else (
    echo.
    echo ===================================================
    echo SAFETY CHECK FAILED
    echo ===================================================
    echo.
    echo The environment is NOT ready for migration.
    echo Please review the detailed report and summary files,
    echo address the issues, and run the check again.
    echo.
    exit /b 1
) 