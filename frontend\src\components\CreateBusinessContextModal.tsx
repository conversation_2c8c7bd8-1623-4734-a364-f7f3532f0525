import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Box,
} from "@mui/material";
import { Save as SaveIcon, Cancel as CancelIcon } from "@mui/icons-material";
import TagInput from "./TagInput";

// Define the shape of the data submitted by the modal
interface NewProfileData {
  name: string;
  profile_overview: string;
  content_focus: string[];
  primary_objectives?: string; // Optional for creation
  // Add other *required* fields for creation if needed by backend, e.g.:
  target_audience?: string; // Temporarily optional in modal, parent adds default
  brand_voice?: string; // Temporarily optional in modal, parent adds default
}

interface CreateBusinessContextModalProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (newProfileData: NewProfileData) => Promise<void>; // Use new interface
}

const CreateBusinessContextModal: React.FC<CreateBusinessContextModalProps> = ({
  open,
  onClose,
  onSubmit,
}) => {
  // State for the form fields
  const [name, setName] = useState("");
  const [profileOverview, setProfileOverview] = useState("");
  const [contentFocus, setContentFocus] = useState<string[]>([]);
  const [primaryObjectives, setPrimaryObjectives] = useState("");
  // Legacy field - remove if not used for objectives
  // const [offerDescription, setOfferDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (open) {
      setName("");
      setProfileOverview("");
      setContentFocus([]);
      setPrimaryObjectives("");
      // setOfferDescription(''); // Remove legacy field reset
      setIsSubmitting(false);
      setError(null);
    }
  }, [open]);

  const handleSubmit = async () => {
    // Validation
    if (!name.trim()) {
      setError("Profile name is required.");
      return;
    }
    if (!profileOverview.trim()) {
      setError("Profile overview is required.");
      return;
    }
    if (!contentFocus || contentFocus.length === 0) {
      setError("At least one Core Content Topic is required.");
      return;
    }
    setError(null);
    setIsSubmitting(true);

    try {
      // Construct payload with new fields
      const newProfileData: NewProfileData = {
        name: name.trim(),
        profile_overview: profileOverview.trim(),
        content_focus: contentFocus, // Already an array of strings
        primary_objectives: primaryObjectives.trim() || undefined, // Send undefined if empty
        // Omit offer_description unless reusing it
        // Omit target_audience and brand_voice, let parent handle defaults for now
      };

      // Call the onSubmit prop provided by the parent
      await onSubmit(newProfileData);
      // Parent component handles closing on success
    } catch (err: any) {
      console.error("Error creating profile:", err); // Renamed context -> profile
      setError(err.message || "Failed to create profile. Please try again."); // Renamed
      setIsSubmitting(false); // Keep modal open on error
    }
    // No need for setIsSubmitting(false) here if parent closes modal on success
  };

  const handleCancel = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  // Determine if the form is valid for submission
  const isFormValid =
    name.trim() !== "" &&
    profileOverview.trim() !== "" &&
    contentFocus.length > 0;

  return (
    <Dialog open={open} onClose={handleCancel} maxWidth="sm" fullWidth>
      <DialogTitle>Create New Business Profile</DialogTitle> {/* Renamed */}
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          <TextField
            autoFocus
            margin="dense"
            id="profile-name"
            label="Profile Name" // Renamed
            type="text"
            fullWidth
            variant="outlined"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            error={!name.trim() && !!error} // Show error if name is empty and there's any error
            disabled={isSubmitting}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            id="profile-overview"
            label="Profile Overview (One sentence)" // New field
            type="text"
            fullWidth
            variant="outlined"
            value={profileOverview}
            onChange={(e) => setProfileOverview(e.target.value)}
            required
            error={!profileOverview.trim() && !!error} // Show error if overview is empty and there's any error
            disabled={isSubmitting}
            sx={{ mb: 2 }}
          />
          <Box sx={{ mb: 2 }}>
            {" "}
            {/* Wrap TagInput in Box for styling */}
            <TagInput
              label="Core Content Topics" // New field
              value={contentFocus}
              onChange={setContentFocus} // Directly set state
              placeholder="Add topics (e.g., SaaS Growth, Nutrition)"
              suggestions={["YouTube", "TikTok/Instagram", "LinkedIn"]} // Use same suggestions
              disabled={isSubmitting}
              error={contentFocus.length === 0 && !!error} // Show error if no tags and there's any error
            />
          </Box>
          <TextField
            margin="dense"
            id="profile-primary-objectives"
            label="Primary Objectives (Optional)" // New field
            type="text"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={primaryObjectives}
            onChange={(e) => setPrimaryObjectives(e.target.value)}
            disabled={isSubmitting}
            sx={{ mb: 2 }}
          />
          {/* Removed legacy Offer Description field */}
        </Box>
      </DialogContent>
      <DialogActions sx={{ p: "16px 24px" }}>
        <Button
          onClick={handleCancel}
          disabled={isSubmitting}
          startIcon={<CancelIcon />}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={isSubmitting || !isFormValid} // Use combined validity check
          startIcon={
            isSubmitting ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              <SaveIcon />
            )
          }
        >
          Create Profile {/* Renamed */}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateBusinessContextModal;
