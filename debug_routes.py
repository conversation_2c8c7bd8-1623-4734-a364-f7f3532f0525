#!/usr/bin/env python3
"""
Debug script to check route registration
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

try:
    print("🔍 Debugging route registration...")
    print("=" * 50)
    
    # Try to import the main app
    print("1. Importing main app...")
    from main import create_app
    
    print("2. Creating app instance...")
    app = create_app()
    
    print("3. Checking registered routes...")
    routes = []
    for route in app.routes:
        if hasattr(route, 'path'):
            methods = getattr(route, 'methods', set())
            routes.append((route.path, methods))
    
    print(f"4. Found {len(routes)} routes:")
    for path, methods in sorted(routes):
        methods_str = ', '.join(sorted(methods)) if methods else 'N/A'
        print(f"   {path} [{methods_str}]")
    
    # Check specifically for API routes
    api_routes = [r for r in routes if r[0].startswith('/api/')]
    print(f"\n5. API routes ({len(api_routes)}):")
    for path, methods in sorted(api_routes):
        methods_str = ', '.join(sorted(methods)) if methods else 'N/A'
        print(f"   {path} [{methods_str}]")
    
    if len(api_routes) == 0:
        print("❌ No API routes found! This indicates a router registration issue.")
    else:
        print("✅ API routes found successfully!")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("This suggests there's a syntax error or missing dependency in the code.")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
