# Frontend Supabase Auth Integration

This document explains how to work with the Supabase Auth integration in the frontend application.

## Overview

The frontend now uses Supabase Auth for authentication, replacing the previous custom JWT implementation. This provides several benefits:

- Built-in session management
- Automatic token refresh
- Consistent auth experience
- Resilience to time synchronization issues

## Setup

The Supabase client is configured in `src/lib/supabase.ts`:

```typescript
import { createClient } from '@supabase/supabase-js'
import { ENV } from '../services/envConfig'

const supabaseUrl = ENV.SUPABASE_URL || ''
const supabaseAnonKey = ENV.SUPABASE_KEY || ''

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

## Using Authentication in Components

### Auth Context

The easiest way to access authentication functionality is through the `AuthContext` provider:

```typescript
import { useAuth } from '../contexts/AuthContext'

function MyComponent() {
  const { user, signIn, signOut, loading } = useAuth()
  
  if (loading) return <p>Loading...</p>
  
  if (user) {
    return (
      <div>
        <p>Welcome, {user.email}</p>
        <button onClick={() => signOut()}>Sign Out</button>
      </div>
    )
  }
  
  return (
    <button onClick={() => signIn('<EMAIL>', 'password')}>
      Sign In
    </button>
  )
}
```

### Available Auth Methods

The Auth Context provides the following methods:

- `signIn(email, password)`: Log in a user
- `signUp(email, password, userData)`: Register a new user
- `signOut()`: Log out the current user
- `resetPassword(email)`: Send a password reset email
- `updatePassword(newPassword)`: Update the user's password
- `updateProfile(userData)`: Update user profile information
- `refreshSession()`: Manually refresh the auth session

### Auth State

The Auth Context provides these state variables:

- `user`: The currently authenticated user (or null)
- `session`: The active Supabase session (or null)
- `loading`: Boolean indicating if auth state is being loaded

## Redux Integration

If your component uses Redux, you can access auth state from the Redux store:

```typescript
import { useSelector, useDispatch } from 'react-redux'
import { selectUser, selectIsAuthenticated, signOut } from '../store/auth/authSlice'

function MyComponent() {
  const dispatch = useDispatch()
  const user = useSelector(selectUser)
  const isAuthenticated = useSelector(selectIsAuthenticated)
  
  return (
    <div>
      {isAuthenticated ? (
        <button onClick={() => dispatch(signOut())}>
          Sign Out ({user.email})
        </button>
      ) : (
        <a href="/login">Sign In</a>
      )}
    </div>
  )
}
```

### Redux Auth Actions

The Redux store provides these authentication actions:

- `signInWithSupabase(credentials)`: Log in and store auth state in Redux
- `signUpWithSupabase(credentials, userData)`: Register and store auth state
- `signOut()`: Log out and clear auth state
- `refreshSession()`: Refresh the current session
- `syncSupabaseSession()`: Manually sync Supabase session to Redux

## Protected Routes

To protect routes that require authentication:

```typescript
import { Navigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

function ProtectedRoute({ children }) {
  const { user, loading } = useAuth()
  
  if (loading) return <p>Loading...</p>
  
  if (!user) {
    // Redirect to login if not authenticated
    return <Navigate to="/login" replace />
  }
  
  // Render children if authenticated
  return children
}

// Usage
<Route 
  path="/dashboard" 
  element={
    <ProtectedRoute>
      <Dashboard />
    </ProtectedRoute>
  } 
/>
```

## Making Authenticated API Requests

The API client automatically includes authentication tokens in requests:

```typescript
import { api } from '../services/api'

// The token is automatically included
const fetchData = async () => {
  try {
    const response = await api.get('/protected-endpoint')
    return response.data
  } catch (error) {
    console.error('API request failed', error)
    // Handle auth errors if needed
    if (error.response?.status === 401) {
      // Handle unauthorized
    }
    throw error
  }
}
```

## Auth Persistence

Supabase Auth manages session persistence automatically, with these available options:

- `'local'`: Persist session in localStorage (default)
- `'session'`: Persist session in sessionStorage (cleared when tab is closed)
- `'none'`: No persistence, in-memory only

To change the persistence type:

```typescript
// In src/lib/supabase.ts
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    storageKey: 'app-auth',
    storage: {
      getItem: (key) => localStorage.getItem(key),
      setItem: (key, value) => localStorage.setItem(key, value),
      removeItem: (key) => localStorage.removeItem(key),
    },
  },
})
```

## Working with User Data

### User Object Structure

The user object includes:

```typescript
{
  id: string;           // User ID
  email: string;        // User email
  created_at: string;   // ISO timestamp
  app_metadata: {       // Application metadata
    provider: string;   // Auth provider (e.g., 'email')
  };
  user_metadata: {      // Custom user metadata
    name?: string;      // Optional user name
    // Any other custom fields
  }
}
```

### Updating User Profile

```typescript
const { updateProfile } = useAuth()

// Update the user's profile
await updateProfile({
  name: 'New Name',
  custom_field: 'Custom Value'
})
```

## Auth Events

To listen for auth events:

```typescript
import { useEffect } from 'react'
import { supabase } from '../lib/supabase'

function AuthEventListener() {
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth event:', event)
      console.log('Session:', session)
      
      switch (event) {
        case 'SIGNED_IN':
          // Handle sign in
          break
        case 'SIGNED_OUT':
          // Handle sign out
          break
        case 'TOKEN_REFRESHED':
          // Handle token refresh
          break
        case 'USER_UPDATED':
          // Handle user data update
          break
      }
    })
    
    // Cleanup subscription
    return () => subscription.unsubscribe()
  }, [])
  
  return null
}
```

## Auth State Synchronization

The frontend automatically synchronizes auth state between:
- Supabase Auth
- Redux store
- Local storage persistence

This ensures that authentication status is consistent throughout the application.

## Common Auth Patterns

### Login Form

```typescript
import { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'

function LoginForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState(null)
  const { signIn, loading } = useAuth()
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    setError(null)
    
    try {
      await signIn(email, password)
      // Redirect or show success message
    } catch (err) {
      setError(err.message || 'Failed to sign in')
    }
  }
  
  return (
    <form onSubmit={handleSubmit}>
      {error && <p className="error">{error}</p>}
      <div>
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
      </div>
      <div>
        <label htmlFor="password">Password</label>
        <input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
      </div>
      <button type="submit" disabled={loading}>
        {loading ? 'Signing in...' : 'Sign In'}
      </button>
    </form>
  )
}
```

### Registration Form

```typescript
import { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'

function RegisterForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [name, setName] = useState('')
  const [error, setError] = useState(null)
  const { signUp, loading } = useAuth()
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    setError(null)
    
    try {
      await signUp(email, password, { name })
      // Show success or verification required message
    } catch (err) {
      setError(err.message || 'Failed to register')
    }
  }
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields similar to login form */}
      <div>
        <label htmlFor="name">Name</label>
        <input
          id="name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
        />
      </div>
      {/* Email and password fields */}
      <button type="submit" disabled={loading}>
        {loading ? 'Registering...' : 'Register'}
      </button>
    </form>
  )
}
```

### Password Reset

```typescript
import { useState } from 'react'
import { useAuth } from '../contexts/AuthContext'

function PasswordResetForm() {
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState(null)
  const [error, setError] = useState(null)
  const { resetPassword, loading } = useAuth()
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    setError(null)
    setMessage(null)
    
    try {
      await resetPassword(email)
      setMessage('Password reset link sent to your email')
    } catch (err) {
      setError(err.message || 'Failed to send reset email')
    }
  }
  
  return (
    <form onSubmit={handleSubmit}>
      {message && <p className="success">{message}</p>}
      {error && <p className="error">{error}</p>}
      <div>
        <label htmlFor="email">Email</label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
      </div>
      <button type="submit" disabled={loading}>
        {loading ? 'Sending...' : 'Reset Password'}
      </button>
    </form>
  )
}
```

## Troubleshooting

### Session Not Persisting

If sessions aren't persisting:

1. Check that localStorage is available and not blocked
2. Verify the Supabase client configuration
3. Ensure the AuthContext provider is at the root of your application

### Authentication Errors

If you're seeing authentication errors:

1. Check browser console for specific error messages
2. Verify that environment variables are correctly set
3. Ensure the backend API is properly configured for Supabase Auth
4. Check for CORS issues if backend is on a different domain

### Redux State Not Updating

If Redux state isn't updating with auth changes:

1. Verify the Redux middleware is correctly set up
2. Check that auth actions are being dispatched
3. Ensure selectors are correctly accessing the updated state structure

## Best Practices

1. **Use AuthContext for UI components**: It provides the most direct access to auth state
2. **Use Redux for complex state management**: When you need to combine auth with other state
3. **Handle loading states**: Always account for the initial loading state
4. **Error handling**: Provide clear error messages for auth failures
5. **Session verification**: Verify auth status before rendering protected content
6. **Automatic redirects**: Redirect unauthenticated users to login page
7. **Token security**: Never store tokens in code or expose them to users

## Further Resources

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [React Auth Hooks](https://supabase.com/docs/guides/auth/auth-helpers/auth-ui)
- [Supabase Auth UI Components](https://supabase.com/docs/guides/auth/auth-ui) 