# tests/test_wizard_prompts.py
import pytest
from app.ai_assistant.prompts.wizard_prompts import (
    brainstorm_titles_prompt,
    hook_variations_prompt,
    intro_cta_prompt,
    outline_prompt,
    draft_prompt,
    edit_prompt
)

def test_brainstorm_titles_prompt():
    topic = "AI in Marketing"
    audience = "Small Business Owners"
    prompt = brainstorm_titles_prompt(topic, audience)
    assert f"'{topic}'" in prompt
    assert f"'{audience}'" in prompt
    assert "5 creative and engaging video title ideas" in prompt

def test_hook_variations_prompt():
    title = "The Future of AI Marketing"
    prompt = hook_variations_prompt(title)
    assert f"'{title}'" in prompt
    assert "3 distinct and compelling 1-sentence hooks" in prompt

def test_intro_cta_prompt():
    hook = "Imagine your marketing on autopilot."
    prompt = intro_cta_prompt(hook)
    assert f"'{hook}'" in prompt
    assert "Two different 30-second video intros" in prompt
    assert "Two different 20-second concluding calls-to-action" in prompt

def test_outline_prompt():
    intro = "Intro text"
    cta = "CTA text"
    title = "Video Title"
    prompt = outline_prompt(intro, cta, title)
    assert f"'{title}'" in prompt
    assert f"'{intro}'" in prompt
    assert f"'{cta}'" in prompt
    assert "structured outline for the main body" in prompt
    assert "4 distinct sections" in prompt

def test_draft_prompt():
    title = "Script Title"
    outline_data = {
        "sections": [
            {"heading": "Section 1", "bullets": ["Point A", "Point B"]},
            {"heading": "Section 2", "bullets": ["Point C"]}
        ]
    }
    prompt = draft_prompt(outline_data, title)
    assert f"'{title}'" in prompt
    assert "Section: Section 1" in prompt
    assert "- Point A" in prompt
    assert "- Point B" in prompt
    assert "Section: Section 2" in prompt
    assert "- Point C" in prompt
    assert "Write the full video script" in prompt

def test_edit_prompt():
    draft = "This is the long draft text."
    style = "make it punchy and under 30 seconds"
    prompt_default = edit_prompt(draft)
    prompt_custom = edit_prompt(draft, style)

    assert draft in prompt_default
    assert "make it snappier and cut to 60 seconds total" in prompt_default
    assert "Edit the following video script draft" in prompt_default

    assert draft in prompt_custom
    assert style in prompt_custom
    assert "Edit the following video script draft" in prompt_custom 