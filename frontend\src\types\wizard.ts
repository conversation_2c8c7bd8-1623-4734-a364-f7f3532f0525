// Define the types for the wizard state and actions

// Enum for wizard action types
export enum WizardActionType {
  SET_BUSINESS_CONTEXT = 'SET_BUSINESS_CONTEXT',
  SET_CONTENT_IDEA = 'SET_CONTENT_IDEA',
  SET_CURRENT_STEP = 'SET_CURRENT_STEP',
  SET_DRAFT_TEXT = 'SET_DRAFT_TEXT',
  SET_SESSION_DATA = 'SET_SESSION_DATA',
  UPDATE_SELECTED_INDICES = 'UPDATE_SELECTED_INDICES',
  RESET_STATE = 'RESET_STATE'
}

// Types for wizard data
export interface BrainstormResult {
  title?: string;
  text?: string;
  [key: string]: any;
}

export interface HookResult {
  text?: string;
  title?: string;
  [key: string]: any;
}

export interface OutlineResult {
  text?: string;
  sections?: string[];
  [key: string]: any;
}

// Interface for the wizard session data
export interface WizardSessionData {
  id: string;
  contentIdea: string;
  brainstormResults: BrainstormResult[];
  hookResults: HookResult[];
  outlineResults: OutlineResult[];
  selectedIndices: {
    title: number | null;
    hook: number | null;
    intro: number | null;
    outline: number | null;
    draft: number | null;
  };
}

// Interface for the wizard state
export interface WizardState {
  businessContextId: string;
  currentStep: number;
  draftText: string;
  sessionData: WizardSessionData;
}

// Interface for wizard actions
export interface WizardAction {
  type: WizardActionType;
  payload: any;
}

// Initial state for the wizard
export const initialWizardState: WizardState = {
  businessContextId: '',
  currentStep: 0,
  draftText: '',
  sessionData: {
    id: '',
    contentIdea: '',
    brainstormResults: [],
    hookResults: [],
    outlineResults: [],
    selectedIndices: {
      title: null,
      hook: null,
      intro: null,
      outline: null,
      draft: null
    }
  }
};

// Wizard reducer function
export const wizardReducer = (state: WizardState, action: WizardAction): WizardState => {
  switch (action.type) {
    case WizardActionType.SET_BUSINESS_CONTEXT:
      return {
        ...state,
        businessContextId: action.payload
      };
    case WizardActionType.SET_CONTENT_IDEA:
      return {
        ...state,
        sessionData: {
          ...state.sessionData,
          contentIdea: action.payload
        }
      };
    case WizardActionType.SET_CURRENT_STEP:
      return {
        ...state,
        currentStep: action.payload
      };
    case WizardActionType.SET_DRAFT_TEXT:
      return {
        ...state,
        draftText: action.payload
      };
    case WizardActionType.SET_SESSION_DATA:
      return {
        ...state,
        sessionData: action.payload
      };
    case WizardActionType.UPDATE_SELECTED_INDICES:
      return {
        ...state,
        sessionData: {
          ...state.sessionData,
          selectedIndices: {
            ...state.sessionData.selectedIndices,
            ...action.payload
          }
        }
      };
    case WizardActionType.RESET_STATE:
      return initialWizardState;
    default:
      return state;
  }
};

// Helper function to save wizard progress
export const saveWizardProgress = (state: WizardState): void => {
  // This would typically save to localStorage or make an API call
  console.log('Saving wizard progress:', state);
  try {
    localStorage.setItem('wizardProgress', JSON.stringify(state));
  } catch (error) {
    console.error('Failed to save wizard progress:', error);
  }
};
