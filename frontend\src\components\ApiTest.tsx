import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Grid,
  Divider,
  Alert,
} from "@mui/material";
import { api } from "../services/api";

const ApiTest: React.FC = () => {
  const [result, setResult] = useState<string>("No results yet");
  const [error, setError] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(
    localStorage.getItem("token")
  );
  const [decodedToken, setDecodedToken] = useState<any>(null);

  useEffect(() => {
    // Check for token on mount
    const storedToken = localStorage.getItem("token");
    setToken(storedToken);

    if (storedToken) {
      analyzeToken(storedToken);
    }
  }, []);

  const analyzeToken = (tokenString: string) => {
    try {
      // Just decode without verification
      const parts = tokenString.split(".");
      if (parts.length !== 3) {
        setError("Invalid token format: not a JWT token (should have 3 parts)");
        return;
      }

      // Decode the payload
      const payload = JSON.parse(atob(parts[1]));
      setDecodedToken(payload);

      // Check expiration
      if (payload.exp) {
        const tokenExpiry = new Date(payload.exp * 1000);
        const clientTime = new Date();

        const isExpired = tokenExpiry < clientTime;

        // Check for year difference
        if (tokenExpiry.getFullYear() !== clientTime.getFullYear()) {
          console.warn(
            `Token year (${tokenExpiry.getFullYear()}) differs from client year (${clientTime.getFullYear()})`
          );
        }
      }
    } catch (e) {
      console.error("Error analyzing token:", e);
      setError(
        `Error analyzing token: ${e instanceof Error ? e.message : String(e)}`
      );
    }
  };

  const pingBackend = async () => {
    setResult("Testing connection...");
    setError(null);

    try {
      const response = await api.get("/health");
      setResult(
        `Backend health check successful: ${JSON.stringify(response.data)}`
      );
    } catch (error: any) {
      console.error("Health check failed:", error);
      setError(`Health check failed: ${error.message}`);

      try {
        // Try auth ping as fallback
        const authResponse = await api.get("/auth/ping");
        setResult(`Auth ping successful: ${JSON.stringify(authResponse.data)}`);
        setError(null);
      } catch (authError: any) {
        console.error("Auth ping failed too:", authError);
        setError(`Both health checks failed. Last error: ${authError.message}`);
      }
    }
  };

  const testAuth = async () => {
    setResult("Testing authentication...");
    setError(null);

    try {
      // Only proceed if we have a token
      if (!token) {
        setError("No authentication token found in local storage");
        return;
      }

      // Test protected endpoint
      const response = await api.get("/auth/profile");
      setResult(
        `Authentication successful. Profile data: ${JSON.stringify(
          response.data
        )}`
      );
    } catch (error: any) {
      console.error("Auth test failed:", error);
      setError(
        `Authentication test failed: ${error.message || "Unknown error"}`
      );

      if (error.response?.status === 401) {
        setResult("Token rejected by server (401 Unauthorized)");
      }
    }
  };

  const clearToken = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("sessionExpiry");
    setToken(null);
    setDecodedToken(null);
    setResult("Token and user data cleared from localStorage");
  };

  const requestNewToken = async () => {
    // This is just for testing - in a real app, this would require valid credentials
    setResult("Requesting new test token...");
    setError(null);

    try {
      // For testing only - creating a mock token
      // In production, you would call a login API
      const mockToken =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjk5OTk5OTk5OTl9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";

      localStorage.setItem("token", mockToken);
      setToken(mockToken);
      analyzeToken(mockToken);

      setResult("New test token stored. Check decoded token for details.");
    } catch (error: any) {
      setError(`Failed to get new token: ${error.message || "Unknown error"}`);
    }
  };

  return (
    <Box sx={{ padding: 3 }}>
      <Typography variant="h4" gutterBottom>
        API and Authentication Test
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Connection Tests
            </Typography>
            <Button
              variant="contained"
              onClick={pingBackend}
              sx={{ mr: 2, mb: 2 }}
            >
              Ping Backend
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={testAuth}
              sx={{ mb: 2 }}
            >
              Test Authentication
            </Button>

            <Divider sx={{ my: 2 }} />

            <Typography variant="h6" gutterBottom>
              Token Management
            </Typography>
            <Button
              variant="outlined"
              color="secondary"
              onClick={clearToken}
              sx={{ mr: 2, mb: 2 }}
            >
              Clear Token
            </Button>
            <Button variant="outlined" onClick={requestNewToken} sx={{ mb: 2 }}>
              Get New Test Token
            </Button>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Current Token
            </Typography>
            {token ? (
              <TextField
                fullWidth
                label="JWT Token"
                variant="outlined"
                value={token}
                InputProps={{ readOnly: true }}
                sx={{ mb: 2 }}
              />
            ) : (
              <Alert severity="warning" sx={{ mb: 2 }}>
                No token found in localStorage
              </Alert>
            )}

            {decodedToken && (
              <>
                <Typography variant="subtitle1" gutterBottom>
                  Decoded Token Payload:
                </Typography>
                <Box
                  component="pre"
                  sx={{
                    p: 2,
                    bgcolor: "background.paper",
                    border: "1px solid",
                    borderColor: "divider",
                    borderRadius: 1,
                    overflow: "auto",
                    maxHeight: 200,
                  }}
                >
                  {JSON.stringify(decodedToken, null, 2)}
                </Box>

                {decodedToken.exp && (
                  <Alert
                    severity={
                      new Date(decodedToken.exp * 1000) > new Date()
                        ? "info"
                        : "error"
                    }
                    sx={{ mt: 2 }}
                  >
                    Token{" "}
                    {new Date(decodedToken.exp * 1000) > new Date()
                      ? "valid until"
                      : "expired at"}
                    : {new Date(decodedToken.exp * 1000).toLocaleString()}
                  </Alert>
                )}
              </>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Results
            </Typography>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}
            <Box
              component="pre"
              sx={{
                p: 2,
                bgcolor: "background.paper",
                border: "1px solid",
                borderColor: "divider",
                borderRadius: 1,
                overflow: "auto",
              }}
            >
              {result}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ApiTest;
