from pydantic import BaseModel, Field, EmailStr, ConfigDict
from typing import Optional, Dict, Any
from datetime import datetime
import uuid


class UserProfile(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    email: EmailStr
    name: Optional[str] = None
    created_at: datetime = Field(
        default_factory=datetime.now
    )  # Assuming DB defaults or ORM handles this
    updated_at: datetime = Field(
        default_factory=datetime.now
    )  # Assuming DB defaults or ORM handles this
    # Add other fields inferred from auth.users if necessary
    app_metadata: Optional[Dict[str, Any]] = None
    user_metadata: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)
